version: '3'
services:
  web:
    build: .
    image: aioslink:latest
    container_name: aioslink
    ports:
      - "80:80"
    # depends_on:
    #   -  postgres
    volumes:
      - .:/src
    environment:
      DATABASE_URL: **********************************************/aioslink
    command: uvicorn app.main:app --host 0.0.0.0 --port 80 --reload
    networks:
      - my_network
    env_file:
      - ./.env
  # db:
  #   image: mysql:latest
  #   restart: always
  #   environment:
  #     MYSQL_DATABASE: 'exrunner'
  #     MYSQL_USER: 'exnodes'
  #     MYSQL_PASSWORD: '123456789'
  #     MYSQL_ROOT_PASSWORD: '123456789'
  #   ports:
  #     - '3306:3306'
  #   networks:
  #     - my_network

      # my_network:
      #   ipv4_address: **********
    # command: --bind-address=0.0.0.0
  # postgres:
  #   image: "postgres:latest"
  #   restart: 'always'
  #   environment:
  #     POSTGRES_DB: "aioslink"
  #     POSTGRES_USER: "postgres"
  #     POSTGRES_PASSWORD: "123456"
  #   ports:
  #     - "5432:5432"
  #   volumes:
  #     - postgres_data:/var/lib/postgresql/data
  #   networks:
  #     - my_network
  # redis:
  #   image: 'redis:latest'
  #   container_name: exrunnerapp_redis
  #   ports:
  #     - "6379:6379"
  #   environment:
  #     - ALLOW_EMPTY_PASSWORD=no
  #     - REDIS_PASSWORD=my_redis_password
  #     - REDIS_PORT=6379
  #   networks:
  #     - my_network


networks:
  my_network:
    driver: bridge
# volumes:
#   postgres_data: