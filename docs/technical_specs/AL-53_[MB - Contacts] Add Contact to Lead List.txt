{"type":"heading","attrs":{"level":3},"content":[{"type":"text","text":"1. Use Case Description"}]}, {"type":"paragraph","content":[{"type":"text","text":"As a user, I want to convert one or more of my existing contacts into leads"},{"type":"hardBreak"},{"type":"text","text":"so that I can track and manage higher-intent relationships separately from my general contact list."}]}, {"type":"rule"}, {"type":"heading","attrs":{"level":3},"content":[{"type":"text","text":"2. User Workflow (Step-by-Step)"}]}, {"type":"heading","attrs":{"level":4},"content":[{"type":"text","text":"🟦 Flow A: Convert Multiple Contacts via Lead List"}]}, {"type":"orderedList","attrs":{"order":1},"content":[{"type":"listItem","content":[{"type":"paragraph","content":[{"type":"text","text":"User navigates to the "},{"type":"text","text":"Contacts tab","marks":[{"type":"strong"}]},{"type":"text","text":"."}]}]},{"type":"listItem","content":[{"type":"paragraph","content":[{"type":"text","text":"User selects the "},{"type":"text","text":"“Leads”","marks":[{"type":"strong"}]},{"type":"text","text":" sub-tab."}]}]},{"type":"listItem","content":[{"type":"paragraph","content":[{"type":"text","text":"User taps the "},{"type":"text","text":"“Convert to Lead”","marks":[{"type":"strong"}]},{"type":"text","text":" button."}]}]},{"type":"listItem","content":[{"type":"paragraph","content":[{"type":"text","text":"System opens a modal allowing the user to "},{"type":"text","text":"select multiple contacts","marks":[{"type":"strong"}]},{"type":"text","text":"."}]}]},{"type":"listItem","content":[{"type":"paragraph","content":[{"type":"text","text":"User selects one or more contacts and taps "},{"type":"text","text":"“Save”","marks":[{"type":"strong"}]},{"type":"text","text":"."}]}]},{"type":"listItem","content":[{"type":"paragraph","content":[{"type":"text","text":"Selected contacts are removed from the general "},{"type":"text","text":"Contact List","marks":[{"type":"strong"}]},{"type":"text","text":" and moved to the "},{"type":"text","text":"Lead List","marks":[{"type":"strong"}]},{"type":"text","text":"."}]}]},{"type":"listItem","content":[{"type":"paragraph","content":[{"type":"text","text":"Toast appears: "},{"type":"text","text":"“3 contacts have been converted to leads.”","marks":[{"type":"em"}]}]}]}]}, {"type":"heading","attrs":{"level":4},"content":[{"type":"text","text":"🟦 Flow B: Convert Individual Contact via Profile"}]}, {"type":"orderedList","attrs":{"order":1},"content":[{"type":"listItem","content":[{"type":"paragraph","content":[{"type":"text","text":"User navigates to a specific "},{"type":"text","text":"Contact Detail screen","marks":[{"type":"strong"}]},{"type":"text","text":"."}]}]},{"type":"listItem","content":[{"type":"paragraph","content":[{"type":"text","text":"User taps the "},{"type":"text","text":"“More”","marks":[{"type":"strong"}]},{"type":"text","text":" (•••) menu and selects "},{"type":"text","text":"“Convert to Lead”","marks":[{"type":"strong"}]},{"type":"text","text":"."}]}]},{"type":"listItem","content":[{"type":"paragraph","content":[{"type":"text","text":"System shows a confirmation modal:"},{"type":"hardBreak"},{"type":"text","text":"“Convert [Contact Name] to your lead list?”","marks":[{"type":"em"}]}]}]},{"type":"listItem","content":[{"type":"paragraph","content":[{"type":"text","text":"User confirms."}]}]},{"type":"listItem","content":[{"type":"paragraph","content":[{"type":"text","text":"Contact is moved from Contact List to Lead List."}]}]},{"type":"listItem","content":[{"type":"paragraph","content":[{"type":"text","text":"Toast appears: "},{"type":"text","text":"“[Contact Name] has been converted to a lead.”","marks":[{"type":"em"}]}]}]}]}, {"type":"rule"}, {"type":"heading","attrs":{"level":3},"content":[{"type":"text","text":"3. Field Definitions Table"}]}, {"type":"table","attrs":{"isNumberColumnEnabled":false,"layout":"center","localId":"733c128a-6d2c-4847-a81d-4279959ab329"},"content":[{"type":"tableRow","content":[{"type":"tableHeader","attrs":{},"content":[{"type":"paragraph","content":[{"type":"text","text":"Field Name"}]}]},{"type":"tableHeader","attrs":{},"content":[{"type":"paragraph","content":[{"type":"text","text":"Field Type"}]}]},{"type":"tableHeader","attrs":{},"content":[{"type":"paragraph","content":[{"type":"text","text":"Validation Rule"}]}]},{"type":"tableHeader","attrs":{},"content":[{"type":"paragraph","content":[{"type":"text","text":"Mandatory"}]}]},{"type":"tableHeader","attrs":{},"content":[{"type":"paragraph","content":[{"type":"text","text":"Description"}]}]}]},{"type":"tableRow","content":[{"type":"tableCell","attrs":{},"content":[{"type":"paragraph","content":[{"type":"text","text":"Selected Contacts"}]}]},{"type":"tableCell","attrs":{},"content":[{"type":"paragraph","content":[{"type":"text","text":"Multi-Select List"}]}]},{"type":"tableCell","attrs":{},"content":[{"type":"paragraph","content":[{"type":"text","text":"Must be in Contact List"}]}]},{"type":"tableCell","attrs":{},"content":[{"type":"paragraph","content":[{"type":"text","text":"Yes"}]}]},{"type":"tableCell","attrs":{},"content":[{"type":"paragraph","content":[{"type":"text","text":"Contacts chosen in bulk conversion flow"}]}]}]},{"type":"tableRow","content":[{"type":"tableCell","attrs":{},"content":[{"type":"paragraph","content":[{"type":"text","text":"Convert Action"}]}]},{"type":"tableCell","attrs":{},"content":[{"type":"paragraph","content":[{"type":"text","text":"Button"}]}]},{"type":"tableCell","attrs":{},"content":[{"type":"paragraph","content":[{"type":"text","text":"Enabled when 1+ contact selected"}]}]},{"type":"tableCell","attrs":{},"content":[{"type":"paragraph","content":[{"type":"text","text":"Yes"}]}]},{"type":"tableCell","attrs":{},"content":[{"type":"paragraph","content":[{"type":"text","text":"Triggers conversion and updates lead state"}]}]}]},{"type":"tableRow","content":[{"type":"tableCell","attrs":{},"content":[{"type":"paragraph","content":[{"type":"text","text":"Confirmation Modal"}]}]},{"type":"tableCell","attrs":{},"content":[{"type":"paragraph","content":[{"type":"text","text":"Dialog"}]}]},{"type":"tableCell","attrs":{},"content":[{"type":"paragraph","content":[{"type":"text","text":"Only shown in single conversion"}]}]},{"type":"tableCell","attrs":{},"content":[{"type":"paragraph","content":[{"type":"text","text":"Yes"}]}]},{"type":"tableCell","attrs":{},"content":[{"type":"paragraph","content":[{"type":"text","text":"Prompts user before moving a contact from contact to lead list"}]}]}]}]}, {"type":"rule"}, {"type":"heading","attrs":{"level":3},"content":[{"type":"text","text":"4. Data Display Table (Lead Card Format)"}]}, {"type":"table","attrs":{"isNumberColumnEnabled":false,"layout":"center","localId":"6e8d2d30-8f4d-4162-a778-e5f9e1547e50"},"content":[{"type":"tableRow","content":[{"type":"tableHeader","attrs":{},"content":[{"type":"paragraph","content":[{"type":"text","text":"Field"}]}]},{"type":"tableHeader","attrs":{},"content":[{"type":"paragraph","content":[{"type":"text","text":"Format Example"}]}]},{"type":"tableHeader","attrs":{},"content":[{"type":"paragraph","content":[{"type":"text","text":"Notes"}]}]}]},{"type":"tableRow","content":[{"type":"tableCell","attrs":{},"content":[{"type":"paragraph","content":[{"type":"text","text":"Name"}]}]},{"type":"tableCell","attrs":{},"content":[{"type":"paragraph","content":[{"type":"text","text":"“Sarah Miller”"}]}]},{"type":"tableCell","attrs":{},"content":[{"type":"paragraph","content":[{"type":"text","text":"Full contact name"}]}]}]},{"type":"tableRow","content":[{"type":"tableCell","attrs":{},"content":[{"type":"paragraph","content":[{"type":"text","text":"Badge"}]}]},{"type":"tableCell","attrs":{},"content":[{"type":"paragraph","content":[{"type":"text","text":"“Investor”"}]}]},{"type":"tableCell","attrs":{},"content":[{"type":"paragraph","content":[{"type":"text","text":"Optional tag or label"}]}]}]},{"type":"tableRow","content":[{"type":"tableCell","attrs":{},"content":[{"type":"paragraph","content":[{"type":"text","text":"Activity History"}]}]},{"type":"tableCell","attrs":{},"content":[{"type":"paragraph","content":[{"type":"text","text":"“Message sent 2w ago”"}]}]},{"type":"tableCell","attrs":{},"content":[{"type":"paragraph","content":[{"type":"text","text":"Carries over from previous contact state"}]}]}]},{"type":"tableRow","content":[{"type":"tableCell","attrs":{},"content":[{"type":"paragraph","content":[{"type":"text","text":"CTA Icons"}]}]},{"type":"tableCell","attrs":{},"content":[{"type":"paragraph","content":[{"type":"text","text":"Refer, Add Note, Remove"}]}]},{"type":"tableCell","attrs":{},"content":[{"type":"paragraph","content":[{"type":"text","text":"Same icon row as in contact list"}]}]}]}]}, {"type":"rule"}, {"type":"heading","attrs":{"level":3},"content":[{"type":"text","text":"5. Acceptance Criteria (AC)"}]}, {"type":"bulletList","content":[{"type":"listItem","content":[{"type":"paragraph","content":[{"type":"text","text":"AC1:","marks":[{"type":"strong"}]},{"type":"text","text":" Users can access “Convert to Lead” button from the Lead List view."}]}]},{"type":"listItem","content":[{"type":"paragraph","content":[{"type":"text","text":"AC2:","marks":[{"type":"strong"}]},{"type":"text","text":" Multi-select modal must only show existing contacts not already in the Lead List."}]}]},{"type":"listItem","content":[{"type":"paragraph","content":[{"type":"text","text":"AC3:","marks":[{"type":"strong"}]},{"type":"text","text":" Selected contacts must be removed from Contact List and added to Lead List immediately after saving."}]}]},{"type":"listItem","content":[{"type":"paragraph","content":[{"type":"text","text":"AC4:","marks":[{"type":"strong"}]},{"type":"text","text":" From an individual contact profile, user can also select “Convert to Lead” from the menu."}]}]},{"type":"listItem","content":[{"type":"paragraph","content":[{"type":"text","text":"AC5:","marks":[{"type":"strong"}]},{"type":"text","text":" Single contact conversion triggers confirmation modal; user must confirm action before update."}]}]},{"type":"listItem","content":[{"type":"paragraph","content":[{"type":"text","text":"AC6:","marks":[{"type":"strong"}]},{"type":"text","text":" After conversion, contact no longer appears in the Contact List."}]}]},{"type":"listItem","content":[{"type":"paragraph","content":[{"type":"text","text":"AC7:","marks":[{"type":"strong"}]},{"type":"text","text":" Contact notes, labels, and metadata are preserved and transferred to the Lead List view."}]}]},{"type":"listItem","content":[{"type":"paragraph","content":[{"type":"text","text":"AC8:","marks":[{"type":"strong"}]},{"type":"text","text":" Contacts already in the Lead List must not be shown in conversion modals."}]}]},{"type":"listItem","content":[{"type":"paragraph","content":[{"type":"text","text":"AC9:","marks":[{"type":"strong"}]},{"type":"text","text":" Converted leads must respect role filters (User / Merchant) and appear accordingly."}]}]},{"type":"listItem","content":[{"type":"paragraph","content":[{"type":"text","text":"AC10:","marks":[{"type":"strong"}]},{"type":"text","text":" Repeated conversions are not allowed unless the contact is first removed from Lead List."}]}]}]}, {"type":"rule"}, {"type":"heading","attrs":{"level":3},"content":[{"type":"text","text":"6. System Rules"}]}, {"type":"bulletList","content":[{"type":"listItem","content":[{"type":"paragraph","content":[{"type":"text","text":"Contact-to-lead relationship is mutually exclusive: a person can be "},{"type":"text","text":"either a contact or a lead","marks":[{"type":"strong"}]},{"type":"text","text":", never both."}]}]},{"type":"listItem","content":[{"type":"paragraph","content":[{"type":"text","text":"System must maintain referential data (e.g., notes, last message sent) during the move."}]}]},{"type":"listItem","content":[{"type":"paragraph","content":[{"type":"text","text":"Action logs are recorded for audit purposes but no notification is sent to the contact."}]}]},{"type":"listItem","content":[{"type":"paragraph","content":[{"type":"text","text":"Conversion state is stored in the backend and persists across sessions/devices."}]}]}]}, {"type":"rule"}, {"type":"heading","attrs":{"level":3},"content":[{"type":"text","text":"7. UX Optimizations"}]}, {"type":"bulletList","content":[{"type":"listItem","content":[{"type":"paragraph","content":[{"type":"text","text":"Modal uses searchable multi-select with avatars and roles."}]}]},{"type":"listItem","content":[{"type":"paragraph","content":[{"type":"text","text":"Conversion confirmation uses brief toast or banner:"}]},{"type":"bulletList","content":[{"type":"listItem","content":[{"type":"paragraph","content":[{"type":"text","text":"“[X] contact(s) moved to Lead List”","marks":[{"type":"em"}]}]}]}]}]},{"type":"listItem","content":[{"type":"paragraph","content":[{"type":"text","text":"Visual difference between Contact and Lead cards for clarity."}]}]},{"type":"listItem","content":[{"type":"paragraph","content":[{"type":"text","text":"Inline badge: "},{"type":"text","text":"“Recently converted”","marks":[{"type":"em"}]},{"type":"text","text":" (optional, expires after 48h)."}]}]},{"type":"listItem","content":[{"type":"paragraph","content":[{"type":"text","text":"Empty state in Convert modal if no eligible contacts are found."}]}]}]}