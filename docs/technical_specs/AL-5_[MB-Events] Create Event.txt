{"type":"heading","attrs":{"level":2},"content":[{"type":"text","text":"1. Use Case Description"}]}, {"type":"paragraph","content":[{"type":"text","text":"As a "},{"type":"text","text":"premium user or merchant","marks":[{"type":"strong"}]},{"type":"text","text":", I want to create events so that I can host sessions, invite attendees, and promote my gatherings via the AIOSLink platform."}]}, {"type":"rule"}, {"type":"heading","attrs":{"level":2},"content":[{"type":"text","text":"2. User Workflow (Step-by-Step)"}]}, {"type":"orderedList","attrs":{"order":1},"content":[{"type":"listItem","content":[{"type":"paragraph","content":[{"type":"text","text":"User navigates to “Events” → “My Events” → Tap “Create Event”"}]}]},{"type":"listItem","content":[{"type":"paragraph","content":[{"type":"text","text":"System checks if user role = merchant OR subscription = premium"}]}]},{"type":"listItem","content":[{"type":"paragraph","content":[{"type":"text","text":"Fills out event creation form with the following fields:"}]},{"type":"bulletList","content":[{"type":"listItem","content":[{"type":"paragraph","content":[{"type":"text","text":"Title"}]}]},{"type":"listItem","content":[{"type":"paragraph","content":[{"type":"text","text":"Date & Time"}]}]},{"type":"listItem","content":[{"type":"paragraph","content":[{"type":"text","text":"Location"}]}]},{"type":"listItem","content":[{"type":"paragraph","content":[{"type":"text","text":"Visibility (Public/Private)"}]}]},{"type":"listItem","content":[{"type":"paragraph","content":[{"type":"text","text":"Free or Ticketed Event "}]}]},{"type":"listItem","content":[{"type":"paragraph","content":[{"type":"text","text":"Max Attendees (optional)"}]}]},{"type":"listItem","content":[{"type":"paragraph","content":[{"type":"text","text":"Category Tags (optional)"}]}]},{"type":"listItem","content":[{"type":"paragraph","content":[{"type":"text","text":"Event Cover Image (optional)"}]}]},{"type":"listItem","content":[{"type":"paragraph","content":[{"type":"text","text":"Description (optional)"}]}]}]}]},{"type":"listItem","content":[{"type":"paragraph","content":[{"type":"text","text":"Taps “Publish”"}]}]},{"type":"listItem","content":[{"type":"paragraph","content":[{"type":"text","text":"Confirmation screen appears → redirect to “My Events”"}]}]},{"type":"listItem","content":[{"type":"paragraph","content":[{"type":"text","text":"Event appears under Upcoming → Detail screen includes Boost and RSVP tools"}]}]}]}, {"type":"rule"}, {"type":"heading","attrs":{"level":2},"content":[{"type":"text","text":"3. Acceptance Criteria (AC)"}]}, {"type":"bulletList","content":[{"type":"listItem","content":[{"type":"paragraph","content":[{"type":"text","text":"AC1","marks":[{"type":"strong"}]},{"type":"text","text":": Users must be explicitly identified as a premium user or merchant before accessing the Create Event screen."}]}]},{"type":"listItem","content":[{"type":"paragraph","content":[{"type":"text","text":"AC2","marks":[{"type":"strong"}]},{"type":"text","text":": The form must validate all required fields, including title, date/time, location, and event type, before submission."}]}]},{"type":"listItem","content":[{"type":"paragraph","content":[{"type":"text","text":"AC3","marks":[{"type":"strong"}]},{"type":"text","text":": Upon successful creation, the event must appear under \"My Events\"."}]}]},{"type":"listItem","content":[{"type":"paragraph","content":[{"type":"text","text":"AC4","marks":[{"type":"strong"}]},{"type":"text","text":": Public visibility settings must ensure the event is listed in the global Discover feed."}]}]},{"type":"listItem","content":[{"type":"paragraph","content":[{"type":"text","text":"AC5","marks":[{"type":"strong"}]},{"type":"text","text":": Hosts should be able to boost or cancel the event after creation."}]}]},{"type":"listItem","content":[{"type":"paragraph","content":[{"type":"text","text":"AC6","marks":[{"type":"strong"}]},{"type":"text","text":": Location input must allow both online and physical options."}]}]},{"type":"listItem","content":[{"type":"paragraph","content":[{"type":"text","text":"AC7","marks":[{"type":"strong"}]},{"type":"text","text":": If no image is uploaded, a fallback visual is used in the listing."}]}]},{"type":"listItem","content":[{"type":"paragraph","content":[{"type":"text","text":"AC8","marks":[{"type":"strong"}]},{"type":"text","text":": Confirmation message or toast is displayed after event is created."}]}]},{"type":"listItem","content":[{"type":"paragraph","content":[{"type":"text","text":"AC9","marks":[{"type":"strong"}]},{"type":"text","text":": Hosts can choose between Free and Ticketed event types, which must be stored and reflected appropriately in the backend."}]}]},{"type":"listItem","content":[{"type":"paragraph","content":[{"type":"text","text":"AC10","marks":[{"type":"strong"}]},{"type":"text","text":": Free users attempting to create an event should see an upgrade prompt and be restricted from proceeding."}]}]}]}, {"type":"rule"}, {"type":"heading","attrs":{"level":2},"content":[{"type":"text","text":"4. Field Definitions Table"}]}, {"type":"table","attrs":{"isNumberColumnEnabled":false,"layout":"center","localId":"11435833-5926-4b37-b8ea-d1fa424dd9d9"},"content":[{"type":"tableRow","content":[{"type":"tableHeader","attrs":{},"content":[{"type":"paragraph","content":[{"type":"text","text":"Field"}]}]},{"type":"tableHeader","attrs":{},"content":[{"type":"paragraph","content":[{"type":"text","text":"Type"}]}]},{"type":"tableHeader","attrs":{},"content":[{"type":"paragraph","content":[{"type":"text","text":"Required"}]}]},{"type":"tableHeader","attrs":{},"content":[{"type":"paragraph","content":[{"type":"text","text":"Validation / Notes"}]}]}]},{"type":"tableRow","content":[{"type":"tableCell","attrs":{},"content":[{"type":"paragraph","content":[{"type":"text","text":"Title"}]}]},{"type":"tableCell","attrs":{},"content":[{"type":"paragraph","content":[{"type":"text","text":"String"}]}]},{"type":"tableCell","attrs":{},"content":[{"type":"paragraph","content":[{"type":"text","text":"✅"}]}]},{"type":"tableCell","attrs":{},"content":[{"type":"paragraph","content":[{"type":"text","text":"3–100 characters"}]}]}]},{"type":"tableRow","content":[{"type":"tableCell","attrs":{},"content":[{"type":"paragraph","content":[{"type":"text","text":"Date & Time"}]}]},{"type":"tableCell","attrs":{},"content":[{"type":"paragraph","content":[{"type":"text","text":"DateTime"}]}]},{"type":"tableCell","attrs":{},"content":[{"type":"paragraph","content":[{"type":"text","text":"✅"}]}]},{"type":"tableCell","attrs":{},"content":[{"type":"paragraph","content":[{"type":"text","text":"Must be in the future"}]}]}]},{"type":"tableRow","content":[{"type":"tableCell","attrs":{},"content":[{"type":"paragraph","content":[{"type":"text","text":"Location"}]}]},{"type":"tableCell","attrs":{},"content":[{"type":"paragraph","content":[{"type":"text","text":"String"}]}]},{"type":"tableCell","attrs":{},"content":[{"type":"paragraph","content":[{"type":"text","text":"✅"}]}]},{"type":"tableCell","attrs":{},"content":[{"type":"paragraph","content":[{"type":"text","text":"Address or “Online”"}]}]}]},{"type":"tableRow","content":[{"type":"tableCell","attrs":{},"content":[{"type":"paragraph","content":[{"type":"text","text":"Visibility"}]}]},{"type":"tableCell","attrs":{},"content":[{"type":"paragraph","content":[{"type":"text","text":"Enum"}]}]},{"type":"tableCell","attrs":{},"content":[{"type":"paragraph","content":[{"type":"text","text":"✅"}]}]},{"type":"tableCell","attrs":{},"content":[{"type":"paragraph","content":[{"type":"text","text":"Public, Private"}]}]}]},{"type":"tableRow","content":[{"type":"tableCell","attrs":{},"content":[{"type":"paragraph","content":[{"type":"text","text":"Event Type"}]}]},{"type":"tableCell","attrs":{},"content":[{"type":"paragraph","content":[{"type":"text","text":"Enum"}]}]},{"type":"tableCell","attrs":{},"content":[{"type":"paragraph","content":[{"type":"text","text":"✅"}]}]},{"type":"tableCell","attrs":{},"content":[{"type":"paragraph","content":[{"type":"text","text":"\"Free\" or \"Ticketed\""}]}]}]},{"type":"tableRow","content":[{"type":"tableCell","attrs":{},"content":[{"type":"paragraph","content":[{"type":"text","text":"Ticket Price"}]}]},{"type":"tableCell","attrs":{},"content":[{"type":"paragraph","content":[{"type":"text","text":"Decimal"}]}]},{"type":"tableCell","attrs":{},"content":[{"type":"paragraph","content":[{"type":"text","text":"✅ if Event Type = Ticketed"}]}]},{"type":"tableCell","attrs":{},"content":[{"type":"paragraph","content":[{"type":"text","text":"Required for ticketed events; added to marketplace/storefront"}]}]}]},{"type":"tableRow","content":[{"type":"tableCell","attrs":{},"content":[{"type":"paragraph","content":[{"type":"text","text":"Max Attendees"}]}]},{"type":"tableCell","attrs":{},"content":[{"type":"paragraph","content":[{"type":"text","text":"Integer"}]}]},{"type":"tableCell","attrs":{},"content":[{"type":"paragraph","content":[{"type":"text","text":"❌"}]}]},{"type":"tableCell","attrs":{},"content":[{"type":"paragraph","content":[{"type":"text","text":"Must be > 0 if set"}]}]}]},{"type":"tableRow","content":[{"type":"tableCell","attrs":{},"content":[{"type":"paragraph","content":[{"type":"text","text":"Tags"}]}]},{"type":"tableCell","attrs":{},"content":[{"type":"paragraph","content":[{"type":"text","text":"Array"}]}]},{"type":"tableCell","attrs":{},"content":[{"type":"paragraph","content":[{"type":"text","text":"❌"}]}]},{"type":"tableCell","attrs":{},"content":[{"type":"paragraph","content":[{"type":"text","text":"Up to 3 categories"}]}]}]},{"type":"tableRow","content":[{"type":"tableCell","attrs":{},"content":[{"type":"paragraph","content":[{"type":"text","text":"Cover Image"}]}]},{"type":"tableCell","attrs":{},"content":[{"type":"paragraph","content":[{"type":"text","text":"File"}]}]},{"type":"tableCell","attrs":{},"content":[{"type":"paragraph","content":[{"type":"text","text":"❌"}]}]},{"type":"tableCell","attrs":{},"content":[{"type":"paragraph","content":[{"type":"text","text":"JPG/PNG, max size 5MB"}]}]}]},{"type":"tableRow","content":[{"type":"tableCell","attrs":{},"content":[{"type":"paragraph","content":[{"type":"text","text":"Description"}]}]},{"type":"tableCell","attrs":{},"content":[{"type":"paragraph","content":[{"type":"text","text":"String"}]}]},{"type":"tableCell","attrs":{},"content":[{"type":"paragraph","content":[{"type":"text","text":"❌"}]}]},{"type":"tableCell","attrs":{},"content":[{"type":"paragraph","content":[{"type":"text","text":"Up to 1000 characters, supports line breaks"}]}]}]}]}, {"type":"rule"}, {"type":"heading","attrs":{"level":2},"content":[{"type":"text","text":"5. Data Display Table (Host View Summary)"}]}, {"type":"table","attrs":{"isNumberColumnEnabled":false,"layout":"center","localId":"b32d26e6-4a06-4d79-99ec-84b4771913a1"},"content":[{"type":"tableRow","content":[{"type":"tableHeader","attrs":{},"content":[{"type":"paragraph","content":[{"type":"text","text":"Field"}]}]},{"type":"tableHeader","attrs":{},"content":[{"type":"paragraph","content":[{"type":"text","text":"Example"}]}]},{"type":"tableHeader","attrs":{},"content":[{"type":"paragraph","content":[{"type":"text","text":"Notes"}]}]}]},{"type":"tableRow","content":[{"type":"tableCell","attrs":{},"content":[{"type":"paragraph","content":[{"type":"text","text":"Event Title"}]}]},{"type":"tableCell","attrs":{},"content":[{"type":"paragraph","content":[{"type":"text","text":"“Startup Pitch Night”"}]}]},{"type":"tableCell","attrs":{},"content":[{"type":"paragraph","content":[{"type":"text","text":"Bold, max 1 line"}]}]}]},{"type":"tableRow","content":[{"type":"tableCell","attrs":{},"content":[{"type":"paragraph","content":[{"type":"text","text":"Date/Time"}]}]},{"type":"tableCell","attrs":{},"content":[{"type":"paragraph","content":[{"type":"text","text":"“Mon, July 8 · 7:00PM–9:00PM”"}]}]},{"type":"tableCell","attrs":{},"content":[{"type":"paragraph","content":[{"type":"text","text":"Localized"}]}]}]},{"type":"tableRow","content":[{"type":"tableCell","attrs":{},"content":[{"type":"paragraph","content":[{"type":"text","text":"RSVP Count"}]}]},{"type":"tableCell","attrs":{},"content":[{"type":"paragraph","content":[{"type":"text","text":"“24 going”"}]}]},{"type":"tableCell","attrs":{},"content":[{"type":"paragraph","content":[{"type":"text","text":"Auto-updates live"}]}]}]},{"type":"tableRow","content":[{"type":"tableCell","attrs":{},"content":[{"type":"paragraph","content":[{"type":"text","text":"Status"}]}]},{"type":"tableCell","attrs":{},"content":[{"type":"paragraph","content":[{"type":"text","text":"“Active” or “Boosted”"}]}]},{"type":"tableCell","attrs":{},"content":[{"type":"paragraph","content":[{"type":"text","text":"With status pill"}]}]}]},{"type":"tableRow","content":[{"type":"tableCell","attrs":{},"content":[{"type":"paragraph","content":[{"type":"text","text":"Visibility"}]}]},{"type":"tableCell","attrs":{},"content":[{"type":"paragraph","content":[{"type":"text","text":"“Public” / “Private”"}]}]},{"type":"tableCell","attrs":{},"content":[{"type":"paragraph","content":[{"type":"text","text":"Icon + text"}]}]}]},{"type":"tableRow","content":[{"type":"tableCell","attrs":{},"content":[{"type":"paragraph","content":[{"type":"text","text":"Event Type"}]}]},{"type":"tableCell","attrs":{},"content":[{"type":"paragraph","content":[{"type":"text","text":"“Free” or “Ticketed”"}]}]},{"type":"tableCell","attrs":{},"content":[{"type":"paragraph","content":[{"type":"text","text":"Displayed on listing card"}]}]}]}]}, {"type":"rule"}, {"type":"heading","attrs":{"level":2},"content":[{"type":"text","text":"6. Search & Filter Logic"}]}, {"type":"paragraph","content":[{"type":"text","text":"Not applicable during creation."}]}, {"type":"paragraph","content":[{"type":"text","text":"Search/filters may be used on the event list post-creation, but not part of this flow."}]}, {"type":"rule"}, {"type":"heading","attrs":{"level":2},"content":[{"type":"text","text":"7. System Rules"}]}, {"type":"bulletList","content":[{"type":"listItem","content":[{"type":"paragraph","content":[{"type":"text","text":"Required fields must be filled before publish button is enabled"}]}]},{"type":"listItem","content":[{"type":"paragraph","content":[{"type":"text","text":"Start time must be > current time"}]}]},{"type":"listItem","content":[{"type":"paragraph","content":[{"type":"text","text":"Max attendee cap = soft limit only (does not block additional RSVPs)"}]}]},{"type":"listItem","content":[{"type":"paragraph","content":[{"type":"text","text":"Events can be edited or cancelled after creation (see M06-08)"}]}]},{"type":"listItem","content":[{"type":"paragraph","content":[{"type":"text","text":"Public events appear in global Discover feed"}]}]},{"type":"listItem","content":[{"type":"paragraph","content":[{"type":"text","text":"Private events appear only via direct invite"}]}]},{"type":"listItem","content":[{"type":"paragraph","content":[{"type":"text","text":"Only premium users and merchants can access “Create Event”"}]}]},{"type":"listItem","content":[{"type":"paragraph","content":[{"type":"text","text":"Event type (Free/Ticketed) determines whether pricing screen is required later"}]}]}]}, {"type":"rule"}, {"type":"heading","attrs":{"level":2},"content":[{"type":"text","text":"8. UX Optimizations (Based on AIOS UX Standards)"}]}, {"type":"bulletList","content":[{"type":"listItem","content":[{"type":"paragraph","content":[{"type":"text","text":"Multi-step form layout (e.g. Step 1: Basics, Step 2: Settings, Step 3: Confirm)"}]}]},{"type":"listItem","content":[{"type":"paragraph","content":[{"type":"text","text":"Use tag pills with autocomplete"}]}]},{"type":"listItem","content":[{"type":"paragraph","content":[{"type":"text","text":"Preview mode toggle before final submission"}]}]},{"type":"listItem","content":[{"type":"paragraph","content":[{"type":"text","text":"Inline validation for date, image size, and title length"}]}]},{"type":"listItem","content":[{"type":"paragraph","content":[{"type":"text","text":"CTA: “Publish Event” disabled until all required fields are valid"}]}]},{"type":"listItem","content":[{"type":"paragraph","content":[{"type":"text","text":"Toast or modal: “Your event is now live”"}]}]}]}