{"type":"heading","attrs":{"level":4},"content":[{"type":"text","text":"1. Use Case Description"}]}, {"type":"paragraph","content":[{"type":"text","text":"As a host","marks":[{"type":"strong"}]},{"type":"text","text":", I want to invite attendees to an event after it has been created,"},{"type":"hardBreak"},{"type":"text","text":"so that","marks":[{"type":"strong"}]},{"type":"text","text":" I can manage participation by selecting users from my contacts or lead list and choose whether to send them tickets for free or let them purchase their own."}]}, {"type":"rule"}, {"type":"heading","attrs":{"level":4},"content":[{"type":"text","text":"2. User Workflow"}]}, {"type":"orderedList","attrs":{"order":1},"content":[{"type":"listItem","content":[{"type":"paragraph","content":[{"type":"text","text":"Host completes the creation of an event."}]}]},{"type":"listItem","content":[{"type":"paragraph","content":[{"type":"text","text":"Host opens the “Invite Attendees” option from the Event Detail or My Events page."}]}]},{"type":"listItem","content":[{"type":"paragraph","content":[{"type":"text","text":"System displays the segmented list: Contacts | Leads."}]}]},{"type":"listItem","content":[{"type":"paragraph","content":[{"type":"text","text":"Host selects users to invite."}]}]},{"type":"listItem","content":[{"type":"paragraph","content":[{"type":"text","text":"If the event is ticketed:"}]},{"type":"bulletList","content":[{"type":"listItem","content":[{"type":"paragraph","content":[{"type":"text","text":"Host chooses one of two options:"}]},{"type":"bulletList","content":[{"type":"listItem","content":[{"type":"paragraph","content":[{"type":"text","text":"Send Invite Without Ticket","marks":[{"type":"strong"}]}]}]},{"type":"listItem","content":[{"type":"paragraph","content":[{"type":"text","text":"Send Invite With Ticket (Free)","marks":[{"type":"strong"}]}]}]}]}]}]}]},{"type":"listItem","content":[{"type":"paragraph","content":[{"type":"text","text":"If “Send With Ticket” is selected:"}]},{"type":"bulletList","content":[{"type":"listItem","content":[{"type":"paragraph","content":[{"type":"text","text":"System calculates credit deduction and shows a confirmation modal with total credits to be deducted."}]}]},{"type":"listItem","content":[{"type":"paragraph","content":[{"type":"text","text":"Host confirms or cancels."}]}]}]}]},{"type":"listItem","content":[{"type":"paragraph","content":[{"type":"text","text":"Upon confirmation, invites are sent, and attendees are notified."}]}]},{"type":"listItem","content":[{"type":"paragraph","content":[{"type":"text","text":"Invite history is tracked in the backend."}]}]}]}, {"type":"rule"}, {"type":"heading","attrs":{"level":4},"content":[{"type":"text","text":"3. Field Definitions"}]}, {"type":"heading","attrs":{"level":5},"content":[{"type":"text","text":"3.1 Submission Fields"}]}, {"type":"table","attrs":{"isNumberColumnEnabled":false,"layout":"center","localId":"4ec98f23-7ddc-41f6-b4c8-c56c69144366"},"content":[{"type":"tableRow","content":[{"type":"tableHeader","attrs":{},"content":[{"type":"paragraph","content":[{"type":"text","text":"Field Name"}]}]},{"type":"tableHeader","attrs":{},"content":[{"type":"paragraph","content":[{"type":"text","text":"Type"}]}]},{"type":"tableHeader","attrs":{},"content":[{"type":"paragraph","content":[{"type":"text","text":"Required"}]}]},{"type":"tableHeader","attrs":{},"content":[{"type":"paragraph","content":[{"type":"text","text":"Description"}]}]}]},{"type":"tableRow","content":[{"type":"tableCell","attrs":{},"content":[{"type":"paragraph","content":[{"type":"text","text":"Selected Users"}]}]},{"type":"tableCell","attrs":{},"content":[{"type":"paragraph","content":[{"type":"text","text":"List"}]}]},{"type":"tableCell","attrs":{},"content":[{"type":"paragraph","content":[{"type":"text","text":"✅"}]}]},{"type":"tableCell","attrs":{},"content":[{"type":"paragraph","content":[{"type":"text","text":"Chosen from contact or lead list"}]}]}]},{"type":"tableRow","content":[{"type":"tableCell","attrs":{},"content":[{"type":"paragraph","content":[{"type":"text","text":"Invitation Type"}]}]},{"type":"tableCell","attrs":{},"content":[{"type":"paragraph","content":[{"type":"text","text":"Enum"}]}]},{"type":"tableCell","attrs":{},"content":[{"type":"paragraph","content":[{"type":"text","text":"✅"}]}]},{"type":"tableCell","attrs":{},"content":[{"type":"paragraph","content":[{"type":"text","text":"“With Ticket” or “Without Ticket”"}]}]}]},{"type":"tableRow","content":[{"type":"tableCell","attrs":{},"content":[{"type":"paragraph","content":[{"type":"text","text":"Event ID"}]}]},{"type":"tableCell","attrs":{},"content":[{"type":"paragraph","content":[{"type":"text","text":"UUID"}]}]},{"type":"tableCell","attrs":{},"content":[{"type":"paragraph","content":[{"type":"text","text":"✅"}]}]},{"type":"tableCell","attrs":{},"content":[{"type":"paragraph","content":[{"type":"text","text":"Tied to existing event object"}]}]}]}]}, {"type":"heading","attrs":{"level":5},"content":[{"type":"text","text":"3.2 Interaction Elements"}]}, {"type":"table","attrs":{"isNumberColumnEnabled":false,"layout":"center","localId":"6c5dc103-d6b8-4dbc-be21-7b9d03f6695f"},"content":[{"type":"tableRow","content":[{"type":"tableHeader","attrs":{},"content":[{"type":"paragraph","content":[{"type":"text","text":"Element Name"}]}]},{"type":"tableHeader","attrs":{},"content":[{"type":"paragraph","content":[{"type":"text","text":"Type"}]}]},{"type":"tableHeader","attrs":{},"content":[{"type":"paragraph","content":[{"type":"text","text":"Required"}]}]},{"type":"tableHeader","attrs":{},"content":[{"type":"paragraph","content":[{"type":"text","text":"Description"}]}]}]},{"type":"tableRow","content":[{"type":"tableCell","attrs":{},"content":[{"type":"paragraph","content":[{"type":"text","text":"Invite CTA"}]}]},{"type":"tableCell","attrs":{},"content":[{"type":"paragraph","content":[{"type":"text","text":"Button"}]}]},{"type":"tableCell","attrs":{},"content":[{"type":"paragraph","content":[{"type":"text","text":"✅"}]}]},{"type":"tableCell","attrs":{},"content":[{"type":"paragraph","content":[{"type":"text","text":"Launches invite screen"}]}]}]},{"type":"tableRow","content":[{"type":"tableCell","attrs":{},"content":[{"type":"paragraph","content":[{"type":"text","text":"Contact/Lead Tabs"}]}]},{"type":"tableCell","attrs":{},"content":[{"type":"paragraph","content":[{"type":"text","text":"Segmented"}]}]},{"type":"tableCell","attrs":{},"content":[{"type":"paragraph","content":[{"type":"text","text":"✅"}]}]},{"type":"tableCell","attrs":{},"content":[{"type":"paragraph","content":[{"type":"text","text":"Switch data source"}]}]}]},{"type":"tableRow","content":[{"type":"tableCell","attrs":{},"content":[{"type":"paragraph","content":[{"type":"text","text":"Ticket Delivery Choice"}]}]},{"type":"tableCell","attrs":{},"content":[{"type":"paragraph","content":[{"type":"text","text":"Radio"}]}]},{"type":"tableCell","attrs":{},"content":[{"type":"paragraph","content":[{"type":"text","text":"✅"}]}]},{"type":"tableCell","attrs":{},"content":[{"type":"paragraph","content":[{"type":"text","text":"Only for ticketed events"}]}]}]},{"type":"tableRow","content":[{"type":"tableCell","attrs":{},"content":[{"type":"paragraph","content":[{"type":"text","text":"Confirmation Modal"}]}]},{"type":"tableCell","attrs":{},"content":[{"type":"paragraph","content":[{"type":"text","text":"Modal"}]}]},{"type":"tableCell","attrs":{},"content":[{"type":"paragraph","content":[{"type":"text","text":"✅ (if ticketed & sending ticket)"}]}]},{"type":"tableCell","attrs":{},"content":[{"type":"paragraph","content":[{"type":"text","text":"Shows credit usage"}]}]}]},{"type":"tableRow","content":[{"type":"tableCell","attrs":{},"content":[{"type":"paragraph","content":[{"type":"text","text":"Final “Send” Button"}]}]},{"type":"tableCell","attrs":{},"content":[{"type":"paragraph","content":[{"type":"text","text":"Button"}]}]},{"type":"tableCell","attrs":{},"content":[{"type":"paragraph","content":[{"type":"text","text":"✅"}]}]},{"type":"tableCell","attrs":{},"content":[{"type":"paragraph","content":[{"type":"text","text":"Triggers backend invite action"}]}]}]}]}, {"type":"rule"}, {"type":"heading","attrs":{"level":4},"content":[{"type":"text","text":"4. Data Display"}]}, {"type":"table","attrs":{"isNumberColumnEnabled":false,"layout":"center","localId":"4ae4bf84-0d3d-4c42-befd-371f2550a393"},"content":[{"type":"tableRow","content":[{"type":"tableHeader","attrs":{},"content":[{"type":"paragraph","content":[{"type":"text","text":"Display Item"}]}]},{"type":"tableHeader","attrs":{},"content":[{"type":"paragraph","content":[{"type":"text","text":"Format / Example"}]}]},{"type":"tableHeader","attrs":{},"content":[{"type":"paragraph","content":[{"type":"text","text":"Notes"}]}]}]},{"type":"tableRow","content":[{"type":"tableCell","attrs":{},"content":[{"type":"paragraph","content":[{"type":"text","text":"Contact Card"}]}]},{"type":"tableCell","attrs":{},"content":[{"type":"paragraph","content":[{"type":"text","text":"Name, avatar, role"}]}]},{"type":"tableCell","attrs":{},"content":[{"type":"paragraph","content":[{"type":"text","text":"Sourced from M04_Connections"}]}]}]},{"type":"tableRow","content":[{"type":"tableCell","attrs":{},"content":[{"type":"paragraph","content":[{"type":"text","text":"Lead Card"}]}]},{"type":"tableCell","attrs":{},"content":[{"type":"paragraph","content":[{"type":"text","text":"Name, avatar, tag: “Lead”"}]}]},{"type":"tableCell","attrs":{},"content":[{"type":"paragraph","content":[{"type":"text","text":"Sourced from CRM contact logic"}]}]}]},{"type":"tableRow","content":[{"type":"tableCell","attrs":{},"content":[{"type":"paragraph","content":[{"type":"text","text":"Confirmation Modal"}]}]},{"type":"tableCell","attrs":{},"content":[{"type":"paragraph","content":[{"type":"text","text":"“Sending 2 tickets will cost 120 credits…”"}]}]},{"type":"tableCell","attrs":{},"content":[{"type":"paragraph","content":[{"type":"text","text":"Based on ticket price"}]}]}]},{"type":"tableRow","content":[{"type":"tableCell","attrs":{},"content":[{"type":"paragraph","content":[{"type":"text","text":"Invite Toast"}]}]},{"type":"tableCell","attrs":{},"content":[{"type":"paragraph","content":[{"type":"text","text":"“Invites sent successfully!”"}]}]},{"type":"tableCell","attrs":{},"content":[{"type":"paragraph","content":[{"type":"text","text":"Appears after completion"}]}]}]}]}, {"type":"rule"}, {"type":"heading","attrs":{"level":4},"content":[{"type":"text","text":"5. Acceptance Criteria"}]}, {"type":"bulletList","content":[{"type":"listItem","content":[{"type":"paragraph","content":[{"type":"text","text":"AC1:","marks":[{"type":"strong"}]},{"type":"text","text":" “Invite Attendees” option is only available to the event’s host and only after event creation."}]}]},{"type":"listItem","content":[{"type":"paragraph","content":[{"type":"text","text":"AC2:","marks":[{"type":"strong"}]},{"type":"text","text":" The invite list includes two segments: Contacts and Leads."}]}]},{"type":"listItem","content":[{"type":"paragraph","content":[{"type":"text","text":"AC3:","marks":[{"type":"strong"}]},{"type":"text","text":" Host can select multiple users from either list."}]}]},{"type":"listItem","content":[{"type":"paragraph","content":[{"type":"text","text":"AC4:","marks":[{"type":"strong"}]},{"type":"text","text":" For ticketed events, host must choose whether to send tickets for free or not."}]}]},{"type":"listItem","content":[{"type":"paragraph","content":[{"type":"text","text":"AC5:","marks":[{"type":"strong"}]},{"type":"text","text":" If host selects “Send Ticket,” the system calculates and deducts credits from their balance."}]}]},{"type":"listItem","content":[{"type":"paragraph","content":[{"type":"text","text":"AC6:","marks":[{"type":"strong"}]},{"type":"text","text":" Before finalizing the invite with tickets, a confirmation modal must display the total credit deduction."}]}]},{"type":"listItem","content":[{"type":"paragraph","content":[{"type":"text","text":"AC7:","marks":[{"type":"strong"}]},{"type":"text","text":" If the host cancels the modal, no credit is deducted and no invite is sent."}]}]},{"type":"listItem","content":[{"type":"paragraph","content":[{"type":"text","text":"AC8:","marks":[{"type":"strong"}]},{"type":"text","text":" Successfully invited users receive notification and see the event in their “Invites” list."}]}]},{"type":"listItem","content":[{"type":"paragraph","content":[{"type":"text","text":"AC9:","marks":[{"type":"strong"}]},{"type":"text","text":" Duplicate invites to the same user for the same event are blocked."}]}]},{"type":"listItem","content":[{"type":"paragraph","content":[{"type":"text","text":"AC10:","marks":[{"type":"strong"}]},{"type":"text","text":" This feature is disabled for cancelled or expired events."}]}]},{"type":"listItem","content":[{"type":"paragraph","content":[{"type":"text","text":"AC11:","marks":[{"type":"strong"}]},{"type":"text","text":" If the host does not have enough credits to send the selected number of tickets, they are prompted to top up credits before proceeding."}]}]}]}, {"type":"rule"}, {"type":"heading","attrs":{"level":4},"content":[{"type":"text","text":"6. System Rules"}]}, {"type":"bulletList","content":[{"type":"listItem","content":[{"type":"paragraph","content":[{"type":"text","text":"Event must be fully created before invites can be sent."}]}]},{"type":"listItem","content":[{"type":"paragraph","content":[{"type":"text","text":"Only the host of the event has permission to invite others."}]}]},{"type":"listItem","content":[{"type":"paragraph","content":[{"type":"text","text":"If sending free tickets, the system pulls ticket pricing and applies the credit conversion rate to calculate deduction."}]}]},{"type":"listItem","content":[{"type":"paragraph","content":[{"type":"text","text":"Credits are only deducted upon modal confirmation."}]}]},{"type":"listItem","content":[{"type":"paragraph","content":[{"type":"text","text":"Sent invites are logged and auditable via backend event analytics."}]}]},{"type":"listItem","content":[{"type":"paragraph","content":[{"type":"text","text":"Invited users cannot invite others unless they become co-hosts (future scope)."}]}]}]}, {"type":"rule"}, {"type":"heading","attrs":{"level":4},"content":[{"type":"text","text":"7. UX Optimizations"}]}, {"type":"bulletList","content":[{"type":"listItem","content":[{"type":"paragraph","content":[{"type":"text","text":"Show user avatars and RSVP status to prevent re-invites."}]}]},{"type":"listItem","content":[{"type":"paragraph","content":[{"type":"text","text":"Use atomic components for list, segmented controls, and modal."}]}]},{"type":"listItem","content":[{"type":"paragraph","content":[{"type":"text","text":"Apply loading state and haptic feedback to the final Send button."}]}]},{"type":"listItem","content":[{"type":"paragraph","content":[{"type":"text","text":"Automatically scroll list to show newly invited users if returning to screen."}]}]},{"type":"listItem","content":[{"type":"paragraph","content":[{"type":"text","text":"Confirmation modal clearly breaks down per-ticket cost and total deduction."}]}]}]}