{"type":"heading","attrs":{"level":3},"content":[{"type":"text","text":"1. Use Case Description"}]}, {"type":"paragraph","content":[{"type":"text","text":"As a host","marks":[{"type":"strong"}]},{"type":"text","text":", I want to cancel an event after it has been published,"},{"type":"hardBreak"},{"type":"text","text":"so that","marks":[{"type":"strong"}]},{"type":"text","text":" I can notify all attendees and prevent future participation, while still retaining access to the event's history and status in the system."}]}, {"type":"rule"}, {"type":"heading","attrs":{"level":3},"content":[{"type":"text","text":"2. User Workflow (Step-by-Step)"}]}, {"type":"orderedList","attrs":{"order":1},"content":[{"type":"listItem","content":[{"type":"paragraph","content":[{"type":"text","text":"Host navigates to “My Events” tab and selects an active event."}]}]},{"type":"listItem","content":[{"type":"paragraph","content":[{"type":"text","text":"Host taps the “More” or “Manage” option and selects "},{"type":"text","text":"Cancel Event","marks":[{"type":"strong"}]},{"type":"text","text":"."}]}]},{"type":"listItem","content":[{"type":"paragraph","content":[{"type":"text","text":"System displays a confirmation modal outlining:"}]},{"type":"bulletList","content":[{"type":"listItem","content":[{"type":"paragraph","content":[{"type":"text","text":"Event will be marked as cancelled"}]}]},{"type":"listItem","content":[{"type":"paragraph","content":[{"type":"text","text":"Users will be notified"}]}]},{"type":"listItem","content":[{"type":"paragraph","content":[{"type":"text","text":"Ticket purchases will be refunded as credit amounts (if applicable)"}]}]}]}]},{"type":"listItem","content":[{"type":"paragraph","content":[{"type":"text","text":"Host confirms the action."}]}]},{"type":"listItem","content":[{"type":"paragraph","content":[{"type":"text","text":"System updates:"}]},{"type":"bulletList","content":[{"type":"listItem","content":[{"type":"paragraph","content":[{"type":"text","text":"Event status → "},{"type":"text","text":"Cancelled","marks":[{"type":"code"}]}]}]},{"type":"listItem","content":[{"type":"paragraph","content":[{"type":"text","text":"All invitees notified"}]}]},{"type":"listItem","content":[{"type":"paragraph","content":[{"type":"text","text":"Ticket holders refunded credits"}]}]},{"type":"listItem","content":[{"type":"paragraph","content":[{"type":"text","text":"Host ticket revenue reversed"}]}]}]}]},{"type":"listItem","content":[{"type":"paragraph","content":[{"type":"text","text":"Event remains in the \"My Events\" list with a "},{"type":"text","text":"Cancelled","marks":[{"type":"strong"}]},{"type":"text","text":" tag."}]}]}]}, {"type":"rule"}, {"type":"heading","attrs":{"level":3},"content":[{"type":"text","text":"3. Field Definitions Table"}]}, {"type":"heading","attrs":{"level":4},"content":[{"type":"text","text":"3.1 Submission Fields"}]}, {"type":"table","attrs":{"isNumberColumnEnabled":false,"layout":"center","localId":"58e36ed6-4d80-46ed-ae25-b5dcaa299152"},"content":[{"type":"tableRow","content":[{"type":"tableHeader","attrs":{},"content":[{"type":"paragraph","content":[{"type":"text","text":"Field Name"}]}]},{"type":"tableHeader","attrs":{},"content":[{"type":"paragraph","content":[{"type":"text","text":"Type"}]}]},{"type":"tableHeader","attrs":{},"content":[{"type":"paragraph","content":[{"type":"text","text":"Required"}]}]},{"type":"tableHeader","attrs":{},"content":[{"type":"paragraph","content":[{"type":"text","text":"Description"}]}]}]},{"type":"tableRow","content":[{"type":"tableCell","attrs":{},"content":[{"type":"paragraph","content":[{"type":"text","text":"Event ID"}]}]},{"type":"tableCell","attrs":{},"content":[{"type":"paragraph","content":[{"type":"text","text":"UUID"}]}]},{"type":"tableCell","attrs":{},"content":[{"type":"paragraph","content":[{"type":"text","text":"✅"}]}]},{"type":"tableCell","attrs":{},"content":[{"type":"paragraph","content":[{"type":"text","text":"Targeted event to cancel"}]}]}]},{"type":"tableRow","content":[{"type":"tableCell","attrs":{},"content":[{"type":"paragraph","content":[{"type":"text","text":"Cancellation Note"}]}]},{"type":"tableCell","attrs":{},"content":[{"type":"paragraph","content":[{"type":"text","text":"String"}]}]},{"type":"tableCell","attrs":{},"content":[{"type":"paragraph","content":[{"type":"text","text":"✅"}]}]},{"type":"tableCell","attrs":{},"content":[{"type":"paragraph","content":[{"type":"text","text":"Optional message for invitees"}]}]}]}]}, {"type":"heading","attrs":{"level":4},"content":[{"type":"text","text":"3.2 Interaction Elements"}]}, {"type":"table","attrs":{"isNumberColumnEnabled":false,"layout":"center","localId":"771369ad-2b81-42e6-839f-f2847a8ade80"},"content":[{"type":"tableRow","content":[{"type":"tableHeader","attrs":{},"content":[{"type":"paragraph","content":[{"type":"text","text":"Element Name"}]}]},{"type":"tableHeader","attrs":{},"content":[{"type":"paragraph","content":[{"type":"text","text":"Type"}]}]},{"type":"tableHeader","attrs":{},"content":[{"type":"paragraph","content":[{"type":"text","text":"Required"}]}]},{"type":"tableHeader","attrs":{},"content":[{"type":"paragraph","content":[{"type":"text","text":"Description"}]}]}]},{"type":"tableRow","content":[{"type":"tableCell","attrs":{},"content":[{"type":"paragraph","content":[{"type":"text","text":"Cancel Event CTA"}]}]},{"type":"tableCell","attrs":{},"content":[{"type":"paragraph","content":[{"type":"text","text":"Button"}]}]},{"type":"tableCell","attrs":{},"content":[{"type":"paragraph","content":[{"type":"text","text":"✅"}]}]},{"type":"tableCell","attrs":{},"content":[{"type":"paragraph","content":[{"type":"text","text":"Located in Event Detail → Host only"}]}]}]},{"type":"tableRow","content":[{"type":"tableCell","attrs":{},"content":[{"type":"paragraph","content":[{"type":"text","text":"Confirmation Modal"}]}]},{"type":"tableCell","attrs":{},"content":[{"type":"paragraph","content":[{"type":"text","text":"Modal"}]}]},{"type":"tableCell","attrs":{},"content":[{"type":"paragraph","content":[{"type":"text","text":"✅"}]}]},{"type":"tableCell","attrs":{},"content":[{"type":"paragraph","content":[{"type":"text","text":"Confirms irreversible cancellation"}]}]}]},{"type":"tableRow","content":[{"type":"tableCell","attrs":{},"content":[{"type":"paragraph","content":[{"type":"text","text":"Status Badge"}]}]},{"type":"tableCell","attrs":{},"content":[{"type":"paragraph","content":[{"type":"text","text":"Label"}]}]},{"type":"tableCell","attrs":{},"content":[{"type":"paragraph","content":[{"type":"text","text":"✅"}]}]},{"type":"tableCell","attrs":{},"content":[{"type":"paragraph","content":[{"type":"text","text":"“Cancelled” shown on card and detail page"}]}]}]}]}, {"type":"rule"}, {"type":"heading","attrs":{"level":3},"content":[{"type":"text","text":"4. Data Display Table (Card View)"}]}, {"type":"table","attrs":{"isNumberColumnEnabled":false,"layout":"center","localId":"31c14c76-71d5-4bbe-ab0d-8e61d685ca57"},"content":[{"type":"tableRow","content":[{"type":"tableHeader","attrs":{},"content":[{"type":"paragraph","content":[{"type":"text","text":"Field"}]}]},{"type":"tableHeader","attrs":{},"content":[{"type":"paragraph","content":[{"type":"text","text":"Format/Example"}]}]},{"type":"tableHeader","attrs":{},"content":[{"type":"paragraph","content":[{"type":"text","text":"Notes"}]}]}]},{"type":"tableRow","content":[{"type":"tableCell","attrs":{},"content":[{"type":"paragraph","content":[{"type":"text","text":"Status Badge"}]}]},{"type":"tableCell","attrs":{},"content":[{"type":"paragraph","content":[{"type":"text","text":"Cancelled","marks":[{"type":"code"}]},{"type":"text","text":" (grayed red label)"}]}]},{"type":"tableCell","attrs":{},"content":[{"type":"paragraph","content":[{"type":"text","text":"Consistent across list and detail screens"}]}]}]},{"type":"tableRow","content":[{"type":"tableCell","attrs":{},"content":[{"type":"paragraph","content":[{"type":"text","text":"Event Title"}]}]},{"type":"tableCell","attrs":{},"content":[{"type":"paragraph","content":[{"type":"text","text":"Text"}]}]},{"type":"tableCell","attrs":{},"content":[{"type":"paragraph","content":[{"type":"text","text":"Unchanged"}]}]}]},{"type":"tableRow","content":[{"type":"tableCell","attrs":{},"content":[{"type":"paragraph","content":[{"type":"text","text":"Attendee Count"}]}]},{"type":"tableCell","attrs":{},"content":[{"type":"paragraph","content":[{"type":"text","text":"“X attendees RSVP’d”"}]}]},{"type":"tableCell","attrs":{},"content":[{"type":"paragraph","content":[{"type":"text","text":"Still viewable"}]}]}]},{"type":"tableRow","content":[{"type":"tableCell","attrs":{},"content":[{"type":"paragraph","content":[{"type":"text","text":"Cancelled Notification"}]}]},{"type":"tableCell","attrs":{},"content":[{"type":"paragraph","content":[{"type":"text","text":"“This event has been cancelled.”"}]}]},{"type":"tableCell","attrs":{},"content":[{"type":"paragraph","content":[{"type":"text","text":"Toast/inline banner on attendee side"}]}]}]},{"type":"tableRow","content":[{"type":"tableCell","attrs":{},"content":[{"type":"paragraph","content":[{"type":"text","text":"Refund Info (Host)"}]}]},{"type":"tableCell","attrs":{},"content":[{"type":"paragraph","content":[{"type":"text","text":"“Your earnings will be deducted...”"}]}]},{"type":"tableCell","attrs":{},"content":[{"type":"paragraph","content":[{"type":"text","text":"Only for ticketed events, shown in modal"}]}]}]}]}, {"type":"rule"}, {"type":"heading","attrs":{"level":3},"content":[{"type":"text","text":"5. Acceptance Criteria"}]}, {"type":"bulletList","content":[{"type":"listItem","content":[{"type":"paragraph","content":[{"type":"text","text":"AC1:","marks":[{"type":"strong"}]},{"type":"text","text":" Only the host of an event can access the “Cancel Event” option."}]}]},{"type":"listItem","content":[{"type":"paragraph","content":[{"type":"text","text":"AC2:","marks":[{"type":"strong"}]},{"type":"text","text":" Selecting “Cancel Event” opens a modal confirming the action and its consequences."}]}]},{"type":"listItem","content":[{"type":"paragraph","content":[{"type":"text","text":"AC3:","marks":[{"type":"strong"}]},{"type":"text","text":" After confirmation, the event’s status is updated to "},{"type":"text","text":"Cancelled","marks":[{"type":"code"}]},{"type":"text","text":"."}]}]},{"type":"listItem","content":[{"type":"paragraph","content":[{"type":"text","text":"AC4:","marks":[{"type":"strong"}]},{"type":"text","text":" The cancelled event remains visible in the host’s “My Events” tab."}]}]},{"type":"listItem","content":[{"type":"paragraph","content":[{"type":"text","text":"AC5:","marks":[{"type":"strong"}]},{"type":"text","text":" The event card and detail page clearly display a "},{"type":"text","text":"Cancelled","marks":[{"type":"strong"}]},{"type":"text","text":" badge."}]}]},{"type":"listItem","content":[{"type":"paragraph","content":[{"type":"text","text":"AC6:","marks":[{"type":"strong"}]},{"type":"text","text":" All attendees receive a system notification or toast that the event was cancelled."}]}]},{"type":"listItem","content":[{"type":"paragraph","content":[{"type":"text","text":"AC7:","marks":[{"type":"strong"}]},{"type":"text","text":" All actions tied to the event (e.g., RSVP, invite, share, boost) are disabled post-cancellation."}]}]},{"type":"listItem","content":[{"type":"paragraph","content":[{"type":"text","text":"AC8:","marks":[{"type":"strong"}]},{"type":"text","text":" The event detail page becomes read-only after cancellation."}]}]},{"type":"listItem","content":[{"type":"paragraph","content":[{"type":"text","text":"AC9:","marks":[{"type":"strong"}]},{"type":"text","text":" A cancelled event cannot be reactivated or edited."}]}]},{"type":"listItem","content":[{"type":"paragraph","content":[{"type":"text","text":"AC10:","marks":[{"type":"strong"}]},{"type":"text","text":" Cancelled events are still visible in logs and analytics."}]}]},{"type":"listItem","content":[{"type":"paragraph","content":[{"type":"text","text":"AC11:","marks":[{"type":"strong"}]},{"type":"text","text":" If attendees purchased tickets, refunds will be issued as credits (in USD value), and the host’s earnings from those tickets will be deducted."}]}]},{"type":"listItem","content":[{"type":"paragraph","content":[{"type":"text","text":"AC12:","marks":[{"type":"strong"}]},{"type":"text","text":" Refunded credits are stored in the user’s AIOS wallet and cannot be withdrawn to a bank account."}]}]}]}, {"type":"rule"}, {"type":"heading","attrs":{"level":3},"content":[{"type":"text","text":"6. System Rules"}]}, {"type":"bulletList","content":[{"type":"listItem","content":[{"type":"paragraph","content":[{"type":"text","text":"Cancellation is a soft delete; all data must remain queryable and audit-ready."}]}]},{"type":"listItem","content":[{"type":"paragraph","content":[{"type":"text","text":"Push/email notification is triggered to all RSVP’d users."}]}]},{"type":"listItem","content":[{"type":"paragraph","content":[{"type":"text","text":"Ticket refunds are processed as AIOS wallet credit in equivalent dollar amount."}]}]},{"type":"listItem","content":[{"type":"paragraph","content":[{"type":"text","text":"Hosts who received earnings will see equivalent deduction in their transaction log."}]}]},{"type":"listItem","content":[{"type":"paragraph","content":[{"type":"text","text":"Boost status (if active) is immediately ended without refund."}]}]},{"type":"listItem","content":[{"type":"paragraph","content":[{"type":"text","text":"Any reminders or future notifications are canceled automatically."}]}]}]}, {"type":"rule"}, {"type":"heading","attrs":{"level":3},"content":[{"type":"text","text":"7. UX Optimizations"}]}, {"type":"bulletList","content":[{"type":"listItem","content":[{"type":"paragraph","content":[{"type":"text","text":"Use a red danger-themed modal for cancellation confirmation."}]}]},{"type":"listItem","content":[{"type":"paragraph","content":[{"type":"text","text":"Add helpful inline warning:"}]},{"type":"paragraph","content":[{"type":"text","text":"“You’re about to cancel this event. This action is permanent and attendees will be notified.”"}]}]},{"type":"listItem","content":[{"type":"paragraph","content":[{"type":"text","text":"Show refund impact in the confirmation modal if ticket purchases exist."}]}]},{"type":"listItem","content":[{"type":"paragraph","content":[{"type":"text","text":"Allow host to enter a brief optional message for attendees."}]}]}]}