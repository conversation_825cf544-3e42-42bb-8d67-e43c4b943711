Summary,Issue key,Issue id,Issue Type,Status,Project key,Project name,Project type,Project lead,Project lead id,Project description,Priority,Resolution,Assignee,Assignee Id,Reporter,Reporter Id,Creator,Creator Id,Created,Updated,Last Viewed,Resolved,Due date,Votes,Description,Environment,Watchers,Watchers Id,Original estimate,Remaining Estimate,Time Spent,Work Ratio,Σ Original Estimate,Σ Remaining Estimate,Σ Time Spent,Security Level,Custom field (% Complete),Custom field (Actual end),Custom field (Actual start),Custom field (Affected services),Custom field (App),Custom field (Approvals),Custom field (Category),Custom field (Change reason),Custom field (Change risk),Custom field (Change type),Custom field (Company size),Custom field (Development),Custom field (Email),Custom field (Environment),Custom field (Epic Color),Custom field (Epic Name),Custom field (Epic Status),Custom field (Goals),Custom field (Impact),Custom field (Issue color),Custom field (Locked forms),Custom field (Open forms),Custom field (Operational categorization),Custom field (Pending reason),Custom field (Phone number),Custom field (Platform),Custom field (Platform),Custom field (Product categorization),Custom field (Project overview key),Custom field (Project overview status),Custom field (Rank),Custom field (Request Type),Custom field (Request language),Custom field (Request participants),Custom field (Responders),Satisfaction rating,Custom field (Satisfaction date),Custom field (Sentiment),Custom field (Start date),Custom field (Story Points),Custom field (Story point estimate),Custom field (Submitted forms),Custom field (Target end),Custom field (Target start),Custom field (Team),Custom field (Test Result),Custom field (Test User Picker),Custom field (Time to first response),Custom field (Time to resolution),Custom field (Total forms),Custom field (Urgency),Custom field (Version),Custom field (Vulnerability),Custom field (Website),Custom field (Work category),Custom field ([CHART] Date of First Response),Custom field ([CHART] Time in Status),Parent,Parent key,Parent summary,Status Category,Status Category Changed
[WEB - User Management] Delete User,AL-76,37778,Story,Specified,AL,AIOS Link,software,Kai Nguyễn,6214a4102d1a9d0069949c96,,Medium,,,,Duyen Nguyen,712020:59b66c6e-1068-441c-9436-130289e16a26,Duyen Nguyen,712020:59b66c6e-1068-441c-9436-130289e16a26,09/Jun/25 4:27 PM,12/Jun/25 10:45 AM,,,,0,"h3. 1. Use Case Description

As an Admin, I want to soft-delete a user from the admin panel so that the user is no longer visible or active in the admin dashboard, while retaining their data in the system for audit, future reactivation, or compliance needs.

----

h3. 2. User Workflow (Step-by-Step)

# Admin logs into the Web Portal.
# Admin navigates to *User Management*.
# Admin searches or selects a user from the list.
# Admin clicks the *“⋮” (more options)* menu on the user row.
# Admin selects *“Delete User”*.
# System displays a confirmation modal:
“Are you sure you want to delete this user? They will no longer be visible on the admin dashboard.”
# Admin confirms the action.
# System updates the user record status to {{deleted}}.
# User is removed from the visible admin list.
# Success toast appears: “User deleted successfully.”

----

h3. 3. Field Definitions Table

||Field Name||Field Type||Validation Rule||Mandatory||Description||
|User ID|UUID|Must exist in system|Yes|Used for identifying user record|
|Status|Enum|[active, deactivated]|Yes|Set to {{deleted}} when soft-deleted|
|Deleted By|Admin ID|Must be current admin|Yes|Logs who performed the delete action|
|Deleted At|Timestamp|System generated datetime|Yes|For audit trail and recovery references|

----

h3. 4. Acceptance Criteria

*AC1*: Admin can access delete action via more options menu in User Management table.
*AC2*: Clicking “Delete User” prompts a confirmation modal with non-reversible warning.
*AC3*: Upon confirmation, system sets {{status = deleted}} for the user.
*AC4*: Deleted users no longer appear in the User Management UI or count toward active users.
*AC5*: System logs {{deleted_by}} and {{deleted_at}} metadata.
*AC6*: Toast message confirms successful action: “User deleted successfully”
*AC7*: Deleted users cannot log into the AIOS Link mobile app.
*AC8*: A deleted user’s record must still be available to backend services for audit and dependency resolution.

----

h3. 5. System Rules

* Deletion is *non-destructive*: the user record is retained in the database.
* User status transitions to {{deleted}}; does not remove authentication records or profile data.
* System must update related modules (e.g., remove from search, hide contact suggestions).
* All sessions and tokens of deleted user are revoked immediately.
* Audit logs store admin ID and timestamp of deletion action.

----

h3. 6. UX Optimizations

* Use red text color for “Delete” in dropdown to distinguish it as a destructive action.
* Modal includes an info icon with text: “You may restore this user from backend if needed.”
* Display strikethrough or ghost-style UI for deleted users in audit tables (future).
* Support “undo” toast (10s) only if deletion is recent and UI-permitted.",,Duyen Nguyen,712020:59b66c6e-1068-441c-9436-130289e16a26,,,,,,,,,,,,,,com.atlassian.servicedesk.plugins.approvals.internal.customfield.ApprovalsCFValue@17217f7f,,,,,,{},,,,,,,,,,,,,,,WEB,,,,0|i02qh2:v,,,,,,,,,,,,,,,,,,,,,,,,,,,37761,AL-70,User Management,To Do,09/Jun/25 4:27 PM
[WEB - User Management] Edit User Detail,AL-75,37775,Story,Specified,AL,AIOS Link,software,Kai Nguyễn,6214a4102d1a9d0069949c96,,Medium,,,,Duyen Nguyen,712020:59b66c6e-1068-441c-9436-130289e16a26,Duyen Nguyen,712020:59b66c6e-1068-441c-9436-130289e16a26,09/Jun/25 4:26 PM,17/Jun/25 12:34 AM,,,,0,"h3. 1. Use Case Description

As an admin, I want to edit the profile details of an existing user so that I can correct or update their information while ensuring any sensitive changes (e.g. contact info) are securely verified.

----

h3. 2. User Workflow (Step-by-Step)

# Admin logs into the AIOS Link Web Portal.
# Admin navigates to *User Management > Users*.
# Admin selects a user and accesses the *User Detail View*.
# Admin clicks the *Edit* button.
# System opens the *Edit User Form* with all current values prefilled.
# Admin modifies one or more fields.
# If *email* or *phone number* is changed:
a. System marks the field for OTP verification.
b. A verification OTP will be sent to the new email/phone for user confirmation.
# Admin clicks *Save Changes*.
# System validates all inputs and applies changes.
# A success toast displays: “User details updated successfully.”
# If OTP is pending, status is shown as “Pending Verification” until confirmed.

----

h3. 3. Field Definitions Table

||Field Name||Field Type||Validation Rule||Mandatory||Notes||
|Full Name|Text|Max 50 characters|Yes|—|
|Email|Email|Must be valid format|Yes|OTP verification required if changed|
|Phone Number|Text|Valid phone format|Yes|OTP verification required if changed|
|Job Title|Text|Max 50 characters|Yes|—|
|Gender|Enum|Male, Female, Other|Yes|—|
|Industry|Text|Max 50 characters|No|—|
|Date of Birth|Date|Valid date|No|—|
|Address|Text|Max 100 characters|No|—|
|Bio|Text|Max 500 characters|No|—|
|Skills & Interests|Tag Chips|Max 15 entries|No|—|
|Profile Picture|Image Upload|JPG/PNG, 2MB max, camera option|No|—|
|LinkedIn URL|URL|Must match LinkedIn profile format|No|—|
|Telegram Link|Username|Must match Telegram handle format|No|—|
|WhatsApp Link|Phone-to-URL|Must be valid WhatsApp link format|No|—|
|Role|Enum|User / Merchant|Yes|Not editable|
|Organization Name|Text (linked)|Must be valid if role = Merchant|Conditional|Disabled if user is not merchant|
|Status|Enum|Active / Deactivated|Yes|—|

----

h3. 4. Acceptance Criteria

*AC1*: Admin can launch Edit User modal or view from User Detail page.
*AC2*: All fields except role and organization are editable.
*AC3*: Editing email or phone triggers system to mark the field for OTP confirmation.
*AC4*: OTP is automatically sent to the new contact info upon saving.
*AC5*: Changes to verified fields are not reflected until OTP is confirmed by the user.
*AC6*: If OTP is not confirmed, system displays “Pending Verification” in admin view.
*AC7*: Form validations are enforced for all mandatory fields.
*AC8*: Toast shows “User details updated successfully” upon successful validation and save.
*AC9*: If changes are canceled mid-edit, no updates are saved and confirmation is shown.

----

h3. 5. System Rules

* OTP count limit: max 5 requests/day per contact method.
* OTP expiration: 30 minutes.
* Only unverified fields are flagged for re-verification.
* Admin cannot modify user’s role or ownership assignment.
* Organization selection is locked unless role is Merchant.",,Duyen Nguyen,712020:59b66c6e-1068-441c-9436-130289e16a26,,,,,,,,,,,,,,com.atlassian.servicedesk.plugins.approvals.internal.customfield.ApprovalsCFValue@53ba56a1,,,,,,{},,,,,,,,,,,,,,,WEB,,,,0|i02qh2:r,,,,,,,,,,,,,,,,,,,,,,,,,,,37761,AL-70,User Management,To Do,09/Jun/25 4:26 PM
[WEB - User Management] View User Detail,AL-74,37772,Story,Specified,AL,AIOS Link,software,Kai Nguyễn,6214a4102d1a9d0069949c96,,Medium,,,,Duyen Nguyen,712020:59b66c6e-1068-441c-9436-130289e16a26,Duyen Nguyen,712020:59b66c6e-1068-441c-9436-130289e16a26,09/Jun/25 4:26 PM,17/Jun/25 12:34 AM,,,,0,"h3. 1. Use Case Description

As an admin, I want to view detailed information of a registered user so that I can assess their status, role, and data completeness for auditing or support purposes.

----

h3. 2. User Workflow (Step-by-Step)

# Admin logs into the AIOS Link Web Portal.
# Admin navigates to *User Management > Users*.
# Admin selects a user from the list by clicking on the row.
# System opens a *User Detail view* in a modal or new page.
# System displays all relevant user information grouped by sections:
#* Personal Info
#* Contact Info
#* Professional Info
#* System Info
# If a field has not been filled by the user, system displays a placeholder (e.g., “Not Provided”).
# Admin can close the view or navigate back to the user list.

----

h3. 3. Field Definitions Table

||Field Name||Field Type||Validation Rule||Mandatory||Data Shown When Empty||
|Full Name|Text|Max 50 characters|Yes|Not Provided|
|Email|Email|Valid format|Yes|Not Provided|
|Phone Number|Text|Valid phone number|Yes|Not Provided|
|Job Title|Text|Max 50 characters|Yes|Not Provided|
|Gender|Enum|Male, Female, Other|Yes|Not Specified|
|Industry|Text|Max 50 characters|No|Not Provided|
|Date of Birth|Date|Valid date|No|Not Provided|
|Address|Text|Max 100 characters|No|Not Provided|
|Bio|Text|Max 500 characters|No|No Bio Added|
|Skills & Interests|Tag Chips|Max 15 entries|No|None Listed|
|Profile Picture|Image|JPG/PNG, 2MB, uploaded or camera taken|No|Default Avatar|
|LinkedIn URL|URL|Must be valid LinkedIn profile URL|No|Not Provided|
|Telegram Link|Username|Must match Telegram handle format|No|Not Provided|
|WhatsApp Link|Phone-to-URL|Must be valid WhatsApp URL|No|Not Provided|
|Role|Enum|User / Merchant|Yes|—|
|Organization Name|Text (linked)|If role = Merchant, must be linked org|Conditional|Not Linked|
|Account Status|Enum|Active / Deactivated|Yes|—|

----

h3. 4. Acceptance Criteria

* *AC1*: Admin can access the full detail view of any user from the list.
* *AC2*: All populated fields are shown with labels and read-only values.
* *AC3*: Any field not filled in by the user is labeled clearly using the “Data Shown When Empty” values.
* *AC4*: If the user has no profile picture, a default avatar is displayed.
* *AC5*: All social link fields are displayed as plain text URLs if provided.
* *AC6*: Admin cannot edit values from the view screen (read-only mode).
* *AC7*: A close or back button must allow admin to return to the main user list.

----

h3. 5. System Rules

* Data is retrieved from backend user profile and cached for 5 minutes to improve performance.
* Empty fields must never display as blank – use system-provided defaults.
* The user’s role determines if the organization name should be shown.
* The view is responsive and mobile-compatible for narrow screens.",,Duyen Nguyen,712020:59b66c6e-1068-441c-9436-130289e16a26,,,,,,,,,,,,,,com.atlassian.servicedesk.plugins.approvals.internal.customfield.ApprovalsCFValue@58e3c4a,,,,,,{},,,,,,,,,,,,,,,WEB,,,,0|i02qh2:i,,,,,,,,,,,,,,,,,,,,,,,,,,,37761,AL-70,User Management,To Do,09/Jun/25 4:26 PM
[WEB - User Management] Create User,AL-73,37769,Story,In Progress,AL,AIOS Link,software,Kai Nguyễn,6214a4102d1a9d0069949c96,,Medium,,,,Duyen Nguyen,712020:59b66c6e-1068-441c-9436-130289e16a26,Duyen Nguyen,712020:59b66c6e-1068-441c-9436-130289e16a26,09/Jun/25 4:25 PM,17/Jun/25 12:33 AM,,,,0,"1. Use Case Description

As an Admin, I want to create a user account and optionally associate them with an organization (if applicable), so that the account is properly linked to business entities and complies with role-based permissions and ownership rules.

----

h3. 2. User Workflow (Step-by-Step)

# Admin logs into the AIOS Link Web Portal.
# Admin navigates to *User Management > Users*.
# Admin clicks the *“Create User”* button.
# System displays the *Create User form*.
# Admin completes the user details including role and all mandatory fields.
# Below the role dropdown, system displays the *Organization Selection section*:
#* If role = *User*, organization linking is optional and can be skipped.
#* If role = *Merchant*, system enforces selection of an organization.
#* If an organization is already owned by another user, its selection card is disabled (grayed out).
# Admin selects an available organization for Merchant user.
# Admin clicks *Save*.
# System validates all fields and:
#* Assigns the user as *Owner* of selected organization if role = Merchant.
#* Sets account status = {{pending_activation}}.
# System sends activation OTP email to the user.
# A success toast is displayed: “User created successfully.”

----

h3. 3. Field Definitions Table

||Field Name||Field Type||Validation Rule||Mandatory||Description||
|Full Name|Text Input|Max 50 characters|Yes|User's legal name|
|Email|Email Input|Valid format, must not already exist|Yes|Unique identifier|
|Phone Number|Text Input|Valid phone number|Yes|For contact & identity|
|Password|Password Input|≥8 chars, 1 uppercase, 1 lowercase, 1 number, 1 special char|Yes|Temporary system password|
|Role|Dropdown|One of: [User, Merchant]|Yes|Defines permission scope|
|Organization|Dropdown/List|Select from available organizations (disabled if owned)|Conditional*|Required if role = Merchant|
|Job Title|Text Input|Max 50 characters|Yes|User's title or professional role|
|Gender|Enum|Male, Female, Other|Yes|Self-reported gender|
|Industry|Text Input|Max 50 characters|No|Field of work|
|Date of Birth|Date Picker|Valid date|No|Used for profile completion|
|Address|Text Area|Max 100 characters|No|Private|
|Bio|Text Area|Max 500 characters|No|Public summary|
|Skills & Interests|Tag Selector|Up to 15 entries|No|User interests or specialties|
|LinkedIn URL|URL Input|Must be valid LinkedIn profile|No|Clickable icon|
|Telegram Link|Text Input|Must be valid handle format|No|Contact channel|
|WhatsApp Link|Text Input|Must be valid URL format|No|Contact channel|
|Profile Picture|Image Upload|JPG/PNG, max 2MB, camera allowed|No|Visible in profile|

* Conditional: Required only if role = Merchant

----

h3. 4. Acceptance Criteria

* *AC1*: Admin can only create a user if all mandatory fields are valid.
* *AC2*: If role = Merchant, an organization *must* be selected.
* *AC3*: Organization dropdown only lists *organizations without owners*.
* *AC4*: Cards for already-owned organizations are visible but *grayed out and disabled*.
* *AC5*: If role = User, system allows skipping the organization field.
* *AC6*: On successful form submission:
** User is created with status = {{pending_activation}}
** Activation OTP is sent to email.
* *AC7*: User is shown in the user list with correct role and organization (if applicable).
* *AC8*: System stores the user-role-organization mapping persistently.
* *AC9*: If merchant user is linked to an org, they are automatically set as “Owner.”
* *AC10*: A success toast appears: “User created successfully.”

----

h3. 5. System Rules

* Email is the primary unique identifier.
* Each *organization can have only one Owner*.
* Users can only log into *mobile app*, not web portal.
* OTP is valid for 30 minutes and resending is allowed after 60s, max 5 per day.
* Created user cannot access the dashboard until activation is complete.
* Audit logs store record of which admin created the user, timestamp, and assigned role/org.

----

h3. 6. UX Optimizations

* Organization selection uses *card grid with search + disabled states* for owned orgs.
* Display tooltip:
“This organization already has an owner and cannot be selected.”
* Role selection dynamically toggles the organization field visibility and rules.
* Save button remains disabled until required conditions (incl. role/org logic) are satisfied.
* Toasts and inline error messages guide completion without form reload.",,Duyen Nguyen,712020:59b66c6e-1068-441c-9436-130289e16a26,,,,,,,,,,,,,,com.atlassian.servicedesk.plugins.approvals.internal.customfield.ApprovalsCFValue@111d4100,,,,,,{},,,,,,,,,,,,,,,WEB,,,,0|i02qh2:,,,,,,,,,,,,,,,,,,,,,,,,,,,37761,AL-70,User Management,In Progress,12/Jun/25 10:45 AM
[WEB - User Management] Activate/ Deactivate User,AL-72,37766,Story,Specified,AL,AIOS Link,software,Kai Nguyễn,6214a4102d1a9d0069949c96,,Medium,,,,Duyen Nguyen,712020:59b66c6e-1068-441c-9436-130289e16a26,Duyen Nguyen,712020:59b66c6e-1068-441c-9436-130289e16a26,09/Jun/25 4:25 PM,12/Jun/25 10:45 AM,,,,0,"h3. 1. Use Case Description

As an Admin, I want to activate or deactivate user accounts so that I can manage platform access for compliance, moderation, or organizational control purposes.

----

h3. 2. User Workflow (Step-by-Step)

*Flow 1: Quick Action via User List Table*

# Admin logs into the AIOS Link Web Portal.
# Admin navigates to *User Management > Users*.
# Admin locates a user in the user table.
# Admin clicks the *action icon* (e.g. three-dot menu) for that user row.
# System displays either *“Deactivate”* or *“Activate”* option depending on current status.
# Admin selects the action → a confirmation modal appears.
# Admin confirms → system updates the user’s status.
# System shows a success toast and updates the list view instantly.

*Flow 2: Action via User Detail Page*

# Admin navigates to *User Management > Users*.
# Admin clicks a user row to open *User Detail View*.
# System loads full user profile in a modal or new view.
# At the top or in the action section, system displays *Activate* or *Deactivate* button based on status.
# Admin clicks the button → confirmation modal appears.
# Admin confirms → system updates the user’s status and reflects change in UI.
# A success toast appears, and status is reflected in both detail view and main list.

----

h3. 3. Field Definitions Table

||Field Name||Field Type||Validation Rule||Mandatory||Description||
|Status|Enum|One of: {{Active}}, {{Deactivated}}|Yes|Current account access state|

----

h3. 4. Acceptance Criteria

* *AC1*: If a user is currently {{Active}}, only the *Deactivate* action is available.
* *AC2*: If a user is currently {{Deactivated}}, only the *Activate* action is available.
* *AC3*: After confirming deactivation, user status is updated to {{Deactivated}} and row is styled accordingly.
* *AC4*: After confirming activation, user status is updated to {{Active}}.
* *AC5*: A confirmation modal appears before activation or deactivation with custom message:
** _Deactivate modal:_ “Are you sure you want to deactivate this user? They will lose access immediately.”
** _Activate modal:_ “Re-activate this user account and allow login?”
* *AC6*: A success toast appears:
** “User account deactivated.”
** “User account activated.”
* *AC7*: Audit trail is updated with action type, admin ID, timestamp, and user ID.
* *AC8*: Deactivated users:
** Cannot sign into the mobile app.
** Cannot access user-only routes if authenticated.
* *AC9*: Activation does not trigger a welcome email or verification resend.
* *AC10*: If an error occurs (e.g., network or backend error), display appropriate toast:
** “Action failed. Please try again.”

----

h3. 5. System Rules

* Soft status toggle: {{Active ↔ Deactivated}}, data is never deleted.
* Admin must have {{manage_user_status}} permission to perform the action.
* Status toggle must be atomic (1 click, 1 transaction).
* Users with status {{Deactivated}} are excluded from active contact recommendations and smart suggestions.
* Status change is immediately reflected in both the web and mobile environments.

----

h3. 6. UX Optimizations

* In-row status toggle (e.g. dropdown or action icon) or batch action support (optional).
* Status indicator (pill UI) color-coded:
** Green = Active
** Gray = Deactivated
* Filter bar includes a quick toggle: {{[ All | Active | Deactivated ]}}.",,Duyen Nguyen,712020:59b66c6e-1068-441c-9436-130289e16a26,,,,,,,,,,,,,,com.atlassian.servicedesk.plugins.approvals.internal.customfield.ApprovalsCFValue@ddb0edb,,,,,,{},,,,,,,,,,,,,,,WEB,,,,0|i02qh1:,,,,,,,,,,,,,,,,,,,,,,,,,,,37761,AL-70,User Management,To Do,09/Jun/25 4:25 PM
[WEB - User Management] User List,AL-71,37763,Story,Specified,AL,AIOS Link,software,Kai Nguyễn,6214a4102d1a9d0069949c96,,Medium,,,,Duyen Nguyen,712020:59b66c6e-1068-441c-9436-130289e16a26,Duyen Nguyen,712020:59b66c6e-1068-441c-9436-130289e16a26,09/Jun/25 4:25 PM,12/Jun/25 10:45 AM,,,,0,"h3. 1. Use Case Description

As an Admin, I want to view a list of all users on the AIOS Link platform so that I can monitor account status, manage user records, and take actions such as view, edit, or deactivate users efficiently from a centralized dashboard.

----

h3. 2. User Workflow (Step-by-Step)

# Admin logs into the AIOS Link Web Portal.
# Admin navigates to *User Management > Users*.
# The system loads a paginated list of all active and deactivated users.
# Admin can:
#* Search by name, email, or phone number.
#* Filter users by role and status.
#* Click a row to view full user details.
#* Take row-level actions: *View*, *Edit*, *Deactivate*.

----

h3. 3. Field Definitions Table (User List View)

||Field Name||Field Type||Description||Mandatory||Displayed in List||
|Full Name|Text|User’s registered full name|Yes|✅|
|Email|Text (Email)|Unique email used for login|Yes|✅|
|Phone Number|Text|Registered phone number|Yes|✅|
|Role|Enum|User / Merchant|Yes|✅|
|Status|Enum|*Active / Deactivated*|Yes|✅|
|Created Date|DateTime|Account registration timestamp|Yes|✅|
|Last Login|DateTime|Timestamp of last successful login (if any)|No|✅|
|Organization Name|Text|Merchant account org name (if applicable)|No|✅ (if available)|

----

h3. 4. Acceptance Criteria

* *AC1*: User list is rendered as a paginated table with default sort by Created Date descending.
* *AC2*: Each user row displays key information including Status, Role, and Organization if available.
* *AC3*: Clicking a user row opens the full User Detail view in a side drawer/modal.
* *AC4*: Admin can take row-level actions including:
** *View* – see full profile info
** *Edit* – update user details
** *Deactivate* – soft-delete user from list view
* *AC5*: Deactivated users do not appear by default unless explicitly filtered for.
* *AC6*: System supports 20 users per page with pagination controls at the bottom.
* *AC7*: If no results match filters or search, system shows: “No users found.”

----

h3. 5. Search & Filter Rules

*Search Rules*:

* Admin can search across:
** Full Name (partial match)
** Email (exact match)
** Phone Number (exact match)
* Search is case-insensitive and persistent between pagination.

*Filter Rules*:

* Role: {{User}}, {{Merchant}}
* Status: {{Active}}, {{Deactivated}}
* Filters and search can be combined.

----

h3. 6. System Rules

* Only users with {{view_user_list}} permission can access this feature.
* Soft deletion is enforced: Deactivated users are excluded from default view but retained in the system.
* Admin actions (Edit, Deactivate) are audit-logged with timestamps and actor ID.
* All dates are shown in admin's local timezone (fallback to UTC).
* Organization column displays only for Merchant accounts.

----

h3. 7. UX Optimizations

* Sticky search and filter header bar.
* Row hover reveals quick actions: *View*, *Edit*, *Deactivate*.
* Colored status pills:
** {{Active}} – green
** {{Deactivated}} – gray
* Sortable columns: Name, Status, Created Date, Last Login.
* Responsive layout for all device sizes.",,Duyen Nguyen,712020:59b66c6e-1068-441c-9436-130289e16a26,,,,,,,,,,,,,,com.atlassian.servicedesk.plugins.approvals.internal.customfield.ApprovalsCFValue@c554e9f,,,,,,{},,,,,,,,,,,,,,,WEB,,,,0|i02qgz:,,,,,,,,,,,,,,,,,,,,,,,,,,,37761,AL-70,User Management,To Do,09/Jun/25 4:25 PM
