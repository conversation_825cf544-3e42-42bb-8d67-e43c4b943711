Summary,Issue key,Issue id,Issue Type,Status,Project key,Project name,Project type,Project lead,Project lead id,Project description,Priority,Resolution,Assignee,Assignee Id,Reporter,Reporter Id,Creator,Creator Id,Created,Updated,Last Viewed,Resolved,Due date,Votes,Description,Environment,Watchers,Watchers,Watchers Id,Watchers Id,Original estimate,Remaining Estimate,Time Spent,Work Ratio,Σ Original Estimate,Σ Remaining Estimate,Σ Time Spent,Security Level,Custom field (% Complete),Custom field (Actual end),Custom field (Actual start),Custom field (Affected services),Custom field (App),Custom field (App),Custom field (App),Custom field (Approvals),Custom field (Bug Type),Custom field (Build Version),Custom field (Build Version),Custom field (Category),Custom field (Change reason),Custom field (Change risk),Custom field (Change type),Custom field (Company size),Custom field (Development),Custom field (Email),Custom field (Environment),Custom field (Environment),Custom field (Environment),Custom field (Epic Color),Custom field (Epic Name),Custom field (Epic Status),Custom field (Goals),Custom field (Impact),Custom field (Issue color),Custom field (Locked forms),Custom field (OS),Custom field (Open forms),Custom field (Operational categorization),Custom field (Pending reason),Custom field (Phone number),Custom field (Platform),Custom field (Platform),Custom field (Product categorization),Custom field (Project overview key),Custom field (Project overview status),Custom field (Rank),Custom field (Request Type),Custom field (Request language),Custom field (Request participants),Custom field (Responders),Satisfaction rating,Custom field (Satisfaction date),Custom field (Sentiment),Custom field (Start date),Custom field (Story Points),Custom field (Story point estimate),Custom field (Submitted forms),Custom field (Target end),Custom field (Target start),Custom field (Team),Custom field (Test Result),Custom field (Test User Picker),Custom field (Time to first response),Custom field (Time to resolution),Custom field (Total forms),Custom field (Type),Custom field (Urgency),Custom field (Version),Custom field (Vulnerability),Custom field (Website),Custom field (Work category),Custom field ([CHART] Date of First Response),Custom field ([CHART] Time in Status),Comment,Comment,Parent,Parent key,Parent summary,Status Category,Status Category Changed
[MB - Payment and Payout] Add Payout Method,AL-138,38638,Story,Specified,AL,AIOS Link,software,Kai Nguyễn,6214a4102d1a9d0069949c96,,Medium,,,,Duyen Nguyen,712020:59b66c6e-1068-441c-9436-130289e16a26,Duyen Nguyen,712020:59b66c6e-1068-441c-9436-130289e16a26,17/Jun/25 6:04 PM,17/Jun/25 7:00 PM,,,,0,"h3. 1. Use Case Description                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                     

As a user or merchant on AIOS Link, I want to securely add a payout method (such as a bank account) so that I can receive earnings and refunds. 

----

h3. 2. User Workflow 

# User navigates to *Settings > Payout Settings*.
# Taps *Add Payout Method*.
# System opens a *Nuvei Web SDK embedded form* (via secure mobile webview).
# User selects payout method type (e.g., bank transfer).
# User fills in required fields depending on payout method:
#* Bank Name
#* Account Number
#* Account Holder Name
#* Routing Number / SWIFT / IBAN (country dependent)
# User may set this method as *Default Payout Method*.
# Taps *Save Payout Method*.
# Nuvei securely tokenizes the bank details and returns a *payout token*.
# AIOS stores token metadata; system shows success confirmation.
# Payout method appears in user’s saved list with masked format (e.g., “•••• 9182”).

----

h3. 3. Field Definitions (via Nuvei Web SDK)

||Field Name||Type||Validation Rule||Required||Description||
|Account Holder Name|Text|Max 100 chars|✅|Name of the recipient|
|Bank Name|Text|Max 100 chars|✅|Name of financial institution|
|Account Number|Number|Country-specific format|✅|Recipient account number|
|Routing/SWIFT/IBAN|Alphanumeric|Based on region|✅|Bank identifier for payout|
|Set as Default|Toggle|None|❌|Optional: mark this as primary payout method|

----

h3. 4. Acceptance Criteria

* *AC1*: App uses Nuvei Web SDK via secure embedded webview for all payout data entry.
* *AC2*: Payout method is saved only if Nuvei returns a valid token.
* *AC3*: Payout token and metadata are securely stored in AIOS backend.
* *AC4*: Only masked payout info is displayed to the user (e.g., Bank ••••9182).
* *AC5*: A user may store multiple payout methods but only one can be default.
* *AC6*: Confirmation toast appears after successful method addition.

----

h3. 5. System Rules

* Payout method data is handled exclusively by Nuvei (AIOS stores only reference tokens).
* Only users with verified identity may add or receive payouts (linked to KYC logic).
* The default method is used when requesting refunds or event payouts.
* Payout logs are recorded in transaction history for transparency.
* Each addition/modification is audit-logged with user ID and timestamp.

----

h3. 6. UX Optimizations

* Display expected processing time (e.g., “Payouts may take 1–2 business days”).
* Mask sensitive fields (e.g., show only last 4 digits of account).
* Auto-suggest bank names based on region and routing code (if supported).
* Show success toast: _“Payout method added successfully.”_
* Let user manage payout methods (edit nickname, delete, set default).",,Duyen Nguyen,,712020:59b66c6e-1068-441c-9436-130289e16a26,,,,,,,,,,,,,,,,,com.atlassian.servicedesk.plugins.approvals.internal.customfield.ApprovalsCFValue@139374ff,,,,,,,,,{},,,,,,,,,,,,,,,,,,MOBILE,,,,0|i02rzr:,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,37475,AL-59,Payment and Payout,To Do,17/Jun/25 6:04 PM
[MB - Payment and Payout] Add Payment Method,AL-137,38635,Story,Specified,AL,AIOS Link,software,Kai Nguyễn,6214a4102d1a9d0069949c96,,Medium,,,,Duyen Nguyen,712020:59b66c6e-1068-441c-9436-130289e16a26,Duyen Nguyen,712020:59b66c6e-1068-441c-9436-130289e16a26,17/Jun/25 5:16 PM,17/Jun/25 5:58 PM,,,,0,"h3. 1. Use Case Description

As a user or merchant on the AIOS Link mobile platform, I want to securely add my payment method so that I can complete transactions.

----

h3. 2. User Workflow (Step-by-Step)

# User navigates to *Settings > Payment Settings*.
# Taps *Add Payment Method*.
# App launches an embedded webview or modal powered by the *Nuvei Web SDK*.
# User completes the payment form (hosted via Nuvei):
#* Cardholder Name
#* Card Number
#* Expiry Date
#* CVV
# User may toggle *Set as Default*.
# On submission, the Nuvei Web SDK:
#* Validates the card data
#* Tokenizes the card
#* Returns a secure *payment token* to AIOS backend
# If successful, the system displays a toast: _“Payment method added successfully.”_
# The card appears in the *Saved Payment Methods* list (masked format).

----

h3. 3. Field Definitions (handled by Nuvei Web SDK)

||Field Name||Type||Validation Rule||Required||Description||
|Cardholder Name|Text|Alphabetic, max 100 chars|✅|As printed on card|
|Card Number|Numeric|Luhn-valid format|✅|Handled securely by Nuvei|
|Expiry Date|MM/YY|Must be in the future|✅|Expiration of card|
|CVV|Numeric|3 or 4 digits|✅|Card Verification Value|
|Set as Default|Toggle|None|❌|Option to mark as default method|

----

h3. 4. Acceptance Criteria

* *AC1*: App uses Nuvei Web SDK inside secure webview or modal for all card data entry.
* *AC2*: No card or CVV data is handled or stored by AIOS Link — only the Nuvei token and metadata.
* *AC3*: Payment method is saved only if Nuvei SDK returns a valid token.
* *AC4*: The newly added method appears with masked card info (e.g., Visa •••• 4242).
* *AC5*: Only one payment method can be default at a time.
* *AC6*: Success message confirms storage and readiness for transaction use.

----

h3. 5. System Rules

* AIOS stores only the token, card brand, last 4 digits, and expiration date.
* All backend communication with Nuvei follows secure OAuth or API key protocol.
* Each user/merchant can store multiple cards but only one default.
* Deleting a method unlinks it from the profile but does not delete the token from Nuvei unless required.
* Changes are audit-logged by user ID and timestamp.

----

h3. 6. UX Optimizations

* Branded card icons (Visa, Mastercard) appear upon card entry.
* Inline error handling via Nuvei SDK ensures seamless UX.
* Use progress indicators during token generation.
* Include entry point to manage cards:
** Edit nickname
** Set as default
** Remove card",,Duyen Nguyen,,712020:59b66c6e-1068-441c-9436-130289e16a26,,,,,,,,,,,,,,,,,com.atlassian.servicedesk.plugins.approvals.internal.customfield.ApprovalsCFValue@4872bbe4,,,,,,,,,{},,,,,,,,,,,,,,,,,,MOBILE,,,,0|i02rzj:,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,37475,AL-59,Payment and Payout,To Do,17/Jun/25 5:16 PM
[WEB - Payment and Payout] Admin Review Event Request Payout,AL-136,38445,Story,To Do,AL,AIOS Link,software,Kai Nguyễn,6214a4102d1a9d0069949c96,,Medium,,,,Duyen Nguyen,712020:59b66c6e-1068-441c-9436-130289e16a26,Duyen Nguyen,712020:59b66c6e-1068-441c-9436-130289e16a26,16/Jun/25 10:29 AM,16/Jun/25 10:29 AM,,,,0,,,Duyen Nguyen,,712020:59b66c6e-1068-441c-9436-130289e16a26,,,,,,,,,,,,,,,,,com.atlassian.servicedesk.plugins.approvals.internal.customfield.ApprovalsCFValue@1c84eb0e,,,,,,,,,{},,,,,,,,,,,,,,,,,,WEB,,,,0|i02rtz:,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,37475,AL-59,Payment and Payout,To Do,16/Jun/25 10:29 AM
[MB - Payment and Payout] Request Event Payout,AL-135,38442,Story,Specified,AL,AIOS Link,software,Kai Nguyễn,6214a4102d1a9d0069949c96,,Medium,,,,Duyen Nguyen,712020:59b66c6e-1068-441c-9436-130289e16a26,Duyen Nguyen,712020:59b66c6e-1068-441c-9436-130289e16a26,16/Jun/25 10:26 AM,16/Jun/25 10:28 AM,,,,0,"h3. 1. Use Case Description

As a merchant or premium user, I want to request a payout for my event earnings so that I can receive my balance.

----

h3. 2. User Workflow (Step-by-Step)

# User opens the AIOS Link mobile app.
# Navigates to *My Events > Past Events*.
# Selects a completed ticketed event.
# If the event ended more than 24 hours ago, a *""Request Payout""* button is shown.
# User taps *Request Payout*.
# System displays a confirmation modal and estimated processing time.
# Upon confirmation, system marks the payout as “Pending Admin Review.”
# Admin receives a request flag on web platform.
# Admin verifies:
#* Event compliance (e.g., no unresolved misconduct reports)
#* Ticket sale records and attendance (if required)
# If approved, payout is released to user’s linked wallet or payout method.
# System updates event status to “Payout Complete” or “Payout Rejected” with reason.

----

h3. 3. Field Definitions

h4. Display Fields (Mobile)

||Field Name||Type||Description||
|Event Title|Text|Event name|
|Event End Date & Time|DateTime|Used to validate 24-hour payout request window|
|Ticket Revenue|Currency|Gross ticket earnings (USD)|
|Service Fees Deducted|Currency|AIOS platform fee (if applicable)|
|Net Payout|Currency|Final amount to be disbursed|
|Payout Status|Badge|Pending, Under Review, Completed, Rejected|
|Admin Note (if rejected)|Text|Displayed with rejection reason|

----

h3. 4. Acceptance Criteria

* *AC1*: Only Merchant or Premium users can access the payout request function.
* *AC2*: “Request Payout” button is shown only if the event ended ≥24h ago.
* *AC3*: Tapping the button opens a confirmation modal with payout summary.
* *AC4*: After confirmation, payout enters “Under Review” status.
* *AC5*: Admin must manually approve or reject payout.
* *AC6*: Rejected payout must include reason viewable by user.
* *AC7*: Upon approval, payout is marked “Completed” and balance is disbursed.
* *AC8*: Each payout request is logged with timestamp and host ID.

----

h3. 5. System Rules

* One payout request allowed per event.
* Event must be completed for at least 24 hours before payout request is allowed.
* Admin review must check for unresolved event misconduct reports.
* Refunds (if any) are deducted from payout total.
* Service fees (if applicable) are automatically deducted from gross total.
* Rejected payout requests do not allow resubmission; user must contact support.
* Only events with at least one ticket sold are eligible for payout.

----

h3. 6. UX Optimizations

* “Request Payout” button is greyed out and disabled with tooltip if within 24h window.
* Confirmation modal includes:
** Net payout breakdown
** “Are you sure you want to submit this request?”
* Real-time badge status reflects backend updates (Pending → Under Review → Completed).
* Admin rejection note shown in red with clear formatting under payout status.

----",,Duyen Nguyen,,712020:59b66c6e-1068-441c-9436-130289e16a26,,,,,,,,,,,,,,,,,com.atlassian.servicedesk.plugins.approvals.internal.customfield.ApprovalsCFValue@6b327e7e,,,,,,,,,{},,,,,,,,,,,,,,,,,,MOBILE,,,,0|i02rtr:,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,37475,AL-59,Payment and Payout,To Do,16/Jun/25 10:26 AM
[WEB - Inventory] Product Details,AL-134,38406,Story,In Progress,AL,AIOS Link,software,Kai Nguyễn,6214a4102d1a9d0069949c96,,Medium,,,,Duyen Nguyen,712020:59b66c6e-1068-441c-9436-130289e16a26,Duyen Nguyen,712020:59b66c6e-1068-441c-9436-130289e16a26,15/Jun/25 11:11 PM,25/Jun/25 10:17 AM,25/Jun/25 9:30 AM,,,0,"h3. 1. Use Case Description

As an admin on the web platform, I want to view complete information about a specific product so that I can verify data, update information, and manage its marketplace listing.

----

h3. 2. User Workflow (Step-by-Step)

# Admin logs into the Web Admin Panel.
# Navigates to *Marketplace > Product List* or *Inventory > Product List (by Organization)*.
# Selects a product to open the *Product Detail* view.
# System displays all product metadata grouped into:
#* General Information
#* Marketplace Listing (if applicable)
# Admin may:
#* View owner’s organization (clickable link)
#* View or edit listing price (if listed)
#* View associated orders (if listed)
#* Click “Update Product” to open edit form
#* Remove product from marketplace (if listed)
#* Click “Put to Marketplace” (if not listed)
# Admin clicks *Back* to return to the product list (scroll state preserved).

----

h3. 3. Field Definitions

h4. General Fields

||Field Name||Type||Description||
|Product Name|Text|Title of the product|
|Category|Text|Assigned category|
|Description|Text Area|Full description|
|Manufacturer|Text|Product origin or brand|
|Quantity|Integer|Number of units available|
|Listing Price|Currency|Price in USD|
|Product Status|Enum Badge|Draft / Inactive / Marketplace|
|Owner Organization|Text Link|Clickable name → navigates to org profile|

h4. Marketplace Section (if listed)

||Field Name||Type||Description||
|Listing Price|Currency|Price in USD, editable inline|
|Listing Date|Date|Date when published on marketplace|
|View Orders|Button|Opens order list filtered by this product|
|Remove from Marketplace|Button|Initiates de-listing flow|

----

h3. 4. Interaction Elements

||Element||Type||Condition||Action||
|Update Product Button|Button|Always visible|Navigates to update screen|
|Put to Marketplace|Button|Visible if not listed|Opens listing flow|
|Edit Price Icon|Icon|Visible if listed|Enables inline field with Save/Discard buttons|
|View Orders Button|Button|Visible if listed|Opens order list filtered by product|
|Remove Button|Button|Visible if listed|Triggers marketplace removal flow|
|Back Button|Button|Always visible|Returns to previous list with scroll state retained|

----

h3. 5. Acceptance Criteria

* *AC1*: All product fields (including status and quantity) are displayed in read-only format.
* *AC2*: If the product is in Marketplace status:
** Listing Price, Listing Date, and control buttons appear.
* *AC3*: Edit icon beside price allows inline update with validation (positive USD).
* *AC4*: Remove button follows the marketplace removal workflow.
* *AC5*: View Orders button opens the filtered marketplace orders page.
* *AC6*: If not listed, “Put to Marketplace” button is shown.
* *AC7*: Owner organization name links to Admin Org Detail view.
* *AC8*: All actions are logged with admin ID and timestamp.

----

h3. 6. System Rules

* Product status must be one of: Draft, Inactive, or Marketplace.
* Listing data is shown only if product is actively listed.
* Price edits require validation (> 0.00 USD).
* Owner cannot be changed here; managed via organization management.
* All updates are recorded in the audit log.

----

h3. 7. UX Optimizations

* Display structured layout with two main sections: General / Marketplace
* Skeleton loader shown while data is fetching
* Sticky action bar at bottom: Update, View Orders, Remove
* Inline price field highlights during edit mode
* “Not listed” banner shown clearly when product is not in Marketplace",,Duyen Nguyen,,712020:59b66c6e-1068-441c-9436-130289e16a26,,,,,,,,,,,,,,,,,com.atlassian.servicedesk.plugins.approvals.internal.customfield.ApprovalsCFValue@3e7ef175,,,,,,,,,{},,,,,,,,,,,,,,,,,,WEB,,,,0|i02rtb:,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,38088,AL-89,Inventory,In Progress,25/Jun/25 10:17 AM
[WEB - Inventory] Product List,AL-133,38403,Story,In Progress,AL,AIOS Link,software,Kai Nguyễn,6214a4102d1a9d0069949c96,,Medium,,,,Duyen Nguyen,712020:59b66c6e-1068-441c-9436-130289e16a26,Duyen Nguyen,712020:59b66c6e-1068-441c-9436-130289e16a26,15/Jun/25 11:11 PM,25/Jun/25 10:17 AM,25/Jun/25 9:30 AM,,,0,"h3. 1. Use Case Description

As an admin on the web platform, I want to view all products from every organization in a table so that I can manage them efficiently and navigate to product or business details as needed.

----

h3. 2. User Workflow (Step-by-Step)

# Admin logs into the *Web Admin Panel*.
# Navigates to *Marketplace > Product List*.
# System displays a paginated table with product records across all organizations.
# Admin may:
#* Use search or filters (e.g., business name, category, manufacturer, price range)
#* Click a product row to view product details
#* Click the owner organization name to open its detail page
#* Use the gear icon to update or delete the product
# Table refreshes and maintains state upon return from detail views.

----

h3. 3. Field Definitions

||Field Name||Display Type||Description||
|Thumbnail|Image|1:1 ratio preview or placeholder if missing|
|Product Name|Text|Title of the product|
|Category|Text|Assigned category label|
|Description|Text|Short summary of the product|
|Owner Org Name|Text Link|Organization name, links to org detail|
|Manufacturer|Text|Product brand or origin|
|Quantity|Number|Inventory count available|
|Product Status|Badge|Draft / Inactive / Marketplace|
|Listing Price|Currency (USD)|Only shown if status = Marketplace|
|Actions|Gear Icon|Options to Update or Delete|

----

h3. 4. Search & Filter Rules

h4. Searchable Fields:

* Product Name (partial match)
* Category
* Manufacturer
* Organization Name

h4. Filter Options:

* Category (multi-select)
* Manufacturer (dropdown)
* Organization (dropdown)
* Status (Draft / Inactive / Marketplace)
* Price Range (min-max in USD)

----

h3. 5. Acceptance Criteria

* *AC1*: Table must show product rows with all columns listed above.
* *AC2*: Click on row → opens Product Detail; click on org → opens Org Detail.
* *AC3*: Search returns results matching any included field.
* *AC4*: Filters apply instantly and can be combined.
* *AC5*: Actions menu includes Update (edit form) and Delete (with confirmation).
* *AC6*: Deletion is blocked if product has any active order linkage.
* *AC7*: Table loads 25 results per page with persistent filter state.
* *AC8*: Listing price only displays for Marketplace products.

----

h3. 6. System Rules

* Products must include one of the three valid statuses: Draft, Inactive, Marketplace.
* Update action checks for race conditions (latest version only editable).
* Delete action is soft-delete, audit-tracked, and disabled if product is linked to orders.
* All admin actions are logged (admin ID, timestamp, action type).
* Products must be sorted alphabetically (A–Z) by default.

----

h3. 7. UX Optimizations

* Hover effect on table rows to indicate interactivity
* Sticky filter/search bar at top during scroll
* Spinner overlay when an action is processing
* Tooltip on gear icon with label: “Manage product”
* Maintain scroll position and filter state on return from detail or update views",,Duyen Nguyen,,712020:59b66c6e-1068-441c-9436-130289e16a26,,,,,,,,,,,,,,,,,com.atlassian.servicedesk.plugins.approvals.internal.customfield.ApprovalsCFValue@46ad2930,,,,,,,,,{},,,,,,,,,,,,,,,,,,WEB,,,,0|i02rt3:,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,38088,AL-89,Inventory,In Progress,25/Jun/25 10:17 AM
[WEB - Organization] Delete Organization,AL-132,38388,Story,In Progress,AL,AIOS Link,software,Kai Nguyễn,6214a4102d1a9d0069949c96,,Medium,,,,Duyen Nguyen,712020:59b66c6e-1068-441c-9436-130289e16a26,Duyen Nguyen,712020:59b66c6e-1068-441c-9436-130289e16a26,15/Jun/25 4:12 PM,25/Jun/25 10:17 AM,25/Jun/25 9:31 AM,,,0,"h3. 1. Use Case Description

As an admin on the web platform, I want to delete an organization that has no related data so that obsolete or unused organizations are removed from the system.

----

h3. 2. User Workflow (Step-by-Step)

# Admin accesses *Organization List* from the Admin Panel.
# Clicks the *gear icon* or opens the *Organization Detail* view.
# Selects *Delete Organization*.
# System checks for existing dependencies:
#* Events
#* Listings
#* Orders
#* Owner account
# If no dependencies found → system displays a *confirmation modal*.
# Admin confirms deletion.
# System deletes the organization permanently and shows success toast.
# If dependencies are present → deletion is blocked, and an informational message is shown recommending archival.

----

h3. 3. Field Definitions

h4. Interaction Elements

||Element Name||Type||Condition||Description||
|Delete Button|Button|Always visible (conditional logic inside)|Initiates the deletion check|
|Confirmation Modal|Modal|On no dependencies|Summarizes deletion action|
|Confirm Delete Button|Button|Enabled in modal|Executes delete|
|Cancel Button|Button|Always enabled|Dismisses modal|
|Error Alert|Inline|On failed check|Informs admin about dependency restrictions|

----

h3. 4. Data Display Table

||Display Item||Example||Notes||
|Success Message|“Organization deleted.”|Appears after successful deletion|
|Failure Notice|“This organization cannot be deleted due to linked events or users.”|Blocks further action|
|Archive Suggestion|“You may archive this organization instead.”|Placeholder for future logic|

----

h3. 5. Acceptance Criteria

* *AC1*: Admin sees Delete option from both the list and detail views.
* *AC2*: System performs dependency check before showing modal.
* *AC3*: If dependent data exists (e.g., events, products, orders, assigned owner), deletion is blocked.
* *AC4*: If no dependencies exist, admin can confirm deletion via modal.
* *AC5*: System removes the record permanently and returns to Organization List.
* *AC6*: Deletion action is logged with admin ID, timestamp, and org ID.

----

h3. 6. System Rules

* Delete is allowed only when:
** No events
** No listings
** No orders
** No assigned owner
* EIN/DUNS must be released from unique constraint on deletion.
* Deleted orgs are not visible in UI but retained in audit logs.
* Related actions (e.g., webhooks, moderation queues) are canceled if pending.

----

h3. 7. UX Optimizations

* Red-colored danger-styled delete buttons
* Confirmation modal includes warning: _“This action is permanent and cannot be undone.”_
* Modal lists basic org info: name and registration date
* Disable confirm button during backend operation (spinner)

----",,Duyen Nguyen,,712020:59b66c6e-1068-441c-9436-130289e16a26,,,,,,,,,,,,,,,,,com.atlassian.servicedesk.plugins.approvals.internal.customfield.ApprovalsCFValue@714b8881,,,,,,,,,{},,,,,,,,,,,,,,,,,,WEB,,,,0|i02rqn:,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,37951,AL-87,Organization,In Progress,25/Jun/25 10:17 AM
[WEB - Organization] Update Organization,AL-131,38385,Story,In Progress,AL,AIOS Link,software,Kai Nguyễn,6214a4102d1a9d0069949c96,,Medium,,,,Duyen Nguyen,712020:59b66c6e-1068-441c-9436-130289e16a26,Duyen Nguyen,712020:59b66c6e-1068-441c-9436-130289e16a26,15/Jun/25 4:11 PM,25/Jun/25 10:17 AM,25/Jun/25 9:31 AM,,,0,"h3. 1. Use Case Description

As an admin on the web platform, I want to update existing organization information and reassign ownership if needed, so that records remain accurate.

----

h3. 2. User Workflow (Step-by-Step)

# Admin navigates to *Organization Management > Organization List*.
# Selects an organization entry to open the *Organization Detail* page.
# Clicks the *Edit Organization* button.
# System displays a form with pre-filled editable fields.
# Admin updates one or more permitted fields.
# Clicks *Save* to submit changes.
# System validates inputs and persists the update.
# A success message confirms the change and redirects back to the detail screen.

----

h3. 3. Editable Field Definitions

||Field Name||Field Type||Validation Rule||Required||Description||
|Organization Name|Text|1–100 characters, must be unique|✅|Legal or registered name of the organization|
|Organization Address|Text Area|Max 200 characters|✅|Business or contact address|
|Date of Establishment|Date Picker|Must be a past or current date|✅|When the organization was founded|
|EIN/DUNS Number|Text|Alphanumeric, must be unique|✅|Federal tax or business identifier|

----

h3. 4. Interaction Elements

||Element||Type||State/Condition||Description||
|Save Button|Button|Enabled on valid input|Commits changes and redirects to detail screen|
|Cancel Button|Button|Always enabled|Returns to detail screen without saving|
|Inline Validation|Text|Visible on error|Field-specific input errors shown during typing|

----

h3. 5. Acceptance Criteria

* *AC1*: Admin can access editable fields defined above—no owner-related fields are shown.
* *AC2*: EIN/DUNS must be unique; error shown if duplicate detected.
* *AC3*: Admin can only save if all required fields are valid.
* *AC4*: Updates are persisted and reflected immediately in Organization Detail view.
* *AC5*: Action is logged with admin ID, timestamp, and before/after values.

----

h3. 6. System Rules

* EIN/DUNS must be unique across all organizations.
* Organization name must remain unique.
* Audit logs store changes with previous and updated values.
* Updates do not trigger revalidation of related entities (e.g., storefronts or events).
* Editing is permission-gated to admin roles with update rights.

----

h3. 7. UX Optimizations

* Pre-filled fields shown on load to minimize admin input
* Real-time validation with field-level error display
* Save action shows spinner and toast: _“Organization updated successfully”_
* Optional tooltips on EIN/DUNS field: “Used for tax and compliance tracking”",,Duyen Nguyen,,712020:59b66c6e-1068-441c-9436-130289e16a26,,,,,,,,,,,,,,,,,com.atlassian.servicedesk.plugins.approvals.internal.customfield.ApprovalsCFValue@63e54d53,,,,,,,,,{},,,,,,,,,,,,,,,,,,WEB,,,,0|i02rqf:,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,37951,AL-87,Organization,In Progress,25/Jun/25 10:17 AM
[WEB - Organization] Organization List,AL-130,38382,Story,In Progress,AL,AIOS Link,software,Kai Nguyễn,6214a4102d1a9d0069949c96,,Medium,,,,Duyen Nguyen,712020:59b66c6e-1068-441c-9436-130289e16a26,Duyen Nguyen,712020:59b66c6e-1068-441c-9436-130289e16a26,15/Jun/25 4:11 PM,25/Jun/25 10:17 AM,25/Jun/25 9:31 AM,,,0,"h3. 1. Use Case Description

As an admin, I want to view and manage a list of registered organizations so that I can quickly identify the owner, contact info, activity status, and take appropriate actions such as viewing details or disabling the account.

----

h3. 2. User Workflow (Step-by-Step)

# Admin logs into the *Web Admin Panel*.
# Navigates to *Organization Management > Organization List*.
# System displays a paginated table view with:
#* Basic information columns
#* Owner contact and organization status
#* Row-level actions
# Admin can:
#* Search or filter by organization name or owner
#* Click an organization row to view details
#* Click the owner name to view their User Detail
#* Use the gear icon for *Update* or *Disable* actions

----

h3. 3. Field Definitions

h4. Summary Fields (Recommended for Table View)

||Field Name||Display Type||Description||
|Organization Name|Text|Registered organization name|
|Owner Name|Text Link|Owner’s full name (clickable to view user detail)|
|Owner Email|Text|Used for identity/contact verification|
|Owner Phone Number|Text|Optional, for quick contact|
|Date of Registration|Date (YYYY-MM-DD)|When the org was registered in the system|
|Status|Badge|Active / Inactive|
|Actions|Gear Icon|Provides Update / Disable|

----

h3. 4. Search & Filter Rules

h4. Search

* Case-insensitive partial match on:
** Organization Name
** Owner Name

h4. Filters

* *Owner Filter*: Dropdown of all registered owners
* *Date Range Filter*: Established date (Start ≤ End)
* *Status Filter*: Active / Inactive

----

h3. 5. Acceptance Criteria

* *AC1*: Table shows columns: Org Name, Owner Name, Email, Phone, Date of Registration, Status, and Actions
* *AC2*: Clicking the row navigates to Organization Detail
* *AC3*: Clicking Owner Name opens User Detail
* *AC4*: Search filters results live as user types
* *AC5*: Filters narrow by selected owner, date, or status
* *AC6*: Update/Disable actions are contextual and show confirmation
* *AC7*: Pagination loads 25 results per page by default
* *AC8*: Gear icon is disabled with spinner during pending operations

----

h3. 6. System Rules

* Delete action (if enabled) is soft delete, requires no dependent data
* Default sort: Date of Registration (newest first)
* Search and filters are preserved on return from detail view
* Action history (e.g., disable) is logged with admin ID and timestamp

----

h3. 7. UX Optimizations

* Hover state highlights clickable rows
* Sticky search/filter bar on scroll
* “Disabled” badge shown in light gray
* Status badge colored:
** Active = green
** Inactive = red
* Tooltip for gear icon: “Manage organization”",,Duyen Nguyen,,712020:59b66c6e-1068-441c-9436-130289e16a26,,,,,,,,,,,,,,,,,com.atlassian.servicedesk.plugins.approvals.internal.customfield.ApprovalsCFValue@51aa41fe,,,,,,,,,{},,,,,,,,,,,,,,,,,,WEB,,,,0|i02rq7:,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,37951,AL-87,Organization,In Progress,25/Jun/25 10:17 AM
[WEB - Organization] Organization Details,AL-129,38379,Story,In Progress,AL,AIOS Link,software,Kai Nguyễn,6214a4102d1a9d0069949c96,,Medium,,,,Duyen Nguyen,712020:59b66c6e-1068-441c-9436-130289e16a26,Duyen Nguyen,712020:59b66c6e-1068-441c-9436-130289e16a26,15/Jun/25 4:11 PM,25/Jun/25 10:17 AM,25/Jun/25 9:31 AM,,,0,"

h3. 1. Use Case Description

As an admin, I want to view the full details of any registered organization so that I can verify ownership, assess compliance status, and determine whether moderation actions or updates are needed.

----

h3. 2. User Workflow

* Step 1: Admin logs into the Admin Panel via web.
* Step 2: Navigates to the *Organization Management* tab.
* Step 3: Admin selects an organization entry from the list.
* Step 4: System navigates to the *Organization Detail* screen with preloaded data.
* Step 5: Admin reviews:
** Profile information
** Owner details
** Organizational activity (event hosting, listings)
* Step 6: Admin can choose to update, deactivate, or return to the list.

----

h3. 3. Field Definitions

h4. Input Fields (read-only for view)

||Field Name||Field Type||Validation Rule||Mandatory||Description||
|Organization Name|Text|1–100 chars|✅|Official name of the organization|
|Organization Address|Text|Optional|✅|Full mailing address|
|Date of Establishment|Date|Valid date|❌|YYYY-MM-DD format|
|Organization Status|Enum|Active / Inactive|✅|Indicates active participation|
|Owner Full Name|Text|Linked user|✅|Linked to user ID of account creator|
|Owner Phone/Email|Text|Valid contact format|✅|Contact method of account owner|
|Total Events Hosted|Number|≥ 0|✅|Count of public or private hosted events|
|Total Listings Posted|Number|≥ 0|✅|Marketplace products listed|
|Created At|Date|System timestamp|✅|When org was registered|

----

h3. 4. Data Display (Detail View)

||Display Field||Format Example||Notes||
|Status Badge|Active / Inactive|Greyed out if inactive|
|Owner Detail Section|Jane Smith (ID: U3425)|Clickable to view user detail|
|Event Summary|“5 Events Hosted”|Quick activity indicator|
|Listing Summary|“8 Products Listed”|Inventory count of active listings|
|Storefront Link|“View Storefront” (if applicable)|Only if active marketplace store exists|

----

h3. 5. Acceptance Criteria

* *AC1*: Admin can access organization detail by clicking on any record in the organization list.
* *AC2*: All organization fields listed above are viewable in read-only mode.
* *AC3*: Admin can navigate to linked owner user detail view.
* *AC4*: If storefront is linked, admin can follow to storefront detail.
* *AC5*: Inactive organizations appear with grey status badge and restricted actions.
* *AC6*: Admin can navigate back to Organization List with scroll state preserved.
* *AC7*: All displayed data matches values entered during registration or updates.

----

h3. 6. System Rules

* Only users with Admin role and permissions may access this screen.
* Owner user must be of role type: *merchant* or *premium*.
* Deactivated organizations cannot create events, list products, or appear in public feeds.
* Organization ID must remain immutable; used for all relational joins.
* View action is logged with admin ID and timestamp.
* If organization was suspended manually, a reason tag is shown in the status badge tooltip.

----

h3. 7. UX Optimizations

* Highlighted admin panel breadcrumbs: “Admin > Organization > Detail”
* Sticky status and organization name at top for easier review
* “Go to User Profile” button under owner details
* “See Activity Log” link for compliance history (if available)
* Inline storefront link (opens in new tab)",,Duyen Nguyen,,712020:59b66c6e-1068-441c-9436-130289e16a26,,,,,,,,,,,,,,,,,com.atlassian.servicedesk.plugins.approvals.internal.customfield.ApprovalsCFValue@7a19900e,,,,,,,,,{},,,,,,,,,,,,,,,,,,WEB,,,,0|i02rpz:,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,37951,AL-87,Organization,In Progress,25/Jun/25 10:17 AM
[WEB - Event] Admin Update Event,AL-128,38337,Story,In Progress,AL,AIOS Link,software,Kai Nguyễn,6214a4102d1a9d0069949c96,,Medium,,,,Duyen Nguyen,712020:59b66c6e-1068-441c-9436-130289e16a26,Duyen Nguyen,712020:59b66c6e-1068-441c-9436-130289e16a26,13/Jun/25 3:35 PM,23/Jun/25 10:32 PM,23/Jun/25 9:35 PM,,,0,"h3. 1. Use Case Description

As an admin on the web platform, I want to update event information without altering the host or ticket requirements so that existing registrations remain valid and consistent.

----

h3. 2. User Workflow (Step-by-Step)

# Admin logs into the Web Admin Panel.
# Navigates to *Events > Manage Events*, then selects an event.
# Clicks the *“Edit”* button.
# System loads the pre-filled event form with all editable fields.
# Admin modifies any allowable field.
# System validates rules (e.g., max attendee reduction, ticket lock).
# Admin saves the changes.
# A success toast appears: _“Event updated successfully.”_
# The updated event reflects changes immediately in Discover and host views.

----

h3. 3. Editable Field Rules

||Field Name||Editable||Conditions||
|Event Title|✅|No restrictions|
|Date & Time|✅|Disabled if event is ticketed and tickets have been sold|
|About|✅|Always editable|
|Visibility|✅|Can switch between Public and Private|
|Tags|✅|Max 10 tags|
|Telegram Link|✅|Must be valid URL|
|WhatsApp Link|✅|Must be valid URL|
|Address|✅|Required only if Event Type = In-Person|
|Meeting Link|✅|Required only if Event Type = Virtual|
|Max Attendees|✅|Cannot set lower than current RSVP count|
|Ticket Price|✅|Editable only if 0 tickets sold|
|Host|❌|Admin cannot change the host after creation|
|Event Type|❌|Not editable post-creation|
|Event Access|❌|Free ↔ Ticketed cannot be toggled after event is live|

----

h3. 4. Acceptance Criteria

* *AC1*: Admin can access and view all editable event fields.
* *AC2*: Admin can only reduce attendee cap to match or exceed current RSVPs.
* *AC3*: Ticket price field is locked if any ticket has been sold.
* *AC4*: Optional fields (e.g. links, tags) are fully editable.
* *AC5*: Boosted events retain their boost status and sorting logic after update.
* *AC6*: Event changes reflect immediately in Discover and related views.
* *AC7*: System shows error if invalid field update is attempted (e.g., attendee drop below current RSVPs).
* *AC8*: All changes are logged with timestamp and admin ID.

----

h3. 5. System Rules

* Date/Time edits on ticketed events are blocked if any sales exist.
* Boost metadata is preserved post-update (rank/expiration unchanged).
* Edit screen highlights any disabled fields with tooltip explanation.
* Admin audit log captures:
** Before/after values
** Admin user ID
** Event ID
** Timestamp
* Ticket structure (Free/Ticketed) cannot be toggled once live.
* Event host cannot be reassigned through this form.
* Discover feed is auto-refreshed after admin update if public.

----

h3. 6. UX Optimizations

* Disabled fields (e.g., locked ticket price) are grayed with a lock icon
* Inline validation for RSVP-related max attendee rule
* Confirmation toast: _“Event updated successfully.”_
* Persistent banner warning if ticket locks apply:
_“Tickets have been sold. Date, time, and price cannot be changed.”_

----",,Duyen Nguyen,,712020:59b66c6e-1068-441c-9436-130289e16a26,,,,,,,,,,,,,,,,,com.atlassian.servicedesk.plugins.approvals.internal.customfield.ApprovalsCFValue@2ee406b5,,,,,,,,,{},,,,,,,,,,,,,,,,,,WEB,,,,0|i02ro7:,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,37186,AL-4,Events,In Progress,23/Jun/25 10:32 PM
[WEB - Event] Cancel Event,AL-127,38332,Story,In Progress,AL,AIOS Link,software,Kai Nguyễn,6214a4102d1a9d0069949c96,,Medium,,,,Duyen Nguyen,712020:59b66c6e-1068-441c-9436-130289e16a26,Duyen Nguyen,712020:59b66c6e-1068-441c-9436-130289e16a26,13/Jun/25 2:55 PM,23/Jun/25 10:32 PM,23/Jun/25 9:35 PM,,,0,"h3. 1. Use Case Description

As an admin, I want to cancel an event that has already been published so that I can enforce platform guidelines, notify all attendees, revoke active transactions, and retain full access to the event’s data for compliance and audit purposes.

----

h3. 2. User Workflow (Step-by-Step)

# Admin logs into the Admin Panel.
# Navigates to *Events > Manage Events* and selects the target event.
# Clicks *“Cancel Event”* button from the action menu.
# System displays a *confirmation modal* with a warning:
#* Event will be marked as cancelled
#* Attendees will be notified
#* Ticket refunds (if applicable) will be issued as wallet credits
#* Host earnings will be reversed
# Admin optionally adds a *cancellation note* to attendees.
# Clicks *Confirm Cancellation*.
# System executes:
#* Event status → Cancelled
#* All users notified
#* Ticket refunds issued
#* Host credits reversed
# Event remains visible in logs and list view with “Cancelled” badge.

----

h3. 3. Field Definitions Table

h4. 3.1 Submission Fields

||Field Name||Type||Required||Description||
|Event ID|UUID|✅|Event to be cancelled|
|Cancellation Note|Text|❌|Optional message to attendees|

h4. 3.2 Interaction Elements

||Element Name||Type||Required||Description||
|Cancel Event CTA|Button|✅|Located in event detail admin view|
|Confirmation Modal|Modal|✅|Confirms irreversible cancellation|
|Status Badge|Label|✅|“Cancelled” shown on cards and detail page|

----

h3. 4. Data Display Table (Admin View)

||Field||Example||Notes||
|Status Badge|“Cancelled” (gray-red)|Visible in event list and detail view|
|Event Title|“AIOS Meetup”|Remains unchanged|
|Host|Jane Nguyen (User)|Remains visible|
|Attendee Count|“57 RSVP’d”|Still displayed, read-only|
|Refund Summary|“$180 in ticket refunds”|Visible in modal if applicable|
|Action Logs|Timestamp + admin ID|Logged for audit tracking|

----

h3. 5. Acceptance Criteria

* *AC1*: Only admins with event moderation rights can cancel any event.
* *AC2*: Cancellation opens a modal showing irreversible consequences.
* *AC3*: Cancelled status is reflected across cards and detail view.
* *AC4*: All attendees receive push/email notification.
* *AC5*: Tickets refunded as wallet credits (USD value).
* *AC6*: Host earnings deducted in equivalent refund amount.
* *AC7*: Event remains in system with “Cancelled” badge and read-only status.
* *AC8*: No further RSVP, boost, or share actions allowed.
* *AC9*: Cancelled events are still visible to admins for reporting.
* *AC10*: Refunds are credited to AIOS wallet and cannot be withdrawn.

----

h3. 6. System Rules

* Cancellation is a *soft delete*; all data must remain intact.
* Active boosts are ended with no refund.
* Admin cancellation action is logged with timestamp and admin ID.
* If event is ticketed:
** Refund = ticket price × quantity
** Host revenue reversed and recorded in transaction log
* User receives standard cancellation notice with optional admin message.
* If private: only invited users are notified.
* Scheduled notifications are deactivated upon cancellation.

----

h3. 7. UX Optimizations

* Use danger-themed confirmation modal with red highlight
* Show refund breakdown in modal if ticketed
* Inline banner:
_“You’re about to cancel this event. This action is permanent. All attendees will be notified.”_
* Allow optional cancellation note input box
* Disable edit, boost, and ticket actions post-cancellation

----",,Duyen Nguyen,,712020:59b66c6e-1068-441c-9436-130289e16a26,,,,,,,,,,,,,,,,,com.atlassian.servicedesk.plugins.approvals.internal.customfield.ApprovalsCFValue@52c2ed12,,,,,,,,,{},,,,,,,,,,,,,,,,,,WEB,,,,0|i02rnr:,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,37186,AL-4,Events,In Progress,23/Jun/25 10:32 PM
[WEB - Events] Create Event,AL-126,38329,Story,In Progress,AL,AIOS Link,software,Kai Nguyễn,6214a4102d1a9d0069949c96,,Medium,,,,Duyen Nguyen,712020:59b66c6e-1068-441c-9436-130289e16a26,Duyen Nguyen,712020:59b66c6e-1068-441c-9436-130289e16a26,13/Jun/25 2:54 PM,23/Jun/25 10:32 PM,23/Jun/25 9:35 PM,,,0,"h3. 1. Use Case Description

As an admin, I want to create an event on behalf of a Premium user or merchant organization with flexible visibility, attendance, and ticketing configurations so that the event can be published to the Discover tab and behave consistently with user-created events.

----

h3. 2. User Workflow (Step-by-Step)

# Admin logs into the web admin dashboard.
# Navigates to *Events > Create Event*.
# Fills in the *Create Event Form*, including selecting the event host (User or Organization).
# Admin chooses Event Type:
#* If *In-person* → “Address” becomes mandatory.
#* If *Virtual* → “Meeting Link” becomes mandatory.
# Admin sets:
#* Visibility: Public or Private
#* Access Type: Free or Ticketed
#* Max Attendees
#* (Optional) RSVP Limit
# Clicks *Create Event*.
# System validates all input.
# On success:
#* Shows toast: “Event created successfully.”
#* If the event is Ticketed → admin is prompted to complete ticket setup.
# The event appears in the Discover tab if public.

----

h3. 3. Field Definitions Table

📍 Required Fields

||Field Name||Field Type||Validation Rule||Description||
|Event Title|Text|Max 100 characters|Title of the event|
|Date & Time|DateTime|Must be in the future|Scheduled time of the event|
|Event Type|Enum|In-Person / Virtual|Determines conditional fields|
|About|Text Area|Max 4096 characters|Event description|
|Visibility|Enum|Public / Private|Public = Discover tab; Private = invite-only|
|Max Attendees|Number|1–200|Total maximum seats|
|Event Access|Enum|Free / Ticketed|If ticketed → redirect to ticket setup|
|Host Type|Enum|User / Organization|Target host category|
|Host Selection|Dropdown|Must be Premium User or Org|Assigns the event to valid host|

📍 Optional Fields

||Field Name||Field Type||Validation Rule||Description||
|Duration|Number|In minutes|Optional estimated event length|
|Tags|Tag Chips|Max 10 entries|Used for Discover filtering|
|Telegram Group Link|URL|Valid format|Optional attendee chat group|
|WhatsApp Group Link|URL|Valid format|Optional attendee chat group|
|RSVP Limit|Number|≤ Max Attendees|Reserved for invitees only|

📍 Conditional Fields

||Field Name||Field Type||Rule||Description||
|Address|Text|Required if Event Type = In-Person|Physical location of event|
|Meeting Link|URL|Required if Event Type = Virtual|Access link for virtual events|
|Ticket Price|Number|Prompted after creation if Ticketed|Set in My Tickets (USD)|

----

h3. 4. Acceptance Criteria

* *AC1*: Admin can only assign events to Premium users or merchant orgs.
* *AC2*: Form enforces conditional logic for Event Type and Access Type.
* *AC3*: Max Attendees range: 1–200.
* *AC4*: RSVP Limit, if set, is subtracted from Max Attendees and reserved.
* *AC5*: Public events only show (Max – RSVP Limit) in Discover.
* *AC6*: Private events do not appear in Discover; joinable only via invite.
* *AC7*: RSVP invites can optionally include a free ticket if event is ticketed.
* *AC8*: For ticketed events, admin is prompted to complete ticket config after creation.
* *AC9*: Confirmation toast shown post-creation.
* *AC10*: Admin may enable Boost later to promote the event.
* *AC11*: Ticket price editable only before first ticket is issued.
* *AC12*: Ticketed events cannot change date/time post-setup without warning:
_“Ticketed event will not allow change of date and time. Do you want to continue?”_

----

h3. 5. System Rules

* Admin-assigned host must be active and eligible (Premium/Business).
* RSVP Limit creates reserved slots and does not auto-release if rejected.
* Event titles must be unique per host per datetime.
* Ticketed events require follow-up setup to complete.
* All creation and assignment actions are logged with admin ID.",,Duyen Nguyen,,712020:59b66c6e-1068-441c-9436-130289e16a26,,,,,,,,,,,,,,,,,com.atlassian.servicedesk.plugins.approvals.internal.customfield.ApprovalsCFValue@42373e84,,,,,,,,,{},,,,,,,,,,,,,,,,,,WEB,,,,0|i02rnj:,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,37186,AL-4,Events,In Progress,23/Jun/25 10:32 PM
[WEB - Misconduct Report] View Misconduct Report Details,AL-125,38326,Story,Specified,AL,AIOS Link,software,Kai Nguyễn,6214a4102d1a9d0069949c96,,Medium,,,,Duyen Nguyen,712020:59b66c6e-1068-441c-9436-130289e16a26,Duyen Nguyen,712020:59b66c6e-1068-441c-9436-130289e16a26,13/Jun/25 2:46 PM,13/Jun/25 2:51 PM,23/Jun/25 10:32 PM,,,0,"

h3. 1. Use Case Description

As an admin, I want to view the full details of a user-submitted report about an event so that I can assess the nature of the violation and determine whether further moderation action is needed.

----

h3. 2. User Workflow (Step-by-Step)

# Admin accesses the *Report List* in the Admin Panel.
# Admin clicks on a report card to open the full report detail view.
# The system displays full metadata for the report including:
#* Reported event info
#* Reporter info
#* Reason selected
#* Additional notes (if any)
#* Report timestamp
# Admin can take one of the following actions:
#* *Mark as Resolved*
#* *Flag Event* for further action
#* *Suspend Event Owner* (if linked to repeated violations)
# Admin submits action → system updates status and audit logs the action.

----

h3. 3. Field Definitions Table

||Field Name||Field Type||Required||Validation Rules||
|Report ID|Text (readonly)|✅|Unique, auto-generated|
|Event Title|Text (readonly)|✅|Pulled from event metadata|
|Event ID|Text (readonly)|✅|Backend reference|
|Submitted By|User Info Block|✅|User full name, ID|
|Submitted At|DateTime|✅|ISO format|
|Reason|Text|✅|Dropdown-selected reason|
|Additional Notes|Multiline Text|❌|Up to 500 characters|
|Status|Dropdown|✅|Outstanding / Resolved|

----

h3. 4. Data Display Table (Detail View Layout)

||Display Field||Example Value||Notes||
|Event Name|“Networking for Tech Founders”|Hyperlink to event profile (optional)|
|Reporter Info|Jane Nguyen (User ID: U123456)|Show avatar, name, and email|
|Reason|“Misleading event description”|Admin-readable version|
|Notes|“Event started 2 hours late…”|Optional|
|Report Time|2025-06-10 14:22:03|Format: YYYY-MM-DD HH:MM|
|Status|“Outstanding”|Admin can switch to “Resolved”|

----

h3. 5. Acceptance Criteria

* *AC1*: Admin can open any report from the report list to view full detail.
* *AC2*: Report shows all submitted metadata including timestamp, user, and reason.
* *AC3*: Admin can mark report as Resolved or escalate to Event Flag/Suspension.
* *AC4*: Action logs include admin ID and timestamp.
* *AC5*: UI disables further edits once report is marked as Resolved.

----

h3. 6. System Rules

* Report data is read-only except for the status field.
* Actions taken on reports (resolve/flag/suspend) are stored in the admin audit log.
* Only admins with moderation rights can perform escalation actions.
* Flagged or suspended events are removed from Discover view immediately.

----

h3. 7. UX Optimizations

* Action buttons grouped at the bottom: *[Mark as Resolved]*, *[Flag Event]*, *[Suspend Owner]*
* Color-coded badges for report status
* Sticky header showing event name and status at top
* Confirmation modal before suspension or flagging

----",,Duyen Nguyen,,712020:59b66c6e-1068-441c-9436-130289e16a26,,,,,,,,,,,,,,,,,com.atlassian.servicedesk.plugins.approvals.internal.customfield.ApprovalsCFValue@74185f2c,,,,,,,,,{},,,,,,,,,,,,,,,,,,WEB,,,,0|i02rnb:,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,38321,AL-123,Misconduct Report,To Do,13/Jun/25 2:46 PM
[WEB - Misconduct Report] Report List,AL-124,38323,Story,Specified,AL,AIOS Link,software,Kai Nguyễn,6214a4102d1a9d0069949c96,,Medium,,,,Duyen Nguyen,712020:59b66c6e-1068-441c-9436-130289e16a26,Duyen Nguyen,712020:59b66c6e-1068-441c-9436-130289e16a26,13/Jun/25 2:36 PM,13/Jun/25 2:43 PM,23/Jun/25 10:32 PM,,,0,"h3. 1. Use Case Description

As an admin, I want to view and manage all user-submitted event reports so that I can prioritize unresolved violations and take appropriate moderation actions efficiently.

----

h3. 2. User Workflow (Step-by-Step)

# Admin logs into the Web Admin Panel.
# Navigates to the *Reports > Event Reports* section.
# System loads a list of reports:
#* Outstanding (unresolved) reports are displayed at the top
#* Resolved reports are greyed out and moved to the bottom
# Admin can:
#* Tap a report card to view its details
#* Filter or search by event, user, or status
#* Update the status (e.g., mark as resolved)
# Any status update refreshes the list order in real time.

----

h3. 3. Field Definitions Table

h4. Input Fields

||Field Name||Field Type||Validation Rule||Mandatory||Description||
|Report Status|Dropdown|Only: Outstanding, Resolved|Yes|Used for sorting and update action|

h4. Interaction Elements

||Element Name||Type||Trigger Condition||Description||
|Report Card|Button|Always visible|Opens detailed view of a single report|
|Mark Resolved|Button|Only if status = Outstanding|Updates the report status to Resolved|
|Filter Toggle|Toggle|Optional|Filter by status, date, event, user|

----

h3. 4. Data Display Table (Report List View)

||Data Name||Data Type||Display When Empty||Format||Description||
|Report Date|Date|“--”|YYYY-MM-DD|Date report was submitted|
|Event Title|Text|“Unnamed Event”|Plaintext|Event associated with the report|
|Reported By|Text|“Anonymous”|Full name or User ID|User who submitted the report|
|Report Status|Badge|“Unresolved”|Outstanding/Resolved|Used to control card placement and color|
|Reason|Text|“--”|One-line reason summary|Admin preview of reported issue|

----

h3. 5. Acceptance Criteria

* *AC1*: Reports must be displayed in descending order by:
** Outstanding reports first (newest first)
** Resolved reports second (newest first within that group)
* *AC2*: Report cards marked as Resolved are greyed out visually.
* *AC3*: Tapping a report opens its full details and allows status update.
* *AC4*: Reports can be filtered by date, event, or reporter.
* *AC5*: Report status update must be logged with admin ID and timestamp.

----

h3. 6. System Rules

* Reports are sorted server-side using {{status}} and {{created_at}}.
* Status field values: {{outstanding}}, {{resolved}}.
* Greyed styling applied only when status = {{resolved}}.
* Admin actions (status updates) are audit-logged.
* Archived reports remain visible unless filtered out explicitly.

----

h3. 7. UX Optimizations

* Use red badge for ""Outstanding"", gray for ""Resolved"".
* Resolved reports are visually dimmed to reduce prominence.
* Live update of list after status change—no manual refresh.
* Empty state message: _“No reports at this time.”_",,Duyen Nguyen,,712020:59b66c6e-1068-441c-9436-130289e16a26,,,,,,,,,,,,,,,,,com.atlassian.servicedesk.plugins.approvals.internal.customfield.ApprovalsCFValue@7a472eb4,,,,,,,,,{},,,,,,,,,,,,,,,,,,WEB,,,,0|i02rn3:,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,38321,AL-123,Misconduct Report,To Do,13/Jun/25 2:36 PM
[MB - Misconduct Report] Report Event,AL-122,38319,Story,Specified,AL,AIOS Link,software,Kai Nguyễn,6214a4102d1a9d0069949c96,,Medium,,,,Duyen Nguyen,712020:59b66c6e-1068-441c-9436-130289e16a26,Duyen Nguyen,712020:59b66c6e-1068-441c-9436-130289e16a26,13/Jun/25 2:04 PM,16/Jun/25 11:02 AM,,,,0,"h3. 1. Use Case Description

As a user who has attended an event (free or ticketed), I want to report that event if it violated community guidelines, so that the admin team can review the case and take appropriate action.

----

h3. 2. User Workflow (Step-by-Step)

# User views an *event detail* from their *past event history*.
# If the event has concluded, a *“Report Event”* button is shown.
# User taps *Report Event* → system opens a form with:
#* Predefined reason dropdown (e.g., spam, misleading info, safety issue)
#* Optional comment box
# User submits the report.
# System confirms submission with toast: _“Your report has been submitted.”_
# Report is forwarded to the *admin dashboard* on the web platform for moderation.

----

h3. 3. Field Definitions Table

||Field Name||Input Type||Required||Validation Rules||
|Reason|Dropdown|✅|Must select one of the predefined report reasons|
|Additional Notes|Multiline Text|❌|Max 500 characters|

----

h3. 4. Data Display Table (Mobile UI)

||Display Element||Example||Notes||
|Report Reason|“Misleading event description”|Pre-filled dropdown|
|Comment Field|“Tell us what happened…”|Optional|
|Submit Button|“Send Report”|Disabled until a reason is selected|
|Confirmation Toast|“Your report has been submitted.”|Shown upon successful submission|

----

h3. 5. Acceptance Criteria

* *AC1*: Report option only visible for events that have ended.
* *AC2*: User must have attended (RSVP'd or had a ticket) to be eligible to report.
* *AC3*: System requires reason to be selected before submitting.
* *AC4*: All submitted reports are logged and sent to the admin web portal.
* *AC5*: Report triggers confirmation toast after submission.

----

h3. 6. System Rules

* Each report is linked to:
** User ID
** Event ID
** Timestamp
** Selected reason
** Optional comment
* Duplicate reports by the same user for the same event are blocked.
* Admin view includes report list with sorting and filtering by event, user, or reason.
* Events with ≥X reports (configurable threshold) are auto-flagged for high priority review.

----

h3. 7. UX Optimizations

* Use alert icon (e.g., ⚠️) next to ""Report Event"" CTA
* Collapse comment field until a reason is selected
* Redirect user back to past event page after submitting
* Display subtle prompt: _“Only report if you believe this event broke our guidelines.”_",,Duyen Nguyen,,712020:59b66c6e-1068-441c-9436-130289e16a26,,,,,,,,,,,,,,,,,com.atlassian.servicedesk.plugins.approvals.internal.customfield.ApprovalsCFValue@7da0ee7c,,,,,,,,,{},,,,,,,,,,,,,,,,,,MOBILE,,,,0|i02rmn:,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,38321,AL-123,Misconduct Report,To Do,13/Jun/25 2:04 PM
[MB - Account Settings] Enable Face ID/Touch ID,AL-121,38274,Story,Specified,AL,AIOS Link,software,Kai Nguyễn,6214a4102d1a9d0069949c96,,Medium,,,,Duyen Nguyen,712020:59b66c6e-1068-441c-9436-130289e16a26,Duyen Nguyen,712020:59b66c6e-1068-441c-9436-130289e16a26,12/Jun/25 3:46 PM,12/Jun/25 6:23 PM,,,,0,"h3. 1. Use Case Description

As a user on AIOS Link, I want to enable or disable biometric authentication (Face ID or Touch ID) so that I can log in quickly and securely without entering my password every time, while maintaining compatibility with the ""Remember Me"" feature used by merchants.

----

h3. 2. User Workflow (Step-by-Step)

# User logs in and navigates to *Settings > Security > Biometric Login*.
# System checks device compatibility (Face ID or Touch ID supported).
# If supported, system displays a toggle: *“Enable biometric login”*
# User enables the toggle:
#* System prompts native biometric authentication dialog.
#* If successful, biometric login is activated.
# User disables the toggle:
#* System confirms: “You will need to enter your password to log in next time.”
# Biometric login is applied starting from next session.
# If user is a merchant and has selected ""Remember Me"" on login:
#* System disables auto-login via credentials and prioritizes biometric login.

----

h3. 3. Field Definitions Table

||Field Name||Input Type||Required||Validation Rules||
|Enable Biometric|Toggle|✅|Must pass device Face ID / Touch ID prompt|

----

h3. 4. Data Display Table

||Display Field||Format Example||Notes||
|Biometric Toggle|[On / Off] Switch|Only visible if device supports biometrics|
|Biometric Type Label|“Use Face ID” / “Use Touch ID”|Automatically adjusts label based on device type|
|Status Toast|“Biometric login enabled” / “Biometric login disabled”|Shown after toggle action|

----

h3. 5. Acceptance Criteria

* *AC1*: System detects and labels biometric type correctly per device.
* *AC2*: Toggle only appears if device supports Face ID or Touch ID.
* *AC3*: Enabling biometric login requires successful native auth prompt.
* *AC4*: If merchant uses “Remember Me”, biometric overrides stored credentials for login.
* *AC5*: Disabling biometric login removes stored biometric key and shows confirmation toast.
* *AC6*: After enabling, login screen shows biometric option automatically on next session.

----

h3. 6. System Rules

* Biometric login uses native iOS/Android secure storage (Keychain / Keystore).
* Only one method (Remember Me or Biometrics) can be active at login — Biometrics takes priority.
* If both are disabled, user must log in with email/phone and password.
* Changing device or clearing app data requires full credential login.
* Logging out disables biometric auto-login until re-enabled.

----

h3. 7. UX Optimizations

* Automatically adapt toggle label based on device (e.g., “Enable Face ID” for iPhone X+).
* Use biometric icons for visual clarity.
* Show biometric test prompt immediately after toggle ON for validation.
* Display fallback error: “Biometric authentication failed. Please try again.”

----",,Duyen Nguyen,,712020:59b66c6e-1068-441c-9436-130289e16a26,,,,,,,,,,,,,,,,,com.atlassian.servicedesk.plugins.approvals.internal.customfield.ApprovalsCFValue@68e0a720,,,,,,,,,{},,,,,,,,,,,,,,,,,,MOBILE,,,,0|i02rkv:,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,37260,AL-21,Account Settings,To Do,12/Jun/25 3:46 PM
[MB - Account Settings] Credit Usage,AL-120,38271,Story,To Do,AL,AIOS Link,software,Kai Nguyễn,6214a4102d1a9d0069949c96,,Medium,,,,Duyen Nguyen,712020:59b66c6e-1068-441c-9436-130289e16a26,Duyen Nguyen,712020:59b66c6e-1068-441c-9436-130289e16a26,12/Jun/25 3:37 PM,12/Jun/25 3:37 PM,,,,0,,,Duyen Nguyen,,712020:59b66c6e-1068-441c-9436-130289e16a26,,,,,,,,,,,,,,,,,com.atlassian.servicedesk.plugins.approvals.internal.customfield.ApprovalsCFValue@21dc0610,,,,,,,,,{},,,,,,,,,,,,,,,,,,MOBILE,,,,0|i02rkn:,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,37260,AL-21,Account Settings,To Do,12/Jun/25 3:37 PM
[MB - Account Settings] Gift History,AL-119,38268,Story,Specified,AL,AIOS Link,software,Kai Nguyễn,6214a4102d1a9d0069949c96,,Medium,,,,Duyen Nguyen,712020:59b66c6e-1068-441c-9436-130289e16a26,Duyen Nguyen,712020:59b66c6e-1068-441c-9436-130289e16a26,12/Jun/25 3:37 PM,12/Jun/25 6:35 PM,,,,0,"h3. 1. Use Case Description

As a user on AIOS Link, I want to view a list of successfully sent gifts so that I can track my past gifting activity and confirm receipt status and product details.

----

h3. 2. User Workflow (Step-by-Step)

# User navigates to *Gifts > Sent History*.
# System displays a list of all *successfully delivered* gifts.
# Each list item shows an overview:
#* Receiver name and avatar
#* Product type (ticket or marketplace item)
#* Product name
#* Date sent
#* Gift status
# User can tap on a gift to open full details.
# Gift detail view includes:
#* Receiver profile
#* Product summary
#* Sent date and time
#* Status (Accepted, Expired, or Returned)
#* Message (if included)
# User taps *Back* to return to the gift history list.

----

h3. 3. Field Definitions Table

||Field Name||Input Type / Display||Required||Notes||
|Receiver Name|Text|✅|Full name of the recipient|
|Receiver Avatar|Image|✅|Circular thumbnail|
|Product Name|Text|✅|Name of the gifted product|
|Product Type|Badge|✅|“Ticket” or “Product”|
|Sent Date|Date|✅|Displayed as YYYY-MM-DD|
|Sent Time|Time (optional)|✅|For chronological sorting or audit|
|Gift Status|Badge|✅|Accepted / Returned / Expired|

----

h3. 4. Data Display Table (Card View)

||Display Field||Format Example||Notes||
|Receiver Name|“Jane Nguyen”|Tapping name can open profile (optional)|
|Product Summary|“Digital Voucher - $25”|Name + product type|
|Date Sent|“2025-06-15”|Format: YYYY-MM-DD|
|Gift Status|“Accepted” (green badge)|Color-coded badge|

----

h3. 5. Acceptance Criteria

* *AC1*: Only gifts with completed status (delivered + accepted/expired/returned) are shown.
* *AC2*: Each record shows receiver, product name, type, date, and status.
* *AC3*: Tapping a record opens the gift detail view.
* *AC4*: Detail screen includes optional message and full gift metadata.
* *AC5*: List supports pagination and sorts by most recent sent date.

----

h3. 6. System Rules

* Only *successful gift transactions* are shown (exclude pending, failed, or in-process).
* Returned or expired gifts are still listed if they were originally sent successfully.
* Gift records are read-only and cannot be edited after sending.
* All product data is pulled from the gift snapshot at the time of send (immutable).

----

h3. 7. UX Optimizations

* Filter toggle: “Show: All | Accepted | Expired | Returned”
* Search bar for receiver name or product name
* Icons for product type (🎟️ = Ticket, 🛍️ = Product)
* “No gift history” empty state illustration and tip text

----",,Duyen Nguyen,,712020:59b66c6e-1068-441c-9436-130289e16a26,,,,,,,,,,,,,,,,,com.atlassian.servicedesk.plugins.approvals.internal.customfield.ApprovalsCFValue@21cb483e,,,,,,,,,{},,,,,,,,,,,,,,,,,,MOBILE,,,,0|i02rkf:,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,37260,AL-21,Account Settings,To Do,12/Jun/25 3:37 PM
[MB - Account Settings] Payment,AL-118,38265,Story,To Do,AL,AIOS Link,software,Kai Nguyễn,6214a4102d1a9d0069949c96,,Medium,,,,Duyen Nguyen,712020:59b66c6e-1068-441c-9436-130289e16a26,Duyen Nguyen,712020:59b66c6e-1068-441c-9436-130289e16a26,12/Jun/25 3:37 PM,12/Jun/25 3:37 PM,,,,0,,,Duyen Nguyen,,712020:59b66c6e-1068-441c-9436-130289e16a26,,,,,,,,,,,,,,,,,com.atlassian.servicedesk.plugins.approvals.internal.customfield.ApprovalsCFValue@351dbc8e,,,,,,,,,{},,,,,,,,,,,,,,,,,,MOBILE,,,,0|i02rk7:,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,37260,AL-21,Account Settings,To Do,12/Jun/25 3:37 PM
[MB - Account Settings] Change Subscription Plan,AL-117,38262,Story,To Do,AL,AIOS Link,software,Kai Nguyễn,6214a4102d1a9d0069949c96,,Medium,,,,Duyen Nguyen,712020:59b66c6e-1068-441c-9436-130289e16a26,Duyen Nguyen,712020:59b66c6e-1068-441c-9436-130289e16a26,12/Jun/25 3:36 PM,12/Jun/25 3:36 PM,,,,0,,,Duyen Nguyen,,712020:59b66c6e-1068-441c-9436-130289e16a26,,,,,,,,,,,,,,,,,com.atlassian.servicedesk.plugins.approvals.internal.customfield.ApprovalsCFValue@2ffee732,,,,,,,,,{},,,,,,,,,,,,,,,,,,MOBILE,,,,0|i02rjz:,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,37260,AL-21,Account Settings,To Do,12/Jun/25 3:36 PM
[MB - Account Settings] Delete Account,AL-116,38259,Story,Specified,AL,AIOS Link,software,Kai Nguyễn,6214a4102d1a9d0069949c96,,Medium,,,,Duyen Nguyen,712020:59b66c6e-1068-441c-9436-130289e16a26,Duyen Nguyen,712020:59b66c6e-1068-441c-9436-130289e16a26,12/Jun/25 3:36 PM,12/Jun/25 5:21 PM,,,,0,"h3. 1. Use Case Description

As a user on AIOS Link, I want the ability to permanently delete my account so that all my data, profile, and activity are removed from the platform in accordance with privacy regulations.

----

h3. 2. User Workflow (Step-by-Step)

# User navigates to *Settings > Account > Delete Account*.
# System displays a warning screen outlining consequences:
#* All profile data will be permanently deleted.
#* Any marketplace listings, orders, messages, connections, and meeting history will be erased or anonymized.
#* This action is *irreversible*.
# User taps *Continue*.
# System prompts for password re-entry or biometric confirmation.
# User confirms and submits.
# System processes deletion:
#* Logs the deletion event.
#* Signs the user out.
#* Displays confirmation message: _“Your account has been deleted successfully.”_

----

h3. 3. Field Definitions Table

||Field Name||Input Type||Required||Validation Rules||
|Confirmation Action|Button|✅|Tapping initiates permanent deletion|
|Password Input / Biometric|Password/Biometrics|✅|Must match session user|

----

h3. 4. Data Display Table

||Display Element||Format Example||Notes||
|Warning Message|“This action is permanent…”|Full-screen modal before final step|
|Confirmation Prompt|“Enter password to continue”|Ensures user intent|
|Deletion Success Toast|“Your account has been deleted.”|Final message after logout|

----

h3. 5. Acceptance Criteria

* *AC1*: Delete option must be accessible under Account Settings.
* *AC2*: System must require password or biometric confirmation.
* *AC3*: Deletion flow must clearly warn users of permanent data loss.
* *AC4*: System logs deletion event with timestamp and user ID.
* *AC5*: Upon deletion, user is logged out and no longer able to sign in.
* *AC6*: All user-related data is anonymized or removed according to platform retention policy.

----

h3. 6. System Rules

* User ID is marked as deleted in the backend immediately after confirmation.
* Active sessions are terminated across all devices.
* Associated data is either:
** Soft-deleted (for admin audit), or
** Permanently erased (if required by policy or region)
* An email confirmation is sent to the registered email (if email notifications are active).
* Users cannot reverse deletion once complete.

----

h3. 7. UX Optimizations

* Use red color and trash icon for delete CTA.
* Provide a “Why are you leaving?” optional dropdown (for analytics).
* Display loading spinner while deletion is processed.
* Offer alternative action: “Deactivate instead?” before confirmation.",,Duyen Nguyen,,712020:59b66c6e-1068-441c-9436-130289e16a26,,,,,,,,,,,,,,,,,com.atlassian.servicedesk.plugins.approvals.internal.customfield.ApprovalsCFValue@605f6a77,,,,,,,,,{},,,,,,,,,,,,,,,,,,MOBILE,,,,0|i02rjr:,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,37260,AL-21,Account Settings,To Do,12/Jun/25 3:36 PM
[MB - Account Settings] Notification Settings,AL-115,38256,Story,Specified,AL,AIOS Link,software,Kai Nguyễn,6214a4102d1a9d0069949c96,,Medium,,,,Duyen Nguyen,712020:59b66c6e-1068-441c-9436-130289e16a26,Duyen Nguyen,712020:59b66c6e-1068-441c-9436-130289e16a26,12/Jun/25 3:34 PM,12/Jun/25 4:54 PM,,,,0,"h3. 1. Use Case Description

As a user or merchant on AIOS Link, I want to configure which types of notifications I receive—by push or email—based on activity across key modules (e.g., marketplace, connections, messages, orders), so I can manage alerts and stay informed about what matters to me.

----

h3. 2. User Workflow (Step-by-Step)

# User opens *Settings > Notifications*.
# System displays grouped categories by module.
# Each trigger type includes separate toggles for:
#* *Push Notifications*
#* *Email Notifications*
# Changes are saved instantly.
# Notification types are grouped by:
#* Shared (Users + Merchants)
#* Merchant-specific (only visible to merchants)

----

h3. 3. Field Definitions Table (Shared: Users & Merchants)

||Module||Trigger Event||Push||Email||
|*Messages*|New chat thread opened|✅|✅|
|*Gifts*|Gift sent|✅|✅|
| |Gift accepted/rejected|✅|✅|
| |Gift expired (14 days unclaimed)|✅|✅|
|*Orders*|Order status updated (buyer's view)|✅|✅|
| |Order canceled (buyer's view)|✅|✅|
|*Connections*|New connection request received|✅|✅|
| |Connection request accepted|✅|✅|
|*Meetings*|Meeting invite received|✅|✅|
| |Invite accepted or rejected|✅|✅|
| |Meeting updated or canceled|✅|✅|
|*System*|Feature announcements|✅|✅|
| |Account or security alert (forced ON)|✅*|✅*|
|*Account*|Login from new device/location|✅|✅|
|*Subscription*|Renewal success|✅|✅|
| |Subscription canceled|✅|✅|
| |Renewal reminder (3 days before)|✅|✅|

{quote}✅* System-level security alerts cannot be turned off.{quote}

----

h3. 4. Field Definitions Table (Merchant-Specific Only)

||Module||Trigger Event||Push||Email||
|*Marketplace*|Product purchased|✅|✅|
| |Product listing removed by admin|✅|✅|
|*Orders*|New order received (seller view)|✅|✅|
| |Order canceled by buyer|✅|✅|
| |Order status changed by merchant (confirmation log)|✅|✅|
|*Storefront*|Buyer left a product review|✅|✅|

----

h3. 5. Acceptance Criteria

* *AC1*: All users see their relevant notification categories with push and email toggles.
* *AC2*: Merchants see an additional section with merchant-specific events.
* *AC3*: Changes are saved instantly and persist across sessions/devices.
* *AC4*: Muted notifications are not sent through suppressed channels.
* *AC5*: System-level alerts (security/account login) are always active.

----

h3. 6. System Rules

* Settings are stored in user profile metadata.
* Toggle OFF disables delivery from both system and third-party push/email providers.
* Email notifications queue through backend; push via Firebase/APNs.
* Merchants identified by role; system auto-loads extended config set.
* All users default to *all notifications ON* upon account creation.

----

h3. 7. UX Optimizations

* Expand/collapse groups by module
* Sticky headers for section titles
* Subtext under each toggle (e.g., “Receive email when someone sends you a gift”)
* “Mute all” option with inline confirm dialog
* “Reset to default” button at the bottom",,Duyen Nguyen,,712020:59b66c6e-1068-441c-9436-130289e16a26,,,,,,,,,,,,,,,,,com.atlassian.servicedesk.plugins.approvals.internal.customfield.ApprovalsCFValue@1bae54fa,,,,,,,,,{},,,,,,,,,,,,,,,,,,MOBILE,,,,0|i02rjj:,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,37260,AL-21,Account Settings,To Do,12/Jun/25 3:34 PM
[MB - Account Settings] Set Visibility,AL-114,38253,Story,Specified,AL,AIOS Link,software,Kai Nguyễn,6214a4102d1a9d0069949c96,,Medium,,,,Duyen Nguyen,712020:59b66c6e-1068-441c-9436-130289e16a26,Duyen Nguyen,712020:59b66c6e-1068-441c-9436-130289e16a26,12/Jun/25 3:34 PM,12/Jun/25 3:48 PM,,,,0,"

h3. 1. Use Case Description

As a user on AIOS Link, I want to toggle my visibility on or off so that I can control whether my profile appears on the map view when other users perform a contact search.

----

h3. 2. User Workflow (Step-by-Step)

# User navigates to *Settings > Privacy > Visibility*.
# System displays a single toggle option:
#* *“Show me on map when others search for contacts nearby”*
# User enables or disables the toggle.
# System saves the setting immediately.
# A confirmation toast is shown:
#* If enabled: _“You are now visible on the map.”_
#* If disabled: _“Your location is hidden from the map.”_

----

h3. 3. Field Definitions Table

||Field Name||Input Type||Required||Validation Rules||
|Map Visibility|Toggle|✅|On = visible on map; Off = hidden|

----

h3. 4. Data Display Table

||Display Field||Format Example||Notes||
|Visibility Toggle|[On/Off] Switch|Current setting state|
|Description|“Your profile will appear on the contact map when enabled.”|Below toggle|

----

h3. 5. Acceptance Criteria

* *AC1*: Visibility toggle appears in Privacy settings.
* *AC2*: Toggle is enabled by default for new users.
* *AC3*: Toggling OFF removes the user from map-based contact search results.
* *AC4*: Toggling ON includes user in real-time map view (based on location).
* *AC5*: Toast confirms setting update immediately upon toggle.

----

h3. 6. System Rules

* Visibility setting is stored in user profile metadata.
* When OFF, the system excludes the user’s geo-coordinates from any map results.
* Toggle state is respected in real-time by the map-based contact search feature.
* This toggle does not affect profile visibility in non-map views (e.g., direct search).

----

h3. 7. UX Optimizations

* Show location pin icon next to the toggle label.
* Tooltip or “i” icon explains: “When enabled, your location will appear on the map to other users.”
* Disable location sync if visibility is OFF to conserve background updates.",,Duyen Nguyen,,712020:59b66c6e-1068-441c-9436-130289e16a26,,,,,,,,,,,,,,,,,com.atlassian.servicedesk.plugins.approvals.internal.customfield.ApprovalsCFValue@438f62cd,,,,,,,,,{},,,,,,,,,,,,,,,,,,MOBILE,,,,0|i02rjb:,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,37260,AL-21,Account Settings,To Do,12/Jun/25 3:34 PM
[MB - Inventory] Boost Product,AL-113,38250,Story,To Do,AL,AIOS Link,software,Kai Nguyễn,6214a4102d1a9d0069949c96,,Medium,,,,Duyen Nguyen,712020:59b66c6e-1068-441c-9436-130289e16a26,Duyen Nguyen,712020:59b66c6e-1068-441c-9436-130289e16a26,12/Jun/25 3:25 PM,12/Jun/25 3:25 PM,23/Jun/25 10:40 PM,,,0,,,Duyen Nguyen,,712020:59b66c6e-1068-441c-9436-130289e16a26,,,,,,,,,,,,,,,,,com.atlassian.servicedesk.plugins.approvals.internal.customfield.ApprovalsCFValue@1b90caf9,,,,,,,,,{},,,,,,,,,,,,,,,,,,MOBILE,,,,0|i02rj3:,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,38088,AL-89,Inventory,To Do,12/Jun/25 3:25 PM
[MB - User Profile] Change Business Details,AL-112,38247,Story,In Progress,AL,AIOS Link,software,Kai Nguyễn,6214a4102d1a9d0069949c96,,Medium,,,,Duyen Nguyen,712020:59b66c6e-1068-441c-9436-130289e16a26,Duyen Nguyen,712020:59b66c6e-1068-441c-9436-130289e16a26,12/Jun/25 2:47 PM,25/Jun/25 10:24 AM,,,,0,"h3. 1. Use Case Description

As a merchant, I want to update my business profile so that I can keep my organizational identity and regulatory details up to date.

----

h3. 2. User Workflow (Step-by-Step)

# Merchant logs into the AIOS Link mobile app.
# Navigates to *Profile > Business Settings*.
# Taps *Edit Organization Details*.
# System loads a pre-filled form with the current business profile.
# Merchant edits one or more fields (e.g., name, address).
# Taps *Save Changes*.
# System validates the fields and updates the record if successful.
# A toast appears: _“Business details updated successfully.”_

----

h3. 3. Field Definitions Table

||Field Name||Field Type||Validation Rule||Required||Description||
|Business Name|Text|1–100 characters, unique|✅|Legal name of the merchant’s business|
|Business Address|Text Area|Max 200 characters|✅|Physical or registered mailing address|
|EIN/DUNS Number|Text|Alphanumeric, must be unique|✅|Tax or legal registration identifier|
|Date of Establishment|Date Picker|Past or current date|✅|When the business was founded|

----

h3. 4. Data Display Table (Confirmation View)

||Field||Example||Notes||
|Name|“Sunrise Coffee Ltd.”|Business display name|
|EIN/DUNS|“92-8471923”|Visible in account view|
|Last Updated|“2025-06-14 09:35”|System timestamp of last update|

----

h3. 5. Acceptance Criteria

* *AC1*: Merchant can access and update only organization-level fields.
* *AC2*: EIN/DUNS must be unique across the system.
* *AC3*: System must show inline errors for invalid inputs.
* *AC4*: Save button remains disabled until all required fields are valid.
* *AC5*: Upon success, updated values reflect immediately in profile.
* *AC6*: Change history is logged with user ID and timestamp.

----

h3. 6. System Rules

* Organization owner cannot be changed via mobile.
* EIN/DUNS must remain unique system-wide.
* Edit access is limited to users with merchant role.
* Change triggers a log entry in system audit history.

----

h3. 7. UX Optimizations

* Pre-filled form with keyboard type matched per field (e.g., numeric for EIN).
* Real-time validation and field-specific error messages
* Sticky “Save Changes” button with loading spinner
* Success toast with optional return-to-dashboard prompt

----",,Duyen Nguyen,,712020:59b66c6e-1068-441c-9436-130289e16a26,,,,,,,,,,,,,,,,,com.atlassian.servicedesk.plugins.approvals.internal.customfield.ApprovalsCFValue@7172e,,,,,,,,,{},,,,,,,,,,,,,,,,,,MOBILE,,,,0|i02riv:,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,37474,AL-58,User Profile,In Progress,25/Jun/25 10:24 AM
[MB - User Profile] Change Phone Number,AL-111,38244,Story,In Progress,AL,AIOS Link,software,Kai Nguyễn,6214a4102d1a9d0069949c96,,Medium,,,,Duyen Nguyen,712020:59b66c6e-1068-441c-9436-130289e16a26,Duyen Nguyen,712020:59b66c6e-1068-441c-9436-130289e16a26,12/Jun/25 2:46 PM,25/Jun/25 10:24 AM,,,,0,"h3. 1. Use Case Description

As a user, I want to change my registered phone number, so that I can keep my contact information current while ensuring account security through OTP verification.

----

h3. 2. User Workflow (Step-by-Step)

# User navigates to *Profile > Profile Settings > Change Phone Number*.
# User enters a new phone number.
# User taps *Submit*.
# System sends a *6-digit OTP* via SMS to the new phone number (valid for 30 minutes).
# User enters the OTP on the verification screen.
# If the OTP is valid → phone number is updated and user is shown a success message.
# If OTP is invalid or expired → show error and do not change the phone number.

----

h3. 3. Field Definitions Table

h4. 3.1 Submission Fields

||Field Name||Input Type||Required||Validation Rules||
|New Phone Number|Text|✅|Valid phone number format, must be unique|
|OTP Code|Numeric|✅|6-digit, expires in 30 minutes|

----

h3. 4. Data Display Table

||Display Field||Format Example||Notes||
|Phone Input|****** 123 4567|Auto-focus field when opened|
|OTP Input|6-digit numeric field|With countdown timer display|
|Status Toast|“Phone number updated successfully”|Shown upon successful update|

----

h3. 5. Acceptance Criteria

* *AC1*: If submitted number is invalid or already used, show: _“Please enter a valid, unused phone number.”_
* *AC2*: OTP must be a 6-digit code and expire in 30 minutes.
* *AC3*: If incorrect OTP entered, show error: _“Invalid or expired verification code.”_
* *AC4*: After 5 failed OTP attempts per day, block verification and show: _“You’ve reached the daily limit. Try again tomorrow.”_
* *AC5*: On successful OTP verification, system updates phone number and returns user to Account Info screen with confirmation toast.

----

h3. 6. System Rules

* Phone number is only updated after successful OTP confirmation.
* OTP is single-use, expires after 30 minutes, and is rate-limited to 5 attempts/day.
* Updated number must be unique across the platform.
* Verification activity is logged (user ID, phone, IP, timestamp).

----

h3. 7. UX Optimizations

* Auto-focus on phone input field
* Show input mask for region-specific number formats
* Display countdown timer for OTP expiration
* Show “Resend OTP” button (enabled after 60 seconds)
* Use inline validation and field-level messages",,Duyen Nguyen,,712020:59b66c6e-1068-441c-9436-130289e16a26,,,,,,,,,,,,,,,,,com.atlassian.servicedesk.plugins.approvals.internal.customfield.ApprovalsCFValue@7f45b53d,,,,,,,,,{},,,,,,,,,,,,,,,,,,MOBILE,,,,0|i02rin:,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,37474,AL-58,User Profile,In Progress,25/Jun/25 10:24 AM
[MB - User Profile] Change Email Address,AL-110,38241,Story,In Progress,AL,AIOS Link,software,Kai Nguyễn,6214a4102d1a9d0069949c96,,Medium,,,,Duyen Nguyen,712020:59b66c6e-1068-441c-9436-130289e16a26,Duyen Nguyen,712020:59b66c6e-1068-441c-9436-130289e16a26,12/Jun/25 2:46 PM,25/Jun/25 10:24 AM,,,,0,"h3. 1. Use Case Description

As a user, I want to change my registered email address, so that I can keep my contact information up to date while ensuring account security through OTP verification.

----

h3. 2. User Workflow (Step-by-Step)

# User navigates to *Profile > Profile Setting > Change Email*.
# User enters a new email address.
# User taps *Submit*.
# System sends a *6-digit OTP* to the new email address (valid for 30 minutes).
# User enters the OTP code on the verification screen.
# If the OTP is valid → email address is updated and user is shown a success message.
# If OTP is invalid or expired → show error and do not change the email.

----

h3. 3. Field Definitions Table

h4. 3.1 Submission Fields

||Field Name||Input Type||Required||Validation Rules||
|New Email|Text|✅|Valid email format, must be unique|
|OTP Code|Numeric|✅|6-digit, expires in 30 minutes|

----

h3. 4. Data Display Table

||Display Field||Format Example||Notes||
|Email Input|[<EMAIL>|mailto:<EMAIL>]|Auto-focus field when opened|
|OTP Input|6-digit numeric field|With countdown timer display|
|Status Toast|“Email updated successfully”|Shown upon successful update|

----

h3. 5. Acceptance Criteria

* *AC1*: If submitted email is invalid or already registered, show: _“Please enter a valid, unused email address.”_
* *AC2*: OTP must be a 6-digit code and expire in 30 minutes.
* *AC3*: If incorrect OTP entered, show error: _“Invalid or expired verification code.”_
* *AC4*: After 5 resend OTP attempts per day, block verification and show: _“You’ve reached the daily limit. Try again tomorrow.”_
* *AC5*: On successful OTP verification, system updates email and returns user to Account Info screen with confirmation toast.
* *AC6:* Email will not be changed until verification step is completed.

----

h3. 6. System Rules

* Email is only updated upon successful OTP confirmation.
* OTP is single-use, expires after 30 minutes, and is rate-limited to 5 attempts/day.
* Updated email must be unique across the platform.
* Verification activity is logged (user ID, IP, timestamp).

----

h3. 7. UX Optimizations

* Auto-focus on email input field
* Mask OTP input until entry complete
* Display countdown timer for OTP expiration
* Inline validation and field-level error messages
* Show “Resend OTP” button (enabled after 60 seconds)",,Duyen Nguyen,,712020:59b66c6e-1068-441c-9436-130289e16a26,,,,,,,,,,,,,,,,,com.atlassian.servicedesk.plugins.approvals.internal.customfield.ApprovalsCFValue@e2e27c,,,,,,,,,{},,,,,,,,,,,,,,,,,,MOBILE,,,,0|i02rif:,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,37474,AL-58,User Profile,In Progress,25/Jun/25 10:24 AM
[MB - Order] Order Product Review,AL-109,38227,Story,Specified,AL,AIOS Link,software,Kai Nguyễn,6214a4102d1a9d0069949c96,,Medium,,,,Duyen Nguyen,712020:59b66c6e-1068-441c-9436-130289e16a26,Duyen Nguyen,712020:59b66c6e-1068-441c-9436-130289e16a26,12/Jun/25 12:13 PM,12/Jun/25 12:20 PM,,,,0,"h3. 1. Use Case Description

As a user on the mobile platform, I want to review every product in an order once it is completed so that I can share feedback and help other shoppers.

----

h3. 2. User Workflow

# Buyer opens a *Completed* order from the *Order History* screen.
# Taps *Review Products* if no review has been submitted yet.
# System opens a scrollable list of products in the order.
# For each product, buyer selects:
#* Star rating (1–5)
#* Optional written comment (0–300 chars)
# Buyer taps *Submit Reviews*.
# System validates ratings, saves reviews, and displays confirmation toast.
# Buyer is redirected back to the *Order Details* screen, now showing submitted reviews as read-only.

----

h3. 3. Field Definitions

h4. Input Fields

||Field Name||Type||Required||Validation||Description||
|Star Rating|1–5 Stars|✅|Integer 1–5|Satisfaction score per product|
|Comment|Text Area|❌|0–300 characters|Optional written feedback|

h4. UI Components

||Element||Type||Condition||Description||
|Product Image|Image|Always visible|Square thumbnail|
|Product Name|Text|Always visible|Displayed above rating controls|
|Submit Reviews Button|Button|Enabled only if all products rated|Saves all entries|
|Back to Order Link|Text|Always visible|Returns to Order Details without save|

----

h3. 4. Data Display

||Field||Type||Format||Description||
|Existing Rating|Number|X.X / 5|Updated after submission|
|Confirmation Toast|Toast|Text|“Thank you for submitting your review.”|
|Inline Char Count|Label|“123/300”|Below comment box|

----

h3. 5. Acceptance Criteria

* *AC1*: “Review Products” is shown only when order status = {{Completed}} and no review submitted.
* *AC2*: Each product must receive a star rating before submission is enabled.
* *AC3*: Comments are optional but validated for character limits.
* *AC4*: Ratings are stored per product per order per buyer.
* *AC5*: Product ratings update system-wide average rating immediately.
* *AC6*: Once submitted, reviews are locked and shown as read-only.

----

h3. 6. System Rules

* Each product in an order can only be reviewed once per buyer.
* Reviews are stored with product ID, buyer ID, order ID, timestamp, rating, and comment.
* Average product rating is recalculated upon submission.
* Submitted reviews appear in both the order detail and public product view (if enabled).

----

h3. 7. UX Optimizations

* Auto-scroll to first unrated product if Submit is tapped prematurely.
* Show dynamic progress indicator (e.g., “2 of 3 rated”).
* Animate toast after successful submission.
* Prevent accidental resubmission or mid-scroll reset.

----",,Duyen Nguyen,,712020:59b66c6e-1068-441c-9436-130289e16a26,,,,,,,,,,,,,,,,,com.atlassian.servicedesk.plugins.approvals.internal.customfield.ApprovalsCFValue@7aa13825,,,,,,,,,{},,,,,,,,,,,,,,,,,,MOBILE,,,,0|i02rgv:,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,37950,AL-86,Order,To Do,12/Jun/25 12:13 PM
[MB - Order] Order List,AL-108,38224,Story,Specified,AL,AIOS Link,software,Kai Nguyễn,6214a4102d1a9d0069949c96,,Medium,,,,Duyen Nguyen,712020:59b66c6e-1068-441c-9436-130289e16a26,Duyen Nguyen,712020:59b66c6e-1068-441c-9436-130289e16a26,12/Jun/25 12:13 PM,12/Jun/25 12:18 PM,,,,0,"h3. 1. Use Case Description

As a user on the mobile platform, I want to view a list of my previous orders so that I can track their status and review past purchases.

----

h3. 2. User Workflow

# Buyer navigates to *Marketplace* < *My Orders* tab.
# System loads a paginated list of the buyer's product orders sorted by date (latest first).
# User may:
#* Scroll to load more
#* Enter a search keyword
#* Tap the *Filter* icon to refine by order status or date
# User taps a row to view the full *Order Details* screen.

----

h3. 3. Search and Filter Rules

h4. A. Search Rules

* Case-insensitive, partial match on:
** *Order Number*
** *Product Name*

h4. B. Filter Options

||Filter Name||Type||Options||
|Order Status|Dropdown|{{New}}, {{Confirmed}}, {{In Delivery}}, {{Completed}}, {{Canceled}}|
|Order Date Range|Date Picker|Start Date & End Date|

----

h3. 4. Field Definitions

h4. Interaction Elements

||Element Name||Type||Description||
|Order Row|Tap Target|Opens full Order Details screen|
|Search Box|Text Input|Filters by Order Number or Product Name|
|Filter Button|Button|Opens filter modal|

h4. Display Fields (Order Summary Row)

||Field Name||Type||Format||Description||
|Order Number|Text|Plaintext|Unique reference ID|
|Order Date|Date|YYYY-MM-DD|Date the order was placed|
|Total Price (USD)|Amount|$XX.XX|Order total in USD (includes tax if set)|
|Status|Badge|Label|One of the defined order states|
|Item Count|Number|Integer|Total number of items in the order|

----

h3. 5. Acceptance Criteria

* *AC1*: List must display Order Number, Order Date, Total Price (USD), Status, and Item Count.
* *AC2*: Default sort is descending by Order Date.
* *AC3*: Search must match Order Number or Product Name.
* *AC4*: Filter modal must allow filtering by status and order date range.
* *AC5*: Tapping an order row must open the correct Order Details screen.

----

h3. 6. System Rules

* Results are fetched from the backend API in batches of 20 per request.
* Filters and search terms are submitted to the backend.
* Order totals include tax if seller has enabled {{tax_enabled = true}}.
* Cached results expire after 5 minutes of user inactivity.

----

h3. 7. UX Optimizations

* Show *skeleton loaders* while fetching data.
* Keep *search box focused* after user initiates search.
* *Retain filters* when returning from Order Details.
* Show empty state message: _“No orders found. Try adjusting your filters.”_",,Duyen Nguyen,,712020:59b66c6e-1068-441c-9436-130289e16a26,,,,,,,,,,,,,,,,,com.atlassian.servicedesk.plugins.approvals.internal.customfield.ApprovalsCFValue@445416dd,,,,,,,,,{},,,,,,,,,,,,,,,,,,MOBILE,,,,0|i02rgn:,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,37950,AL-86,Order,To Do,12/Jun/25 12:13 PM
[MB - Storefront] Sell Orders List,AL-107,38220,Story,Specified,AL,AIOS Link,software,Kai Nguyễn,6214a4102d1a9d0069949c96,,Medium,,,,Duyen Nguyen,712020:59b66c6e-1068-441c-9436-130289e16a26,Duyen Nguyen,712020:59b66c6e-1068-441c-9436-130289e16a26,12/Jun/25 10:26 AM,16/Jun/25 11:32 PM,,,,0,"h3. 1. Use Case Description

As a merchant user on the mobile platform, I want to view a list of orders placed for my marketplace products so that I can track their status and manage fulfillment.

----

h3. 2. User Workflow

# Merchant navigates to *My Storefront* > *Sell Orders*.
# System displays a paginated list of orders sorted by most recent first.
# Merchant can:
#* Enter a search keyword
#* Open filters and refine by status, product, or date
# Tapping a row opens the *Sell Order Detail* screen for that order.

----

h3. 3. Search and Filter Rules

h4. A. Search Rules

* Match against:
** *Order Number*
** *Customer Name*
** *Product Name*
* Matching is case-insensitive and supports partial text.

h4. B. Filter Rules

||Filter Name||Type||Options||
|Order Status|Dropdown|{{New}}, {{Processing}}, {{Delivering}}, {{Completed}}, {{Canceled}}|
|Order Date|Date Picker|Order dates|
|Product|Dropdown|List of the merchant's own marketplace products|

----

h3. 4. Field Definitions

h4. Interaction Elements

||Element||Type||Description||
|Search Input|Text Field|Always visible|
|Filter Button|Button|Opens the filter modal|
|Order Row Link|Tap Target|Opens Sell Order Details|

h4. Display Fields (List Row Format)

||Field Name||Data Type||Format||Description||
|Order Number|Text|Plaintext|Unique order identifier|
|Customer Name|Text|Plaintext|Name of buyer|
|Product Summary|Text|“[Product Name] +X more”|First product name and total items|
|Total Price (USD)|Currency|$XX.XX|Includes tax if seller-enabled|
|Status|Badge|Label|Current status of order|
|Order Date|Date|YYYY-MM-DD|Date when order was placed|

----

h3. 5. Acceptance Criteria

* *AC1*: Display order number, customer name, product summary, total price (USD), status, and order date.
* *AC2*: Search works across Order Number, Product Name, and Customer Name.
* *AC3*: Filter modal enables multi-field filtering as per defined options.
* *AC4*: Tapping a row opens corresponding Sell Order Details.
* *AC5*: Orders are loaded in pages of 20 with infinite scroll or pagination.

----

h3. 6. System Rules

* All prices displayed in *USD*, tax included if applicable per product.
* List is sorted by {{order_created_at DESC}}.
* Search and filter states persist when returning from the detail screen.
* Order data is refreshed every 30 seconds automatically.
* Canceled and completed orders are not editable and only viewable.

----

h3. 7. UX Optimizations

* Sticky search bar while scrolling through orders.
* Skeleton loader on scroll for seamless pagination.
* Highlight row on tap to show interactivity.
* Empty state message: _“No orders match your filters.”_",,Duyen Nguyen,,712020:59b66c6e-1068-441c-9436-130289e16a26,,,,,,,,,,,,,,,,,com.atlassian.servicedesk.plugins.approvals.internal.customfield.ApprovalsCFValue@2f2dc502,,,,,,,,,{},,,,,,,,,,,,,,,,,,MOBILE,,,,0|i02rg7:,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,37258,AL-19,Storefront,To Do,12/Jun/25 10:26 AM
[MB - Storefront] Seller view Sell Order Details,AL-106,38217,Story,Specified,AL,AIOS Link,software,Kai Nguyễn,6214a4102d1a9d0069949c96,,Medium,,,,Duyen Nguyen,712020:59b66c6e-1068-441c-9436-130289e16a26,Duyen Nguyen,712020:59b66c6e-1068-441c-9436-130289e16a26,12/Jun/25 10:26 AM,16/Jun/25 11:04 PM,,,,0,"h3. 1. Use Case Description

As a merchant user on the mobile platform, I want to view detailed information about a sell order so that I can fulfill it accurately or take further actions.

----

h3. 2. User Workflow

# Merchant opens the *My Storefront > Orders* tab.
# Taps on an order to view its *Sell Order Details*.
# System displays:
#* Order metadata (number, date, status)
#* Customer info (name, contact, delivery address)
#* List of purchased items with quantity and unit price
#* Final total in *USD* (includes tax if applicable)
# Merchant may:
#* Tap *Cancel Order* (if order is cancellable)
#* Tap *Contact Buyer* (opens in-app chat)
# Merchant taps *Back* to return to Sell Order List.

----

h3. 3. Field Definitions

h4. Interaction Elements

||Element Name||Type||Visibility Condition||Description||
|Cancel Order Button|Button|If order is not completed|Launches cancel flow|
|Contact Buyer|Button|Always|Opens chat or default communication app|
|Back Button|Button|Always|Returns to Sell Order List|

----

h3. 4. Data Display

h4. Summary Fields

||Field Name||Data Type||Display When Empty||Format||Description||
|Order Number|Text|""--""|Plaintext|Unique identifier|
|Customer Name|Text|""--""|Full name|Name of buyer|
|Shipping Address|Text|""--""|Multiline Text|Delivery destination|
|Phone Number|Integer|“--”| |Buyer’s mobile or contact number|
|Order Status|Badge|""--""|New / Processing / Delivering/ Completed / Cancelled|Current order state|
|Order Date|Date|""--""|YYYY-MM-DD|Date order was placed|
|Total Price (USD)|Amount|""--""|$XX.XX|Final amount including tax|

h4. Product Table

||Column Name||Data Type||Description||
|Product Name|Text|Name of the purchased product|
|Unit Price (USD)|Amount|Price per unit in USD (incl. tax if set)|
|Quantity|Number|Number of units ordered|

----

h3. 5. Acceptance Criteria

* *AC1*: Display all order details, customer info, and product breakdown in USD.
* *AC2*: Show Cancel Order button only for eligible statuses (e.g., Pending, Processing).
* *AC3*: Show Contact Buyer button with valid communication channel.
* *AC4*: Tap on Back returns to Sell Order List.
* *AC5*: Tax is reflected in Total Price if the product had {{tax_enabled = true}}.

----

h3. 6. System Rules

* Order details fetched live via API upon opening the screen.
* Cancellation and contact buttons trigger respective backend workflows.
* Tax applied per product level, not globally.
* Cancellable states include: {{new}}, {{processing}}. Completed or cancelled orders cannot be modified.
* Inventory and fulfillment status must stay synced with sell order actions.

----

h3. 7. UX Optimizations

* Sticky action buttons (Cancel, Contact) for thumb-friendly access.
* Skeleton loaders during data fetch.
* Display buyer contact method icon (chat or email) for visual clarity.
* Status badge color-coded (e.g., green for Completed, red for Cancelled).",,Duyen Nguyen,,712020:59b66c6e-1068-441c-9436-130289e16a26,,,,,,,,,,,,,,,,,com.atlassian.servicedesk.plugins.approvals.internal.customfield.ApprovalsCFValue@5c3eb0d2,,,,,,,,,"{pullrequest={dataType=pullrequest, state=MERGED, stateCount=1}, json={""cachedValue"":{""errors"":[],""summary"":{""pullrequest"":{""overall"":{""count"":1,""lastUpdated"":""2025-06-17T16:52:16.000+0700"",""stateCount"":1,""state"":""MERGED"",""dataType"":""pullrequest"",""open"":false},""byInstanceType"":{""GitHub"":{""count"":1,""name"":""GitHub""}}}}},""isStale"":true}}",,,,,,,,,,,,,,,,,,MOBILE,,,,0|i02rfz:,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,37258,AL-19,Storefront,To Do,12/Jun/25 10:26 AM
[MB - Storefront] Seller Cancel Sell Order,AL-105,38214,Story,Specified,AL,AIOS Link,software,Kai Nguyễn,6214a4102d1a9d0069949c96,,Medium,,,,Duyen Nguyen,712020:59b66c6e-1068-441c-9436-130289e16a26,Duyen Nguyen,712020:59b66c6e-1068-441c-9436-130289e16a26,12/Jun/25 10:25 AM,12/Jun/25 10:54 AM,,,,0,"h3. 1. Use Case Description

As a merchant on the AIOS Link mobile platform, I want the ability to cancel an active sell order—before it is completed—so that inventory, buyer communication, and payment flows are properly updated.

----

h3. 2. User Workflow

# User navigates to the *Sell Order Details* screen via their *My Storefront > Orders* tab.
# User taps the *Cancel Order* button.
# System opens a *confirmation modal* requiring a cancellation reason.
# User provides a reason and taps *Confirm Cancellation*.
# System performs the following:
#* Updates order status to {{Canceled}}
#* Reverses inventory deduction
#* Initiates refund if payment has been captured
#* Logs cancellation event
#* Sends *in-app notification* and *email* to the buyer
# User is returned to Sell Order Details screen with updated status.

----

h3. 3. Field Definitions

h4. Input Fields

||Field Name||Type||Required||Validation||Description||
|Cancellation Reason|Text Area|✅|5–200 characters|Required reason for canceling the order|

h4. Interaction Elements

||Element Name||Type||Condition||Description||
|Cancel Order Button|Button|Shown on Sell Order Details if not completed|Opens cancellation modal|
|Confirmation Modal|Dialog|On tapping Cancel Order|Requires cancellation reason and confirm/cancel|
|Confirm Button|Button|Enabled if reason valid|Executes cancellation logic|
|Back/Dismiss Button|Button|Always visible in modal|Closes modal without taking action|

----

h3. 4. Data Display

||Element||Format||Description||
|Confirmation Message|Text|“Are you sure you want to cancel this order?”|
|Success Toast|Text|“Order canceled successfully.”|
|Notification Title|Text|“Order Canceled”|
|Notification Body|Text|“Your order #[order_id] has been canceled by the seller.”|
|Email Subject|Text|“Your AIOS Link order has been canceled”|
|Email Body|Text|“Order #[order_id] has been canceled. Reason: [reason].”|

----

h3. 5. Acceptance Criteria

* *AC1*: Cancel Order button is visible for eligible orders (not yet completed or canceled).
* *AC2*: Modal enforces cancellation reason before enabling Confirm.
* *AC3*: Status updates to {{Canceled}} only after confirmation.
* *AC4*: Refund is processed immediately if payment was already captured.
* *AC5*: Buyer is notified via push and email.
* *AC6*: Seller is returned to the details view with status visibly updated.

----

h3. 6. System Rules

* Only orders with status {{Pending}} or {{Processing}} are eligible for cancellation.
* Refunds are processed through the original Nuvei payment method before notifications are sent.
* Cancellation event must be audit-logged with:
** Seller ID
** Order ID
** Timestamp
** Reason
* Inventory units for the product(s) are restored.
* After cancellation, product availability is updated accordingly.

----

h3. 7. UX Optimizations

* Autofocus into the cancellation reason text area.
* Disable Confirm button until valid reason is entered.
* Show spinner while cancellation processes.
* Toast on success: _“Order canceled”_
* Optional: pre-fill recent cancellation reasons (future enhancement).

----",,Duyen Nguyen,,712020:59b66c6e-1068-441c-9436-130289e16a26,,,,,,,,,,,,,,,,,com.atlassian.servicedesk.plugins.approvals.internal.customfield.ApprovalsCFValue@1ed35878,,,,,,,,,{},,,,,,,,,,,,,,,,,,MOBILE,,,,0|i02rfr:,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,37258,AL-19,Storefront,To Do,12/Jun/25 10:25 AM
[MB - Inventory] Remove Product from Marketplace,AL-104,38211,Story,Specified,AL,AIOS Link,software,Kai Nguyễn,6214a4102d1a9d0069949c96,,Medium,,,,Duyen Nguyen,712020:59b66c6e-1068-441c-9436-130289e16a26,Duyen Nguyen,712020:59b66c6e-1068-441c-9436-130289e16a26,12/Jun/25 10:18 AM,12/Jun/25 3:24 PM,,,,0,"h3. 1. Use Case Description

As a merchant using AIOS Link, I want to remove one of my products from the public marketplace so that it is no longer available for new purchases, while preserving existing sell orders and inventory data.

----

h3. 2. User Workflow

# Merchant navigates to *My Storefront* or *Marketplace Product Details*.
# Taps the *Remove from Marketplace* button.
# System opens a *confirmation modal*, stating that the product will be delisted and removed from public visibility and carts, but active orders will remain unaffected.
# Merchant taps *Confirm Removal*.
# System:
#* Updates the product listing status to {{inactive}}
#* Removes the product from the marketplace view and carts
#* Displays a success toast
#* Returns the user to the previous screen

----

h3. 3. Field Definitions

||Element Name||Type||Condition||Description||
|Remove Button|Button|Shown in My Storefront or Product Detail|Opens confirmation modal|
|Confirmation Modal|Dialog|On Remove tap|Summarizes impact and asks for action|
|Confirm Removal|Button|Enabled in modal|Executes delisting|
|Cancel Removal|Button|Enabled in modal|Closes modal without action|

----

h3. 4. Data Display

||Field Name||Type||Display When Empty||Format||Description||
|Success Message|Toast|Hidden|“Product removed from marketplace.”| |
|Error Message|Text|Hidden|“Cannot remove product with active orders.”| |

----

h3. 5. Acceptance Criteria

* *AC1*: Remove option is accessible from both My Storefront and Product Details.
* *AC2*: Modal must inform that removal hides the product from buyers but preserves unfulfilled orders.
* *AC3*: After removal, product no longer appears in:
** Marketplace product list
** Buyer carts
** Seller’s public storefront
* *AC4*: A toast confirms successful removal.
* *AC5*: If product has any *unfulfilled orders*, system blocks removal with error message.

----

h3. 6. System Rules

* Product status is updated to {{inactive}}, but product record remains editable.
* Any cart instances with the product are deleted across users.
* Audit log entry is created with:
** Product ID
** Merchant ID
** Timestamp
** Action type: {{marketplace_removal}}
* Removal is only allowed if no pending or processing sell orders exist for that product.

----

h3. 7. UX Optimizations

* Prefill modal with product name for context.
* Display loading spinner inside modal on submission.
* Restore scroll position in My Storefront after removal.
* Optional: show “Inactive” badge in Inventory or Product List views.",,Duyen Nguyen,,712020:59b66c6e-1068-441c-9436-130289e16a26,,,,,,,,,,,,,,,,,com.atlassian.servicedesk.plugins.approvals.internal.customfield.ApprovalsCFValue@7a555781,,,,,,,,,{},,,,,,,,,,,,,,,,,,MOBILE,,,,0|i02rfj:,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,38088,AL-89,Inventory,To Do,12/Jun/25 10:18 AM
[MB - Marketplace] Order Details,AL-103,38207,Story,Specified,AL,AIOS Link,software,Kai Nguyễn,6214a4102d1a9d0069949c96,,Medium,,,,Duyen Nguyen,712020:59b66c6e-1068-441c-9436-130289e16a26,Duyen Nguyen,712020:59b66c6e-1068-441c-9436-130289e16a26,12/Jun/25 9:45 AM,12/Jun/25 12:13 PM,,,,0,"h3. 1. Use Case Description

As a buyer on AIOS Link, I want to view full details of my marketplace product orders so that I can confirm item details, pricing, and delivery status, and manage cancellations when permitted.

----

h3. 2. User Workflow

# Buyer navigates to *My Orders* > Marketplace tab.
# Taps on a specific order to open the *Order Details* screen.
# Screen displays:
#* Order Number
#* Order Date
#* Total Price (USD)
#* Order Status
#* Shipping Address
#* Merchant Info
#* Product list: name, quantity, unit price, subtotal
# If order status is {{New}} or {{Confirmed}}, buyer sees *Cancel Order* button.
# Buyer can:
#* Tap *Cancel Order* (if eligible)
#* Tap *Back* to return to order list view

----

h3. 3. Field Definitions

||Field Name||Type||Format||Description||
|Order Number|Text|Alphanumeric|Unique order reference|
|Order Date|Date|YYYY-MM-DD|Purchase date|
|Order Status|Badge|Enum|One of: {{New}}, {{Confirmed}}, {{In Delivery}}, {{Completed}}, {{Canceled}}|
|Total Price (USD)|Currency|$XX.XX|Final price incl. tax if applicable|
|Shipping Address|Text|Multiline|Buyer's delivery address|
|Merchant Name|Text|Plaintext|Seller's name|
|Product Name|Text|Plaintext|Title of item ordered|
|Quantity|Number|Integer|Units ordered|
|Unit Price (USD)|Currency|$X.XX|Price per unit (USD)|
|Subtotal (USD)|Currency|$XX.XX|Quantity × Unit Price|

----

h3. 4. Interaction Elements

||Element Name||Type||Condition||Description||
|Cancel Order Button|Button|Shown only if status = {{New}} or {{Confirmed}}|Allows buyer to cancel order|
|Back Button|Button|Always visible|Returns to My Orders view|

----

h3. 5. Acceptance Criteria

* *AC1*: Buyers see order summary with all fields outlined above.
* *AC2*: Product list displays quantity, unit price, and subtotal for each item.
* *AC3*: Total reflects all item subtotals and tax (if applied by seller).
* *AC4*: Cancel Order button is only shown for {{New}} or {{Confirmed}} statuses.
* *AC5*: Tapping cancel triggers Buyer Cancel Order workflow.
* *AC6*: Back button returns buyer to their previous scroll state.

----

h3. 6. System Rules

* All pricing is displayed in *USD*.
* Order data is retrieved live upon screen load.
* Cancellation is only allowed if order status is not yet in fulfillment or delivery.
* Product-level tax is included only if {{tax_enabled = true}} by the merchant.
* Once canceled, order remains visible but read-only with {{Canceled}} status badge.

----

h3. 7. UX Optimizations

* Use two sections: “Order Summary” and “Products”.
* Color-coded status badge (e.g., green for Completed, red for Canceled).
* Sticky “Cancel Order” action button for eligible orders.
* Show skeleton loaders while data is fetching.
* Confirmation toast after successful cancelation or status update.",,Duyen Nguyen,,712020:59b66c6e-1068-441c-9436-130289e16a26,,,,,,,,,,,,,,,,,com.atlassian.servicedesk.plugins.approvals.internal.customfield.ApprovalsCFValue@3b11981c,,,,,,,,,{},,,,,,,,,,,,,,,,,,MOBILE,,,,0|i02rf3:,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,37950,AL-86,Order,To Do,12/Jun/25 9:45 AM
[MB - Marketplace] Product List,AL-102,38204,Story,Specified,AL,AIOS Link,software,Kai Nguyễn,6214a4102d1a9d0069949c96,,Medium,,,,Duyen Nguyen,712020:59b66c6e-1068-441c-9436-130289e16a26,Duyen Nguyen,712020:59b66c6e-1068-441c-9436-130289e16a26,12/Jun/25 9:44 AM,12/Jun/25 10:56 AM,,,,0,"h3. 1. Use Case Description

As a user on the mobile platform, I want to browse marketplace products with search and filter options so that I can quickly find items to purchase.

----

h3. 2. User Workflow

# User taps *Marketplace* from the bottom navigation bar.
# System loads a scrollable list of available products with:
#* Search input
#* Filter button
# User:
#* Enters a *search keyword* to refine results
#* Or taps the *Filter* button to configure search filters
# Product list updates dynamically based on inputs.
# User scrolls to explore listings and taps any *Product Card* to open *Product Details*.

----

h3. 3. Search and Filter Logic

h4. A. Search Rules

||Rule||Description||
|Fields searched|Product Name, Category, Merchant Name|
|Match behavior|Case-insensitive, partial match|

h4. B. Filter Options

||Filter Name||Type||Options||Description||
|Category|Multi-select|Based on available categories|Filter by product type|
|Price Range (USD)|Min/Max Number|Currency: USD|Filter by USD selling price|
|Merchant Rating|Dropdown|5★, 4★+, 3★+|Show products from well-rated sellers|
|In Stock Only|Toggle|On / Off|Exclude out-of-stock items|

----

h3. 4. Field Definitions

h4. Interaction Elements

||Element||Type||Description||
|Search Box|Text Input|Allows keyword search|
|Filter Button|Button|Opens filter modal|
|Product Card|Tap Target|Opens detailed view of the selected product|
|Load More Trigger|Infinite Scroll|Loads next batch of products|

----

h3. 5. Data Display (Product Card Format)

||Field Name||Data Type||Format||Notes||
|Thumbnail|Image|1:1 Aspect Ratio|Product image or placeholder|
|Product Name|Text|Plaintext|Max 2 lines, bolded|
|Price (USD)|Currency|$XX.XX|Includes tax if enabled by seller|
|Category|Text|Plaintext|Shown in tag or chip format|
|Merchant Name|Text|Plaintext|Linked to merchant profile|
|Rating|Badge|X.X / 5.0|Average rating with star icon|

----

h3. 6. Acceptance Criteria

* *AC1*: Product list displays cards with image, name, price (USD), category, merchant, and rating.
* *AC2*: Search works across Product Name, Category, and Merchant Name.
* *AC3*: Filter panel includes Category, Price (USD), Rating, and In Stock toggle.
* *AC4*: Infinite scroll loads 20 products per batch.
* *AC5*: Tapping a card opens full *Marketplace Product Detail*.
* *AC6*: All prices shown in *USD*, tax included if {{tax_enabled = true}} by the merchant.

----

h3. 7. System Rules

* Listings are sorted by relevance to search first, then by latest publish date.
* Filters persist until reset or cleared by the user.
* List updates in real-time on search input or filter change.
* Listings with quantity = 0 are hidden if “In Stock Only” is toggled ON.

----

h3. 8. UX Optimizations

* Sticky search bar remains visible during scroll.
* Skeleton loaders show while loading product batches.
* “No Results” message appears when no listings match the search/filter.
* Swipe-down gesture refreshes product list.
* Display “Only X left” if stock is critically low (≤5 units).",,Duyen Nguyen,,712020:59b66c6e-1068-441c-9436-130289e16a26,,,,,,,,,,,,,,,,,com.atlassian.servicedesk.plugins.approvals.internal.customfield.ApprovalsCFValue@1200178c,,,,,,,,,{},,,,,,,,,,,,,,,,,,MOBILE,,,,0|i02rev:,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,37259,AL-20,Marketplace,To Do,12/Jun/25 9:44 AM
[MB - Marketplace] Product Details,AL-101,38201,Story,Specified,AL,AIOS Link,software,Kai Nguyễn,6214a4102d1a9d0069949c96,,Medium,,,,Duyen Nguyen,712020:59b66c6e-1068-441c-9436-130289e16a26,Duyen Nguyen,712020:59b66c6e-1068-441c-9436-130289e16a26,12/Jun/25 9:44 AM,17/Jun/25 12:13 AM,,,,0,"h3. 1. Use Case Description

As a user on the mobile platform, I want to view detailed product information with a buy option so that I can decide whether to purchase the item.

----

h3. 2. User Workflow

# User browses the *Marketplace Product List* and taps a product.
# System navigates to the *Product Detail* screen.
# Screen displays:
#* Product images (carousel)
#* Name, description, price (in USD), quantity selector
#* Merchant name (linked to Storefront)
#* Stock availability, rating (if any)
# User selects quantity
# User may:
#* Tap *Add to Cart* → Item added to cart with confirmation toast
#* Tap *Buy Now* → Navigated to prefilled *Checkout* screen

----

h3. 3. Field Definitions

h4. Input Fields

||Field Name||Type||Required||Validation||Description||
|Quantity|Stepper|✅|Integer (1–9999)|Number of units to purchase|

h4. Interaction Elements

||Element Name||Type||Condition||Description||
|Add to Cart Button|Button|Enabled if Quantity > 0 and stock available|Adds item(s) to cart|
|Buy Now Button|Button|Enabled if Quantity > 0 and stock available|Goes directly to Checkout|
|Image Carousel|Carousel|Always|Swipe through product images|
|Merchant Store Link|Link|Always|Opens merchant's storefront|

----

h3. 4. Data Display Table

||Field Name||Data Type||Display When Empty||Format||Description||
|Product Name|Text|""--""|Plaintext|Title of the product|
|Price (USD)|Currency|""--""|$XX.XX|Final unit price (USD), tax included if set|
|Description|Text|""--""|Multiline text|Full product description|
|Stock Quantity|Badge|""--""|Number/Out of Stock|Availability tag|
|Merchant Name|Text Link|""--""|Plaintext|Merchant name, links to their Storefront|
|Rating|Number|""-""|X.X / 5.0|Average customer rating|

----

h3. 5. Acceptance Criteria

* *AC1*: Product Details screen must show all core info: image, name, description, price (USD), merchant name, stock status, rating.
* *AC2*: Quantity stepper is limited between 1–99.
* *AC3*: Add to Cart stores item in cart and shows toast: _“Item added to cart”_.
* *AC4*: Buy Now pre-fills checkout with product, quantity, and price.
* *AC5*: Buttons are disabled if stock = 0.
* *AC6*: If merchant has enabled tax, price shown includes tax amount.

----

h3. 6. System Rules

* All prices displayed in *USD*.
* Final price includes *merchant-defined tax* if {{tax_enabled = true}}.
* Button visibility is governed by real-time stock status from backend.
* Product rating is calculated from verified buyer reviews only.
* System logs view activity for personalization and analytics.

----

h3. 7. UX Optimizations

* Display “Only X left” if stock ≤ 5.
* Show related products section under description.
* Image carousel auto-zooms on tap with swipe navigation.
* Include “Verified Seller” badge next to merchant name (if applicable).
* Animate confirmation toast on Add to Cart action.",,Duyen Nguyen,,712020:59b66c6e-1068-441c-9436-130289e16a26,,,,,,,,,,,,,,,,,com.atlassian.servicedesk.plugins.approvals.internal.customfield.ApprovalsCFValue@16149d2c,,,,,,,,,{},,,,,,,,,,,,,,,,,,MOBILE,,,,0|i02ren:,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,37259,AL-20,Marketplace,To Do,12/Jun/25 9:44 AM
[MB - Marketplace] Buyer Cancel Order,AL-100,38198,Story,Specified,AL,AIOS Link,software,Kai Nguyễn,6214a4102d1a9d0069949c96,,Medium,,,,Duyen Nguyen,712020:59b66c6e-1068-441c-9436-130289e16a26,Duyen Nguyen,712020:59b66c6e-1068-441c-9436-130289e16a26,12/Jun/25 9:38 AM,12/Jun/25 12:13 PM,,,,0,"h3. 1. Use Case Description

As a buyer on AIOS Link, I want the ability to cancel my order from the mobile app, only if the order is still in an eligible status (e.g., not shipped or completed), so I can manage accidental purchases or changes in intent.

----

h3. 2. User Workflow

# Buyer navigates to *My Orders*.
# Taps on an order to open *Order Detail*.
# If the order status is eligible for cancellation ({{New}} or {{Confirmed}}), a *Cancel Order* button is visible.
# Buyer taps *Cancel Order*.
# A confirmation modal appears:
_“Are you sure you want to cancel this order? This action cannot be undone.”_
# Buyer confirms cancellation.
# System updates the order status to {{Canceled}}, restores inventory, and sends notifications:
#* To the merchant: _“Order #[order_id] has been canceled by the buyer.”_
#* To the buyer: _“You have canceled order #[order_id].”_

----

h3. 3. Field Definitions

||Field Name||Type||Required||Validation||Description||
|Cancel Order Button|Button|Yes|Visible if status = {{New}} or {{Confirmed}}|Initiates cancellation|
|Confirmation Modal|Dialog|Yes|Requires confirmation|Prevents accidental cancellations|

----

h3. 4. Data Display

||Display Element||Type||Description||
|Order Status Badge|Badge|Changes from {{New/Confirmed}} → {{Canceled}}|
|Cancel Success Toast|Toast|“Order canceled successfully.”|
|Notifications|System|Sent to both buyer and seller|

----

h3. 5. Acceptance Criteria

* *AC1*: Cancel button is visible only for orders with status {{New}} or {{Confirmed}}.
* *AC2*: Confirming cancellation updates order status to {{Canceled}}.
* *AC3*: Inventory is restored to merchant stock.
* *AC4*: System notifies both parties (merchant and buyer).
* *AC5*: Order details remain viewable post-cancellation but are marked as {{Canceled}}.
* *AC6*: Refund logic (if payment captured) is processed via Nuvei.

----

h3. 6. System Rules

* Cancellation allowed only if order status = {{New}} or {{Confirmed}}.
* If the order is already {{In Delivery}}, {{Completed}}, or {{Canceled}}, the Cancel button is hidden and action blocked.
* Cancel action is irreversible.
* Logs stored with timestamp, buyer ID, order ID, and cancellation method.
* Tax and fees are refunded along with product cost (if applicable).

----

h3. 7. UX Optimizations

* Display “Eligible for cancellation until [date/time]” for time-sensitive orders.
* Use red text/button for destructive actions.
* Confirmation dialog includes order number and product name for clarity.
* Auto-refresh status in Order List view after cancellation.

----",,Duyen Nguyen,,712020:59b66c6e-1068-441c-9436-130289e16a26,,,,,,,,,,,,,,,,,com.atlassian.servicedesk.plugins.approvals.internal.customfield.ApprovalsCFValue@69678a50,,,,,,,,,{},,,,,,,,,,,,,,,,,,MOBILE,,,,0|i02ref:,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,37950,AL-86,Order,To Do,12/Jun/25 9:38 AM
[MB - Marketplace] Checkout,AL-99,38195,Story,Specified,AL,AIOS Link,software,Kai Nguyễn,6214a4102d1a9d0069949c96,,Medium,,,,Duyen Nguyen,712020:59b66c6e-1068-441c-9436-130289e16a26,Duyen Nguyen,712020:59b66c6e-1068-441c-9436-130289e16a26,12/Jun/25 9:38 AM,12/Jun/25 10:54 AM,,,,0,"h3. 1. Use Case Description

As a user on the mobile platform, I want to finalize my purchase by reviewing cart items, entering delivery details, and submitting payment so that I can complete the order. When my cart contains products from multiple merchants, the checkout should create separate orders for each merchant with their own delivery information.

----

h3. 2. User Workflow

# User taps *Checkout* from the Cart screen.
# If items come from multiple merchants, system groups them by merchant and shows a separate delivery section for each order.
#* Each section includes item list, price in USD, any tax if applicable, and shipping.
# User enters or confirms:
#* Full Name
#* Phone Number
#* Delivery Address
#* Payment Method (via Nuvei)
# User reviews total amounts (item + tax + shipping).
# Taps *Place Order*.
# System processes payment and confirms with unique order IDs per merchant.

----

h3. 3. Field Definitions

h4. Input Fields

||Field Name||Type||Validation Rule||Mandatory||Description||
|Full Name|Text|1–50 characters|Yes|Recipient’s full name|
|Phone Number|Phone|Valid mobile format|Yes|Contact number|
|Delivery Address|Text Area|5–200 characters|Yes|Shipping address|
|Payment Method|Dropdown|Nuvei required|Yes|Selected payment method|
|Notes|Text Area|0–200 characters|No|Optional delivery note|

----

h3. 4. Data Display (Summary)

||Data Name||Type||Display When Empty||Format||Description||
|Order Summary|Card|Hidden|Text|Item list with quantity and USD price|
|Subtotal|Amount|“--”|USD|Price × quantity|
|Tax|Amount|“$0.00” if not set|USD|% of subtotal (only if seller applies)|
|Shipping Fee|Amount|“--”|USD|Merchant-based shipping cost|
|Total Cost|Amount|“--”|USD|Subtotal + tax + shipping|
|Order Confirmation|Toast|Hidden|Text|Message shown upon success|

----

h3. 5. Acceptance Criteria

* *AC1*: All required fields must be valid to enable *Place Order*.
* *AC2*: Total includes seller-defined tax if {{tax_enabled = true}}.
* *AC3*: Nuvei handles payment; on success, unique order numbers are returned per merchant.
* *AC4*: “Edit Cart” link navigates back without clearing entered data.
* *AC5*: Split checkout by merchant, each requiring its own delivery data.
* *AC6*: Confirmation screen displays all created order numbers.

----

h3. 6. System Rules

* All monetary fields are stored and displayed in *USD* with two decimal precision.
* If seller has {{tax_enabled}}, apply {{tax_percentage}} to their item subtotal.
* Each seller group results in one order object.
* Shipping rates are fetched dynamically based on delivery location.
* Orders are created only after Nuvei payment is confirmed.

----

h3. 7. UX Optimizations

* Autofill saved delivery info if available.
* Real-time tax and total update as inputs change.
* Spinner during Nuvei processing.
* Push and email sent after successful order placement.
* Show “Tax included by [Merchant Name]” label if applicable.",,Duyen Nguyen,,712020:59b66c6e-1068-441c-9436-130289e16a26,,,,,,,,,,,,,,,,,com.atlassian.servicedesk.plugins.approvals.internal.customfield.ApprovalsCFValue@3f349238,,,,,,,,,{},,,,,,,,,,,,,,,,,,MOBILE,,,,0|i02re7:,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,37259,AL-20,Marketplace,To Do,12/Jun/25 9:38 AM
[MB - Marketplace] Cart Management,AL-98,38192,Story,Specified,AL,AIOS Link,software,Kai Nguyễn,6214a4102d1a9d0069949c96,,Medium,,,,Duyen Nguyen,712020:59b66c6e-1068-441c-9436-130289e16a26,Duyen Nguyen,712020:59b66c6e-1068-441c-9436-130289e16a26,12/Jun/25 9:38 AM,12/Jun/25 10:00 AM,,,,0,"h3. 1. Use Case Description

As a user on the mobile platform, I want to manage items in my cart so that I can adjust quantities or remove products before checking out.

h3. 2. User Workflow

* Step 1: User opens the *Cart* from the navigation menu.
* Step 2: System displays a list of cart items with quantity steppers, subtotal per item, and overall total.
* Step 3: User adjusts quantity using the stepper or taps *Remove* to delete an item.
* Step 4: System updates totals instantly and disables Checkout if the cart becomes empty.
* Step 5: User taps *Checkout* to proceed when finished editing.

h3. 3. Field Definitions

h4. Input Fields

||Field Name||Field Type||Validation Rule||Mandatory||Description||
|Quantity|Stepper|1–9999|Yes|Adjusts units per product|

h4. Interaction Elements

||Element Name||Type||Trigger Condition||Description||
|Remove Button|Icon Button|Always visible|Deletes item from cart|
|Checkout Button|Button|Enabled when cart has items|Navigates to Checkout screen|

h3. 4. Data Display (Summary)

||Data Name||Data Type||Display When Empty||Format||Description||
|Product Name|Text|“–”|Plaintext|Name of product in cart|
|Price|Amount|“–”|USD|Unit price|
|Subtotal|Amount|“–”|USD|Quantity × price|
|Cart Total|Amount|“0”|USD|Sum of all subtotals|

h3. 5. Acceptance Criteria

* AC1: Cart shows each item with Quantity stepper, Price, and Subtotal.
* AC2: Changing quantity updates Subtotal and Cart Total immediately.
* AC3: Remove Button deletes the item and updates totals.
* AC4: Checkout Button is disabled when cart is empty.

h3. 6. System Rules

* Maximum quantity per item is limited by available stock.
* Cart data persists across sessions for logged-in users.
* Cart totals recalculate after each modification.

h3. 7. UX Optimizations

* Swipe left on an item to reveal the Remove action.
* Show micro-animation on total amount change.
* Provide “Continue Shopping” link when cart is empty.",,Duyen Nguyen,,712020:59b66c6e-1068-441c-9436-130289e16a26,,,,,,,,,,,,,,,,,com.atlassian.servicedesk.plugins.approvals.internal.customfield.ApprovalsCFValue@babf048,,,,,,,,,{},,,,,,,,,,,,,,,,,,MOBILE,,,,0|i02rdz:,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,37259,AL-20,Marketplace,To Do,12/Jun/25 9:38 AM
[MB - Marketplace] Add to Cart,AL-97,38190,Story,Specified,AL,AIOS Link,software,Kai Nguyễn,6214a4102d1a9d0069949c96,,Medium,,,,Duyen Nguyen,712020:59b66c6e-1068-441c-9436-130289e16a26,Duyen Nguyen,712020:59b66c6e-1068-441c-9436-130289e16a26,12/Jun/25 9:34 AM,12/Jun/25 9:52 AM,,,,0,"h3. 
1. Use Case Description

As a user on the mobile platform, I want to add products to my shopping cart so that I can review and purchase multiple items in one checkout.

h3. 2. User Workflow

* Step 1: User views a product on the *Product Details* screen.
* Step 2: User selects quantity and taps *Add to Cart*.
* Step 3: System confirms the action with a toast message and updates the cart badge in the navigation.
* Step 4: User may continue browsing or open the *Cart* from the navigation menu.

h3. 3. Field Definitions

h4. Input Fields

||Field Name||Field Type||Validation Rule||Mandatory||Description||
|Quantity|Stepper|1–9999|Yes|Number of units to add|

h4. Interaction Elements

||Element Name||Type||Trigger Condition||Description||
|Add to Cart Button|Button|Enabled when Quantity > 0|Adds selected quantity to cart|
|Cart Badge|Icon Badge|Visible when cart not empty|Shows total item count|

h3. 4. Data Display (Summary)

||Data Name||Data Type||Display When Empty||Format||Description||
|Confirmation Toast|Toast|Hidden|Plaintext|Appears after item added|
|Cart Item Count|Number|“0”|Numeric|Total items in cart|

h3. 5. Acceptance Criteria

* AC1: Add to Cart must respect the quantity stepper value.
* AC2: Confirmation toast appears after the item is added.
* AC3: Cart Badge updates with the new total item count.
* AC4: System prevents adding quantity exceeding available stock.

h3. 6. System Rules

* Cart items are stored locally and synced to the backend when the user is online.
* If the same product is added again, quantity increments rather than creating a new line item.
* Cart state persists across sessions for logged-in users.

h3. 7. UX Optimizations

* Shake animation on the cart badge when items are added.
* Provide undo action in the toast for 5 seconds.",,Duyen Nguyen,,712020:59b66c6e-1068-441c-9436-130289e16a26,,,,,,,,,,,,,,,,,com.atlassian.servicedesk.plugins.approvals.internal.customfield.ApprovalsCFValue@21062c72,,,,,,,,,{},,,,,,,,,,,,,,,,,,MOBILE,,,,0|i02rdr:,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,37259,AL-20,Marketplace,To Do,12/Jun/25 9:34 AM
[MB - Meeting Scheduling] Delete Meeting,AL-96,38155,Story,Specified,AL,AIOS Link,software,Kai Nguyễn,6214a4102d1a9d0069949c96,,Medium,,,,Duyen Nguyen,712020:59b66c6e-1068-441c-9436-130289e16a26,Duyen Nguyen,712020:59b66c6e-1068-441c-9436-130289e16a26,11/Jun/25 10:05 PM,15/Jun/25 11:01 PM,,,,0,"h3. 1. Use Case Description

As a user who initiated a meeting, I want to delete it so that it no longer appears in either user’s schedule, and the other party is clearly notified.

----

h3. 2. User Workflow (Step-by-Step)

# User opens a meeting they initiated from the *Schedule* view.
# User taps *Delete Meeting*.
# System shows a confirmation modal:
_“Deleting this meeting will cancel it for both parties. Continue?”_
# User confirms deletion.
# System removes the meeting from both users’ schedules.
# System sends a notification to the other party:
_“Your meeting with [User] has been canceled.”_

----

h3. 3. Field Definitions

h4. Interaction Elements

||Element Name||Type||Description||
|Delete Meeting|Button|Opens confirmation modal|
|Confirm Delete|Button|Confirms cancellation|
|Cancel Delete|Button|Dismisses without action|

----

h3. 4. Data Display

||Data Field||Data Type||Description||
|Deletion Notice|Notification|Sent to recipient|
|Success Toast|Toast|“Meeting deleted” confirmation to sender|

----

h3. 5. Acceptance Criteria

* *AC1*: Only the sender can delete the meeting.
* *AC2*: Confirmation modal appears before deletion.
* *AC3*: After deletion, meeting is removed from both schedules.
* *AC4*: Recipient is notified about the cancellation.
* *AC5*: Deleted meetings are removed from the Schedule screen immediately.

----

h3. 6. System Rules

* Deleted meetings are hard-removed from both users' schedules.
* Deletion is logged with user ID and timestamp.
* Notifications are pushed and stored in the recipient’s notification center.

----

h3. 7. UX Optimizations

* Use red button for destructive action.
* Show recipient’s name in confirmation modal.
* Optionally allow deletion reason (future enhancement).
* Scroll to top of Schedule after deletion confirmation.",,Duyen Nguyen,,712020:59b66c6e-1068-441c-9436-130289e16a26,,,,,,,,,,,,,,,,,com.atlassian.servicedesk.plugins.approvals.internal.customfield.ApprovalsCFValue@15c9d65b,,,,,,,,,{},,,,,,,,,,,,,,,,,,MOBILE,,,,0|i02rdb:,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,37636,AL-68,Meeting Scheduling,To Do,11/Jun/25 10:05 PM
[MB - Inventory] Edit Product,AL-95,38105,Story,In Progress,AL,AIOS Link,software,Kai Nguyễn,6214a4102d1a9d0069949c96,,Medium,,,,Duyen Nguyen,712020:59b66c6e-1068-441c-9436-130289e16a26,Duyen Nguyen,712020:59b66c6e-1068-441c-9436-130289e16a26,11/Jun/25 1:25 PM,25/Jun/25 10:18 AM,,,,0,"h3. 1. Use Case Description

As a user on the mobile platform, I want to update the details of a product in my inventory so that I can keep inventory data accurate and up to date.

----

h3. 2. User Workflow

# User navigates to *Inventory Product Details*.
# User taps *Update Product*.
# System displays an editable form pre-filled with current product details:
#* Product Name
#* Category
#* Cost (USD)
#* Quantity
#* Description (optional)
#* Photo (optional)
# User updates one or more fields.
# User taps *Save Changes*.
# System validates inputs, updates the record, and returns to the Product Details screen with confirmation.

----

h3. 3. Field Definitions

h4. Input Fields

||Field Name||Field Type||Validation Rule||Mandatory||Description||
|Product Name|Text|1–100 characters|Yes|Name of the product|
|Category|Dropdown|Must select existing category|Yes|Product classification|
|Price (USD)|Number|Must be > 0|Yes|Product price in *USD*|
|Quantity|Integer|Must be ≥ 0|Yes|Number of units available|
|Description|Text Area|Up to 500 characters|No|Product details|
|Photo|Image Upload|JPG/PNG ≤ 5MB|No|Optional image for product|

h4. Interaction Elements

||Element Name||Type||Trigger Condition||Description||
|Save Changes Button|Button|Enabled when form is valid|Saves changes to the product|
|Cancel Button|Button|Always visible|Returns to Inventory Product Details|

----

h3. 4. Data Display (Summary)

||Data Field||Data Type||Display When Empty||Format||Description||
|Price (USD)|Amount|“--”|$X.XX|Cost in USD|
|Quantity|Integer|“--”|Numeric|Number of available units|
|Success Message|Toast|Hidden|Text|“Product updated successfully”|
|Error Message|Text|Hidden|Text|Validation or API failure|

----

h3. 5. Acceptance Criteria

* *AC1*: Form includes editable fields for Product Name, Category, Price (USD), Quantity, optional Description, and Photo.
* *AC2*: Save Changes button is disabled until all required fields are valid.
* *AC3*: Cost must be a positive number in USD; Quantity must be a non-negative integer.
* *AC4*: On success, product updates are reflected immediately in the Product Details screen.
* *AC5*: Cancel returns user without applying any changes.

----

h3. 6. System Rules

* Product Name must remain unique within the user's inventory.
* Price is stored in USD with up to two decimal places.
* Quantity updates are synced to backend and used for stock-level logic.
* Updated timestamp is recorded automatically.
* Photo replacement is optional and resized before upload.

----

h3. 7. UX Optimizations

* Modified fields highlighted before submission.
* Real-time validation on Price and Quantity.
* Currency label “USD” shown inline next to price field.
* Retain form values if validation fails.
* Spinner displayed during save operation.",,Duyen Nguyen,,712020:59b66c6e-1068-441c-9436-130289e16a26,,,,,,,,,,,,,,,,,com.atlassian.servicedesk.plugins.approvals.internal.customfield.ApprovalsCFValue@4036b34e,,,,,,,,,{},,,,,,,,,,,,,,,,,,MOBILE,,,,0|i02rav:,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,38088,AL-89,Inventory,In Progress,25/Jun/25 10:18 AM
[MB - Inventory] Publish Product to Marketplace,AL-94,38102,Story,In Progress,AL,AIOS Link,software,Kai Nguyễn,6214a4102d1a9d0069949c96,,Medium,,,,Duyen Nguyen,712020:59b66c6e-1068-441c-9436-130289e16a26,Duyen Nguyen,712020:59b66c6e-1068-441c-9436-130289e16a26,11/Jun/25 1:24 PM,25/Jun/25 10:18 AM,,,,0,"h3. 1. Use Case Description

As a user on the mobile platform, I want to list a product from my inventory on the marketplace so that other users can purchase it.

h3. 2. User Workflow

* Step 1: From *Inventory Product Details*, user taps *Put to Marketplace* for a product not currently listed.
* Step 2: System displays a form requesting listing information.
* Step 3: User enters Listing Price and optionally enables Tax. If Tax is enabled, a Tax Percentage field appears.
* Step 4: User taps *Save*. System validates inputs and creates the marketplace listing.
* Step 5: Product details update to show marketplace information including listing price and listing date.

h3. 3. Field Definitions

h4. Input Fields

||Field Name||Field Type||Validation Rule||Mandatory||Description||
|Listing Price|Amount|> 0|Yes|Selling price in USD|
|Enable Tax|Toggle|On or Off|Yes|Determines if tax applies|
|Tax Percentage|Number|0–100, shown only when Enable Tax = On|Conditional|Percentage tax applied|

h4. Interaction Elements

||Element Name||Type||Trigger Condition||Description||
|Put to Marketplace Button|Button|Visible when product not listed|Opens listing form|
|Save Button|Button|Enabled when form valid|Creates marketplace listing|
|Discard Button|Button|Always visible|Cancels and closes form|

h3. 4. Data Display (Summary)

||Data Name||Data Type||Display When Empty||Format||Description||
|Success Message|Toast|Hidden|Plaintext|Shown when listing created|
|Error Message|Text|Hidden|Plaintext|Displays validation errors|

h3. 5. Acceptance Criteria

* AC1: Put to Marketplace button appears only for products not yet listed.
* AC2: Listing form requires Listing Price and optionally Tax Percentage when tax is enabled.
* AC3: On successful save, product becomes visible in the Marketplace Product List and Inventory status updates to {{Marketplace}}.
* AC4: Discard button closes the form without saving.
* AC5: System records the listing date automatically at creation.

h3. 6. System Rules

* Listing Price must be a positive value in USD.
* Tax Percentage must be a whole number between 0 and 100 when enabled.
* Listing date is stored in UTC and displayed according to user timezone.
* Audit log captures user ID and timestamp for the listing action.

h3. 7. UX Optimizations

* Autofocus on the Listing Price field when the form opens.
* Display a live preview of total price including tax while editing.
* Show spinner on Save while the request processes.",,Duyen Nguyen,,712020:59b66c6e-1068-441c-9436-130289e16a26,,,,,,,,,,,,,,,,,com.atlassian.servicedesk.plugins.approvals.internal.customfield.ApprovalsCFValue@12081a28,,,,,,,,,{},,,,,,,,,,,,,,,,,,MOBILE,,,,0|i02ran:,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,38088,AL-89,Inventory,In Progress,25/Jun/25 10:18 AM
[MB - Inventory] Product List,AL-93,38099,Story,In Progress,AL,AIOS Link,software,Kai Nguyễn,6214a4102d1a9d0069949c96,,Medium,,,,Duyen Nguyen,712020:59b66c6e-1068-441c-9436-130289e16a26,Duyen Nguyen,712020:59b66c6e-1068-441c-9436-130289e16a26,11/Jun/25 1:23 PM,25/Jun/25 10:18 AM,,,,0,"

h3. 1. Use Case Description

As a merchant, I want to view, search, and manage a list of products in my inventory so that I can monitor and update my available listings.
As a standard or premium user, I want to view a list of my purchased digital products waiting to be used so that I can track and access them when needed.

----

h3. 2. User Workflow

h4. For Merchants:

# Merchant taps *Inventory* from the main menu.
# System loads *Inventory Product List*.
# Each product row shows:
#* Thumbnail
#* Product Name
#* Category
#* Cost (USD)
#* Quantity
#* Status (Draft, Inactive, Marketplace)
# Merchant may:
#* Search or filter the list
#* Tap any product row to view details
#* Pull to refresh data

h4. For Standard/Premium Users:

# User taps *My Inventory* from the menu or dashboard shortcut.
# System loads a list of *purchased digital products*.
# Each row shows:
#* Thumbnail
#* Product Name
#* Category
#* Purchase Price (USD)
#* Seller Name (optional)
#* Product Type (e.g., service, content)
# User may:
#* View product details (read-only)
#* Use or redeem the product (if eligible)
#* Pull to refresh the list

{quote}🔒 *Users cannot* create, update, or delete inventory items. Their list is read-only and system-managed.{quote}

----

h3. 3. Search and Filter Rules

||Filter Capability||Merchant||User||
|*Search* (by name/category)|✅ Yes|✅ Yes|
|*Filter by Category*|✅ Yes|✅ Yes|
|*Filter by Status*|✅ (Draft/Inactive/Marketplace)|❌ No (not applicable)|
|*Filter by Price Range*|✅ Yes|❌ No|

----

h3. 4. Field Definitions

h4. A. Merchant View

||Field Name||Type||Format||Description||
|Thumbnail|Image|1:1 ratio|Product photo|
|Product Name|Text|Plaintext|Title of product|
|Category|Text|Plaintext|Product category|
|Cost (USD)|Currency|$X.XX|Selling price|
|Quantity|Integer|Numeric|Units available|
|Status|Badge|Draft/Inactive/Marketplace|Listing status|

h4. B. User View

||Field Name||Type||Format||Description||
|Thumbnail|Image|1:1 ratio|Product photo|
|Product Name|Text|Plaintext|Title of purchased product|
|Category|Text|Plaintext|Product category|
|Purchase Price|Currency|$X.XX|Price paid|
|Product Type|Text|Label|Service / Digital Content / Other|
|Seller Name|Text|Optional|Merchant name (if shown)|

----

h3. 5. Interaction Elements

||Element Name||Type||Actor||Description||
|Search Bar|Input|All|Search by name or category|
|Filter Button|Button|Merchant only|Opens filter modal|
|Product Row|Tap|All|Opens product details|
|Pull to Refresh|Gesture|All|Refresh list|

----

h3. 6. Acceptance Criteria

* *AC1*: Merchants can view and manage their products with quantity, cost, and status.
* *AC2*: Users can only view purchased digital products; no create/edit/delete actions are allowed.
* *AC3*: Search and filters apply correctly to both views with role-specific rules.
* *AC4*: Cost is displayed in USD with 2 decimal places.
* *AC5*: Pull-to-refresh reloads the correct data for each actor.

----

h3. 7. System Rules

* Cost is stored and shown in USD.
* Quantity is non-negative; pulled from product record (merchant only).
* User’s inventory contains only:
** *Completed orders* of digital products
** *Unused or active* items
* Product list is sorted by *updated date descending* (merchant) or *purchase date descending* (user).
* Filters persist per session unless cleared.
* Access to this screen is controlled by user role.

----

h3. 8. UX Optimizations

* Right-align Quantity and Price columns.
* Use sticky header for search and category filters.
* Empty states:
** Merchant: “No products found. Add one to get started.”
** User: “No digital products yet. Your purchases will appear here.”
* Skeleton loading cards for smoother experience.",,Duyen Nguyen,,712020:59b66c6e-1068-441c-9436-130289e16a26,,,,,,,,,,,,,,,,,com.atlassian.servicedesk.plugins.approvals.internal.customfield.ApprovalsCFValue@5eeeb17,,,,,,,,,{},,,,,,,,,,,,,,,,,,MOBILE,,,,0|i02raf:,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,38088,AL-89,Inventory,In Progress,25/Jun/25 10:18 AM
[MB - Inventory] Product Details,AL-92,38096,Story,In Progress,AL,AIOS Link,software,Kai Nguyễn,6214a4102d1a9d0069949c96,,Medium,,,,Duyen Nguyen,712020:59b66c6e-1068-441c-9436-130289e16a26,Duyen Nguyen,712020:59b66c6e-1068-441c-9436-130289e16a26,11/Jun/25 1:23 PM,25/Jun/25 10:18 AM,,,,0,"h3. 1. Use Case Description

As a merchant on the mobile platform, I want to view complete information about a product in my inventory—including quantity—so that I can verify inventory levels and take actions like updating, deleting, or listing the product.

----

h3. 2. User Workflow

# User selects a product from the *Inventory Product List*.
# System navigates to the *Inventory Product Details* screen.
# Screen displays all product data fields:
#* Product Name
#* Category
#* Cost (in USD)
#* Quantity
#* Description
#* Status
# User may:
#* Tap *Update Product* to modify data
#* Tap *Put to Marketplace* (if eligible)
#* Tap *Delete Product*
#* Tap *Back* to return to list view

----

h3. 3. Field Definitions

h4. Display Fields

||Field Name||Data Type||Display When Empty||Format||Description||
|Product Name|Text|“--”|Plaintext|Product title|
|Category|Text|“--”|Plaintext|Category label|
|Cost (USD)|Amount|“--”|USD|Product price in USD|
|*Quantity*|Number|“--”|Integer|Available stock quantity|
|Description|Text|“--”|Plaintext|Additional product info|
|Status|Badge|“--”|Draft/Inactive/Marketplace|Current inventory state|

h4. Interaction Elements

||Element Name||Type||Trigger Condition||Description||
|Update Product Button|Button|Always visible|Navigates to Update Product form|
|Put to Marketplace Button|Button|Visible when status = Active|Opens listing form|
|Delete Product Button|Button|Always visible|Initiates deletion confirmation|
|Back Button|Button|Always visible|Returns to Inventory Product List|

----

h3. 4. Data Display (Card View)

||Data Field||Data Type||Format||Description||
|Quantity|Integer|e.g., “15”|Number of units in inventory|

----

h3. 5. Acceptance Criteria

* *AC1*: Quantity must be displayed alongside other product fields.
* *AC2*: If quantity is not available (corrupt or null), show ""--"".
* *AC3*: Quantity is fetched and displayed in read-only format.
* *AC4*: All update or delete actions continue to function as before.

----

h3. 6. System Rules

* Quantity is stored per product and retrieved on detail view load.
* Quantity value is updated through the Update Product workflow.
* Quantity must be a non-negative integer.

----

h3. 7. UX Optimizations

* Quantity field shown in “General Information” section.
* Field is styled consistently with Cost and Category.
* Skeleton loader shown until quantity is loaded.",,Duyen Nguyen,,712020:59b66c6e-1068-441c-9436-130289e16a26,,,,,,,,,,,,,,,,,com.atlassian.servicedesk.plugins.approvals.internal.customfield.ApprovalsCFValue@5e4e9eff,,,,,,,,,{},,,,,,,,,,,,,,,,,,MOBILE,,,,0|i02ra7:,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,38088,AL-89,Inventory,In Progress,25/Jun/25 10:18 AM
[MB - Inventory] Delete Product,AL-91,38093,Story,In Progress,AL,AIOS Link,software,Kai Nguyễn,6214a4102d1a9d0069949c96,,Medium,,,,Duyen Nguyen,712020:59b66c6e-1068-441c-9436-130289e16a26,Duyen Nguyen,712020:59b66c6e-1068-441c-9436-130289e16a26,11/Jun/25 1:23 PM,25/Jun/25 10:18 AM,,,,0,"h3. 1. Use Case Description

As a user on the mobile platform, I want to remove a product from my inventory so that it no longer appears in my list, while ensuring that existing orders referencing it are preserved.

h3. 2. User Workflow

* Step 1: From *Inventory Product Details*, user taps *Delete Product*.
* Step 2: System displays a confirmation modal explaining that deletion is only allowed if the product has no active marketplace listings or unfulfilled orders.
* Step 3: User confirms deletion.
* Step 4: System deletes the product record and returns to the Inventory Product List with a success message.

h3. 3. Field Definitions

h4. Interaction Elements

||Element Name||Type||Trigger Condition||Description||
|Delete Product Button|Button|Always visible on details screen|Opens confirmation modal|
|Confirmation Modal|Dialog|Shown after Delete pressed|Explains data constraint and asks for confirmation|
|Confirm Delete|Button|Enabled within modal|Confirms deletion|
|Cancel Delete|Button|Enabled within modal|Dismisses modal without changes|

h3. 4. Data Display (Summary)

||Data Name||Data Type||Display When Empty||Format||Description||
|Success Message|Toast|Hidden|Plaintext|Displayed when product is deleted|
|Error Message|Text|Hidden|Plaintext|Shown if data constraint prevents deletion|

h3. 5. Acceptance Criteria

* AC1: Delete Product button is available from Inventory Product Details.
* AC2: Confirmation modal clearly states that products with active marketplace listings or unfulfilled orders cannot be deleted.
* AC3: If constraints are met, product record is removed and success message appears.
* AC4: If deletion is blocked, error message explains why.
* AC5: After deletion, user returns to the Inventory Product List with the item removed.

h3. 6. System Rules

* Backend checks for marketplace listings and unfulfilled orders before deletion.
* Deleted products are permanently removed from the user’s inventory but remain in historical order records.
* Audit log records user ID and timestamp of deletion.

h3. 7. UX Optimizations

* Prefill modal with product name for clarity.
* Display spinner inside modal while processing deletion.
* Return focus to the previous list position after successful deletion.",,Duyen Nguyen,,712020:59b66c6e-1068-441c-9436-130289e16a26,,,,,,,,,,,,,,,,,com.atlassian.servicedesk.plugins.approvals.internal.customfield.ApprovalsCFValue@2b97eabb,,,,,,,,,{},,,,,,,,,,,,,,,,,,MOBILE,,,,0|i02r9z:,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,38088,AL-89,Inventory,In Progress,25/Jun/25 10:18 AM
[MB-Inventory] Create Product,AL-90,38090,Story,In Progress,AL,AIOS Link,software,Kai Nguyễn,6214a4102d1a9d0069949c96,,Medium,,,,Duyen Nguyen,712020:59b66c6e-1068-441c-9436-130289e16a26,Duyen Nguyen,712020:59b66c6e-1068-441c-9436-130289e16a26,11/Jun/25 1:22 PM,25/Jun/25 10:18 AM,,,,0,"h3. 1. Use Case Description

As a user on the mobile platform, I want to add a new product to my inventory by entering its details, including quantity and pricing in USD, so that I can manage inventory items and prepare them for potential marketplace listings.

----

h3. 2. User Workflow

# User opens *Inventory* and taps *Add Product*.
# System displays a form with fields for Product Name, Category, Cost (in USD), Quantity, optional Description, and optional Photo.
# User fills in the form.
# User taps *Create Product*. System validates all mandatory fields.
# On success, system adds the product to the Inventory Product List and shows a confirmation.

----

h3. 3. Field Definitions

h4. Input Fields

||Field Name||Field Type||Validation Rule||Mandatory||Description||
|Product Name|Text|1–100 characters|Yes|Name of the product|
|Category|Dropdown|Must select existing category|Yes|Product category|
|Cost (USD)|Number|Positive value|Yes|Base cost in *USD*|
|Quantity|Integer|Must be ≥ 0|Yes|Inventory quantity available|
|Description|Text Area|Up to 500 characters|No|Optional product notes|
|Photo|Image Upload|JPG/PNG ≤ 5MB|No|Optional product image|

h4. Interaction Elements

||Element Name||Type||Trigger Condition||Description||
|Add Product Button|Button|Always visible|Opens the create product form|
|Create Product Button|Button|Enabled when form is valid|Submits and saves the new product|
|Cancel Button|Button|Always visible|Returns to Inventory Product List|

----

h3. 4. Data Display (Summary)

||Data Name||Data Type||Display When Empty||Format||Description||
|Success Message|Toast|Hidden|Text|“Product created successfully”|
|Error Message|Text|Hidden|Text|Form validation or API error message|

----

h3. 5. Acceptance Criteria

* *AC1*: Form must include Product Name, Category, Cost (in USD), and Quantity.
* *AC2*: Create Product button is disabled until all mandatory fields are valid.
* *AC3*: On success, product appears in Inventory Product List.
* *AC4*: Cancel button returns user to list without saving.
* *AC5*: Quantity must be a non-negative whole number.

----

h3. 6. System Rules

* Product Name must be unique per user inventory.
* Cost is stored in USD with two decimal places.
* Quantity is stored as a positive integer.
* Photo is resized before upload to optimize storage.
* Created timestamp is automatically generated.

----

h3. 7. UX Optimizations

* Autofocus on Product Name field when form loads.
* Live validation for cost and quantity fields.
* Retain input values if form validation fails.
* Show unit label “USD” next to cost field.
* Spinner displayed during save operation.",,Duyen Nguyen,,712020:59b66c6e-1068-441c-9436-130289e16a26,,,,,,,,,,,,,,,,,com.atlassian.servicedesk.plugins.approvals.internal.customfield.ApprovalsCFValue@791c7cbb,,,,,,,,,{},,,,,,,,,,,,,,,,,,MOBILE,,,,0|i02r9r:,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,38088,AL-89,Inventory,In Progress,25/Jun/25 10:18 AM
[MB - Account Settings] Set Availability,AL-88,38054,Story,Specified,AL,AIOS Link,software,Kai Nguyễn,6214a4102d1a9d0069949c96,,Medium,,,,Duyen Nguyen,712020:59b66c6e-1068-441c-9436-130289e16a26,Duyen Nguyen,712020:59b66c6e-1068-441c-9436-130289e16a26,11/Jun/25 11:51 AM,22/Jun/25 9:22 PM,25/Jun/25 10:10 AM,,,0,"1. Use Case Description

As a user on the mobile platform, I want to configure when I’m available or busy, and optionally indicate the context of that availability using smart tags, so that others can know when and how to interact with me in real time through the contact list.

----

h3. 2. User Workflow (Step-by-Step)

# User navigates to *Settings > Set Availability*.
# Toggles *Availability → ON*.
# System shows a *Status Selector* with two options:
#* *Active*
#* *Busy*

----

h4. If user selects *Active*:

# System displays a *Mon–Sun selector*.
# User selects one or more days to define their availability windows.
# For each selected day, user configures:
#* *Start Time*
#* *End Time*
# System validates that Start < End.
# User exits or confirms.

✅ Result:

* If the user enters the app during any configured time window for that weekday, their *status will appear as “Active”* to others in the contact list.
* On the Home Dashboard, a module appears prompting the user to optionally add *Smart Tags* like:
** ☕ Coffee
** 🍳 Brunch
** 🍽️ Dinner
** 🎉 Event
** 💬 Chat

{quote}⏳ *Tags auto-expire in 12 hours* if no new tag is set.
If no tag is set at all, *only the Active status* will appear to others — no activity context will be shown.{quote}

----

h4. If user selects *Busy*:

* User does not need to define specific days or hours.
* Anytime they *enter the app*, their status will show as *Busy* to others.

----

h4. If user toggles *Availability OFF*:

* No status or tag is shown anywhere.
* The user appears *offline or invisible* in all modules (contact list, profile, discovery).

----

h3. 3. Field Definitions

||Field Name||Type||Validation Rule||Required||Description||
|Availability Toggle|Toggle|None|✅|Enables or disables user status system|
|Status Selector|Enum|“Active” / “Busy”|✅|Defines presence mode|
|Day Selector|Multi-select|Mon–Sun|✅*|Only for “Active”|
|Start Time|Time Picker|Must be < End Time|✅|Start of visibility window|
|End Time|Time Picker|Must be > Start Time|✅|End of visibility window|
|Smart Tags|Multi-select|Max 5 / auto-expire in 12 hrs|❌|Optional context tags|

----

h3. 4. Display Logic

||Scenario||Display to Others||
|Availability OFF|No status or tag shown|
|Availability ON + Active + Tag|“Active for ☕ Coffee” in contact list|
|Availability ON + Active + No Tag|“Active” only, no context|
|Availability ON + Busy|“Busy” appears when user enters app|
|Outside configured time window|Status hidden even if availability = ON|

----

h3. 5. Acceptance Criteria

* *AC1:* Availability toggle OFF → no status or tag shown anywhere
* *AC2:* If Active, system shows Mon–Sun selector with time ranges
* *AC3:* If user opens app during their Active window → status appears in contact list
* *AC4:* If user adds tag, tag shows alongside status and expires in 12 hrs
* *AC5:* If user sets Busy, it shows whenever they open app regardless of time or day
* *AC6:* If user does not open app during an Active window, no status is shown
* *AC7:* Only one status can be shown at a time (Active, Busy, or hidden)

----

h3. 6. System Rules

* Availability is stored per weekday with time windows
* Tags expire 12 hours after being set
* Status only appears if user is online _and_ conditions are met
* No status is shown if toggle is OFF
* Busy overrides Active scheduling
* Visibility is updated in real time across contact list and profile

----

h3. 7. UX Optimizations

* When user opens app during availability window → toast: “You’re now visible as Active”
* Tag prompt appears at top of Home Dashboard if Active
* DND badge style used for Busy
* Subtle fading status badge on profile for visibility toggle",,Duyen Nguyen,,712020:59b66c6e-1068-441c-9436-130289e16a26,,,,,,,,,,,,,,,,,com.atlassian.servicedesk.plugins.approvals.internal.customfield.ApprovalsCFValue@bd886dc,,,,,,,,,{},,,,,,,,,,,,,,,,,,MOBILE,,,,0|i02r9b:,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,37260,AL-21,Account Settings,To Do,11/Jun/25 11:51 AM
[WEB - Marketplace] Put Product to Marketplace,AL-85,37948,Story,In Progress,AL,AIOS Link,software,Kai Nguyễn,6214a4102d1a9d0069949c96,,Medium,,,,Duyen Nguyen,712020:59b66c6e-1068-441c-9436-130289e16a26,Duyen Nguyen,712020:59b66c6e-1068-441c-9436-130289e16a26,10/Jun/25 7:35 PM,25/Jun/25 10:18 AM,23/Jun/25 10:32 PM,,,0,"h1. Put Product to Marketplace Requirement Specification

h3. 1. Use Case Description

As an admin on the web platform, I want to create a marketplace listing for a product that is not yet listed so that customers can purchase it through the platform.

h3. 2. User Workflow

* Step 1: Admin opens the *Product Details* page for a product not currently in the marketplace.
* Step 2: Admin taps *Put to Marketplace* within the Marketplace section.
* Step 3: System displays a form requesting listing information.
* Step 4: Admin enters Price and chooses whether to enable Tax. If Tax is enabled, a Tax Percentage field appears.
* Step 5: Admin taps *Save*. System validates input and creates the marketplace listing.
* Step 6: Product details update to show marketplace information including the listing price and date.

h3. 3. Field Definitions

h4. Input Fields

||Field Name||Field Type||Validation Rule||Mandatory||Description||
|Price|Amount|> 0|Yes|Listing price in VND|
|Enable Tax|Toggle|On or Off|Yes|Determines if tax applies|
|Tax Percentage|Number|0–100, shown only when Enable Tax = On|Conditional|Percentage tax applied|

h4. Interaction Elements

||Element Name||Type||Trigger Condition||Description||
|Put to Marketplace Button|Button|Visible when product not listed|Opens listing form|
|Save Button|Button|Enabled when form valid|Creates marketplace listing|
|Discard Button|Button|Always visible|Cancels and closes form|

h3. 4. Data Display (Summary)

||Data Name||Data Type||Display When Empty||Format||Description||
|Success Message|Toast|Hidden|Plaintext|Shown when listing created|
|Error Message|Text|Hidden|Plaintext|Displays validation errors|

h3. 5. Acceptance Criteria

* AC1: Put to Marketplace button appears only for products not yet listed.
* AC2: Listing form requires Price and optionally Tax Percentage when tax is enabled.
* AC3: On successful save, product becomes visible in the Marketplace Product List and the Product Details page shows the marketplace section.
* AC4: Discard button closes the form without saving.
* AC5: System records the listing date automatically at creation.

h3. 6. System Rules

* Price must be a positive value in USD.
* Tax Percentage must be a whole number between 0 and 100 when enabled.
* Listing date is stored in UTC and displayed according to admin timezone.
* Audit log captures admin ID and timestamp for the listing action.

h3. 7. UX Optimizations

* Autofocus on the Price field when the form opens.
* Display a live preview of total price including tax while editing.
* Show spinner on Save while the request processes.",,Duyen Nguyen,,712020:59b66c6e-1068-441c-9436-130289e16a26,,,,,,,,,,,,,,,,,com.atlassian.servicedesk.plugins.approvals.internal.customfield.ApprovalsCFValue@514604bb,,,,,,,,,{},,,,,,,,,,,,,,,,,,WEB,,,,0|i02r7r:,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,37259,AL-20,Marketplace,In Progress,25/Jun/25 10:18 AM
[WEB - Marketplace] Product List,AL-84,37945,Story,In Progress,AL,AIOS Link,software,Kai Nguyễn,6214a4102d1a9d0069949c96,,Medium,,,,Duyen Nguyen,712020:59b66c6e-1068-441c-9436-130289e16a26,Duyen Nguyen,712020:59b66c6e-1068-441c-9436-130289e16a26,10/Jun/25 7:24 PM,25/Jun/25 10:18 AM,23/Jun/25 10:32 PM,,,0,"h1. Marketplace Product List Requirement Specification

*Menu Path:* Marketplace > Product List

h3. 1. Use Case Description

As an admin on the web platform, I want to view all products currently listed in the marketplace so that I can manage prices and remove listings when needed.

h3. 2. User Workflow

* Step 1: Admin opens *Marketplace > Product List* from the Web Admin Module dashboard.
* Step 2: System displays a searchable table listing marketplace products with pagination.
* Step 3: Admin uses the search box or filters to locate specific products.
* Step 4: Admin clicks a row (excluding links) to open the *Product Details* page.
* Step 5: Admin clicks the Owner name link within the row to open the *Business Profile* page.
* Step 6: Admin clicks the *Actions* gear icon in the row and chooses *Update* or *Remove from Marketplace* from the popover.
* Step 7: System confirms the action and updates the listing accordingly.

h3. 3. Search and Filter Rules

h4. Search Rules

* Partial, case-insensitive match on *Product Name*, *Category*, *Manufacturer*, and *Business Name*.

h4. Filter Rules

* *Category*: Multi-select dropdown of marketplace product categories.
* *Manufacturer*: Dropdown listing all manufacturers.
* *Business*: Dropdown of business names.
* *Price Range*: Min/Max numeric fields to filter by listing price.
* *Listing Date Range*: Date range picker to filter by listing date.

h3. 4. Field Definitions

h4. Interaction Elements

||Element Name||Type||Trigger Condition||Description||
|Row Link|Row Click|Always enabled|Navigates to Product Details page|
|Business Link|Text Link|Always visible|Opens Business Profile page|
|Actions Gear Icon|Icon Button|Always visible|Opens popover with Update and Remove options|

h3. 5. Data Display (Summary)

||Data Name||Data Type||Display When Empty||Format||Description||
|Product Name|Text|""--""|Plaintext|Name of the product|
|Owner|Text Link|""--""|Plaintext|Business name of owner|
|Price|Amount|""--""|USD|Listing price|
|Category|Text|""--""|Plaintext|Product category|
|Manufacturer|Text|""--""|Plaintext|Manufacturer brand|
|Listing Date|Date|""--""|YYYY-MM-DD|Date product was listed|
|Actions|Gear Icon|Hidden|N/A|Popover with Update or Remove options|

h3. 6. Acceptance Criteria

* AC1: Table must display columns for Product Name, Owner, Price, Category, Manufacturer, Listing Date, and Actions.
* AC2: Clicking a row opens Product Details; clicking the Owner link opens the Business Profile page.
* AC3: Search returns matching results on Product Name, Category, Manufacturer, or Business Name.
* AC4: Filters narrow results by Category, Manufacturer, Business, Price Range, and Listing Date Range.
* AC5: Update and Remove actions trigger confirmation and reflect changes immediately in the table.
* AC6: Table loads with pagination of 25 marketplace products per page by default.

h3. 7. System Rules

* Remove action requires confirmation and is only allowed when the product has no active orders.
* Table data is sorted by Listing Date (newest first) by default.
* Update action must check for concurrent changes to avoid conflicts.

h3. 8. UX Optimizations

* Keep search box focused after returning from details pages.
* Highlight rows on hover to indicate clickability.
* Disable the gear icon menu while processing and show a spinner.
* Remember last applied filters when admin returns to the screen.
* Provide a tooltip for the gear icon to clarify its purpose.",,Duyen Nguyen,,712020:59b66c6e-1068-441c-9436-130289e16a26,,,,,,,,,,,,,,,,,com.atlassian.servicedesk.plugins.approvals.internal.customfield.ApprovalsCFValue@4c1ccb68,,,,,,,,,{},,,,,,,,,,,,,,,,,,WEB,,,,0|i02r7j:,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,37259,AL-20,Marketplace,In Progress,25/Jun/25 10:18 AM
[WEB - Order] Order Details,AL-83,37942,Story,Specified,AL,AIOS Link,software,Kai Nguyễn,6214a4102d1a9d0069949c96,,Medium,,,,Duyen Nguyen,712020:59b66c6e-1068-441c-9436-130289e16a26,Duyen Nguyen,712020:59b66c6e-1068-441c-9436-130289e16a26,10/Jun/25 7:05 PM,10/Jun/25 7:52 PM,,,,0,"h1. Order Details Requirement Specification

h3. 1. Use Case Description

As an admin on the web platform, I want to view complete information about a specific order so that I can verify its data, update quantities when allowed, and manage order status.

h3. 2. User Workflow

* Step 1: Admin selects an order from the Order List.
* Step 2: System navigates to the *Order Details* screen and loads all stored fields.
* Step 3: Admin reviews high-level information such as Order Number, Organization, Customer, Total Price, Status, and Order Date.
* Step 4: System displays an *Organization Information* section with organization name, address, and owner name.
* Step 5: System displays a *Customer Information* section with full name, email, and phone number, address (if applicable).
* Step 6: If the order is for marketplace products, system lists the ordered products in a table with edit and remove actions as allowed.
* Step 7: If the order is for ticket selling, system lists purchased tickets in a table showing Ticket Number and Price only.
* Step 8: Admin performs allowed actions such as editing quantity or removing products when applicable, or simply reviews ticket information.
* Step 9: Admin taps *Back* to return to the Order List.

h3. 3. Field Definitions

h4. Interaction Elements

||Element Name||Type||Trigger Condition||Description||
|Edit Quantity Icon|Icon Button|Visible when order status is {{New}}|Opens inline quantity editor|
|Save Quantity Button|Button|Enabled when quantity changed|Saves new quantity after stock check|
|Cancel Quantity Edit|Button|Visible when editing|Cancels quantity edit|
|Remove Product Button|Button|Visible when order status is {{New}}|Removes product from order|
|Cancel Order Button|Button|Always visible|Initiates cancel order workflow|
|Back Button|Button|Always visible|Returns to Order List|
|Organization Owner Link|Text Link|Always visible|Opens owner profile page|
|Customer Link|Text Link|Always visible|Opens user profile page|

h3. 4. Data Display (Summary)

||Data Name||Data Type||Display When Empty||Format||Description||
|Order Number|Text|""--""|Plaintext|Unique order identifier|
|Organization|Text Link|""--""|Plaintext|Selling organization name|
|Customer Name|Text Link|""--""|Plaintext|Full name of purchaser|
|Total Price|Amount|""--""|VND|Order total amount|
|Status|Badge|""--""|New/Confirmed/...|Current order state|
|Order Date|Date|""--""|YYYY-MM-DD|Date the order was placed|
|Organization Address|Text|""--""|Plaintext|Organization location|
|Owner Name|Text Link|""--""|Plaintext|Organization owner, links to profile|
|Customer Email|Text|""--""|Plaintext|Purchaser email address|
|Customer Phone|Text|""--""|Plaintext|Purchaser phone number|
|Customer Address|Text|“--”|Plaintext|Delivery Address|

h4. Product List Table

||Column Name||Data Type||Description||
|Product Name|Text|Name of the ordered product|
|Organization|Text|Organization selling the product|
|Price|Amount|Unit price|
|Quantity|Number with Edit Icon|Editable when order is {{New}}|
|Action|Button|Remove product option|

h4. Ticket List Table

(Shown only for ticket selling orders)

||Column Name||Data Type||Description||
|Ticket Number|Text|Unique ticket identifier|
|Price|Amount|Paid price per ticket|

h3. 5. Acceptance Criteria

* AC1: Order Details must display Order Number, Organization, Customer Name, Total Price, Status, and Order Date.
* AC2: Organization Information section shows organization name, address, and owner name linked to the profile page.
* AC3: Customer Information section shows full name, email, and phone number with a link to the profile page, address if purchased product.
* AC4: *Product List* table is shown only for marketplace orders and lists columns for Product Name, Organization, Price, Quantity, and Action.
* AC5: *Ticket List* table is shown only for ticket selling orders and lists columns for Ticket Number and Price with no edit actions.
* AC6: Save action must verify product stock before committing quantity changes.
* AC7: Cancel Order button is always visible and triggers the Cancel Order workflow.
* AC8: Back button returns to the Order List and preserves scroll position.

h3. 6. System Rules

* Editing quantity validates against current product stock; if insufficient, system shows an error and keeps previous quantity.
* Removing a product recalculates the Total Price immediately.
* Status changes and quantity updates are logged with admin ID and timestamp.
* Details screen fetches the latest order data from backend API on load.

h3. 7. UX Optimizations

* Display order information in clearly labeled sections for readability.
* Use skeleton loaders while data is loading.
* Keep action buttons sticky at the bottom for easy access.
* Highlight editable quantity field during editing mode.
* Provide confirmation toast after quantity updates or product removal.",,Duyen Nguyen,,712020:59b66c6e-1068-441c-9436-130289e16a26,,,,,,,,,,,,,,,,,com.atlassian.servicedesk.plugins.approvals.internal.customfield.ApprovalsCFValue@741a0ba2,,,,,,,,,{},,,,,,,,,,,,,,,,,,WEB,,,,0|i02r7b:,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,37950,AL-86,Order,To Do,10/Jun/25 7:05 PM
[WEB - Events] Event List,AL-82,37939,Story,In Progress,AL,AIOS Link,software,Kai Nguyễn,6214a4102d1a9d0069949c96,,Medium,,,,Duyen Nguyen,712020:59b66c6e-1068-441c-9436-130289e16a26,Duyen Nguyen,712020:59b66c6e-1068-441c-9436-130289e16a26,10/Jun/25 7:03 PM,23/Jun/25 10:32 PM,23/Jun/25 9:35 PM,,,0,"h1. Event List Requirement Specification

*Menu Path:* Events

h3. 1. Use Case Description

As an admin on the web platform, I want to view all events created by premium users or organizations so that I can manage them effectively and access details quickly.

h3. 2. User Workflow

* Step 1: Admin opens the *Event List* from the Web Admin Module.
* Step 2: System displays a paginated table of events with search and filter tools.
* Step 3: Admin uses the search box or filters to locate specific events.
* Step 4: Admin clicks a table row (excluding links) to open the *Event Details* page.
* Step 5: Admin clicks the host name link within the row to open the User or Organization Profile page.
* Step 6: Admin clicks the *Actions* gear icon in the row and chooses *Update* or *Delete* from the popover.

h3. 3. Search and Filter Rules

h4. Search Rules

* Partial, case-insensitive match on *Event Name* and *Host Name*.

h4. Filter Rules

* *Privacy*: Dropdown with options {{Public}} or {{Private}}.
* *Status*: Dropdown with options {{Active}} or {{Inactive}}.
* *Host Type*: Dropdown with {{User}} or {{Organization}}.
* *Created Date Range*: Start and End date pickers.

h3. 4. Field Definitions

h4. Interaction Elements

||Element Name||Type||Trigger Condition||Description||
|Row Link|Row Click|Always enabled|Navigates to Event Details page|
|Host Link|Text Link|Always visible|Opens User or Organization profile|
|Actions Gear Icon|Icon Button|Always visible|Opens popover with Update and Delete options|

h3. 5. Data Display (Summary)

||Data Name||Data Type||Display When Empty||Format||Description||
|Event Name|Text|""--""|Plaintext|Name of the event|
|Privacy|Badge|""--""|Public/Private|Visibility level|
|Status|Badge|""--""|Active/Inactive|Current event status|
|Location|Text|""--""|Plaintext/URL|Address or virtual link|
|Maximum Attendees|Number|""--""|0|Max participant count|
|Created Date|Date|""--""|YYYY-MM-DD|Date event was created|
|Host Name|Text Link|""--""|Plaintext|User or organization hosting|
|Actions|Gear Icon|Hidden|N/A|Popover with Update and Delete options|

h3. 6. Acceptance Criteria

* AC1: Table must list columns for Event Name, Privacy, Status, Location, Maximum Attendees, Created Date, Host Name, and Actions.
* AC2: Clicking a row opens Event Details, while the Host Name link opens the respective profile page.
* AC3: Search returns events matching Event Name or Host Name.
* AC4: Filters narrow results by Privacy, Status, Host Type, and Created Date Range.
* AC5: Update and Delete actions must confirm the intent and refresh the table upon success.
* AC6: Table loads with pagination of 25 events per page.

h3. 7. System Rules

* Deleted events are removed only if there are no sold tickets.
* Table data is sorted by Created Date (newest first) by default.
* Search and filter values persist when navigating back from other pages.
* Update and Delete operations log admin ID, timestamp, and event ID.

h3. 8. UX Optimizations

* Keep the search box focused after returning from Event Details.
* Highlight rows on hover to indicate they are clickable.
* Disable the gear icon menu while processing and show a spinner.
* Remember last applied filters when admin returns to the screen.
* Provide a tooltip for the gear icon to clarify its purpose.",,Duyen Nguyen,,712020:59b66c6e-1068-441c-9436-130289e16a26,,,,,,,,,,,,,,,,,com.atlassian.servicedesk.plugins.approvals.internal.customfield.ApprovalsCFValue@21fa3a7b,,,,,,,,,{},,,,,,,,,,,,,,,,,,WEB,,,,0|i02r73:,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,37186,AL-4,Events,In Progress,23/Jun/25 10:32 PM
[WEB -Events] View Event Details,AL-81,37936,Story,In Progress,AL,AIOS Link,software,Kai Nguyễn,6214a4102d1a9d0069949c96,,Medium,,,,Duyen Nguyen,712020:59b66c6e-1068-441c-9436-130289e16a26,Duyen Nguyen,712020:59b66c6e-1068-441c-9436-130289e16a26,10/Jun/25 7:00 PM,23/Jun/25 10:33 PM,20/Jun/25 3:35 PM,,,0,"h1. Event Details Requirement Specification

h3. 1. Use Case Description

As an admin on the web platform, I want to view complete information about a specific event so that I can verify its data, manage tickets, and update or delete the event if necessary.

h3. 2. User Workflow

* Step 1: Admin selects an event from the Event List.
* Step 2: System navigates to the *Event Details* screen and loads all stored fields including host information.
* Step 3: Admin reviews details such as Event Name, Privacy, Status, Location, Maximum Attendees, Tags, Description, Created Date, and Host Name.
* Step 4: If the event requires tickets, system displays a table of sold tickets with a *Send Ticket* action.
* Step 5: Admin can tap *Update Event* to modify the record or *Delete Event* if allowed.
* Step 6: Admin taps *Back* to return to the Event List.

h3. 3. Field Definitions

h4. Interaction Elements

||Element Name||Type||Trigger Condition||Description||
|Update Event Button|Button|Always visible|Navigates to Update Event screen|
|Delete Event Button|Button|Visible when deletion is permitted|Initiates delete workflow|
|Back Button|Button|Always visible|Returns to Event List|
|Send Ticket Button|Button|Visible for each sold ticket|Sends ticket to owner|

h3. 4. Data Display (Summary)

||Data Name||Data Type||Display When Empty||Format||Description||
|Event Title|Text|""--""|Plaintext|Event title|
|Visibility|Badge|""--""|Public/Private|Visibility level|
|Status|Badge|""--""|Active/Cancelled|Current status|
|Address|Text|""--""|Plaintext/URL|Address or virtual link|
|Maximum Attendees|Number|""--""|0|Max participant count|
|Created Date|Date|""--""|YYYY-MM-DD|When the event was created|
|Host Name|Text Link|""--""|Plaintext|Link to host profile|
|Tags|Text|""--""|Comma-separated|Event tags|
|About|Text|""--""|Plaintext|Event description|
|Event Access|Text|""--""|Free or Requires Ticket|Indicates if tickets are needed|

h4. Sold Tickets Table (Shown only when event requires tickets)

||Column Name||Data Type||Description||
|Ticket Number|Text|Unique ticket identifier|
|Owner|Text Link|Name of ticket buyer, links to User Details|
|Bought Date|Date|When the ticket was purchased|
|Action|Button|Send Ticket option|

h3. 5. Acceptance Criteria

* AC1: Event Details must display all fields defined in Create Event and Event List.
* AC2: Host Name must be a clickable link that opens the respective profile page.
* AC3: Sold Tickets table appears only when the event requires tickets.
* AC4: Send Ticket button is available for each sold ticket and triggers the defined sending workflow.
* AC5: Update Event and Delete Event buttons navigate to their respective screens.
* AC6: Back button returns to the Event List and preserves scroll position.

h3. 6. System Rules

* Details screen fetches the latest event data from backend API on load.
* Delete button is hidden or disabled when the event has sold tickets.
* Send Ticket action logs admin ID, timestamp, and ticket number.

h3. 7. UX Optimizations

* Display event information in clearly labeled sections for readability.
* Show skeleton loaders while data is loading.
* Keep the Update and Delete buttons sticky at the bottom for easy access.
* Provide pagination for Sold Tickets table if more than 25 tickets are sold.
* Highlight Send Ticket button on hover to encourage action.",,Duyen Nguyen,,712020:59b66c6e-1068-441c-9436-130289e16a26,,,,,,,,,,,,,,,,,com.atlassian.servicedesk.plugins.approvals.internal.customfield.ApprovalsCFValue@40f89123,,,,,,,,,{},,,,,,,,,,,,,,,,,,WEB,,,,0|i02r6v:,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,37186,AL-4,Events,In Progress,23/Jun/25 10:33 PM
[MB - Messages] Accept/Reject/Block Group Chat,AL-80,37933,Story,Specified,AL,AIOS Link,software,Kai Nguyễn,6214a4102d1a9d0069949c96,,Medium,,,,Duyen Nguyen,712020:59b66c6e-1068-441c-9436-130289e16a26,Duyen Nguyen,712020:59b66c6e-1068-441c-9436-130289e16a26,10/Jun/25 6:32 PM,10/Jun/25 7:29 PM,,,,0,"h3. 1. Use Case Description

As a user who has been added to a group chat, I want to accept or reject the invitation so that I can choose whether to join the conversation, and optionally block the group from ever re-adding me.

----

h3. 2. User Workflow (Step-by-Step)

# User receives a new group chat invitation — thread appears in Message list with “Pending Invite” tag.
# User taps on the group chat.
# System displays a full-screen prompt with 3 options:
#* *Accept Group Chat*
#* *Reject Group Chat*
#* *Block Group Chat*
# If *Accept* → System loads full conversation history, and user joins the chat.
#* If *Reject* → Group chat thread disappears from message list.
#* If *Block* → Group chat is removed from view, and user is permanently excluded from being re-added.
# System shows relevant success toast or confirmation.

----

h3. 3. Field Definitions Table

||Field Name||Field Type||Validation Rule||Mandatory||Description||
|Group Chat Thread|Chat Item|Status = ""Pending Invite""|Yes|Appears in message module as unread thread|
|Accept Button|CTA Button|Always visible|Yes|Joins chat and loads full history|
|Reject Button|CTA Button|Always visible|Yes|Removes thread from message list|
|Block Button|CTA Button|Always visible|Yes|Removes thread and prevents any future invitation to this chat|

----

h3. 4. Acceptance Criteria

*AC1.* When a user is added to a group, the group chat appears in the message list with a “Pending Invite” indicator.
*AC2.* On tapping the group, the system must show a modal or screen with 3 clear options: Accept, Reject, Block.
*AC3.* If the user accepts, full chat history becomes visible and user joins the chat.
*AC4.* If the user rejects, the group chat disappears from their message list.
*AC5.* If the user blocks, the group chat disappears and this group ID is added to a permanent exclusion list for that user.
*AC6.* A user who has blocked a group chat cannot be re-added to that group under any circumstance.
*AC7.* Toast must confirm each action:

* Accept: “You’ve joined the group chat.”
* Reject: “Group chat removed.”
* Block: “You’ve blocked this group chat.”


*AC8.* Group admin is not notified when a user rejects or blocks the group.

----

h3. 5. System Rules

* A group chat remains in a “Pending” state for a user until action is taken.
* Blocked group chat IDs must persist across sessions.
* Block action is final unless manually unblocked (handled in separate Blocked Groups screen if designed).
* Accepting adds user to participant list; rejecting or blocking does not.
* No system notification is sent to others regarding accept/reject/block.

----

h3. 6. UX Optimizations

* ""Pending Invite"" badge in chat list is styled distinct from unread badge.
* Modal design should clearly separate the three choices with icons and brief explanation.
* Once accepted, show chat history with a joining message like: “You joined the group.”
* Blocked group chats should not trigger notification, vibration, or badge count.",,Duyen Nguyen,,712020:59b66c6e-1068-441c-9436-130289e16a26,,,,,,,,,,,,,,,,,com.atlassian.servicedesk.plugins.approvals.internal.customfield.ApprovalsCFValue@6a9545df,,,,,,,,,{},,,,,,,,,,,,,,,,,,MOBILE,,,,0|i02r6n:,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,37255,AL-16,Messages,To Do,10/Jun/25 6:32 PM
[MB - Messages] Leave Group Chat,AL-79,37930,Story,Specified,AL,AIOS Link,software,Kai Nguyễn,6214a4102d1a9d0069949c96,,Medium,,,,Duyen Nguyen,712020:59b66c6e-1068-441c-9436-130289e16a26,Duyen Nguyen,712020:59b66c6e-1068-441c-9436-130289e16a26,10/Jun/25 6:32 PM,10/Jun/25 7:08 PM,,,,0,"h3. 1. Use Case Description

As a group chat participant, I want to leave the chat group so that I no longer receive messages or remain part of the conversation, and if I’m the admin, I want to transfer my admin rights before exiting.

----

h3. 2. User Workflow (Step-by-Step)

# User opens the group chat thread.
# Taps the menu (options) icon.
# Selects ""Leave Group"" from the options.
# System checks if the user is the group admin:
#* *If not an admin* → Proceed to Step 6.
#* *If admin* → Show a list of members to choose a new admin.
# User selects a new admin → Confirm.
# System shows confirmation modal:
“Are you sure you want to leave this group chat? You won’t receive any more messages unless re-added.”
# User confirms → system removes user and redirects to Message home.
# Other members continue conversation as normal.

----

h3. 3. Field Definitions Table

||Field Name||Field Type||Validation Rule||Mandatory||Description||
|Leave Group Button|CTA Button|Visible to all group members|Yes|Triggers exit from group chat|
|Transfer Admin Dropdown|Selection|Visible if current user is admin|Yes (if admin)|Must select one other member to become admin|

----

h3. 4. Acceptance Criteria

*AC1.* Any user can leave the group chat, including admin.
*AC2.* A confirmation modal must appear before exiting.
*AC3.* After leaving, user is removed from the group and redirected to Message home.
*AC4.* Conversation continues for remaining members.
*AC5.* Group chat persists unless explicitly deleted by a user from their own message module.
*AC6.* A toast message appears post-leave: “You’ve left the group chat.”
*AC7.* If the leaving user is the *admin*, they must assign another member as admin before exit.
*AC8.* The system will not allow admin to leave until transfer is confirmed.
*AC9.* Admin transfer selection must show members of the group excluding self.

----

h3. 5. System Rules

* Group persists with or without admin.
* Leaving member is removed from all participant lists.
* Messages sent prior to leaving remain visible to others.
* Admin rights must always belong to one member; cannot be empty.

----

h3. 6. UX Optimizations

* “Leave Group” button appears at the bottom of chat settings.
* If admin: transfer dropdown appears as step before confirmation modal.
* Admin dropdown excludes the current user.
* Toast appears after successful admin transfer: “Admin rights transferred. You have left the group.”",,Duyen Nguyen,,712020:59b66c6e-1068-441c-9436-130289e16a26,,,,,,,,,,,,,,,,,com.atlassian.servicedesk.plugins.approvals.internal.customfield.ApprovalsCFValue@4e760434,,,,,,,,,{},,,,,,,,,,,,,,,,,,MOBILE,,,,0|i02r6f:,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,37255,AL-16,Messages,To Do,10/Jun/25 6:32 PM
[MB - Events] Edit Event ,AL-78,37927,Story,In Progress,AL,AIOS Link,software,Kai Nguyễn,6214a4102d1a9d0069949c96,,Medium,,,,Duyen Nguyen,712020:59b66c6e-1068-441c-9436-130289e16a26,Duyen Nguyen,712020:59b66c6e-1068-441c-9436-130289e16a26,10/Jun/25 6:01 PM,25/Jun/25 10:19 AM,23/Jun/25 5:56 PM,,,0,"h4. 1. Use Case Description

As a host, I want to update my existing event details so that I can reflect real-time changes to attendees or event structure within allowed constraints.

h4. 2. User Workflow

# Host opens My Events and selects an event.
# Clicks “Edit” button.
# System loads form with pre-filled values.
# Host edits allowed fields.
# System applies constraints and saves changes.

h4. 3. Editable Field Rules

||Field Name||Editable||Conditions||
|Event Title|✅| |
|Date & Time|✅|Ticketed event: not editable|
|Duration|✅| |
|About|✅| |
|Visibility|✅|Joined or RSVP’s member accessibility to the event remains unchanged|
|Tags|✅| |
|Telegram Link|✅| |
|WhatsApp Link|✅| |
|Address|✅|Only if Event Type = In-person|
|Meeting Link|✅|Only if Event Type = Virtual|
|Max Attendees|✅|Can only reduce to the number of users already joined|
|Ticket Price|✅|Only if 0 tickets have been sold|

h4. 4. Acceptance Criteria

AC1. System loads event detail with editable fields populated.
AC2. Host can only reduce attendee limit to current joined count or higher.
AC3. Ticket price field is disabled if any ticket has been sold.
AC4. Host can change optional links and tags.
AC5. Boost settings must persist across edits.
AC6. Event reflects changes in Discover or My Events after update.

h4. 5. System Rules

* If max attendees is reduced, the system validates against joined user count.
* Ticket edit disables if sales > 0.
* Boosted events retain priority display.",,Duyen Nguyen,Phạm Xuân Định,712020:59b66c6e-1068-441c-9436-130289e16a26,63e3536328cddcc707748a66,,,,,,,,,,,,,,,,com.atlassian.servicedesk.plugins.approvals.internal.customfield.ApprovalsCFValue@3e2fc663,,,,,,,,,{},,,,,,,,,,,,,,,,,,MOBILE,,,,0|i02r67:,,,,,,,,,,,,,,,,,,,,,,,,,,2025-06-16 08:34:48.434,,16/Jun/25 3:34 PM;63e3536328cddcc707748a66;Done,,37186,AL-4,Events,In Progress,25/Jun/25 10:19 AM
[WEB - User Management] Delete User,AL-76,37778,Story,In Progress,AL,AIOS Link,software,Kai Nguyễn,6214a4102d1a9d0069949c96,,Medium,,,,Duyen Nguyen,712020:59b66c6e-1068-441c-9436-130289e16a26,Duyen Nguyen,712020:59b66c6e-1068-441c-9436-130289e16a26,09/Jun/25 4:27 PM,25/Jun/25 10:19 AM,,,,0,"h3. 1. Use Case Description

As an Admin, I want to soft-delete a user from the admin panel so that the user is no longer visible or active in the admin dashboard, while retaining their data in the system for audit, future reactivation, or compliance needs.

----

h3. 2. User Workflow (Step-by-Step)

# Admin logs into the Web Portal.
# Admin navigates to *User Management*.
# Admin searches or selects a user from the list.
# Admin clicks the *“⋮” (more options)* menu on the user row.
# Admin selects *“Delete User”*.
# System displays a confirmation modal:
“Are you sure you want to delete this user? They will no longer be visible on the admin dashboard.”
# Admin confirms the action.
# System updates the user record status to {{deleted}}.
# User is removed from the visible admin list.
# Success toast appears: “User deleted successfully.”

----

h3. 3. Field Definitions Table

||Field Name||Field Type||Validation Rule||Mandatory||Description||
|User ID|UUID|Must exist in system|Yes|Used for identifying user record|
|Status|Enum|[active, deactivated]|Yes|Set to {{deleted}} when soft-deleted|
|Deleted By|Admin ID|Must be current admin|Yes|Logs who performed the delete action|
|Deleted At|Timestamp|System generated datetime|Yes|For audit trail and recovery references|

----

h3. 4. Acceptance Criteria

*AC1*: Admin can access delete action via more options menu in User Management table.
*AC2*: Clicking “Delete User” prompts a confirmation modal with non-reversible warning.
*AC3*: Upon confirmation, system sets {{status = deleted}} for the user.
*AC4*: Deleted users no longer appear in the User Management UI or count toward active users.
*AC5*: System logs {{deleted_by}} and {{deleted_at}} metadata.
*AC6*: Toast message confirms successful action: “User deleted successfully”
*AC7*: Deleted users cannot log into the AIOS Link mobile app.
*AC8*: A deleted user’s record must still be available to backend services for audit and dependency resolution.

----

h3. 5. System Rules

* Deletion is *non-destructive*: the user record is retained in the database.
* User status transitions to {{deleted}}; does not remove authentication records or profile data.
* System must update related modules (e.g., remove from search, hide contact suggestions).
* All sessions and tokens of deleted user are revoked immediately.
* Audit logs store admin ID and timestamp of deletion action.

----

h3. 6. UX Optimizations

* Use red text color for “Delete” in dropdown to distinguish it as a destructive action.
* Modal includes an info icon with text: “You may restore this user from backend if needed.”
* Display strikethrough or ghost-style UI for deleted users in audit tables (future).
* Support “undo” toast (10s) only if deletion is recent and UI-permitted.",,Duyen Nguyen,,712020:59b66c6e-1068-441c-9436-130289e16a26,,,,,,,,,,,,,,,,,com.atlassian.servicedesk.plugins.approvals.internal.customfield.ApprovalsCFValue@3a5a390c,,,,,,,,,{},,,,,,,,,,,,,,,,,,WEB,,,,0|i02qh2:v,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,37761,AL-70,User Management,In Progress,25/Jun/25 10:19 AM
[WEB - User Management] Edit User Detail,AL-75,37775,Story,In Progress,AL,AIOS Link,software,Kai Nguyễn,6214a4102d1a9d0069949c96,,Medium,,,,Duyen Nguyen,712020:59b66c6e-1068-441c-9436-130289e16a26,Duyen Nguyen,712020:59b66c6e-1068-441c-9436-130289e16a26,09/Jun/25 4:26 PM,25/Jun/25 10:19 AM,,,,0,"h3. 1. Use Case Description

As an admin, I want to edit the profile details of an existing user so that I can correct or update their information while ensuring any sensitive changes (e.g. contact info) are securely verified.

----

h3. 2. User Workflow (Step-by-Step)

# Admin logs into the AIOS Link Web Portal.
# Admin navigates to *User Management > Users*.
# Admin selects a user and accesses the *User Detail View*.
# Admin clicks the *Edit* button.
# System opens the *Edit User Form* with all current values prefilled.
# Admin modifies one or more fields.
# If *email* or *phone number* is changed:
a. System marks the field for OTP verification.
b. A verification OTP will be sent to the new email/phone for user confirmation.
# Admin clicks *Save Changes*.
# System validates all inputs and applies changes.
# A success toast displays: “User details updated successfully.”
# If OTP is pending, status is shown as “Pending Verification” until confirmed.

----

h3. 3. Field Definitions Table

||Field Name||Field Type||Validation Rule||Mandatory||Notes||
|Full Name|Text|Max 50 characters|Yes|—|
|Email|Email|Must be valid format|Yes|OTP verification required if changed|
|Phone Number|Text|Valid phone format|Yes|OTP verification required if changed|
|Job Title|Text|Max 50 characters|Yes|—|
|Gender|Enum|Male, Female, Other|Yes|—|
|Industry|Text|Max 50 characters|No|—|
|Date of Birth|Date|Valid date|No|—|
|Address|Text|Max 100 characters|No|—|
|Bio|Text|Max 500 characters|No|—|
|Skills & Interests|Tag Chips|Max 15 entries|No|—|
|Profile Picture|Image Upload|JPG/PNG, 2MB max, camera option|No|—|
|LinkedIn URL|URL|Must match LinkedIn profile format|No|—|
|Telegram Link|Username|Must match Telegram handle format|No|—|
|WhatsApp Link|Phone-to-URL|Must be valid WhatsApp link format|No|—|
|Role|Enum|User / Merchant|Yes|Not editable|
|Organization Name|Text (linked)|Must be valid if role = Merchant|Conditional|Disabled if user is not merchant|
|Status|Enum|Active / Deactivated|Yes|—|

----

h3. 4. Acceptance Criteria

*AC1*: Admin can launch Edit User modal or view from User Detail page.
*AC2*: All fields except role and organization are editable.
*AC3*: Editing email or phone triggers system to mark the field for OTP confirmation.
*AC4*: OTP is automatically sent to the new contact info upon saving.
*AC5*: Changes to verified fields are not reflected until OTP is confirmed by the user.
*AC6*: If OTP is not confirmed, system displays “Pending Verification” in admin view.
*AC7*: Form validations are enforced for all mandatory fields.
*AC8*: Toast shows “User details updated successfully” upon successful validation and save.
*AC9*: If changes are canceled mid-edit, no updates are saved and confirmation is shown.

----

h3. 5. System Rules

* OTP count limit: max 5 requests/day per contact method.
* OTP expiration: 30 minutes.
* Only unverified fields are flagged for re-verification.
* Admin cannot modify user’s role or ownership assignment.
* Organization selection is locked unless role is Merchant.",,Duyen Nguyen,,712020:59b66c6e-1068-441c-9436-130289e16a26,,,,,,,,,,,,,,,,,com.atlassian.servicedesk.plugins.approvals.internal.customfield.ApprovalsCFValue@408c4e1,,,,,,,,,{},,,,,,,,,,,,,,,,,,WEB,,,,0|i02qh2:r,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,37761,AL-70,User Management,In Progress,25/Jun/25 10:19 AM
[WEB - User Management] View User Detail,AL-74,37772,Story,In Progress,AL,AIOS Link,software,Kai Nguyễn,6214a4102d1a9d0069949c96,,Medium,,,,Duyen Nguyen,712020:59b66c6e-1068-441c-9436-130289e16a26,Duyen Nguyen,712020:59b66c6e-1068-441c-9436-130289e16a26,09/Jun/25 4:26 PM,25/Jun/25 10:19 AM,,,,0,"h3. 1. Use Case Description

As an admin, I want to view detailed information of a registered user so that I can assess their status, role, and data completeness for auditing or support purposes.

----

h3. 2. User Workflow (Step-by-Step)

# Admin logs into the AIOS Link Web Portal.
# Admin navigates to *User Management > Users*.
# Admin selects a user from the list by clicking on the row.
# System opens a *User Detail view* in a modal or new page.
# System displays all relevant user information grouped by sections:
#* Personal Info
#* Contact Info
#* Professional Info
#* System Info
# If a field has not been filled by the user, system displays a placeholder (e.g., “Not Provided”).
# Admin can close the view or navigate back to the user list.

----

h3. 3. Field Definitions Table

||Field Name||Field Type||Validation Rule||Mandatory||Data Shown When Empty||
|Full Name|Text|Max 50 characters|Yes|Not Provided|
|Email|Email|Valid format|Yes|Not Provided|
|Phone Number|Text|Valid phone number|Yes|Not Provided|
|Job Title|Text|Max 50 characters|Yes|Not Provided|
|Gender|Enum|Male, Female, Other|Yes|Not Specified|
|Industry|Text|Max 50 characters|No|Not Provided|
|Date of Birth|Date|Valid date|No|Not Provided|
|Address|Text|Max 100 characters|No|Not Provided|
|Bio|Text|Max 500 characters|No|No Bio Added|
|Skills & Interests|Tag Chips|Max 15 entries|No|None Listed|
|Profile Picture|Image|JPG/PNG, 2MB, uploaded or camera taken|No|Default Avatar|
|LinkedIn URL|URL|Must be valid LinkedIn profile URL|No|Not Provided|
|Telegram Link|Username|Must match Telegram handle format|No|Not Provided|
|WhatsApp Link|Phone-to-URL|Must be valid WhatsApp URL|No|Not Provided|
|Role|Enum|User / Merchant|Yes|—|
|Organization Name|Text (linked)|If role = Merchant, must be linked org|Conditional|Not Linked|
|Account Status|Enum|Active / Deactivated|Yes|—|

----

h3. 4. Acceptance Criteria

* *AC1*: Admin can access the full detail view of any user from the list.
* *AC2*: All populated fields are shown with labels and read-only values.
* *AC3*: Any field not filled in by the user is labeled clearly using the “Data Shown When Empty” values.
* *AC4*: If the user has no profile picture, a default avatar is displayed.
* *AC5*: All social link fields are displayed as plain text URLs if provided.
* *AC6*: Admin cannot edit values from the view screen (read-only mode).
* *AC7*: A close or back button must allow admin to return to the main user list.

----

h3. 5. System Rules

* Data is retrieved from backend user profile and cached for 5 minutes to improve performance.
* Empty fields must never display as blank – use system-provided defaults.
* The user’s role determines if the organization name should be shown.
* The view is responsive and mobile-compatible for narrow screens.",,Duyen Nguyen,,712020:59b66c6e-1068-441c-9436-130289e16a26,,,,,,,,,,,,,,,,,com.atlassian.servicedesk.plugins.approvals.internal.customfield.ApprovalsCFValue@2f707030,,,,,,,,,{},,,,,,,,,,,,,,,,,,WEB,,,,0|i02qh2:i,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,37761,AL-70,User Management,In Progress,25/Jun/25 10:19 AM
[WEB - User Management] Create User,AL-73,37769,Story,In Progress,AL,AIOS Link,software,Kai Nguyễn,6214a4102d1a9d0069949c96,,Medium,,,,Duyen Nguyen,712020:59b66c6e-1068-441c-9436-130289e16a26,Duyen Nguyen,712020:59b66c6e-1068-441c-9436-130289e16a26,09/Jun/25 4:25 PM,17/Jun/25 12:33 AM,,,,0,"1. Use Case Description

As an Admin, I want to create a user account and optionally associate them with an organization (if applicable), so that the account is properly linked to business entities and complies with role-based permissions and ownership rules.

----

h3. 2. User Workflow (Step-by-Step)

# Admin logs into the AIOS Link Web Portal.
# Admin navigates to *User Management > Users*.
# Admin clicks the *“Create User”* button.
# System displays the *Create User form*.
# Admin completes the user details including role and all mandatory fields.
# Below the role dropdown, system displays the *Organization Selection section*:
#* If role = *User*, organization linking is optional and can be skipped.
#* If role = *Merchant*, system enforces selection of an organization.
#* If an organization is already owned by another user, its selection card is disabled (grayed out).
# Admin selects an available organization for Merchant user.
# Admin clicks *Save*.
# System validates all fields and:
#* Assigns the user as *Owner* of selected organization if role = Merchant.
#* Sets account status = {{pending_activation}}.
# System sends activation OTP email to the user.
# A success toast is displayed: “User created successfully.”

----

h3. 3. Field Definitions Table

||Field Name||Field Type||Validation Rule||Mandatory||Description||
|Full Name|Text Input|Max 50 characters|Yes|User's legal name|
|Email|Email Input|Valid format, must not already exist|Yes|Unique identifier|
|Phone Number|Text Input|Valid phone number|Yes|For contact & identity|
|Password|Password Input|≥8 chars, 1 uppercase, 1 lowercase, 1 number, 1 special char|Yes|Temporary system password|
|Role|Dropdown|One of: [User, Merchant]|Yes|Defines permission scope|
|Organization|Dropdown/List|Select from available organizations (disabled if owned)|Conditional*|Required if role = Merchant|
|Job Title|Text Input|Max 50 characters|Yes|User's title or professional role|
|Gender|Enum|Male, Female, Other|Yes|Self-reported gender|
|Industry|Text Input|Max 50 characters|No|Field of work|
|Date of Birth|Date Picker|Valid date|No|Used for profile completion|
|Address|Text Area|Max 100 characters|No|Private|
|Bio|Text Area|Max 500 characters|No|Public summary|
|Skills & Interests|Tag Selector|Up to 15 entries|No|User interests or specialties|
|LinkedIn URL|URL Input|Must be valid LinkedIn profile|No|Clickable icon|
|Telegram Link|Text Input|Must be valid handle format|No|Contact channel|
|WhatsApp Link|Text Input|Must be valid URL format|No|Contact channel|
|Profile Picture|Image Upload|JPG/PNG, max 2MB, camera allowed|No|Visible in profile|

* Conditional: Required only if role = Merchant

----

h3. 4. Acceptance Criteria

* *AC1*: Admin can only create a user if all mandatory fields are valid.
* *AC2*: If role = Merchant, an organization *must* be selected.
* *AC3*: Organization dropdown only lists *organizations without owners*.
* *AC4*: Cards for already-owned organizations are visible but *grayed out and disabled*.
* *AC5*: If role = User, system allows skipping the organization field.
* *AC6*: On successful form submission:
** User is created with status = {{pending_activation}}
** Activation OTP is sent to email.
* *AC7*: User is shown in the user list with correct role and organization (if applicable).
* *AC8*: System stores the user-role-organization mapping persistently.
* *AC9*: If merchant user is linked to an org, they are automatically set as “Owner.”
* *AC10*: A success toast appears: “User created successfully.”

----

h3. 5. System Rules

* Email is the primary unique identifier.
* Each *organization can have only one Owner*.
* Users can only log into *mobile app*, not web portal.
* OTP is valid for 30 minutes and resending is allowed after 60s, max 5 per day.
* Created user cannot access the dashboard until activation is complete.
* Audit logs store record of which admin created the user, timestamp, and assigned role/org.

----

h3. 6. UX Optimizations

* Organization selection uses *card grid with search + disabled states* for owned orgs.
* Display tooltip:
“This organization already has an owner and cannot be selected.”
* Role selection dynamically toggles the organization field visibility and rules.
* Save button remains disabled until required conditions (incl. role/org logic) are satisfied.
* Toasts and inline error messages guide completion without form reload.",,Duyen Nguyen,,712020:59b66c6e-1068-441c-9436-130289e16a26,,,,,,,,,,,,,,,,,com.atlassian.servicedesk.plugins.approvals.internal.customfield.ApprovalsCFValue@1b21767f,,,,,,,,,{},,,,,,,,,,,,,,,,,,WEB,,,,0|i02qh2:,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,37761,AL-70,User Management,In Progress,12/Jun/25 10:45 AM
[WEB - User Management] Activate/ Deactivate User,AL-72,37766,Story,In Progress,AL,AIOS Link,software,Kai Nguyễn,6214a4102d1a9d0069949c96,,Medium,,,,Duyen Nguyen,712020:59b66c6e-1068-441c-9436-130289e16a26,Duyen Nguyen,712020:59b66c6e-1068-441c-9436-130289e16a26,09/Jun/25 4:25 PM,25/Jun/25 10:19 AM,,,,0,"h3. 1. Use Case Description

As an Admin, I want to activate or deactivate user accounts so that I can manage platform access for compliance, moderation, or organizational control purposes.

----

h3. 2. User Workflow (Step-by-Step)

*Flow 1: Quick Action via User List Table*

# Admin logs into the AIOS Link Web Portal.
# Admin navigates to *User Management > Users*.
# Admin locates a user in the user table.
# Admin clicks the *action icon* (e.g. three-dot menu) for that user row.
# System displays either *“Deactivate”* or *“Activate”* option depending on current status.
# Admin selects the action → a confirmation modal appears.
# Admin confirms → system updates the user’s status.
# System shows a success toast and updates the list view instantly.

*Flow 2: Action via User Detail Page*

# Admin navigates to *User Management > Users*.
# Admin clicks a user row to open *User Detail View*.
# System loads full user profile in a modal or new view.
# At the top or in the action section, system displays *Activate* or *Deactivate* button based on status.
# Admin clicks the button → confirmation modal appears.
# Admin confirms → system updates the user’s status and reflects change in UI.
# A success toast appears, and status is reflected in both detail view and main list.

----

h3. 3. Field Definitions Table

||Field Name||Field Type||Validation Rule||Mandatory||Description||
|Status|Enum|One of: {{Active}}, {{Deactivated}}|Yes|Current account access state|

----

h3. 4. Acceptance Criteria

* *AC1*: If a user is currently {{Active}}, only the *Deactivate* action is available.
* *AC2*: If a user is currently {{Deactivated}}, only the *Activate* action is available.
* *AC3*: After confirming deactivation, user status is updated to {{Deactivated}} and row is styled accordingly.
* *AC4*: After confirming activation, user status is updated to {{Active}}.
* *AC5*: A confirmation modal appears before activation or deactivation with custom message:
** _Deactivate modal:_ “Are you sure you want to deactivate this user? They will lose access immediately.”
** _Activate modal:_ “Re-activate this user account and allow login?”
* *AC6*: A success toast appears:
** “User account deactivated.”
** “User account activated.”
* *AC7*: Audit trail is updated with action type, admin ID, timestamp, and user ID.
* *AC8*: Deactivated users:
** Cannot sign into the mobile app.
** Cannot access user-only routes if authenticated.
* *AC9*: Activation does not trigger a welcome email or verification resend.
* *AC10*: If an error occurs (e.g., network or backend error), display appropriate toast:
** “Action failed. Please try again.”

----

h3. 5. System Rules

* Soft status toggle: {{Active ↔ Deactivated}}, data is never deleted.
* Admin must have {{manage_user_status}} permission to perform the action.
* Status toggle must be atomic (1 click, 1 transaction).
* Users with status {{Deactivated}} are excluded from active contact recommendations and smart suggestions.
* Status change is immediately reflected in both the web and mobile environments.

----

h3. 6. UX Optimizations

* In-row status toggle (e.g. dropdown or action icon) or batch action support (optional).
* Status indicator (pill UI) color-coded:
** Green = Active
** Gray = Deactivated
* Filter bar includes a quick toggle: {{[ All | Active | Deactivated ]}}.",,Duyen Nguyen,,712020:59b66c6e-1068-441c-9436-130289e16a26,,,,,,,,,,,,,,,,,com.atlassian.servicedesk.plugins.approvals.internal.customfield.ApprovalsCFValue@63a7316b,,,,,,,,,{},,,,,,,,,,,,,,,,,,WEB,,,,0|i02qh1:,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,37761,AL-70,User Management,In Progress,25/Jun/25 10:19 AM
[WEB - User Management] User List,AL-71,37763,Story,In Progress,AL,AIOS Link,software,Kai Nguyễn,6214a4102d1a9d0069949c96,,Medium,,,,Duyen Nguyen,712020:59b66c6e-1068-441c-9436-130289e16a26,Duyen Nguyen,712020:59b66c6e-1068-441c-9436-130289e16a26,09/Jun/25 4:25 PM,25/Jun/25 10:19 AM,19/Jun/25 4:47 PM,,,0,"h3. 1. Use Case Description

As an Admin, I want to view a list of all users on the AIOS Link platform so that I can monitor account status, manage user records, and take actions such as view, edit, or deactivate users efficiently from a centralized dashboard.

----

h3. 2. User Workflow (Step-by-Step)

# Admin logs into the AIOS Link Web Portal.
# Admin navigates to *User Management > Users*.
# The system loads a paginated list of all active and deactivated users.
# Admin can:
#* Search by name, email, or phone number.
#* Filter users by role and status.
#* Click a row to view full user details.
#* Take row-level actions: *View*, *Edit*, *Deactivate*.

----

h3. 3. Field Definitions Table (User List View)

||Field Name||Field Type||Description||Mandatory||Displayed in List||
|Full Name|Text|User’s registered full name|Yes|✅|
|Email|Text (Email)|Unique email used for login|Yes|✅|
|Phone Number|Text|Registered phone number|Yes|✅|
|Role|Enum|User / Merchant|Yes|✅|
|Status|Enum|*Active / Deactivated*|Yes|✅|
|Created Date|DateTime|Account registration timestamp|Yes|✅|
|Last Login|DateTime|Timestamp of last successful login (if any)|No|✅|
|Organization Name|Text|Merchant account org name (if applicable)|No|✅ (if available)|

----

h3. 4. Acceptance Criteria

* *AC1*: User list is rendered as a paginated table with default sort by Created Date descending.
* *AC2*: Each user row displays key information including Status, Role, and Organization if available.
* *AC3*: Clicking a user row opens the full User Detail view in a side drawer/modal.
* *AC4*: Admin can take row-level actions including:
** *View* – see full profile info
** *Edit* – update user details
** *Deactivate* – soft-delete user from list view
* *AC5*: Deactivated users do not appear by default unless explicitly filtered for.
* *AC6*: System supports 20 users per page with pagination controls at the bottom.
* *AC7*: If no results match filters or search, system shows: “No users found.”

----

h3. 5. Search & Filter Rules

*Search Rules*:

* Admin can search across:
** Full Name (partial match)
** Email (exact match)
** Phone Number (exact match)
* Search is case-insensitive and persistent between pagination.

*Filter Rules*:

* Role: {{User}}, {{Merchant}}
* Status: {{Active}}, {{Deactivated}}
* Filters and search can be combined.

----

h3. 6. System Rules

* Only users with {{view_user_list}} permission can access this feature.
* Soft deletion is enforced: Deactivated users are excluded from default view but retained in the system.
* Admin actions (Edit, Deactivate) are audit-logged with timestamps and actor ID.
* All dates are shown in admin's local timezone (fallback to UTC).
* Organization column displays only for Merchant accounts.

----

h3. 7. UX Optimizations

* Sticky search and filter header bar.
* Row hover reveals quick actions: *View*, *Edit*, *Deactivate*.
* Colored status pills:
** {{Active}} – green
** {{Deactivated}} – gray
* Sortable columns: Name, Status, Created Date, Last Login.
* Responsive layout for all device sizes.",,Duyen Nguyen,,712020:59b66c6e-1068-441c-9436-130289e16a26,,,,,,,,,,,,,,,,,com.atlassian.servicedesk.plugins.approvals.internal.customfield.ApprovalsCFValue@426336ef,,,,,,,,,{},,,,,,,,,,,,,,,,,,WEB,,,,0|i02qgz:,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,37761,AL-70,User Management,In Progress,25/Jun/25 10:19 AM
[MB - Meeting Scheduling] Invite to Meet,AL-69,37638,Story,Specified,AL,AIOS Link,software,Kai Nguyễn,6214a4102d1a9d0069949c96,,Medium,,,,Duyen Nguyen,712020:59b66c6e-1068-441c-9436-130289e16a26,Duyen Nguyen,712020:59b66c6e-1068-441c-9436-130289e16a26,06/Jun/25 2:25 PM,15/Jun/25 11:00 PM,,,,0,"h3. 1. Use Case Description

As a mobile user, I want to invite any contact to meet by specifying a date, time, and meeting method (in person or virtual), so that I can coordinate meetings easily.

----

h3. 2. User Workflow (Step-by-Step)

h4. A. From Home Dashboard

# User taps the *“Invite to Meet”* CTA on the home dashboard.
# System displays the *full contact list*.
# User selects a contact.
# User proceeds to the *Meeting Setup Screen*:
#* Selects *Meeting Type*: In Person or Virtual
#* Chooses *Date* via date picker
#* Chooses *Time* via time picker
#* If In Person → enters *Address*
#* If Virtual → enters *Meeting Link*
# User taps *Send Invite*.
# System sends the invitation to the recipient and shows a confirmation.

h4. B. From Contact Profile

# User views a contact’s profile.
# User taps *“Invite to Meet”*.
# System opens the *Meeting Setup Screen* as described above.
# User completes and submits the invite.

----

h3. 3. Field Definitions Table

h4. Input Fields

||Field Name||Field Type||Required||Validation Rules||Description||
|Contact|Selector|✅|Must be from user's contact/lead list|Person to invite|
|Meeting Type|Radio Button|✅|“In Person” or “Virtual”|Format of the meeting|
|Date|Date Picker|✅|Must be today or future|Scheduled date|
|Time|Time Picker|✅|Any valid 24-hour format|Scheduled time|
|Address|Text Field|✅ if In Person|5–100 characters|Physical meeting location|
|Meeting Link|URL Input|✅ if Virtual|Must be a valid URL|Link to video meeting|

h4. Interaction Elements

||Element Name||Type||Trigger Condition||Description||
|Invite to Meet CTA|Button|On home dashboard|Starts the invite workflow|
|Contact Profile CTA|Button|On individual profile|Opens the meeting setup screen|
|Send Invite Button|Button|Enabled when form is complete|Sends the invitation|

----

h3. 4. Data Display Table (Card View)

||Display Field||Data Type||Display When Empty||Format Example||Description||
|Contact Name|Text|“Not selected”|“John D.”|Name of the invite recipient|
|Meeting Type|Badge|“TBD”|“In Person” / “Virtual”|Selected meeting format|
|Date & Time|Datetime|“Not Scheduled”|“Jun 14, 15:00”|Meeting date and time|
|Address/Link|Text/URL|“Not Provided”|Location or URL|Based on meeting type|

----

h3. 5. Acceptance Criteria

* *AC1*: User must be able to invite any contact without availability constraints.
* *AC2*: Invite requires meeting type, date, time, and corresponding location/link.
* *AC3*: Tapping “Send Invite” delivers a complete meeting request to the recipient.
* *AC4*: Recipient receives notification and can view invite details.
* *AC5*: System displays a success message after the invite is sent.

----

h3. 6. System Rules

* No availability check is performed; all contacts are always selectable.
* Invite metadata is stored with sender ID, recipient ID, meeting type, date/time, and status=pending.
* Invite is delivered via in-app notification (and optionally email or push).
* Sender cannot send duplicate invites with the same date/time/contact combination.

----

h3. 7. UX Optimizations

* Full contact list shown alphabetically.
* Autofocus on date after contact is selected.
* Show confirmation summary: _“Invite [Contact Name] to meet on [Date, Time] via [Type]?”_
* Toast: _“Invite sent to [Name] successfully.”_
* Meeting type icons help visually distinguish in-person vs virtual in preview.",,Duyen Nguyen,,712020:59b66c6e-1068-441c-9436-130289e16a26,,,,,,,,,,,,,,,,,com.atlassian.servicedesk.plugins.approvals.internal.customfield.ApprovalsCFValue@36ce0e99,,,,,,,,,{},,,,,,,,,,,,,,,,,,MOBILE,,,,0|i02qxz:,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,37636,AL-68,Meeting Scheduling,To Do,06/Jun/25 2:25 PM
[WEB - Authentication] Remember Me,AL-64,37521,Story,In Progress,AL,AIOS Link,software,Kai Nguyễn,6214a4102d1a9d0069949c96,,Medium,,,,Duyen Nguyen,712020:59b66c6e-1068-441c-9436-130289e16a26,Duyen Nguyen,712020:59b66c6e-1068-441c-9436-130289e16a26,05/Jun/25 3:30 PM,23/Jun/25 2:40 PM,23/Jun/25 9:35 PM,,,0,"h3. 1. Use Case Description

As a registered admin user, I want the option to stay signed in by using a ""Remember Me"" checkbox on the sign-in form, so that I can avoid re-entering my credentials every time I access the admin dashboard from a trusted device.

----

h3. 2. User Workflow (Step-by-Step)

* Step 1: Admin navigates to the Sign In page.
* Step 2: Admin enters valid email and password credentials.
* Step 3: Admin optionally checks the “Remember Me” checkbox.
* Step 4: Admin taps “Sign In.”
* Step 5: If “Remember Me” is selected and credentials are valid:
** System stores a long-lived secure session token or cookie on the browser.
** Admin remains logged in for up to 30 days or until manual logout.
* Step 6: If “Remember Me” is not selected:
** Session expires after the default timeout (e.g., 30 minutes of inactivity).
* Step 7: On return visit within the valid session period, admin is auto-authenticated and redirected to the dashboard.
* Step 8: Admin can log out at any time to invalidate the remembered session.

----

h3. 3. Field Definitions

h4. Input Fields

||Field Name||Field Type||Required||Validation Rules||Description||
|Email|Text|✅|Valid email format|Registered admin email|
|Password|Password|✅|≥8 chars, 1 upper, 1 lower, 1 number, 1 special character|Admin password|
|Remember Me|Checkbox|❌|Optional boolean|Enables persistent session logic|

h4. Interaction Elements

||Element Name||Type||Trigger Condition||Description||
|Sign In Button|Button|Enabled when fields valid|Submits login credentials|
|Remember Me Checkbox|Checkbox|Always visible|Sets a persistent authentication flag|

----

h3. 4. Data Display Table (Card View)

||Display Element||Format Example||Notes||
|Sign In Form|Email, Password, Checkbox|Form shows all fields including “Remember Me”|
|Cookie/Session Timer|{{Expires in 30 days}}|Shown in browser dev tools only|

----

h3. 5. Acceptance Criteria

* *AC1*: “Remember Me” must be unchecked by default.
* *AC2*: If selected, admin remains signed in for up to 30 days unless manually logged out.
* *AC3*: If not selected, session expires after inactivity timeout (default: 30 min).
* *AC4*: Persistent session must be stored securely using HttpOnly, Secure cookies or token-based mechanism.
* *AC5*: Admin must be redirected to dashboard if the session is still valid.
* *AC6*: Logging out clears all remembered sessions.
* *AC7*: Feature must not work on public/shared devices where local storage is disabled.

----

h3. 6. System Rules

* Session persistence tied to browser/device and not shared across devices.
* System auto-invalidates the token after expiration or logout.
* Device-specific tokens must be managed per admin account in backend.

----

h3. 7. UX Optimizations

* “Remember Me” uses a clearly labeled checkbox placed near the Sign In CTA.
* Add info tooltip: “Keep me signed in for up to 30 days on this device.”
* Auto-fill previously remembered sessions if still valid (no visible login screen).
* Optionally prompt for re-authentication if suspicious activity is detected (e.g., location change).",,Duyen Nguyen,,712020:59b66c6e-1068-441c-9436-130289e16a26,,,,,,,,,,,,,,,,,com.atlassian.servicedesk.plugins.approvals.internal.customfield.ApprovalsCFValue@6e661954,,,,,,,,,"{repository={count=2, dataType=repository}, json={""cachedValue"":{""errors"":[],""summary"":{""repository"":{""overall"":{""count"":2,""lastUpdated"":""2025-06-06T14:12:03.000+0700"",""dataType"":""repository""},""byInstanceType"":{""GitHub"":{""count"":2,""name"":""GitHub""}}}}},""isStale"":true}}",,,,,,,,,,,,,,,,,,WEB,,,,0|i02qvr:,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,37144,AL-1,Authentication,In Progress,23/Jun/25 2:40 PM
[WEB - Authentication] Sign In as Admin,AL-62,37515,Story,In Progress,AL,AIOS Link,software,Kai Nguyễn,6214a4102d1a9d0069949c96,,Medium,,,,Duyen Nguyen,712020:59b66c6e-1068-441c-9436-130289e16a26,Duyen Nguyen,712020:59b66c6e-1068-441c-9436-130289e16a26,05/Jun/25 3:29 PM,23/Jun/25 2:40 PM,23/Jun/25 9:35 PM,,,0,"h3. 1. Use Case Description

As an admin user, I want to securely sign into the AIOS Link web platform using my registered email and password, so that I can access administrative dashboards, manage content, and oversee platform configurations.

----

h3. 2. User Workflow

* Step 1: Admin navigates to the AIOS Link Admin URL (e.g., {{admin.aios.link}}).
* Step 2: System displays the Sign In screen with Email and Password fields.
* Step 3: Admin enters valid credentials and clicks “Sign In.”
* Step 4: System validates credentials against the Admin database.
* Step 5: If valid, system redirects to the Admin Dashboard.
* Step 6: If invalid, system displays an error message with retry option.
* Step 7: Admin may use the “Forgot Password” link to reset credentials.

----

h3. 3. Field Definitions

h4. Input Fields

||Field Name||Field Type||Validation Rule||Mandatory||Description||
|Email|Text|Valid email format|Yes|Registered admin email|
|Password|Password|Must be more than 8 characters, including at least 1 uppercase, 1 lowercase, 1 number, and 1 special character|Yes|Login credential|

h4. Interaction Elements

||Element Name||Type||Trigger Condition||Description||
|Sign In Button|Button|Enabled if both fields valid|Submits login credentials for verification|
|Forgot Password Link|Link|Always visible|Redirects to password recovery screen|

----

h3. 4. Data Display (Sign In View)

||Data Name||Data Type||Display When Empty||Format||Description||
|Error Message|Text|Hidden|Plaintext|E.g., “Invalid email or password.”|
|Loading Indicator|Spinner|Hidden|Spinner|Shown while validating credentials|

----

h3. 5. Acceptance Criteria

* AC1: Sign In screen must load at the correct admin URL.
* AC2: Both email and password must be mandatory for submission.
* AC3: Invalid credentials must display appropriate error messages.
* AC4: On successful sign-in, admin is redirected to {{/dashboard}}.
* AC5: “Forgot Password” must redirect to {{/forgot-password}}.
* AC6: Inputs must be masked and protected against XSS/SQLi.
* AC7: Session cookies must use HttpOnly and Secure flags.

----

h3. 6. System Rules

* Email must match an active admin account.
* Password is hashed and verified via secure auth service.
* Lockout after 5 failed attempts (5-min cooldown).
* All sessions use secure JWT or session cookies.
* Admin accounts are managed exclusively through internal provisioning.

----

h3. 7. UX Optimizations

* Auto-focus cursor on Email field.
* Pressing Enter submits form when fields are valid.
* Display loader and disable button during authentication.
* Responsive layout for desktop and tablet.
* Display link to support/help for login issues.",,Duyen Nguyen,,712020:59b66c6e-1068-441c-9436-130289e16a26,,,,,,,,,,,,,,,,,com.atlassian.servicedesk.plugins.approvals.internal.customfield.ApprovalsCFValue@2f72db42,,,,,,,,,"{pullrequest={dataType=pullrequest, state=DECLINED, stateCount=1}, json={""cachedValue"":{""errors"":[],""summary"":{""pullrequest"":{""overall"":{""count"":1,""lastUpdated"":""2025-06-11T18:20:28.000+0700"",""stateCount"":1,""state"":""DECLINED"",""dataType"":""pullrequest"",""open"":false},""byInstanceType"":{""GitHub"":{""count"":1,""name"":""GitHub""}}}}},""isStale"":true}}",,,,,,,,,,,,,,,,,,WEB,,,,0|i02qvb:,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,37144,AL-1,Authentication,In Progress,23/Jun/25 2:40 PM
[MB - User Profile] Edit My Profile,AL-61,37512,Story,In Progress,AL,AIOS Link,software,Kai Nguyễn,6214a4102d1a9d0069949c96,,Medium,,,,Duyen Nguyen,712020:59b66c6e-1068-441c-9436-130289e16a26,Duyen Nguyen,712020:59b66c6e-1068-441c-9436-130289e16a26,05/Jun/25 1:44 PM,25/Jun/25 10:22 AM,,,,0,"h3. 1. Use Case Description

As a user, I want to edit and update my personal and professional profile information
so that I can keep my data current and control how I appear to others on AIOS Link.
However, my email and phone number will be view-only and cannot be changed via this form.

----

h3. 2. User Workflow (Step-by-Step)

# User opens the *Profile* tab and taps *“Edit Profile”*.
# System loads the *Edit Profile* form with current values populated.
# User modifies any allowed fields (e.g. name, bio, links, avatar).
# *Email and Phone* fields are displayed in disabled state (read-only, uneditable).
# User optionally updates avatar via file picker or uses the camera.
# User taps *Save*.
# System validates changes and updates the profile.
# Updated data is reflected in the *My Profile* screen.
# A toast confirms: _“Profile updated successfully.”_

----

h3. 3. Field Definitions Table (Updated)

||Field Name||Field Type||Validation Rule||Mandatory||Visibility||Editable||
|Full Name|Text Input|Max 50 characters|Yes|Public|Yes|
|Email|Email Input|Valid format, non-editable in this feature|Yes|Private (self only)|No|
|Phone Number|Phone Input|Valid format, non-editable in this feature|Yes|Private (self only)|No|
|Job Title|Text Input|Max 50 characters|Yes|Public|Yes|
|Gender|Enum|Male, Female, Other|No|Public|Yes|
|Industry|Text Input|Max 50 characters|No|Public|Yes|
|Date of Birth|Date Picker|Must be valid date|No|Private|Yes|
|Address|Text Area|Max 100 characters|No|Private|Yes|
|Bio|Text Area|Max 500 characters|No|Public|Yes|
|Skills & Interests|Tag Selector|Max 15 entries|No|Public|Yes|
|Profile Picture|Image Upload|JPG/PNG, 2MB max, cropper, camera enabled|No|Public|Yes|
|LinkedIn URL|URL Input|Must be valid LinkedIn profile URL|No|Public (icon only)|Yes|
|Telegram Link|Username|Must match Telegram handle format|No|Public (icon only)|Yes|
|WhatsApp Link|Phone-to-URL|Must match valid WhatsApp link format|No|Public (icon only)|Yes|

----

h3. 4. Data Display Table (Grouped Fields)

||Section||Fields Included||
|Personal Info|Full Name, Gender, DOB, Address|
|Contact Info|Phone (view-only), Email (view-only)|
|Professional Info|Job Title, Industry, Skills & Interests, Bio|
|Profile Media|Avatar with upload & camera|
|Social Links|LinkedIn, Telegram, WhatsApp|

----

h3. 5. Acceptance Criteria

* AC1: Only editable fields are Full Name, Position, Bio, Skills, Address, DOB, Gender, Industry, Social Links, and Avatar.
* AC2: Email and Phone are visible but disabled (read-only).
* AC3: Avatar can be updated from gallery or camera.
* AC4: Bio input supports up to 500 characters with live character counter.
* AC5: Skills input allows up to 15 tag-style entries.
* AC6: On save, all valid edits are persisted to the profile.
* AC7: Toast shows: _“Profile updated successfully.”_
* AC8: If user tries to leave with unsaved changes, a modal appears:
_“You have unsaved changes. Discard or continue editing?”_

----

h3. 6. System Rules

* Email and phone are system-bound identifiers and can only be changed via a separate flow.
* Avatar upload supports crop, compression, and camera for mobile devices.
* Social icons are only displayed if the corresponding field is filled.
* OTP verification is not triggered because email and phone are not editable.

----

h3. 7. UX Optimizations

* Email and phone fields appear visually consistent but disabled (greyed input state).
* Editable fields display real-time validation and helper texts.
* ""Take Photo"" or ""Choose from Gallery"" appears when updating avatar.
* Save button is sticky and only becomes active when changes are detected.",,Duyen Nguyen,,712020:59b66c6e-1068-441c-9436-130289e16a26,,,,,,,,,,,,,,,,,com.atlassian.servicedesk.plugins.approvals.internal.customfield.ApprovalsCFValue@49c60f6e,,,,,,,,,{},,,,,,,,,,,,,,,,,,MOBILE,,,,0|i02qv3:,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,37474,AL-58,User Profile,In Progress,25/Jun/25 10:22 AM
[MB - User Profile] View My Profile,AL-60,37509,Story,In Progress,AL,AIOS Link,software,Kai Nguyễn,6214a4102d1a9d0069949c96,,Medium,,,,Duyen Nguyen,712020:59b66c6e-1068-441c-9436-130289e16a26,Duyen Nguyen,712020:59b66c6e-1068-441c-9436-130289e16a26,05/Jun/25 1:43 PM,25/Jun/25 10:22 AM,,,,0,"h3. 1. Use Case Description

As a user, I want to view and manage my own profile
so that I can ensure my information is complete, accurate, and professional, and control what others can see when they view my public profile.

----

h3. 2. User Workflow (Step-by-Step)

# After successful first-time login, user is redirected to a *Profile Completion screen*.
# User fills out missing fields and submits to complete onboarding.
# After onboarding or from later sessions, user navigates to *Profile tab*.
# System displays the full *My Profile* view with:
#* Personal details (some private, some public)
#* Avatar
#* Editable fields and media
#* Button: “Edit Profile”
# User taps *Edit Profile* and updates information as needed.
# System saves the changes and reloads the updated profile view.

----

h3. 3. Field Definitions Table

||Field Name||Field Type||Validation Rule||Mandatory||Visibility||
|Full Name|Text Input|Max 50 characters|Yes|Public|
|Email|Email Input|Must be valid email format|Yes|Private (system only)|
|Phone Number|Phone Input|Must be valid phone format|Yes|Private (system only)|
|Job Title|Text Input|Max 50 characters|Yes|Public|
|Gender|Enum|Male, Female, Other|Yes|Public|
|Industry|Text Input|Max 50 characters|No|Public|
|Date of Birth|Date Picker|Optional, must be valid date|No|Private|
|Address|Text Area|Max 100 characters|No|Private (shown only to self)|
|Bio|Text Area|Max 500 characters|No|Public|
|Skills & Interests|Tag Selector|Max 15 entries, predefined or custom|No|Public|
|Profile Picture|Image Upload|JPG/PNG, max size 2MB|No|Public|
|LinkedIn URL|URL Input|Must match LinkedIn format|No|Public (icon only)|
|Telegram Link|URL Input|Must match Telegram username format|No|Public (icon only)|
|WhatsApp Link|Phone-to-URL|Valid WhatsApp format|No|Public (icon only)|

----

h3. 4. Data Display Table (My Profile View)

||Display Element||Format Example||Notes||
|Avatar|Circle image|Editable via tap|
|Name + Job Title|""Sarah Miller – Product Manager""|Public header|
|Bio|“10+ yrs in B2B SaaS…”|Optional section|
|Skills & Interests|Tag chips|Visual category display|
|Contact Info Section|Email, Phone (shown only to self)|Hidden from public view|
|Edit Button|Primary CTA|Navigates to edit profile screen|

----

h3. 5. Acceptance Criteria (AC)

* *AC1:* On first login, user is prompted to complete profile.
* *AC2:* Required fields must be validated before user can proceed.
* *AC3:* Profile tab displays the full personal profile view including public and private fields.
* *AC4:* Tapping “Edit Profile” opens editable fields grouped by category.
* *AC5:* Saving changes updates backend and refreshes the profile view.
* *AC6:* Fields marked as private (e.g. DOB, Address, Email, Phone) are only visible to the profile owner.
* *AC7:* Social links are displayed as icons and an URL
* *AC8:* Avatar is editable by tap and supports cropping before upload.
* *AC9:* All changes persist across sessions and devices.
* *AC10:* If the user skips onboarding, prompt must reappear until mandatory fields are completed.

----

h3. 6. System Rules

* User profile must be initialized on account creation with partial or empty values.
* Incomplete profiles are flagged for reminder prompts at login.
* Fields are separated by visibility: public vs private vs internal.
* System uses a timestamp to track last profile update.
* Profile changes are autosaved only after full field validation.

----

h3. 7. UX Optimizations

* Use helper texts under each optional field to encourage completion.
* Group fields under collapsible headers (e.g., “Basic Info”, “Social Links”).
* Toast confirmation:
_“Profile updated successfully.”_
* Avatar update uses circular crop tool before upload.",,Duyen Nguyen,,712020:59b66c6e-1068-441c-9436-130289e16a26,,,,,,,,,,,,,,,,,com.atlassian.servicedesk.plugins.approvals.internal.customfield.ApprovalsCFValue@f3a7a09,,,,,,,,,{},,,,,,,,,,,,,,,,,,MOBILE,,,,0|i02quv:,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,37474,AL-58,User Profile,In Progress,25/Jun/25 10:22 AM
[MB - Contacts] Contact Map View,AL-57,37431,Story,Specified,AL,AIOS Link,software,Kai Nguyễn,6214a4102d1a9d0069949c96,,Medium,,,,Duyen Nguyen,712020:59b66c6e-1068-441c-9436-130289e16a26,Duyen Nguyen,712020:59b66c6e-1068-441c-9436-130289e16a26,04/Jun/25 4:46 PM,05/Jun/25 11:06 AM,,,,0,"h3. 1. Use Case Description

As a user, I want to view other nearby contacts who are currently available and have visibility enabled
so that I can explore connections geographically and reach out to people near me in real time.

----

h3. 2. User Workflow (Step-by-Step)

# User navigates to the *Contacts tab*.
# User taps the *“Map”* tab in the Connections section.
# System checks if the user has enabled:
#* *Availability to connect*
#* *Location visibility*
# If either setting is disabled:
#* System displays a blocking prompt:
_“To use the map view, please turn on your visibility and availability to connect.”_
#* User can tap *“Update Settings”* or *“Not Now”*
# If user taps *“Update Settings”*:
#* Availability and visibility toggles are activated.
#* System reloads the Map View.
# If user taps *“Not Now”*, they are returned to the previous tab; Map View is not accessible.
# Once visibility is active, Map View loads:
#* The user appears at their own location pin (labeled ""You"").
#* Other nearby users are shown only if:
#** They are marked *Available to connect*
#** They have *visibility enabled*
# User can tap on another contact’s pin to open a mini profile with options (e.g., message, view full profile).

----

h3. 3. Field Definitions Table

||Field Name||Field Type||Validation Rule||Mandatory||Description||
|Availability Toggle|Boolean|True to activate map|Yes|User must be available to appear or view on map|
|Visibility Toggle|Boolean|True to activate map|Yes|Location visibility must be enabled|
|User Location|GeoPoint|Accurate to city-level or better|Yes|User’s current location|
|Visible Contacts List|List|Filtered by availability + visibility|Yes|Other users to be plotted on the map|

----

h3. 4. Data Display Table (Map View)

||Element||Format Example||Notes||
|User Pin|“You” + avatar|Always visible if settings enabled|
|Other Contact Pins|Avatar only|Visible only if contact is available + visible|
|Mini Profile Pop-up|“Sarah Johnson – 500m away”|Includes actions: message, view profile|
|Status Toggle UI|“Available to connect” toggle with Update button|Controls map access|

----

h3. 5. Acceptance Criteria (AC)

* *AC1:* If either Availability or Visibility is turned off, system must block access to Map View.
* *AC2:* Blocking prompt must contain message and two buttons: “Update Settings” and “Not Now.”
* *AC3:* Selecting “Update Settings” enables both toggles and reloads the Map View.
* *AC4:* Selecting “Not Now” redirects user to the previous tab and does not load the map.
* *AC5:* Once access is granted, system displays user location centered on map.
* *AC6:* Nearby contact pins are only shown if they also have visibility + availability enabled.
* *AC7:* Tapping another user’s pin shows a mini profile card.
* *AC8:* User's location pin should remain updated while in map view (with permissions enabled).
* *AC9:* If user turns off visibility or availability while on the map, system exits map with alert:
_“You must be visible and available to stay on Map View.”_

----

h3. 6. System Rules

* Availability and Visibility settings are stored in user profile.
* Users with either setting off are fully excluded from the Map View experience (in both directions).
* Location data is retrieved from the device and must use permission-controlled APIs.
* Map updates are throttled to reduce performance impact (e.g., every 15 seconds or on move).
* Only contacts within a 25km radius are shown (configurable threshold).

----

h3. 7. UX Optimizations

* Use soft transition overlays for onboarding prompt.
* Animated map pins when new users appear nearby.
* Fallback UI:
_“No nearby contacts available right now. Try again later.”_
* Location denied scenario:
_“Please enable location access in your device settings to use this feature.”_",,Duyen Nguyen,,712020:59b66c6e-1068-441c-9436-130289e16a26,,,,,,,,,,,,,,,,,com.atlassian.servicedesk.plugins.approvals.internal.customfield.ApprovalsCFValue@51486e03,,,,,,,,,{},,,,,,,,,,,,,,,,,,MOBILE,,,,0|i02qsn:,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,37256,AL-17,Contacts,To Do,04/Jun/25 4:46 PM
[WEB - Organization] Create Organization,AL-56,37428,Story,In Progress,AL,AIOS Link,software,Kai Nguyễn,6214a4102d1a9d0069949c96,,Medium,,,,Duyen Nguyen,712020:59b66c6e-1068-441c-9436-130289e16a26,Duyen Nguyen,712020:59b66c6e-1068-441c-9436-130289e16a26,04/Jun/25 4:42 PM,25/Jun/25 10:22 AM,23/Jun/25 10:51 PM,,,0,"h1. Create Organization Profile Requirement Specification

h3. 1. Use Case Description

As an admin on the web platform, I want to create a new organization by entering basic details and optionally assigning an owner, so that the organization can participate in AIOS Link activities.

h3. 2. User Workflow

* Step 1: Admin navigates to the *Create Organization* screen from the Organization List.
* Step 2: System displays a form with fields for organization information and an optional Owner selector.
* Step 3: Admin enters Organization Name, Address, and Date of Establishment.
* Step 4: Admin optionally selects a user from the Owner dropdown (shows only users who are not currently owners of another organization).
* Step 5: Admin taps *Create*. System validates the form and creates the organization record.
* Step 6: System shows a success message and returns to the Organization List.

h3. 3. Field Definitions

h4. Input Fields

||Field Name||Field Type||Validation Rule||Mandatory||Description||
|Organization Name|Text|1-100 characters|Yes|Official organization name|
|Address|Text Area|Up to 200 characters|Yes|Physical or mailing address|
|Date of Establishment|Date|Must be a past or current date|Yes|Date the organization was founded|
|EIN/DUNS|Text|9 digits
format: 
XX-XXXXXXX|Yes|Employment Identification Number|
|Owner|Dropdown|Lists users without existing organization ownership|No|Assigns a user as owner|

h4. Interaction Elements

||Element Name||Type||Trigger Condition||Description||
|Create Button|Button|Enabled when mandatory fields are valid|Submits form and creates organization|
|Cancel Button|Button|Always visible|Returns to Organization List without saving|

h3. 4. Data Display (Summary)

||Data Name||Data Type||Display When Empty||Format||Description||
|Success Message|Toast|Hidden|Plaintext|Shown when organization is created|
|Error Message|Text|Hidden|Plaintext|Displays validation or server errors|

h3. 5. Acceptance Criteria

* AC1: Create Organization form must display fields for Organization Name, Address, EIN, and Owner.
* AC2: Owner dropdown only lists users who are not currently owners of another organization.
* AC3: Create button remains disabled until all mandatory fields are valid.
* AC4: After creation, new organization appears in the Organization List with provided details.
* AC5: Organization can be created without selecting an Owner.

h3. 6. System Rules

* Organization names must be unique; duplicates are rejected.
* Once a user is assigned as Owner, that user is no longer listed as available in the dropdown.
* Audit log records the admin ID and timestamp for each organization creation.

h3. 7. UX Optimizations

* Autofocus on Organization Name when the form loads.
* Show real-time validation for unique organization name.
* Provide calendar widget for selecting Date of Establishment.
* Display a loading spinner on the Create button while processing.",,Duyen Nguyen,,712020:59b66c6e-1068-441c-9436-130289e16a26,,,,,,,,,,,,,,,,,com.atlassian.servicedesk.plugins.approvals.internal.customfield.ApprovalsCFValue@685917a3,,,,,,,,,{},,,,,,,,,,,,,,,,,,WEB,,,,0|i02qsf:,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,37951,AL-87,Organization,In Progress,25/Jun/25 10:22 AM
[WEB - Order] Order List,AL-55,37425,Story,Specified,AL,AIOS Link,software,Kai Nguyễn,6214a4102d1a9d0069949c96,,Medium,,,,Duyen Nguyen,712020:59b66c6e-1068-441c-9436-130289e16a26,Duyen Nguyen,712020:59b66c6e-1068-441c-9436-130289e16a26,04/Jun/25 4:42 PM,10/Jun/25 8:09 PM,,,,0,"h1. Order List Requirement Specification

*Menu Path:* Orders

h3. 1. Use Case Description

As an admin on the web platform, I want to view all orders from every organization in a table so that I can monitor their status and navigate to related details.

h3. 2. User Workflow

* Step 1: Admin opens the *Order List* from the Web Admin Module.
* Step 2: System displays a paginated table of orders with search and filter tools.
* Step 3: Admin uses the search box or filters to locate specific orders.
* Step 4: Admin clicks a table row (excluding links) to open the *Order Details* page.
* Step 5: Admin clicks the organization name link within the row to open the *Business Profile* page.
* Step 6: Admin clicks the customer name link within the row to open the *User Profile* page.

h3. 3. Search and Filter Rules

h4. Search Rules

* Partial, case-insensitive match on *Order Number*, *Organization Name*, and *Customer Name*.

h4. Filter Rules

* *Status*: Dropdown with options {{New}}, {{Confirmed}}, {{In Progress}}, {{In Delivery}}, {{Completed}}, {{Canceled}}.
* *Order Date Range*: Start and End date pickers.
* *Organization*: Dropdown of organization names.

h3. 4. Field Definitions

h4. Interaction Elements

||Element Name||Type||Trigger Condition||Description||
|Row Link|Row Click|Always enabled|Navigates to Order Details page|
|Organization Link|Text Link|Always visible|Opens Business Profile page|
|Customer Link|Text Link|Always visible|Opens User Profile page|

h3. 5. Data Display (Summary)

||Data Name||Data Type||Display When Empty||Format||Description||
|Order Number|Text|""--""|Plaintext|Unique order identifier|
|Organization|Text Link|""--""|Plaintext|Name of selling organization|
|Customer Name|Text Link|""--""|Plaintext|Full name of purchaser|
|Total Price|Amount|""--""|USD|Order total amount|
|Status|Badge|""--""|New/Confirmed/...|Current order state|
|Order Date|Date|""--""|YYYY-MM-DD|Date the order was placed|

h3. 6. Acceptance Criteria

* AC1: Table must list columns for Order Number, Organization, Customer Name, Total Price, Status, and Order Date.
* AC2: Clicking a row opens Order Details, while Organization and Customer Name links open their respective profile pages.
* AC3: Search returns orders matching Order Number, Organization Name, or Customer Name.
* AC4: Filters narrow results by Status, Order Date Range, and Organization.
* AC5: Table loads with pagination of 25 orders per page.

h3. 7. System Rules

* Table data is sorted by Order Date (newest first) by default.
* Search and filter values persist when navigating back from Order Details.
* Order list reflects real-time status updates via backend polling or WebSocket.

h3. 8. UX Optimizations

* Keep the search box focused after returning from Order Details.
* Highlight rows on hover to indicate clickability.
* Disable search and filter controls while a request is processing and show a spinner.
* Remember last applied filters when admin returns to the screen.
* Provide tooltips for column headers to clarify data meaning.",,Duyen Nguyen,,712020:59b66c6e-1068-441c-9436-130289e16a26,,,,,,,,,,,,,,,,,com.atlassian.servicedesk.plugins.approvals.internal.customfield.ApprovalsCFValue@119dd8b9,,,,,,,,,{},,,,,,,,,,,,,,,,,,WEB,,,,0|i02qs7:,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,37950,AL-86,Order,To Do,04/Jun/25 4:42 PM
[WEB - Order] Cancel Order,AL-54,37422,Story,Specified,AL,AIOS Link,software,Kai Nguyễn,6214a4102d1a9d0069949c96,,Medium,,,,Duyen Nguyen,712020:59b66c6e-1068-441c-9436-130289e16a26,Duyen Nguyen,712020:59b66c6e-1068-441c-9436-130289e16a26,04/Jun/25 4:41 PM,10/Jun/25 7:45 PM,,,,0,"h1. Cancel Order Requirement Specification

h3. 1. Use Case Description

As an admin on the web platform, I want to cancel an order at any status so that inventory and billing are adjusted properly and the customer is notified.

h3. 2. User Workflow

* Step 1: From the *Order Details* screen, admin taps the *Cancel Order* button.
* Step 2: System displays a confirmation modal with a required cancellation reason field and Yes/No actions.
* Step 3: Admin enters the reason and confirms cancellation.
* Step 4: System records the reason and updates order status to {{Canceled}}.
* Step 5: If the order has been paid, system initiates a refund to the user through the original payment method.
* Step 6: System notifies the customer via in-app notification and email.
* Step 7: Admin is returned to the Order Details screen with the updated status.

h3. 3. Field Definitions

h4. Input Fields

||Field Name||Field Type||Validation Rule||Mandatory||Description||
|Cancellation Reason|Text Area|5-200 characters|Yes|Explanation for canceling the order|

h4. Interaction Elements

||Element Name||Type||Trigger Condition||Description||
|Cancel Order Button|Button|Always visible on Order Details|Opens confirmation modal|
|Confirmation Modal|Dialog|Shown after clicking Cancel Order|Requests reason and confirmation|
|Yes Button|Button|Enabled when reason is provided|Confirms cancellation|
|No Button|Button|Always enabled|Dismisses modal without changes|

h3. 4. Data Display (Summary)

||Data Name||Data Type||Display When Empty||Format||Description||
|Confirmation Message|Text|Hidden|Plaintext|""Are you sure you want to cancel this order?""|
|Notification Title|Text|Hidden|Plaintext|""Order Canceled""|
|Notification Description|Text|Hidden|Plaintext|""Your order #[order number] has been canceled.""|
|Email Subject|Text|Hidden|Plaintext|""Your AIOS Link order has been canceled""|
|Email Content|Text|Hidden|Plaintext|""Order #[order number] has been canceled for the following reason: [reason].""|

h3. 5. Acceptance Criteria

* AC1: Cancel Order button must be accessible from the Order Details screen in any order status.
* AC2: Confirmation modal must display the message ""Are you sure you want to cancel this order?"" and require a reason before Yes is enabled.
* AC3: On confirmation, order status updates to {{Canceled}} and the reason is stored in the system log.
* AC4: If the order was paid, the system issues a refund to the user through the original payment method.
* AC5: Customer receives an in-app notification with title ""Order Canceled"" and description ""Your order #[order number] has been canceled.""
* AC6: Customer receives an email with subject ""Your AIO Link order has been canceled"" and content referencing the cancellation reason.
* AC7: After cancellation, the Order Details screen reflects the new status immediately.

h3. 6. System Rules

* Cancellation is allowed regardless of current order status.
* Notification and email are triggered only after the cancellation is successfully saved in the backend.
* For paid orders, the refund is processed via the payment gateway before customer notification.
* Cancellation reason is stored with admin ID and timestamp for auditing.

h3. 7. UX Optimizations

* Focus the text area when the confirmation modal appears.
* Disable the Yes button until a valid reason is entered.
* Show a spinner on the Yes button while the request is processing.
* Provide a toast message ""Order canceled"" after successful completion.
* Remember the modal position on repeated cancellations for consistency.",,Duyen Nguyen,,712020:59b66c6e-1068-441c-9436-130289e16a26,,,,,,,,,,,,,,,,,com.atlassian.servicedesk.plugins.approvals.internal.customfield.ApprovalsCFValue@4dab02b1,,,,,,,,,{},,,,,,,,,,,,,,,,,,WEB,,,,0|i02qrz:,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,37950,AL-86,Order,To Do,04/Jun/25 4:41 PM
[MB - Contacts] Add Contact to Lead List,AL-53,37419,Story,In Progress,AL,AIOS Link,software,Kai Nguyễn,6214a4102d1a9d0069949c96,,Medium,,,,Duyen Nguyen,712020:59b66c6e-1068-441c-9436-130289e16a26,Duyen Nguyen,712020:59b66c6e-1068-441c-9436-130289e16a26,04/Jun/25 4:40 PM,25/Jun/25 10:22 AM,,,,0,"h3. 1. Use Case Description

As a user, I want to convert one or more of my existing contacts into leads
so that I can track and manage higher-intent relationships separately from my general contact list.

----

h3. 2. User Workflow (Step-by-Step)

h4. 🟦 Flow A: Convert Multiple Contacts via Lead List

# User navigates to the *Contacts tab*.
# User selects the *“Leads”* sub-tab.
# User taps the *“Convert to Lead”* button.
# System opens a modal allowing the user to *select multiple contacts*.
# User selects one or more contacts and taps *“Save”*.
# Selected contacts are removed from the general *Contact List* and moved to the *Lead List*.
# Toast appears: _“3 contacts have been converted to leads.”_

h4. 🟦 Flow B: Convert Individual Contact via Profile

# User navigates to a specific *Contact Detail screen*.
# User taps the *“More”* (•••) menu and selects *“Convert to Lead”*.
# System shows a confirmation modal:
_“Convert [Contact Name] to your lead list?”_
# User confirms.
# Contact is moved from Contact List to Lead List.
# Toast appears: _“[Contact Name] has been converted to a lead.”_

----

h3. 3. Field Definitions Table

||Field Name||Field Type||Validation Rule||Mandatory||Description||
|Selected Contacts|Multi-Select List|Must be in Contact List|Yes|Contacts chosen in bulk conversion flow|
|Convert Action|Button|Enabled when 1+ contact selected|Yes|Triggers conversion and updates lead state|
|Confirmation Modal|Dialog|Only shown in single conversion|Yes|Prompts user before moving a contact from contact to lead list|

----

h3. 4. Data Display Table (Lead Card Format)

||Field||Format Example||Notes||
|Name|“Sarah Miller”|Full contact name|
|Activity History|“Message sent 2w ago”|Carries over from previous contact state|
|CTA Icons|Refer, Add Note, Remove|Same icon row as in contact list|

----

h3. 5. Acceptance Criteria (AC)

* *AC1:* Users can access “Convert to Lead” button from the Lead List view.
* *AC2:* Multi-select modal must only show existing contacts not already in the Lead List.
* *AC3:* Selected contacts must be removed from Contact List and added to Lead List immediately after saving.
* *AC4:* From an individual contact profile, user can also select “Convert to Lead” from the menu.
* *AC5:* Single contact conversion triggers confirmation modal; user must confirm action before update.
* *AC6:* After conversion, contact no longer appears in the Contact List.
* *AC7:* Contact notes, labels, and metadata are preserved and transferred to the Lead List view.
* *AC8:* Contacts already in the Lead List must not be shown in conversion modals.
* *AC9:* Converted leads must respect role filters (User / Merchant) and appear accordingly.
* *AC10:* Repeated conversions are not allowed unless the contact is first removed from Lead List.

----

h3. 6. System Rules

* Contact-to-lead relationship is mutually exclusive: a person can be *either a contact or a lead*, never both.
* System must maintain referential data (e.g., notes, last message sent) during the move.
* Action logs are recorded for audit purposes but no notification is sent to the contact.
* Conversion state is stored in the backend and persists across sessions/devices.

----

h3. 7. UX Optimizations

* Modal uses searchable multi-select with avatars and roles.
* Conversion confirmation uses brief toast or banner:
** _“[X] contact(s) moved to Lead List”_
* Visual difference between Contact and Lead cards for clarity.
* Inline badge: _“Recently converted”_ (optional, expires after 48h).
* Empty state in Convert modal if no eligible contacts are found.",,Duyen Nguyen,,712020:59b66c6e-1068-441c-9436-130289e16a26,,,,,,,,,,,,,,,,,com.atlassian.servicedesk.plugins.approvals.internal.customfield.ApprovalsCFValue@5f015571,,,,,,,,,{},,,,,,,,,,,,,,,,,,MOBILE,,,,0|i02qrr:,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,37256,AL-17,Contacts,In Progress,25/Jun/25 10:22 AM
[MB - Contacts] Lead List,AL-52,37416,Story,In Progress,AL,AIOS Link,software,Kai Nguyễn,6214a4102d1a9d0069949c96,,Medium,,,,Duyen Nguyen,712020:59b66c6e-1068-441c-9436-130289e16a26,Duyen Nguyen,712020:59b66c6e-1068-441c-9436-130289e16a26,04/Jun/25 4:40 PM,25/Jun/25 10:22 AM,,,,0,"h3. 1. Use Case Description

As a user, I want to view and manage my lead contacts separately from my general contact list
so that I can track referral, follow-up, and engagement actions with higher intent contacts.

----

h3. 2. User Workflow (Step-by-Step)

# User navigates to the *Contacts* tab from the dashboard.
# User taps the *“Lead”* section/tab to view all contacts previously marked as leads.
# On the Lead List screen, user can:
#* Scroll and browse lead cards.
#* Tap the *“+” (Convert to Lead)* button.
# System displays a modal where user can select one or more contacts to convert into leads.
# User confirms the selection.
# Selected contacts are added to the lead list and displayed as lead cards.
# Each lead card includes:
#* *Refer icon* → refer this lead to another contact.
#* *Add Note icon* → open note editor for this lead.
#* *Remove icon* → remove the lead (returns contact to Contact List).

----

h3. 3. Field Definitions Table

||Field Name||Input Type||Validation Rule||Mandatory||Description||
|Lead Source Contact|List|Contact must already exist|Yes|Contacts eligible for conversion to lead|
|Lead Notes|Text|Max 500 characters|No|Optional notes associated with each lead|
|Action Icons|Button Row|Refer, Note, Remove|Yes|Quick actions available for each lead|

----

h3. 4. Data Display Table (Card View)

||Field Name||Display Type||Notes||
|Lead Name|Bold Text|Full name from contact profile|
|Last Interaction|Subtext|“Gift sent 2w ago”, “Message sent 1w ago”, etc.|
|Lead Avatar|Circular Image|Profile image pulled from contact card|
|Action Buttons|3 Icons|Refer, Add Note, Remove (returns to contact list)|

----

h3. 5. Acceptance Criteria (AC)

* *AC1:* Lead list must be accessible via the Contacts tab → Lead sub-tab.
* *AC2:* “Convert to Lead” button must open a modal with multi-select contact picker.
* *AC3:* Only contacts not already in the lead list are available for conversion.
* *AC4:* On confirmation, selected contacts are added to the lead list and shown in the UI.
* *AC5:* Tapping the *Refer* icon opens the referral modal with the lead pre-filled.
* *AC6:* Tapping *Add Note* opens the lead-specific note editor.
* *AC7:* Tapping *Remove* removes the lead from the list and places the contact back into the general Contacts list.
* *AC8:* System must prevent duplicate conversion of the same contact into lead more than once.
* *AC9:* Changes to lead list are synced with the backend and persist across sessions.
* *AC10:* When a contact is converted to a lead or removed back to contact list, any existing notes must be preserved and moved with them.
* *AC11:* Once a contact is converted to a lead, it must no longer appear in the general Contact List until removed from the Lead List.

----

h3. 6. System Rules

* A contact can exist in only one list at a time: either *Contacts* or *Leads*.
* Removing a lead does not delete the contact; it returns them to the Contact List.
* No export, drag, swipe, or sort is included in MVP scope.
* All actions on lead (note, refer, remove) should use existing atomic components.

----

h3. 7. UX Optimizations

* “Convert to Lead” CTA uses primary color and icon for visibility.
* Lead cards visually distinct from general contacts (e.g., shadow or border variant).
* Timestamps for last action auto-populated (e.g., last message sent, note added).
* Use animation or toast:
** _“3 contacts added to Lead List.”_
** _“Contact removed from Lead List.”_",,Duyen Nguyen,,712020:59b66c6e-1068-441c-9436-130289e16a26,,,,,,,,,,,,,,,,,com.atlassian.servicedesk.plugins.approvals.internal.customfield.ApprovalsCFValue@3fa8e693,,,,,,,,,{},,,,,,,,,,,,,,,,,,MOBILE,,,,0|i02qrj:,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,37256,AL-17,Contacts,In Progress,25/Jun/25 10:22 AM
[MB - Contacts] Contact List,AL-51,37413,Story,In Progress,AL,AIOS Link,software,Kai Nguyễn,6214a4102d1a9d0069949c96,,Medium,,,,Duyen Nguyen,712020:59b66c6e-1068-441c-9436-130289e16a26,Duyen Nguyen,712020:59b66c6e-1068-441c-9436-130289e16a26,04/Jun/25 4:40 PM,25/Jun/25 10:22 AM,,,,0,"h3. 1. Use Case Description

As a logged-in user, I want to view, filter, and interact with my list of contacts so that I can manage relationships, refer users, and message individuals efficiently.

----

h3. 2. User Workflow

* Step 1: User navigates to the *Contacts* module from the dashboard.
* Step 2: System displays the list of contacts segmented by role (User / Merchant).
* Step 3: User scrolls through the contact list.
* Step 4: For each contact card, user can:
** Tap *Refer* icon to initiate a referral
** Tap *Add Note* icon to add contextual information
** Tap *Delete* icon to remove the contact
** Tap anywhere else on the card to view contact details
** Tap *Message* icon to open message module
* Step 5: System updates or redirects based on selected interaction.

----

h3. 3. Field Definitions

h4. Interaction Elements

||Element Name||Type||Description||
|Refer Icon|IconTap|Opens contact referral modal|
|Add Note Icon|IconTap|Opens note editor for selected contact|
|Delete Icon|IconTap|Deletes contact with confirmation modal|
|Message Icon|IconTap|Opens message module with selected contact|
|Contact Card|CardTap|Navigates to the full profile/contact detail page|

----

h3. 4. Data Display

||Data Name||Data Type||Display When Empty||Format||Description||
|Contact Name|Text|“Unnamed Contact”|Full name|User's or merchant's registered name|
|Avatar|Image|Default icon|Circle image|Contact’s profile picture|
|Action Icons|Icons|Hidden|Row of 3 buttons|Refer, Add Note, Delete|

----

h3. 5. Acceptance Criteria

* *AC1:* Contacts must be loaded and shown as cards with avatar, name, position and last interaction (met at X event)                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                            and role.
* *AC2:* Tapping the *Refer* icon must open the referral modal pre-filled with contact data.
* *AC3:* Tapping the *Add Note* icon must open a note editor linked to the selected contact.
* *AC4:* Tapping the *Delete* icon must open a confirmation modal before removing the contact.
* *AC5:* Tapping the *Message* icon must navigate to the message module.
* *AC6:* Contact cards must not support drag, swipe, or export actions.
* *AC7:* Contacts deleted from the list must not appear in the next refresh or session.

----

h3. 6. System Rules

* Contact list must be cached for session duration and updated with pull-to-refresh.
* Message icon must trigger navigation to Message Module with selected contact preloaded.
* Deletion must update both local view and backend reference (via soft delete flag).
* Notes are stored per user and not visible to the contact.
* No export, share, or download option is available in this module.

----

h3. 7. UX Optimizations

* Sticky filter bar on top when scrolling long contact lists.
* Subtle animation on card tap or icon press for feedback.
* Toast message:
** “Contact deleted” on success
** “Referral sent” if referral flow is completed
* Display avatars using lazy-load strategy to enhance performance.
* Use tooltips or icon labels for first-time users (e.g., “Refer”, “Note”, “Delete”).",,Duyen Nguyen,Kai Nguyễn,712020:59b66c6e-1068-441c-9436-130289e16a26,6214a4102d1a9d0069949c96,,,,,,,,,,,,,,,,com.atlassian.servicedesk.plugins.approvals.internal.customfield.ApprovalsCFValue@44102cfc,,,,,,,,,{},,,,,,,,,,,,,,,,,,MOBILE,,,,0|i02qrb:,,,,,,,,,,,,,,,,,,,,,,,,,,2025-06-05 01:47:53.791,,05/Jun/25 8:47 AM;6214a4102d1a9d0069949c96;Role filter đúng không v [~accountid:712020:59b66c6e-1068-441c-9436-130289e16a26] ,05/Jun/25 9:18 AM;712020:59b66c6e-1068-441c-9436-130289e16a26;[~accountid:6214a4102d1a9d0069949c96] dạ đúng rồi anh,37256,AL-17,Contacts,In Progress,25/Jun/25 10:22 AM
[MB - Contacts] Refer contact to another user,AL-50,37410,Story,In Progress,AL,AIOS Link,software,Kai Nguyễn,6214a4102d1a9d0069949c96,,Medium,,,,Duyen Nguyen,712020:59b66c6e-1068-441c-9436-130289e16a26,Duyen Nguyen,712020:59b66c6e-1068-441c-9436-130289e16a26,04/Jun/25 4:38 PM,25/Jun/25 10:22 AM,,,,0,"h3. 1. Use Case Description

As a user, I want to refer one of my contacts to other users in my network
so that they can view the referred profile and choose to connect—only if the referred contact allows referrals.

----

h3. 2. User Workflow (Step-by-Step)

h4. 🟦 Flow A: Refer via Contacts Tab

# User navigates to the *Contacts tab*.
# Each contact card includes a *Refer icon*.
# User taps the *Refer icon* on a specific contact.
# System opens a *modal* titled “Refer this Contact to...”
# User sees a searchable list of their own contacts and selects *one or more* recipients.
# System verifies that the selected contact has *referral permission enabled*.
# If allowed, referral is sent, and a *notification* is delivered to each recipient.
# Recipients see the referral in their *Notification Center*.
# Tapping the notification opens the referred user’s profile.
# Recipient may tap *“Add to Contact”* to initiate connection.

h4. 🟦 Flow B: Refer via Contact Profile

# User views a specific contact’s profile.
# User taps *“Refer”* button.
# System opens the same *multi-select modal* for selecting recipient contacts.
# Remaining steps match Flow A.

----

h3. 3. Field Definitions Table

||Field Name||Field Type||Validation Rule||Mandatory||Description||
|Contact to Refer|Object (User ID)|Must exist in sender’s Contact List|Yes|The person being referred|
|Recipients|Multi-select|Must exist in sender’s Contact List|Yes|One or more contacts who will receive referral|

----

h3. 4. Data Display Table (Notification)

||Field||Example||Notes||
|Title|“Sarah has referred you a new contact”|Standard format|
|Action CTA|Tap → View referred contact profile|Navigates to limited-view profile screen|

----

h3. 5. Acceptance Criteria (AC)

* *AC1:* Refer icon must be shown on all contact cards and contact profiles.
* *AC2:* Clicking Refer opens a modal with a searchable multi-select list of the sender’s contacts.
* *AC3:* Referrals are only allowed if the *referred contact has referral permission enabled*.
* *AC4:* If the referred contact has disabled referral sharing, display error:
_“This contact does not allow referrals.”_
* *AC5:* System prevents referring a user who is already in the recipient's contact list.
* *AC6:* Tapping referral notification opens the referred user’s *public profile*.
* *AC7:* Recipient can choose to *Add to Contact* from the profile view.
* *AC8:* Referral does *not* automatically add contact for either party.
* *AC9:* A toast confirms success:
_“[Name] was referred to 2 people.”_
* *AC10:* Duplicate referral to the same user pair within 24 hours is not allowed.

----

h3. 6. System Rules

* Referral opt-out is toggled by each user under *Privacy Settings > Referral Visibility*.
* If toggle is off, the user *cannot be selected* in referral flows.
* No referral history is stored beyond delivery metadata.
* Referrals cannot be recalled once sent.
* Blocked users cannot refer or be referred.

----

h3. 7. UX Optimizations

* Modal grays out and disables non-eligible recipients or referrals.
* Tooltip for disabled refer button:
_“This contact has disabled referrals.”_
* Suggest most active contacts at top of picker modal.
* Avatar + name shown in notification with quick CTA.",,Duyen Nguyen,,712020:59b66c6e-1068-441c-9436-130289e16a26,,,,,,,,,,,,,,,,,com.atlassian.servicedesk.plugins.approvals.internal.customfield.ApprovalsCFValue@7247ea6d,,,,,,,,,{},,,,,,,,,,,,,,,,,,MOBILE,,,,0|i02qr3:,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,37256,AL-17,Contacts,In Progress,25/Jun/25 10:22 AM
[MB - Contacts] Contact Profile,AL-49,37407,Story,In Progress,AL,AIOS Link,software,Kai Nguyễn,6214a4102d1a9d0069949c96,,Medium,,,,Duyen Nguyen,712020:59b66c6e-1068-441c-9436-130289e16a26,Duyen Nguyen,712020:59b66c6e-1068-441c-9436-130289e16a26,04/Jun/25 4:29 PM,25/Jun/25 10:22 AM,,,,0,"h3. 1. Use Case Description

As a user, I want to view the complete profile of another user on the platform
so that I can understand their background, see mutual connections and shared activity, and take appropriate contextual actions based on our relationship status.

----

h3. 2. User Workflow (Step-by-Step)

# User taps on a profile from Contacts, Leads, Suggestions, or Search.
# System opens the *Contact Profile View* screen.
# Regardless of status (connected or not), the screen shows:
#* Full profile details
#* Mutual connections
#* Shared activity
# Based on the relationship status, the *CTA row* displays a different set of available actions:
#* *If not in contact list*: show CTAs for *Add to Contact*, *Message*, *Block*, *Report*
#* *If in contact list*: show *Message*, *Send Gift*, *Refer*, *Invite to Meet*, *Remove Contact*, *Convert to Lead*, *Block*, *Report*
#* *If in lead list*: show *Message*, *Send Gift*, *Refer*, *Invite to Meet*, *Remove Lead*, *Block*, *Report*

----

h3. 3. Field Definitions Table (Always Visible)

||Field Name||Field Type||Description||Visibility||
|Full Name|Text|User’s display name|Public|
|Job Title|Text|Job title and company|Public|
|Gender|Enum|Male, Female, Other|Public|
|Industry|Text|Professional industry or vertical|Public|
|Bio|Text Area|Profile summary, up to 500 characters|Public|
|Skills & Interests|Tags (max 15)|User-defined areas of focus or expertise|Public|
|Profile Picture|Image|Circular avatar with status indicator|Public|
|LinkedIn URL|Icon/Link|Copy-able URL|Public|
|Telegram Handle|Icon/Link|Copy-able URL|Public|
|WhatsApp Link|Icon/Link|Copy-able URL|Public|
|Mutual Contacts|Avatars + Count|Horizontal scroll of shared contacts + ""See All""|Public|
|Shared Activity|Timeline List|Chronological feed of interactions (messages, invites, etc.)|Private (self)|

----

h3. 4. CTA Logic by Relationship Status

||Relationship Status||CTA Buttons||
|*Not in Contact List*|Add to Contact, Message, Block, Report|
|*In Contact List*|Message, Send Gift, Refer, Invite to Meet, Remove Contact, Convert to Lead, Block, Report|
|*In Lead List*|Message, Send Gift, Refer, Invite to Meet, Remove Lead, Block, Report|

----

h3. 5. Acceptance Criteria (AC)

* *AC1:* Profile fields (bio, position, skills, etc.) must always be shown regardless of contact status.
* *AC2:* Mutual connections and shared activity sections are visible in all states.
* *AC3:* CTA row adapts based on current relationship state:
** If no connection: shows Add to Contact, Message, Block, Report
** If contact: shows full interaction tools including lead conversion
** If lead: shows Remove Lead instead of Remove Contact and no Convert to Lead
* *AC4:* Action buttons trigger appropriate workflows:
** Add to Contact → sends connection request
** Send Gift → opens send gift modal
** Refer → opens contact referral flow
** Convert to Lead / Remove Lead / Remove Contact → triggers confirmation modal
** Invite to Meet → opens calendar or scheduling feature
** Block / Report → triggers platform-level abuse workflow

----

h3. 6. System Rules

* A user cannot appear in both contact and lead lists simultaneously; conversion updates their status.
* Actions like block/report must immediately disable mutual interactions and visibility.
* Contact status ({{none}}, {{contact}}, {{lead}}) is resolved server-side and cached client-side for faster CTA rendering.
* Social links must be validated during profile edit and rendered conditionally if present.

----

h3. 7. UX Optimizations

* *Sticky CTA row* under profile picture aligned horizontally for fast tap.
* Use consistent iconography:
** 💬 Message
** 🎁 Gift
** 🔗 Refer
** 🗓 Invite
** ➕ Add Contact
** ➖ Remove Contact/Lead
** 🚫 Block
** 🛑 Report
* Group *Message/Gift/Invite/Refer* together in first row, and *Block/Report/Remove* in overflow or bottom section.
* “Convert to Lead” and “Remove” actions show confirmation modals.",,Duyen Nguyen,,712020:59b66c6e-1068-441c-9436-130289e16a26,,,,,,,,,,,,,,,,,com.atlassian.servicedesk.plugins.approvals.internal.customfield.ApprovalsCFValue@31b6581f,,,,,,,,,{},,,,,,,,,,,,,,,,,,MOBILE,,,,0|i02qqv:,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,37256,AL-17,Contacts,In Progress,25/Jun/25 10:22 AM
[MB - Contacts] Smart Suggestion List,AL-48,37404,Story,Specified,AL,AIOS Link,software,Kai Nguyễn,6214a4102d1a9d0069949c96,,Medium,,,,Duyen Nguyen,712020:59b66c6e-1068-441c-9436-130289e16a26,Duyen Nguyen,712020:59b66c6e-1068-441c-9436-130289e16a26,04/Jun/25 4:28 PM,16/Jun/25 10:45 PM,,,,0,"h3. 1. Use Case Description

As a user, I want to receive personalized contact suggestions based on my profile and behavior
so that I can easily discover relevant people to connect with on the platform.

----

h3. 2. User Workflow (Step-by-Step)

*Entry Points:*

* *Home Dashboard* → ""People You May Know"" horizontal list
* *Contacts Tab* → “Suggestions” sub-tab (full vertical list view)

*Interaction Flow:*

# On the home dashboard, user sees a horizontal list of up to *5* suggested contacts.
# Each contact card displays:
#* Avatar
#* Name
#* Position
#* Optional context (e.g., mutual connection or shared event)
# A *""View All""* button is shown after the fifth item (if available).
#* When tapped, user is redirected to the full *Suggestions* tab inside the Contacts module.
# In the Suggestions tab, user can scroll through the complete vertical list of suggestions.
# Users can take action on each card:
#* *Connect* → sends a request and moves the user to Sent Requests
#* *Dismiss (X)* → hides the contact for 48 hours

----

h3. 3. Field Definitions

||Field Name||Field Type||Description||
|Profile Picture|Image|Avatar shown in horizontal or vertical card|
|Full Name|Text|Display name of the suggested user|
|Job Title|Text|Job title|
|Match Context Label|Text (optional)|One of: “Mutual Connection”, “Met at [Event]”, or empty if no shared signal|
|Connect Button|Button|Sends connection request and removes the suggestion|
|Dismiss Button (X)|Button|Removes user from suggestion for 48 hours|
|View All Button|Link Button|Appears only on Home view after 5th card; opens full Suggestions list|

----

h3. 4. Acceptance Criteria

* AC1: On Home Dashboard, show *up to 5* suggested contacts in a horizontal scroll list.
* AC2: Each card on Home displays profile picture, full name, position, and match context (if applicable).
* AC3: After 5th suggestion, a *View All* button appears linking to Contacts > Suggestions.
* AC4: Full vertical suggestion list is shown in the Contacts tab, with all Connect/Dismiss actions enabled.
* AC5: Tapping *Connect* removes the card and logs the user in Sent Requests.
* AC6: Tapping *Dismiss (X)* hides the card and prevents reappearance for 48 hours.
* AC7: Suggestions do not include current contacts or those with pending requests.
* AC8: Suggestions are ordered by descending similarity score.

----

h3. 5. System Rules

*Similarity scoring logic:*

||Signal||Description||Weight Priority||
|Mutual Connections|Shared confirmed contacts|Very High|
|Shared Event Attendance|Attended the same event|High|
|Shared Job Title Keywords|Overlapping words in position|Medium|
|Shared Industry|Same industry profile field|Medium|
|Nearby Location (if enabled)|Users are within geo-range and visibility is enabled|Low|

* Scores are computed server-side.
* Users dismissed manually are excluded from results for 48 hours.
* Home Dashboard fetch is optimized to return a maximum of 5 users.

----

h3. 6. UX Notes

* Horizontal card layout on Home should include truncation logic for long names/titles.
* Cards should animate slide-out on Connect or Dismiss.
* ""View All"" should only appear if there are more than 5 suggestions available.
* Avatar tap on card (Home or Contacts) opens profile preview.",,Duyen Nguyen,,712020:59b66c6e-1068-441c-9436-130289e16a26,,,,,,,,,,,,,,,,,com.atlassian.servicedesk.plugins.approvals.internal.customfield.ApprovalsCFValue@49d2acc4,,,,,,,,,{},,,,,,,,,,,,,,,,,,MOBILE,,,,0|i02qqn:,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,37256,AL-17,Contacts,To Do,04/Jun/25 4:29 PM
[MB - Contacts] Request List,AL-47,37401,Story,Specified,AL,AIOS Link,software,Kai Nguyễn,6214a4102d1a9d0069949c96,,Medium,,,,Duyen Nguyen,712020:59b66c6e-1068-441c-9436-130289e16a26,Duyen Nguyen,712020:59b66c6e-1068-441c-9436-130289e16a26,04/Jun/25 4:28 PM,05/Jun/25 10:11 AM,,,,0,"h3. 1. Use Case Description

As a user, I want to manage incoming and outgoing contact requests
so that I can control who is added to my contact list and track which requests I’ve sent or received.

----

h3. 2. User Workflow (Step-by-Step)

# User navigates to the *Contacts tab* from the bottom navigation bar.
# User selects the *""Requests""* tab from the segmented control.
# The screen displays two sections:
#* *Incoming Requests*: shows pending connection requests from others.
#* *Sent Requests*: shows requests the user has sent but have not yet been accepted or declined.
# For each incoming request, user can:
#* Tap *Accept* → adds the sender to the user’s contact list.
#* Tap *Reject* → removes the request without notifying the sender.
# For each sent request, user can:
#* Tap *Cancel Request* → removes the request and also deletes it from the recipient’s incoming request queue.
# If the sent request is accepted by the recipient:
#* The contact is automatically moved to the user’s *Contact List*.
# If the recipient *declines* the request:
#* The request stays in the sender’s *Sent Requests*, without indication it was rejected.

----

h3. 3. Field Definitions Table

||Field Name||Input Type||Validation Rule||Mandatory||Description||
|Incoming Requests|List|Must be valid user ID|Yes|List of all pending requests received by the user|
|Sent Requests|List|Must be valid user ID|Yes|List of all requests this user has sent and are awaiting response|
|Accept Button|CTA|Enabled if request exists|Yes|Adds sender to contacts and removes request|
|Reject Button|CTA|Enabled if request exists|Yes|Deletes request without notification to sender|
|Cancel Request|CTA|Enabled for pending sent request|Yes|Removes the outgoing request and clears it from the receiver’s view|

----

h3. 4. Data Display Table (Card View)

||Display Field||Format Example||Notes||
|Avatar|Circular image|Sender/receiver profile image|
|Name|“David Chen”|Full name|
|Job Title|“Tech Lead at Microsoft”|Optional metadata pulled from profile|
|Mutual Connections|“Mutual connections: 8”|Optional UI detail|
|Timestamp|“Sent 2 days ago”|Shown only in Sent Requests|

----

h3. 5. Acceptance Criteria (AC)

* *AC1:* Requests screen must show two clearly separated sections: Incoming and Sent.
* *AC2:* Accepting an incoming request adds the contact to the user’s Contact List.
* *AC3:* Rejecting an incoming request removes it from view without notifying the requester.
* *AC4:* Canceling a sent request removes it from the sender’s Sent Requests and from the recipient’s Incoming Requests.
* *AC5:* If a sent request is accepted by the other user, the request is removed and the contact is added to the user’s Contact List.
* *AC6:* If the other user rejects a sent request, the request remains in the sender’s Sent Requests (no rejection notification).
* *AC7:* Tapping Cancel Request immediately updates the UI and backend in real time.
* *AC8:* Requests must not appear in both “Sent” and “Confirmed” lists at the same time.
* *AC9:* Contact request status is persistent across sessions and device restarts.
* *AC10:* Once clicked on Request Card, it will direct user to that Contact Profile.

----

h3. 6. System Rules

* One contact request can exist at a time between any two users (no duplicates).
* All requests must include timestamps and sender metadata.
* Once accepted, contact is moved to confirmed list and request record is archived.
* Notifications for “request accepted” are allowed, but *no notifications for rejected or cancelled requests.*

----

h3. 7. UX Optimizations

* Use tabbed UI for *Confirmed / Requests / Suggestions* under Contacts.
* “Accept” and “Reject” buttons are large, high-contrast for quick access.
* Show toast messages:
** _“Request accepted. Contact added.”_
** _“Request removed.”_
** _“Request cancelled.”_
* Grey out rejected or cancelled requests immediately.
* Maintain count badge on tab if there are pending incoming requests.",,Duyen Nguyen,,712020:59b66c6e-1068-441c-9436-130289e16a26,,,,,,,,,,,,,,,,,com.atlassian.servicedesk.plugins.approvals.internal.customfield.ApprovalsCFValue@ce01ece,,,,,,,,,{},,,,,,,,,,,,,,,,,,MOBILE,,,,0|i02qqf:,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,37256,AL-17,Contacts,To Do,04/Jun/25 4:28 PM
[MB - Contacts] Remove Contacts,AL-46,37398,Story,Specified,AL,AIOS Link,software,Kai Nguyễn,6214a4102d1a9d0069949c96,,Medium,,,,Duyen Nguyen,712020:59b66c6e-1068-441c-9436-130289e16a26,Duyen Nguyen,712020:59b66c6e-1068-441c-9436-130289e16a26,04/Jun/25 4:27 PM,16/Jun/25 10:43 PM,,,,0,"h3. 1. Use Case Description

As a user, I want to remove someone from my contact list
so that we are both disconnected from each other’s contact space and unable to perform any contact-based actions unless we reconnect.

----

h3. 2. User Workflow (Step-by-Step)

# User navigates to the *Contacts* tab.
# User scrolls or searches for a specific contact.
# User clicks the *Remove icon* on the contact card or selects *“Remove Contact”* from the contact detail view.
# System displays a confirmation modal:
_“Are you sure you want to remove this contact? This will disconnect both sides and revoke any contact-based actions.”_
# User confirms the action.
# Contact is removed from both users’ contact lists.
# After removal:
#* Neither user can send gifts, event invites, or meeting invitations.
#* The *message thread remains accessible* unless blocked.
#* The relationship can be re-established via a new contact request flow initiated by either user.

----

h3. 3. Field Definitions Table

||Field Name||Field Type||Validation Rule||Mandatory||Description||
|Contact ID|UUID|Must match active connection|Yes|The user/contact pair to be removed|
|Delete Trigger|Icon / CTA|Confirmation modal shown|Yes|User’s intent to remove contact is captured and confirmed|

----

h3. 4. Data Display Table (Contact List Card View)

||Display Element||Format Example||Notes||
|Contact Name|“Emma Wilson”|Full name|
|Last Interaction|“Message sent 2w ago”|Shown unless contact is removed|
|Remove Icon|Trash or “X” icon|Initiates removal flow|

----

h3. 5. Acceptance Criteria

* *AC1:* Users can remove a contact via contact card icon or contact detail view.
* *AC2:* System displays a confirmation modal before finalizing the removal.
* *AC3:* Once confirmed, the contact is removed from the user’s contact list.
* *AC4:* Removal is *mutual* — it also removes the user from the other party’s contact list.
* *AC5:* The removed user does not receive a notification.
* *AC6:* Once removed, the connection is fully dissolved and the contact does not appear in any contact-based UI flows (e.g., invite, gift).
* *AC7:* Messaging between the two users remains accessible via existing chat thread unless blocked.
* *AC8:* The contact can be re-added via a new contact request, starting a fresh approval cycle.
* *AC9:* Notes and referral history associated with the removed contact are archived locally and restored if the contact is reconnected.
* *AC10:* System prevents accidental double-taps and ensures full backend sync for both parties on removal.

----

h3. 6. System Rules

* Contact connections are *mutual and bi-directional*.
* Removing a contact must:
** Remove the contact from both parties’ lists.
** Invalidate any dependent relationships (e.g., lead, referral).
* Any pending events, gifts, or invites involving the removed contact must be cancelled or hidden from UI.
* Messaging is a separate channel and remains open unless explicitly blocked by either party.
* Timestamp and metadata of removal are stored for audit and potential re-sync.

----

h3. 7. UX Optimizations

* Confirmation modal includes clear destructive action copy:
_“This will disconnect both users and remove all contact access. Continue?”_
* Toast message after action:
_“Contact removed for both parties. You can reconnect later if needed.”_
* Gift, invite, and meeting CTAs are disabled immediately for the removed contact.
* Re-adding the contact via new request reactivates history and permissions where appropriate.

 ",,Duyen Nguyen,,712020:59b66c6e-1068-441c-9436-130289e16a26,,,,,,,,,,,,,,,,,com.atlassian.servicedesk.plugins.approvals.internal.customfield.ApprovalsCFValue@41b989bd,,,,,,,,,{},,,,,,,,,,,,,,,,,,MOBILE,,,,0|i02qq7:,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,37256,AL-17,Contacts,To Do,04/Jun/25 4:27 PM
[MB - Contacts] Add User to Contact from Smart Suggestion,AL-45,37395,Story,Specified,AL,AIOS Link,software,Kai Nguyễn,6214a4102d1a9d0069949c96,,Medium,,,,Duyen Nguyen,712020:59b66c6e-1068-441c-9436-130289e16a26,Duyen Nguyen,712020:59b66c6e-1068-441c-9436-130289e16a26,04/Jun/25 4:27 PM,05/Jun/25 11:30 AM,,,,0,"h3. 1. Use Case Description

As a user, I want to connect with people suggested to me by the system
so that I can easily add relevant contacts I may have met or share mutual context with, by sending a request and waiting for them to accept.

----

h3. 2. User Workflow (Step-by-Step)

# User navigates to the *Contacts tab*.
# User selects the *“Suggestions”* tab within the *Connections* section.
# System displays a list of smart-suggested users based on:
#* Events attended together
#* Shared locations
#* Recent interactions
#* Platform behavior patterns
# For each suggestion, user sees:
#* Profile picture, name, title, company, and contextual info (e.g., “Met at AI Conf 2024”)
#* A *“Connect”* button
#* An optional dismiss *“X”* icon
# User taps *Connect* → a contact request is sent to the suggested user.
# Suggested user sees the request in their *Incoming Requests* tab.
# Once the suggested user *Accepts* the request:
#* Both users are added to each other’s *Contact Lists*
#* Request is removed from suggestion queue

----

h3. 3. Field Definitions Table

||Field Name||Field Type||Validation Rule||Mandatory||Description||
|Suggestion Source|Enum|System-generated only|Yes|Based on behavior, events, mutual presence|
|Connect Button|CTA|Enabled if no request exists|Yes|Sends a connection request to the suggested contact|
|Dismiss Button|Icon|Optional|No|Removes the suggestion from view temporarily|
|Context Tag|Text|Auto-generated|Yes|Describes why this person is being suggested (e.g., “Met at DevCon”)|

----

h3. 4. Data Display Table (Suggestion Card)

||Field||Format Example||Notes||
|Name|“Sarah Miller”|Full name|
|Title / Company|“Marketing Director at Nike”|Optional metadata|
|Context|“Met at Digital Summit 2024”|System-generated context tag|
|Avatar|Circle photo|Clickable|
|Connect CTA|Blue Button: “Connect”|Sends request and disables on tap|
|Dismiss Icon|X icon|Removes suggestion from local view|

----

h3. 5. Acceptance Criteria (AC)

* *AC1:* Suggestions tab must show cards only for users who are not yet connected.
* *AC2:* Each suggestion must display name, role, avatar, and suggestion context.
* *AC3:* Tapping “Connect” sends a contact request to that user.
* *AC4:* Once sent, the contact will appear in ‘Sent Request’ and no longer stay in Suggestion
* *AC5:* The other user must manually accept the request before the contact appears in either user’s Contact List.
* *AC6:* Upon acceptance, both users are added to each other’s *Contact Lists*.
* *AC7:* Dismissing a suggestion removes it from the current session but does not block future reappearance unless configured to do so.
* *AC8:* Smart suggestions must not include:
** Users already in contact list
** Users with a pending request (sent or received)
** Blocked or removed users

----

h3. 6. System Rules

* Suggestions are refreshed dynamically based on interaction context, time, and location data.
* Suggestions should be cached per session with a refresh on app reopen or manual pull-to-refresh.
* Contact requests via this flow follow the same approval logic as QR and link-based flows.
* System prevents duplicate requests by checking for pending or rejected statuses before enabling the “Connect” button.

----

h3. 7. UX Optimizations

* Context tags are styled as subtle grey labels under job title.
* “Connect” button transitions to a disabled state on tap with feedback (e.g., spinner + “Sent”).
* Toast:
_“Request sent. You’ll be notified once they accept.”_
* Empty state message if no suggestions:
_“No smart suggestions yet. Attend events or engage with more users to see matches.”_
* Show count badge on Suggestions tab if there are unseen smart matches.

----",,Duyen Nguyen,,712020:59b66c6e-1068-441c-9436-130289e16a26,,,,,,,,,,,,,,,,,com.atlassian.servicedesk.plugins.approvals.internal.customfield.ApprovalsCFValue@42cc5735,,,,,,,,,{},,,,,,,,,,,,,,,,,,MOBILE,,,,0|i02qpz:,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,37256,AL-17,Contacts,To Do,04/Jun/25 4:27 PM
[MB - Contacts] Add User to Contact list from their Profile,AL-44,37392,Story,Specified,AL,AIOS Link,software,Kai Nguyễn,6214a4102d1a9d0069949c96,,Medium,,,,Duyen Nguyen,712020:59b66c6e-1068-441c-9436-130289e16a26,Duyen Nguyen,712020:59b66c6e-1068-441c-9436-130289e16a26,04/Jun/25 4:27 PM,16/Jun/25 10:43 PM,,,,0,"h3. 1. Use Case Description

As a user, I want to add new contacts via QR code or profile link
so that I can quickly connect with people I meet or are referred to, while respecting mutual consent before adding to my contact list.

----

h3. 2. User Workflow (Step-by-Step)

h4. 🟦 Flow A: Add Contact via QR Code

# User opens another person’s profile QR code (shared in person or from message).
# User taps the *QR Scanner* within the app.
# After scanning, system opens the scanned user's *Public Profile page*.
# User taps *“Add to Contact”* button.
# System sends a *contact request* to the scanned user.
# The scanned user sees the request in *Incoming Requests* tab and must *Accept* to establish connection.
# Once accepted, both users appear in each other's *Contact Lists*.

h4. 🟦 Flow B: Add Contact via Profile Link

# User receives a contact profile link (via SMS, chat, WhatsApp, etc.).
# User goes to *Contacts tab* → taps *“+ Add”*.
# User pastes the link into a *Profile Link input modal*.
# System verifies the link and loads the sender’s *Public Profile view*.
# User taps *“Add to Contact”* button.
# System sends a *contact request* to the link owner.
# The recipient sees the request under *Incoming Requests*, accepts it, and connection is established.

----

h3. 3. Field Definitions Table

||Field Name||Field Type||Validation Rule||Mandatory||Description||
|Profile Link Input|Text URL Field|Must match valid AIOS profile link format|Yes|Used in Add Contact via link flow|
|QR Code Payload|Encoded JSON|Must contain user ID,  public token|Yes|Used in Add Contact via QR scanning|
|Contact Request|Internal State|Created after pressing ""Add to Contact""|Yes|Pending until recipient accepts|

----

h3. 4. Data Display Table (Confirmation and Request Views)

||Display Element||Format Example||Notes||
|Public Profile View|Name, Avatar, Bio|Same screen shown after scanning QR or link resolution|
|Add to Contact CTA|Blue Button|Tapping sends request to the profile’s owner|
|Toast|“Request sent”|Confirmation after action|

----

h3. 5. Acceptance Criteria (AC)

* *AC1:* Scanning a valid AIOS QR opens the profile of the scanned user.
* *AC2:* Tapping “Add to Contact” sends a contact request to that user.
* *AC3:* In the profile link flow, pasting a valid AIOS Link opens the same profile view.
* *AC4:* If the profile link is invalid, show error:
_“Invalid or expired profile link.”_
* *AC5:* Requests sent from either method must appear in the *Sent Requests* tab.
* *AC6:* Recipients must see requests in their *Incoming Requests* tab with Accept/Reject actions.
* *AC7:* Once accepted, both users are added to each other’s *Contact Lists*.
* *AC8:* Contact will not be added unless both users accept (mutual connection model).
* *AC9:* If a user has blocked the requester, the “Add to Contact” action must be hidden or disabled.

----

h3. 6. System Rules

* Contact requests are *mutual opt-in*: both parties must accept.
* Each request is a single object stored with status: {{pending}}, {{accepted}}, {{rejected}}.
* QR and profile link both resolve to the same profile presentation and follow the same logic path.
* Duplicates are prevented: one request per user pair can exist at any time.
* QR codes and profile links are permanent unless manually refreshed or revoked.

----

h3. 7. UX Optimizations

* Smart detection: pasted links auto-detect AIOS profile format and open immediately.
* QR scanner launches from multiple entry points (e.g., Contacts tab, Message thread).
* On success:
_“Your request has been sent. You’ll be notified when it’s accepted.”_
* On duplicate request:
_“You’ve already sent a request to this user.”_
* Blocked state (if applicable):
_“This user is not accepting connection requests.”_",,Duyen Nguyen,,712020:59b66c6e-1068-441c-9436-130289e16a26,,,,,,,,,,,,,,,,,com.atlassian.servicedesk.plugins.approvals.internal.customfield.ApprovalsCFValue@1ea46724,,,,,,,,,{},,,,,,,,,,,,,,,,,,MOBILE,,,,0|i02qpr:,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,37256,AL-17,Contacts,To Do,04/Jun/25 4:27 PM
[MB - Messages] Add/Remove Contact from Group Chat,AL-40,37383,Story,Specified,AL,AIOS Link,software,Kai Nguyễn,6214a4102d1a9d0069949c96,,Medium,,,,Duyen Nguyen,712020:59b66c6e-1068-441c-9436-130289e16a26,Duyen Nguyen,712020:59b66c6e-1068-441c-9436-130289e16a26,04/Jun/25 2:39 PM,10/Jun/25 7:54 PM,,,,0,"h3. 1. Use Case Description

As a group chat participant, I want to add other users to the group from my contact list so that we can expand the conversation.
As a group chat admin, I want to remove users from the group so that I can manage the group’s participants effectively.

----

h3. 2. User Workflow (Step-by-Step)

*Add User Flow:*

# User enters an existing group chat.
# User taps on the group chat info icon or header to access participant settings.
# User selects “Add Member”.
# System opens contact picker screen.
# User selects one or more contacts.
# System adds selected users to the group.
# Invited users receive the group chat invite with Accept/Reject/Block options (handled via separate requirement).

*Remove User Flow (Admin only):*

# Admin taps on group chat info.
# Admin selects a member from the participants list.
# System shows “Remove from group” option.
# Admin confirms removal via modal.
# System removes the user from group and chat history remains.

----

h3. 3. Field Definitions Table

||Field Name||Field Type||Validation Rule||Mandatory||Description||
|Add Member Button|CTA Button|Visible to all users|Yes|Initiates contact picker to add members|
|Contact Picker|Multi-Select UI|Only shows existing contacts|Yes|Allows selecting multiple contacts|
|Remove Member|Option in Menu|Visible to admin only|Yes|Triggers confirmation modal|
|Confirmation Modal|Modal|Action must be confirmed|Yes|Prevents accidental removals|

----

h3. 4. Acceptance Criteria

*AC1.* Any user in a group chat can add other users by selecting them from their contact list.
*AC2.* When a new user is added, the group chat thread appears in their message list with a pending invitation.
*AC3.* Only group admins can remove members from the group.
*AC4.* If a non-admin user attempts to remove a participant, system blocks the action.
*AC5.* Admin sees “Remove from group” option in the participant details menu.
*AC6.* Removal action requires confirmation via modal before processing.
*AC7.* Removed users are no longer able to send or receive messages in the group.
*AC8.* Group history remains visible to other participants; removed users no longer have access.
*AC9.* Toast notifications for successful add or removal:

* Add: “You’ve added [Name] to the group.”
* Remove: “[Name] has been removed from the group.”
*AC10.* Removed users are not notified directly but will no longer see or access the chat.

----

h3. 5. System Rules

* Group chat must always retain at least 1 user (admin cannot remove themselves unless a new admin is assigned).
* Add flow is limited to contacts only — user must exist in sender’s contact list.
* A user cannot be added to a group they’ve previously blocked.
* Removed users can be re-added later unless they’ve blocked the group.

----

h3. 6. UX Optimizations

* Group info screen displays “Add Member” at the top of the participant list.
* Admins see a small “Admin” badge beside their name in the participant list.
* Remove action includes contextual info: “Are you sure you want to remove [Name]?”
* Toasts and success states follow standard design system used in message module.",,Duyen Nguyen,,712020:59b66c6e-1068-441c-9436-130289e16a26,,,,,,,,,,,,,,,,,com.atlassian.servicedesk.plugins.approvals.internal.customfield.ApprovalsCFValue@12be0c50,,,,,,,,,{},,,,,,,,,,,,,,,,,,MOBILE,,,,0|i02qov:,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,37255,AL-16,Messages,To Do,04/Jun/25 2:39 PM
[MB - Messages] Edit Group Info,AL-39,37380,Story,Specified,AL,AIOS Link,software,Kai Nguyễn,6214a4102d1a9d0069949c96,,Medium,,,,Duyen Nguyen,712020:59b66c6e-1068-441c-9436-130289e16a26,Duyen Nguyen,712020:59b66c6e-1068-441c-9436-130289e16a26,04/Jun/25 2:39 PM,04/Jun/25 5:22 PM,,,,0,"h4. 1. *Use Case Description*

As a user who is part of a group chat,
I want to edit the group name and avatar
so that the group reflects its purpose or participants more accurately.

----

h4. 2. *User Workflow (Step-by-Step)*

# User navigates to the desired group chat thread.
# User taps the *Group Info* header or “...” menu.
# User selects *Edit Group Info* option.
# System displays a form with current *Group Name* and *Group Avatar*.
# User updates the group name (or leaves unchanged).
# User selects a new image from device or uses the camera to capture a new photo.
# User taps *Save*.
# System validates input and updates group info.
# All participants see updated group name and avatar in their group chat.

----

h4. 3. *Field Definitions Table*

||Field Name||Field Type||Validation Rule||Mandatory||Description||
|Group Name|Text Input|Max 50 characters|Yes|Editable group label. If left blank, retains previous name.|
|Group Avatar|Image Upload|JPG/PNG, Max 2MB, square crop, or capture via camera|No|Optional new photo to replace group icon. Defaults to existing or stacked avatars.|

----

h4. 4. *Data Display Table (Group Info View)*

||Element||Display Type||Notes||
|Group Name|Header Text|Display updated name immediately across app.|
|Group Avatar|Image/Icon|Display new avatar or fallback to initials/stack.|
|Member Count|Subtext|Reflects real-time participant number.|
|Last Updated By|Text|""Last updated by [User Name]"" timestamped (optional).|

----

h4. 5. *Acceptance Criteria (AC)*

* *AC1:* Users can tap into group settings and see current group name and avatar.
* *AC2:* System accepts a new name if under 50 characters and not empty.
* *AC3:* System allows avatar update via gallery or camera.
* *AC4:* If no new data is submitted, system retains previous values.
* *AC5:* On success, group thread UI updates name and avatar immediately.
* *AC6:* All group participants receive a system message: “Group name/avatar updated by [User].”
* *AC7:* Changes do not require reconfirmation from other group members.
* *AC8:* System compresses avatar upload before saving to optimize storage.

----

h4. 6. *System Rules*

* Only group creator or admin (if role exists) can edit group info.
* Avatar images are resized to standard 1:1 ratio thumbnails.
* Change logs are not stored but reflected in group preview metadata.
* Multiple edits can be made, but changes are broadcast once user taps Save.

----

h4. 7. *UX Optimizations*

* Inline avatar editing from top header for quick access.
* Avatar preview with “Edit” pencil icon overlay.
* Name input has live character counter and validation.
* Toast: “Group info updated successfully.”",,Duyen Nguyen,,712020:59b66c6e-1068-441c-9436-130289e16a26,,,,,,,,,,,,,,,,,com.atlassian.servicedesk.plugins.approvals.internal.customfield.ApprovalsCFValue@d3a45c3,,,,,,,,,{},,,,,,,,,,,,,,,,,,MOBILE,,,,0|i02qon:,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,37255,AL-16,Messages,To Do,04/Jun/25 2:39 PM
[MB - Meeting Scheduling] Edit Meeting,AL-38,37377,Story,In Progress,AL,AIOS Link,software,Kai Nguyễn,6214a4102d1a9d0069949c96,,Medium,,,,Duyen Nguyen,712020:59b66c6e-1068-441c-9436-130289e16a26,Duyen Nguyen,712020:59b66c6e-1068-441c-9436-130289e16a26,04/Jun/25 2:37 PM,25/Jun/25 10:21 AM,,,,0,"h3. 1. Use Case Description

As a user, I want to request an update to the date, time, or location of a confirmed meeting so that I can adjust the meeting schedule if necessary. The change should only apply if the other party agrees.

----

h3. 2. User Workflow (Step-by-Step)

# User opens a confirmed meeting from the *Schedule* view.
# User taps *Edit Meeting*.
# System displays the current meeting details in editable form:
#* Meeting Date (Date Picker)
#* Meeting Time (Time Picker)
#* Location or Link (Text input)
# User submits updated info and taps *Send Update Request*.
# System sends a notification to the recipient with:
#* “You have a request to reschedule your meeting with [Sender Name].”
#* Options: *Accept Change* or *Decline Change*
# If recipient *accepts*:
#* Meeting is updated for both users.
#* Confirmation notification is sent to both parties.
# If recipient *declines* or takes no action within 24 hours:
#* Meeting remains unchanged.
#* Sender is notified: “Your update request was declined / expired.”

----

h3. 3. Field Definitions

h4. Input Fields

||Field Name||Type||Required||Description||
|Date|Date Picker|✅|Proposed new meeting date|
|Time|Time Picker|✅|Proposed new meeting time|
|Location/Link|Text Input|✅|Updated address or virtual meeting link|

h4. Interaction Elements

||Element Name||Type||Description||
|Edit Meeting Button|Button|Opens editable form|
|Send Update Request|Button|Submits updated fields for confirmation|
|Accept / Decline Buttons|Button|Displayed in recipient's notification|

----

h3. 4. Data Display

||Data Field||Data Type||Description||
|Pending Update|Badge|Shown on meetings awaiting confirmation|
|Change History|Text|Logs timestamp and fields changed|
|Confirmation Toast|Toast|Shown after user sends or accepts update|

----

h3. 5. Acceptance Criteria

* *AC1*: Only meetings with status “Accepted” can be updated.
* *AC2*: Update applies only after recipient confirms.
* *AC3*: System notifies both users on outcome (accepted, declined, expired).
* *AC4*: Pending requests disable further edits until resolved.

----

h3. 6. System Rules

* Only the original sender may initiate updates.
* Recipients can only accept or decline; they cannot edit.
* Audit log stores all updates and responses.

----

h3. 7. UX Optimizations

* Display status tag: _“Pending Update”_
* Inline preview of proposed changes in recipient notification
* Show countdown until request expires
* Offer “Withdraw Update” button to sender",,Duyen Nguyen,,712020:59b66c6e-1068-441c-9436-130289e16a26,,,,,,,,,,,,,,,,,com.atlassian.servicedesk.plugins.approvals.internal.customfield.ApprovalsCFValue@1fc78bdb,,,,,,,,,{},,,,,,,,,,,,,,,,,,MOBILE,,,,0|i02qlq:zr,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,37636,AL-68,Meeting Scheduling,In Progress,25/Jun/25 10:21 AM
[MB - Meeting Scheduling] View Schedule,AL-37,37374,Story,In Progress,AL,AIOS Link,software,Kai Nguyễn,6214a4102d1a9d0069949c96,,Medium,,,,Duyen Nguyen,712020:59b66c6e-1068-441c-9436-130289e16a26,Duyen Nguyen,712020:59b66c6e-1068-441c-9436-130289e16a26,04/Jun/25 2:16 PM,25/Jun/25 10:21 AM,,,,0,"h3. 1. Use Case Description

As a user, I want to view all my upcoming meetings, events, and booked services in a single schedule view, so that I can manage my time efficiently and keep track of my commitments within the app.

----

h3. 2. User Workflow (Step-by-Step)

* Step 1: User navigates to the *Schedule* section from dashboard shortcut.
* Step 2: System loads a consolidated view of all future appointments including:
** Confirmed *Meetings* (via Invite to Meet)
** Registered *Events*
** Confirmed *Booked Services* (from Marketplace or Gifting)
* Step 3: User scrolls through upcoming items sorted by *date & time*.
* Step 4: Tapping on any item opens a *detail view* (e.g., contact, time, location, notes).
* Step 5: If applicable, user may *cancel* or *reschedule* (based on event/service rules).

----

h3. 3. Field Definitions Table

h4. Data Fields (Schedule Entries)

||Field Name||Field Type||Required||Description||
|Schedule Type|Enum|✅|One of: Meeting, Event, Booked Service|
|Date & Time|Datetime|✅|Scheduled time of the activity|
|Title|Text|✅|Activity title or subject|
|Related Contact|User Link|✅ if Meeting|Who the meeting or service is with|
|Location / Link|Text/URL|✅|Address or virtual meeting link|
|Status|Badge|✅|Confirmed, Cancelled, Rescheduled, Completed|

----

h3. 4. Data Display Table (Card View)

||Data Name||Data Type||Display When Empty||Format||Description||
|Date Header|Text|“No events yet”|“Today”, “Tomorrow”|Group label by day|
|Schedule Card|Card|Hidden|Structured card|Displays icon, title, time, location|
|Type Icon|Icon|Hidden|Event/Meeting/Service|Visual cue for entry type|
|Status Tag|Badge|“Pending”|e.g., Confirmed|Status color-coded|

----

h3. 5. Acceptance Criteria

* *AC1*: Schedule screen must display all confirmed future meetings, events, and services.
* *AC2*: Entries are grouped and sorted by date in ascending order.
* *AC3*: Each card must show time, type icon, title, and location/link.
* *AC4*: Tapping a card opens detail view with full metadata.
* *AC5*: System must exclude cancelled or expired items by default (filter optional).
* *AC6*: Supports scrollable list with lazy loading for long schedules.

----

h3. 6. System Rules

* Schedule data is synced in real time with event, meeting, and service modules.
* Past entries are not shown unless user activates a “History” toggle.
* Cancelled items must be retained in backend but excluded from default view.
* All times are normalized to user’s device timezone.

----

h3. 7. UX Optimizations

* Sticky day headers (e.g., “Today”, “This Week”) while scrolling.
* Use icons or colored borders to differentiate between Meeting, Event, and Service.
* Optionally allow “Add to Calendar” or export (ICS) for Pro/Premium users.
* Empty state message: _“No scheduled activities. Start by inviting or booking!”_
* Quick filters at the top: [All] [Meetings] [Events] [Services]

----",,Duyen Nguyen,,712020:59b66c6e-1068-441c-9436-130289e16a26,,,,,,,,,,,,,,,,,com.atlassian.servicedesk.plugins.approvals.internal.customfield.ApprovalsCFValue@3f9202e9,,,,,,,,,{},,,,,,,,,,,,,,,,,,MOBILE,,,,0|i02qlq:zi,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,37636,AL-68,Meeting Scheduling,In Progress,25/Jun/25 10:21 AM
[MB - Send Gift] Accept/Reject Gift,AL-36,37371,Story,In Progress,AL,AIOS Link,software,Kai Nguyễn,6214a4102d1a9d0069949c96,,Medium,,,,Duyen Nguyen,712020:59b66c6e-1068-441c-9436-130289e16a26,Duyen Nguyen,712020:59b66c6e-1068-441c-9436-130289e16a26,04/Jun/25 2:15 PM,25/Jun/25 10:21 AM,,,,0,"h3. 1. Use Case Description

As a recipient of a digital gift, I want the ability to accept or reject the gift within 14 days, so that I have control over whether to receive it and the sender can be informed of the outcome or retrieve it if unclaimed.

----

h3. 2. User Workflow (Step-by-Step)

# User receives a *Gift Notification* and sees a new item in the *Gift Inbox*.
# User taps on the gift to view details:
#* Sender name
#* Gift type (ticket or product)
#* Gift name
#* Date received
# User chooses one of the following:
#* *Accept Gift*
→ Gift is transferred to the corresponding tab:
#** Ticket → appears in “My Tickets”
#** Product → appears in “My Digital Products”
→ Sender receives a confirmation notification.
#* *Reject Gift*
→ Gift is returned to the sender.
→ Sender is notified of rejection.
# If no action is taken for *14 days*, the system:
#* Automatically returns the gift to the sender.
#* Sends expiration notification to both sender and receiver.

----

h3. 3. Field Definitions Table

h4. Interaction Elements

||Element Name||Type||Required||Description||
|Accept Button|Button|✅|Confirms acceptance; adds gift to inventory|
|Reject Button|Button|✅|Declines gift; returns to sender|
|Gift Detail View|Card|✅|Displays sender, type, and expiration|

----

h3. 4. Data Display Table (Card View)

||Data Field||Data Type||Display When Empty||Format||Description||
|Gift Name|Text|“Unnamed Gift”|Plaintext|Name of ticket or product|
|Gift Type|Badge|“Unknown”|Ticket/Product|Visual tag indicating type|
|Sender Name|Text|“Unknown”|Full Name|Who sent the gift|
|Expiration Time|Countdown|“14d”|Remaining duration|Time left to respond|
|Gift Status|Badge|“Pending”|Accepted/Rejected/Expired|Current state of gift|

----

h3. 5. Acceptance Criteria

* *AC1*: User must be able to accept or reject any received gift from the Gift Inbox.
* *AC2*: Accepted gifts are transferred to either the Tickets or Products section.
* *AC3*: Rejected gifts are returned to the original sender.
* *AC4*: Gift auto-expires and is returned to sender if no action is taken in 14 days.
* *AC5*: Both sender and receiver are notified upon acceptance, rejection, or expiration.
* *AC6*: User cannot accept an already expired or recalled gift.

----

h3. 6. System Rules

* Each gift has a {{sent_at}} timestamp and a {{14-day response window}}.
* If gift is not accepted/rejected in 14 days:
** Status → “Expired”
** Gift is removed from recipient inbox and restored to sender's inventory
* Gift acceptance updates the ownership record (gift_id → recipient_id).
* Sender cannot revoke gift during active waiting period unless rejected/expired.

----

h3. 7. UX Optimizations

* Countdown indicator: “Respond within X days” under each pending gift.
* Accept/Reject as sticky buttons in gift detail view.
* Toasts:
** Accept: _“Gift added to your [Tickets/Products]. Sender notified.”_
** Reject: _“Gift declined and returned to [Sender].”_
** Expired: _“Gift request expired. Returned to sender.”_
* Badge on Gift Inbox tab: e.g., “2 Pending Gifts”

----",,Duyen Nguyen,,712020:59b66c6e-1068-441c-9436-130289e16a26,,,,,,,,,,,,,,,,,com.atlassian.servicedesk.plugins.approvals.internal.customfield.ApprovalsCFValue@2b2df78,,,,,,,,,{},,,,,,,,,,,,,,,,,,MOBILE,,,,0|i02qlq:z,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,37257,AL-18,Send Gift,In Progress,25/Jun/25 10:21 AM
[WEB - Marketplace] Remove Product from Marketplace,AL-35,37368,Story,In Progress,AL,AIOS Link,software,Kai Nguyễn,6214a4102d1a9d0069949c96,,Medium,,,,Duyen Nguyen,712020:59b66c6e-1068-441c-9436-130289e16a26,Duyen Nguyen,712020:59b66c6e-1068-441c-9436-130289e16a26,04/Jun/25 2:15 PM,25/Jun/25 10:21 AM,23/Jun/25 10:32 PM,,,0,"h1. Remove Product from Marketplace Requirement Specification

h3. 1. Use Case Description

As an admin on the web platform, I want to remove a product listing from the marketplace so that it no longer appears for purchase while preserving historical orders.

h3. 2. User Workflow

* Step 1: Admin opens the *Actions* gear icon in the Marketplace Product List row and selects *Remove from Marketplace*, or taps *Remove* inside the Product Details page.
* Step 2: System displays a confirmation modal explaining that the product will disappear from the marketplace and active carts but remain in existing orders.
* Step 3: Admin confirms the removal.
* Step 4: System removes the listing, updates the Marketplace Product List, and shows a success message.

h3. 3. Field Definitions

h4. Interaction Elements

||Element Name||Type||Trigger Condition||Description||
|Remove Button|Button|Visible in Product Details page|Initiates removal workflow|
|Remove Option|Popover Item|Available under Actions gear icon|Initiates removal workflow|
|Confirmation Modal|Dialog|Shown after Remove pressed|Asks admin to confirm removal|
|Confirm Removal|Button|Enabled within modal|Confirms removal of listing|
|Cancel Removal|Button|Enabled within modal|Dismisses modal without changes|

h3. 4. Data Display (Summary)

||Data Name||Data Type||Display When Empty||Format||Description||
|Success Message|Toast|Hidden|Plaintext|Displayed when product is removed|
|Error Message|Text|Hidden|Plaintext|Shows validation or server errors|

h3. 5. Acceptance Criteria

* AC1: Remove option is available from both the Marketplace Product List and Product Details page.
* AC2: Confirmation modal clearly states that removed products disappear from the marketplace and carts but remain in all orders.
* AC3: After confirmation, product no longer appears in the Marketplace Product List.
* AC4: Success message appears after a successful removal.
* AC5: System prevents removal if the product has active unfulfilled orders and shows an error message.

h3. 6. System Rules

* Removal only updates marketplace listing status; original product record remains intact.
* Cart entries referencing the removed product are deleted immediately.
* Audit log records admin ID and timestamp of removal action.

h3. 7. UX Optimizations

* Prefill modal with product name for clarity.
* Display spinner inside modal while processing removal.
* Return focus to previous table row after successful removal.",,Duyen Nguyen,,712020:59b66c6e-1068-441c-9436-130289e16a26,,,,,,,,,,,,,,,,,com.atlassian.servicedesk.plugins.approvals.internal.customfield.ApprovalsCFValue@600c9ff2,,,,,,,,,{},,,,,,,,,,,,,,,,,,WEB,,,,0|i02qlq:y,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,37259,AL-20,Marketplace,In Progress,25/Jun/25 10:21 AM
[MB - Send Gift] Send Gift,AL-34,37365,Story,In Progress,AL,AIOS Link,software,Kai Nguyễn,6214a4102d1a9d0069949c96,,Medium,,,,Duyen Nguyen,712020:59b66c6e-1068-441c-9436-130289e16a26,Duyen Nguyen,712020:59b66c6e-1068-441c-9436-130289e16a26,04/Jun/25 2:14 PM,25/Jun/25 10:21 AM,,,,0,"h3. 1. Use Case Description

As a mobile user, I want to send a digital gift—either an event ticket or a previously purchased product—to one of my contacts, so that I can share value and strengthen relationships directly through the platform.

----

h3. 2. User Workflow (Step-by-Step)

h4. A. Workflow 1: From Home Dashboard

# User taps *“Send Gift”* CTA on the home dashboard.
# System opens the *Contact/Lead List*.
# User selects a recipient.
# System prompts:
#* *Choose Gift Type*:
#** *Send Ticket*
#** *Send Product from Marketplace*
# If *Send Ticket* is selected → System navigates to the *My Tickets tab* under Events.
#* User browses and selects a ticket to gift.
# If *Send Product* is selected → System navigates to the *My Completed Orders tab* under Marketplace.
#* User selects a product or service to send.
# User confirms the selection and taps “Send.”
# System delivers the gift to the recipient and displays confirmation.

h4. B. Workflow 2: From Contact Profile

# User views a contact’s profile.
# User taps *“Send Gift”*.
# Same flow continues as in Steps 4–8 above.

----

h3. 3. Field Definitions Table

h4. Input Fields

||Field Name||Field Type||Required||Validation Rules||Description||
|Recipient|Selector|✅|Must be an existing contact or lead|Who will receive the gift|
|Gift Type|Choice Button|✅|“Send Ticket” or “Send Product”|Defines gift type|
|Ticket|Item Selector|✅ if Ticket selected|Must be unused & transferable|Chosen event ticket|
|Product|Item Selector|✅ if Product selected|Must be digital & fulfilled|Chosen product from completed orders|

----

h3. 4. Data Display Table (Card View)

||Display Field||Data Type||Display When Empty||Format||Description||
|Contact Name|Text|“Not Selected”|Full Name|Gift recipient|
|Gift Type|Tag/Badge|“Not Selected”|Ticket/Product|Chosen gift type|
|Item Name|Text|“No item selected”|Plaintext|Ticket title or product name|
|Confirmation Toast|Text|Hidden|“Gift sent to…”|Confirmation after send|

----

h3. 5. Acceptance Criteria

* *AC1*: Both workflows (Home CTA and Contact Profile) must allow access to gift selection.
* *AC2*: User must choose between sending a ticket or product.
* *AC3*: On choosing “Send Ticket”, system must load user’s valid tickets.
* *AC4*: On choosing “Send Product”, system must load completed digital orders.
* *AC5*: Only transferable, unused tickets and fulfilled digital products can be sent.
* *AC6*: Gift is delivered digitally, and recipient receives a notification.
* *AC7*: Sending confirmation message must appear after success.

----

h3. 6. System Rules

* Gifts can only be sent once per order item or ticket ID.
* Sender must have ownership of the item being gifted.
* Gifting action logs are stored with gift ID, sender ID, recipient ID, and timestamp.
* Gifting a ticket marks it as “transferred” and prevents reuse by sender.
* Gifting a product sends a one-time access token or delivery to the recipient's profile.

----

h3. 7. UX Optimizations

* Use product/ticket thumbnails in selection list for visual clarity.
* Confirmation modal: _“Send [item] to [contact]? This action cannot be undone.”_
* Toast: _“Gift sent to [Name]. They’ll receive it instantly.”_
* Auto-return to home/dashboard after send.
* Add filters in ticket/product selection screens (e.g., “Available Only”).

----",,Duyen Nguyen,,712020:59b66c6e-1068-441c-9436-130289e16a26,,,,,,,,,,,,,,,,,com.atlassian.servicedesk.plugins.approvals.internal.customfield.ApprovalsCFValue@43b6497b,,,,,,,,,{},,,,,,,,,,,,,,,,,,MOBILE,,,,0|i02qlq:x,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,37257,AL-18,Send Gift,In Progress,25/Jun/25 10:21 AM
[MB - Meeting Scheduling] Accept/Reject Invite to Meet,AL-33,37362,Story,In Progress,AL,AIOS Link,software,Kai Nguyễn,6214a4102d1a9d0069949c96,,Medium,,,,Duyen Nguyen,712020:59b66c6e-1068-441c-9436-130289e16a26,Duyen Nguyen,712020:59b66c6e-1068-441c-9436-130289e16a26,04/Jun/25 2:12 PM,25/Jun/25 10:21 AM,,,,0,"h3. 1. Use Case Description

As a user who receives a meeting invitation, I want to be able to accept or reject the invite, so that I can control my availability and confirm or decline meetings while notifying the inviter accordingly.

----

h3. 2. User Workflow (Step-by-Step)

# User receives an *Invite to Meet* notification or sees the invite in the inbox/activity feed.
# User taps the invitation to view the meeting details (date, time, contact, meeting type).
# User chooses either:
#* *Accept Invite* → Meeting is added to user’s schedule, and a confirmation is sent to the inviter.
#* *Reject Invite* → A rejection message is sent to the inviter; no meeting is created.
# System displays confirmation toast and returns to previous screen.

----

h3. 3. Field Definitions Table

h4. Interaction Elements

||Element Name||Type||Trigger Condition||Description||
|Accept Invite|Button|Always visible|Confirms the meeting and adds to schedule|
|Reject Invite|Button|Always visible|Declines the invite and notifies inviter|
|Invite Details|Card View|On invitation open|Displays time, location/link, sender, type|

----

h3. 4. Data Display Table (Card View)

||Data Name||Data Type||Display When Empty||Format||Description||
|Meeting Type|Text|“TBD”|“In Person”/“Virtual”|Displayed on invite detail screen|
|Meeting Location|Text/Link|“Not Provided”|Address or URL|Based on invite type|
|Sender Name|Text|“Unknown”|Plaintext|Who sent the invite|
|Date & Time|Datetime|“Pending”|Relative format|Scheduled time of meeting|

----

h3. 5. Acceptance Criteria

* *AC1*: User must be able to accept or reject an invitation from both push notification and in-app interface.
* *AC2*: Accepting an invite automatically creates a calendar/schedule entry for the recipient.
* *AC3*: Inviter receives a confirmation notification upon acceptance.
* *AC4*: Rejecting an invite sends a rejection notification to the inviter.
* *AC5*: No schedule is created if invite is rejected.
* *AC6*: User cannot accept an expired or revoked invite (system should handle gracefully).

----

h3. 6. System Rules

* Schedule entries are bi-directional: both users see the confirmed meeting.
* Notifications must indicate invite status (“Accepted”, “Rejected”) and show sender/recipient.
* Once an invite is responded to, response buttons are disabled or replaced with status tag.
* All responses are logged in the system for audit trail.

----

h3. 7. UX Optimizations

* Use icons for Accept (✓) and Reject (✕) with clear color cues (green/red).
* Confirmation toast:
** On accept: _“You’ve accepted the invite. Meeting added to your schedule.”_
** On reject: _“You’ve declined the invite. Notification sent to [Sender Name].”_
* Inline feedback: “Awaiting response” badge replaced with “Accepted” or “Rejected”.
* Allow tapping sender’s name to view their profile before deciding.
* Provide optional comments on rejection (future enhancement).",,Duyen Nguyen,,712020:59b66c6e-1068-441c-9436-130289e16a26,,,,,,,,,,,,,,,,,com.atlassian.servicedesk.plugins.approvals.internal.customfield.ApprovalsCFValue@6c9c9eae,,,,,,,,,{},,,,,,,,,,,,,,,,,,MOBILE,,,,0|i02qlq:v,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,37636,AL-68,Meeting Scheduling,In Progress,25/Jun/25 10:21 AM
[MB - Meeting Scheduling] Meeting Details,AL-32,37359,Story,In Progress,AL,AIOS Link,software,Kai Nguyễn,6214a4102d1a9d0069949c96,,Medium,,,,Duyen Nguyen,712020:59b66c6e-1068-441c-9436-130289e16a26,Duyen Nguyen,712020:59b66c6e-1068-441c-9436-130289e16a26,04/Jun/25 2:10 PM,25/Jun/25 10:21 AM,,,,0,"h3. 1. Use Case Description

As a mobile user, I want to view all relevant details of a scheduled meeting so that I can confirm the time, location, and participants, and take further actions like updating or canceling the meeting if I am the inviter.

----

h3. 2. User Workflow (Step-by-Step)

# User opens the *Schedule* screen.
# User taps on a scheduled meeting.
# System navigates to the *Meeting Detail* screen displaying:
#* Meeting Title (e.g., “Meeting with John D.”)
#* Meeting Type: In Person / Virtual
#* Date & Time
#* Location (or virtual meeting link)
#* Status: Scheduled / Pending Change / Cancelled
#* Inviter and Invitee names
#* Action buttons (based on user role and meeting state)
# If user is the *inviter*:
#* Can tap *Edit Meeting* (if accepted and not pending change)
#* Can tap *Delete Meeting*
# If user is the *recipient* and a change request is pending:
#* Can tap *Accept Change* or *Decline Change*
# User taps *Back* to return to the Schedule screen.

----

h3. 3. Field Definitions

||Field Name||Data Type||Description||
|Meeting Type|Badge|“In Person” or “Virtual”|
|Meeting Date|Date|Scheduled meeting date|
|Meeting Time|Time|Scheduled time|
|Location / Link|Text / URL|Address or virtual meeting link|
|Status|Badge|Scheduled / Pending Change / Cancelled|
|Participants|Text|Names of inviter and invitee|

----

h3. 4. Interaction Elements

||Element Name||Type||Visible When||Description||
|Edit Meeting|Button|If user is inviter & meeting is confirmed|Opens update flow|
|Delete Meeting|Button|If user is inviter|Opens delete confirmation modal|
|Accept Change|Button|If user is invitee & update pending|Accepts the proposed changes|
|Decline Change|Button|If user is invitee & update pending|Rejects the proposed changes|
|Back|Button|Always visible|Returns to the Schedule screen|

----

h3. 5. Acceptance Criteria

* *AC1*: Meeting Detail must display all fields: type, date, time, location/link, participants, and status.
* *AC2*: Edit and Delete buttons appear only for the user who created the invite.
* *AC3*: Accept/Decline Change appears only when an update is pending and user is the recipient.
* *AC4*: Back button navigates cleanly back to previous screen.
* *AC5*: Virtual meeting links must be tappable and open in browser or app.

----

h3. 6. System Rules

* Data is loaded based on meeting ID and real-time status.
* Meeting status reflects latest update request or cancellation.
* If the meeting is expired or cancelled, all actions are disabled.
* Any changes made from this screen must sync with the Schedule module.

----

h3. 7. UX Optimizations

* Use calendar and clock icons next to date/time.
* Format date/time in local user timezone.
* Show profile photos next to participant names.
* Highlight pending changes in yellow badge.
* Add copy/share icon next to virtual link. ",,Duyen Nguyen,,712020:59b66c6e-1068-441c-9436-130289e16a26,,,,,,,,,,,,,,,,,com.atlassian.servicedesk.plugins.approvals.internal.customfield.ApprovalsCFValue@78c1c55a,,,,,,,,,{},,,,,,,,,,,,,,,,,,MOBILE,,,,0|i02qlq:r,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,37636,AL-68,Meeting Scheduling,In Progress,25/Jun/25 10:21 AM
[MB - Messages] Create Group Chat,AL-31,37356,Story,In Progress,AL,AIOS Link,software,Kai Nguyễn,6214a4102d1a9d0069949c96,,Medium,,,,Duyen Nguyen,712020:59b66c6e-1068-441c-9436-130289e16a26,Duyen Nguyen,712020:59b66c6e-1068-441c-9436-130289e16a26,04/Jun/25 2:09 PM,25/Jun/25 10:21 AM,,,,0,"h3. 1. Use Case Description

*As a user*, I want to create a group chat by selecting multiple contacts from my contact list
*so that* I can message several people at once in a shared conversation space.

----

h3. 2. User Workflow (Step-by-Step)

# User navigates to the *Messages tab* from the dashboard.
# User taps the *“+” button* to create a new chat.
# User selects *“Add Group Chat”* from the action sheet.
# System displays the *Contact Picker screen*.
# User selects two or more contacts from their contact list.
# User taps *“Next”* and enters the group name.
# User confirms to *create the group*.
# System creates the group chat and redirects to the new group message thread.

----

h3. 3. Field Definitions Table

||Field Name||Field Type||Validation Rule||Mandatory||Description||
|Group Name|Text Input|Max 50 characters|Yes|Name displayed in group chat header|
|Group Avatar|Image Upload|JPG/PNG, Max size 2MB or capture via device camera|No|Optional uploaded image shown in group header. Defaults to stacked avatars or icon.|
|Selected Contacts|List|Minimum 2 contacts|Yes|Users added to the group|
|Group ID|Auto-Gen|Unique identifier for group|Yes|Used for backend routing and history|

----

h3. 4. Data Display Table (Card View)

||Field||Format Example||Notes||
|Group Name|“AIOS Biz Network”|Shown in chat list and header bar|
|Member Avatars|Stack of 3 avatars|Represent most recent participants|
|Last Message|“You: See you there!”|Shown in preview on Messages list|
|Timestamp|“2h ago”|Relative or absolute message time|

----

h3. 5. Acceptance Criteria

* *AC1:* “+” button must be visible on the Messages tab when a user is signed in.
* *AC2:* Tapping “+” must open an action sheet with options: “New Message” and “Add Group Chat.”
* *AC3:* Selecting “Add Group Chat” must open a contact picker allowing multiple selections.
* *AC4:* System must enforce a minimum of 2 participants to proceed to the group setup screen.
* *AC5:* User must enter a valid group name before confirming group creation.
* *AC6:* Upon creation, the system redirects user to the newly created group chat.
* *AC7:* All selected participants must receive a system-generated message like:
_“You’ve been added to a new group: {Group Name}”_
* *AC8:* Group metadata (name, members, group ID) must be stored and retrievable for all future sessions.
* *AC9:* A group chat must appear in all members’ message list with the correct group name and participants.

----

h3. 6. System Rules

* Group must have a *minimum of 3 users* (creator + 2 contacts).
* Group ID must be globally unique and stored with group metadata.
* Only *contacts already in the user’s contact list* can be invited to a group.
* All group messages are delivered via existing messaging infrastructure (no separate protocol).
* Group name can be edited later (via a separate setting screen).

----

h3. 7. UX Optimizations (Based on Industry UX Patterns)

* Use *multi-select chips* for selected contacts.
* Display *live participant count* (e.g., “3 selected”).
* Allow back-navigation from group name screen without losing selection.
* Use default group icon (3-person stack) if no custom icon is provided.
* Highlight CTA when minimum selection is met.",,Duyen Nguyen,,712020:59b66c6e-1068-441c-9436-130289e16a26,,,,,,,,,,,,,,,,,com.atlassian.servicedesk.plugins.approvals.internal.customfield.ApprovalsCFValue@7cb636bf,,,,,,,,,{},,,,,,,,,,,,,,,,,,MOBILE,,,,0|i02qlq:i,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,37255,AL-16,Messages,In Progress,25/Jun/25 10:21 AM
[MB - Messages] Delete Chat Thread,AL-30,37353,Story,In Progress,AL,AIOS Link,software,Kai Nguyễn,6214a4102d1a9d0069949c96,,Medium,,,,Duyen Nguyen,712020:59b66c6e-1068-441c-9436-130289e16a26,Duyen Nguyen,712020:59b66c6e-1068-441c-9436-130289e16a26,04/Jun/25 2:09 PM,25/Jun/25 10:20 AM,,,,0,"h3. 1. Use Case Description

*As a user*, I want to delete a chat thread from my Messages tab
*so that* I can clear my conversation list without affecting the other participant's copy of the chat.

----

h3. 2. User Workflow (Step-by-Step)

# User navigates to the *Messages tab*.
# User long-presses or swipe left & taps on the *more options (•••)* menu of a specific chat thread.
# User selects *“Delete Chat”* from the list of actions.
# System displays a *confirmation modal* stating that this action only deletes the chat from their device.
# User confirms deletion.
# System removes the thread from the user's local view only.
# If a new message is later received from that contact, a new thread is created.

----

h3. 3. Field Definitions Table

||Field Name||Field Type||Validation Rule||Mandatory||Description||
|Chat Thread ID|String|Must exist in user inbox|Yes|Unique ID of the chat thread to be deleted|
| | | | | |

----

h3. 4. Data Display Table (Card View)

||Field||Format Example||Notes||
|Chat Name|“Danny Tran” or “Group Chat A”|Header of chat card in message list|
|Last Message|“You: Thanks for today.”|Not visible after deletion|
|Timestamp|“3d ago”|Not visible after deletion|
|Restore Event|Not supported|Deleted threads are removed until re-initiated|

----

h3. 5. Acceptance Criteria

* *AC1:* The Messages screen must allow users to access deletion via long-press or options menu.
* *AC2:* System must show a confirmation modal with clear language:
_“This will delete the chat from your view only. The other person will still keep their copy.”_
* *AC3:* Upon confirmation, the thread is permanently removed from the user’s local app.
* *AC4:* The deletion does *not* delete the chat for the other participant(s).
* *AC5:* Group chats can also be deleted locally by the user without affecting other members’ threads.
* *AC6:* Deleted thread must be removed from local search results.
* *AC7:* User should not be able to undo or recover a locally deleted thread from UI.

----

h3. 6. System Rules

* Deletion affects only the *signed-in user’s local thread record*.
* Messages remain on the server unless a global delete (admin or other feature) is invoked.
* Deleted thread IDs are disassociated from the local message index.
* Push notifications for new messages from deleted threads are still delivered.
* Group chats behave the same: delete removes the local thread, not the group itself.

----

h3. 7. UX Optimizations (Based on Industry UX Patterns)

* Use a *red warning icon* in the confirmation modal.
* Provide *short haptic feedback* on long press for consistency.
* Confirmation modal should include *“Cancel”* and *“Delete”* as clearly labeled buttons.
* Deleted threads should *not display any success toast* to avoid accidental confirmation confusion.
* Do not offer archive if delete and archive are not both supported.",,Duyen Nguyen,,712020:59b66c6e-1068-441c-9436-130289e16a26,,,,,,,,,,,,,,,,,com.atlassian.servicedesk.plugins.approvals.internal.customfield.ApprovalsCFValue@7e611652,,,,,,,,,{},,,,,,,,,,,,,,,,,,MOBILE,,,,0|i02qlq:,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,37255,AL-16,Messages,In Progress,25/Jun/25 10:20 AM
[MB - Messages] View Chat Thread List,AL-29,37350,Story,In Progress,AL,AIOS Link,software,Kai Nguyễn,6214a4102d1a9d0069949c96,,Medium,,,,Duyen Nguyen,712020:59b66c6e-1068-441c-9436-130289e16a26,Duyen Nguyen,712020:59b66c6e-1068-441c-9436-130289e16a26,04/Jun/25 2:09 PM,25/Jun/25 10:20 AM,,,,0,"h3. 1. Use Case Description

As a user, I want to view all my existing chat threads in a single list, ordered by the most recent activity, so that I can quickly access ongoing conversations and see user availability.

----

h3. 2. User Workflow (Step-by-Step)

# User opens the Messages tab in the mobile app.
# System loads all 1:1 and group chat threads the user is a part of.
# Threads are sorted in descending order of most recent message sent or received.
# For each 1:1 chat thread:
#* Display profile picture of the contact.
#* Show availability indicator (green, red, gray, or hidden).
# For each group chat thread:
#* Display group avatar (if set) or collage of participant avatars (up to 3).
#* No availability indicators are shown.
# User taps a thread to enter the corresponding chat screen.

----

h3. 3. Field Definitions Table

||Field Name||Field Type||Validation Rule||Mandatory||Description||
|Chat Thread List|Scroll List|Must include all active chat IDs|Yes|List of active chat threads with preview of last message|
|Last Message Timestamp|Datetime Label|ISO format, used for sorting|Yes|Used to determine display order (most recent first)|
|Profile Picture|Image|User-uploaded or default fallback|Yes|Displays circular avatar for 1:1 threads|
|Availability Indicator|Dot Icon|Green (Active), Red (DND), Gray (Offline), None|No|Real-time status shown for 1:1 only|
|Group Avatar|Image/Stacked UI|Image upload or truncated stack of avatars|Yes|If no group icon set, fallback to collage of first 3 member avatars|

----

h3. 4. Acceptance Criteria

*AC1.* All chat threads are ordered by most recent message timestamp in descending order.
*AC2.* Each 1:1 chat thread shows:

* Contact’s profile picture
* Contact name
* Preview of the latest message
* Timestamp of the last message
* Availability indicator based on user status:
** Green = Active
** Red = Do Not Disturb
** Gray = Offline
** No icon if availability is disabled

*AC3.* Each group chat thread shows:

* Group avatar if available
* Otherwise, a collage of avatars from up to 3 participants
* No availability indicator shown

*AC4.* If a thread has unread messages, it is visually indicated (e.g. bold text, badge counter).
*AC5.* Empty thread list state shows friendly placeholder and CTA: “Start a New Chat”.

----

h3. 5. System Rules

* Availability status must update in real-time and sync with presence service.
* Threads are sorted client-side based on {{last_message_timestamp}}.
* Threads without messages are not shown unless a draft exists.
* If user disables availability, indicator is suppressed in 1:1 thread.
* For group chats with no avatar, use top-left, top-right, and bottom center alignment for 3-avatar collage.

----

h3. 6. UX Optimizations

* Profile images and availability indicators are aligned left for 1:1 threads.
* Group threads show avatars at fixed dimensions (e.g. 36px), cropped into collage layout.
* Tap target area includes the entire row for better usability.
* Dark mode-friendly icon colors and contrast handling.
* Sticky header with “New Chat” button always visible at top of screen.",,Duyen Nguyen,,712020:59b66c6e-1068-441c-9436-130289e16a26,,,,,,,,,,,,,,,,,com.atlassian.servicedesk.plugins.approvals.internal.customfield.ApprovalsCFValue@630edb6b,,,,,,,,,{},,,,,,,,,,,,,,,,,,MOBILE,,,,0|i02qlp:,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,37255,AL-16,Messages,In Progress,25/Jun/25 10:20 AM
[MB - Messages] Create a new 1:1 chat,AL-28,37347,Story,In Progress,AL,AIOS Link,software,Kai Nguyễn,6214a4102d1a9d0069949c96,,Medium,,,,Duyen Nguyen,712020:59b66c6e-1068-441c-9436-130289e16a26,Duyen Nguyen,712020:59b66c6e-1068-441c-9436-130289e16a26,04/Jun/25 2:08 PM,25/Jun/25 10:20 AM,,,,0,"h3. 1. Use Case Description

As a user, I want to initiate a one-on-one chat with a contact
so that I can have private conversations with individual users directly from their profile or the messaging module.

----

h3. 2. User Workflow (Step-by-Step)

h4. 🟦 Flow A: Start Chat from Contact Profile

# User navigates to *Contacts tab* and taps on a contact’s name.
# System loads the *Contact Detail screen*.
# User taps the *“Message”* icon.
# System checks if a 1:1 chat thread with that contact already exists:
#* If exists → redirect to that chat thread.
#* If not → create a new 1:1 chat and open the message screen.
# User composes and sends a message.

h4. 🟦 Flow B: Start Chat from Messaging Module

# User navigates to the *Messages tab*.
# User taps the *“+” (Add Chat)* button.
# System displays a searchable *Contact Picker* modal.
# User selects *one contact* from their Contact List.
# System either:
#* Opens an existing chat thread if one already exists.
#* Or creates a new 1:1 chat thread and navigates to the message screen.
# User sends a message.

----

h3. 3. Field Definitions Table

||Field Name||Field Type||Validation Rule||Mandatory||Description||
|Contact Picker|Search + List|Must be from Contact List only|Yes|Selects one user to start 1:1 chat|
|Chat Thread ID|UUID|System-generated|Yes|Unique thread identifier between 2 users|
|Message Content|Text, Media|Max 4,096 characters or file rules|Yes|Input for sending the initial message|

----

h3. 4. Data Display Table (Chat Thread Card)

||Display Element||Format Example||Notes||
|Contact Name|“Sarah Lee”|Full name of the recipient|
|Avatar|Circle avatar image|Taps into chat screen|
|Last Message Preview|“See you at 3pm!”|Displays latest message sent/received|
|Timestamp|“2h ago”|Time of last message|

----

h3. 5. Acceptance Criteria (AC)

* *AC1:* Tapping the “Message” icon in Contact Profile must open or create a 1:1 chat thread.
* *AC2:* Messaging module’s “+” action must show a searchable list of user’s contacts only.
* *AC3:* Only one 1:1 chat thread can exist between any two users.
* *AC4:* If a thread exists, system must *reuse* it instead of creating a duplicate.
* *AC5:* The chat thread must be accessible from the Messages tab once initiated.
* *AC6:* Initial message must be editable and sendable in real-time via text input field.
* *AC7:* If selected contact is blocked or removed, “Message” button must be hidden or disabled.
* *AC8:* Toast confirmation appears if the new thread is successfully created:
_“Chat with [Name] started.”_
* *AC9:* Users can send message attachments (optional spec, if messaging supports media).

----

h3. 6. System Rules

* A chat thread is created only when no existing 1:1 thread between the two users is found.
* Contact Picker should prevent selecting more than one contact for this flow.
* A removed contact will also remove access to previously active threads (unless restored).
* Thread metadata includes sender, receiver IDs, creation timestamp, and unread state.

----

h3. 7. UX Optimizations

* Smart autocomplete in Contact Picker.
* Highlight recent contacts in the top of picker modal.
* Allow tapping on avatar in chat list to open contact profile.
* Sticky text field and send button in chat view.
* Reuse all UI patterns from the messaging module (e.g., typing indicator, sent/read status).",,Duyen Nguyen,,712020:59b66c6e-1068-441c-9436-130289e16a26,,,,,,,,,,,,,,,,,com.atlassian.servicedesk.plugins.approvals.internal.customfield.ApprovalsCFValue@519b12f,,,,,,,,,{},,,,,,,,,,,,,,,,,,MOBILE,,,,0|i02qln:,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,37255,AL-16,Messages,In Progress,25/Jun/25 10:20 AM
[MB - Authentication] Sign In with Google,AL-27,37311,Story,Specified,AL,AIOS Link,software,Kai Nguyễn,6214a4102d1a9d0069949c96,,Medium,,,,Duyen Nguyen,712020:59b66c6e-1068-441c-9436-130289e16a26,Duyen Nguyen,712020:59b66c6e-1068-441c-9436-130289e16a26,04/Jun/25 9:22 AM,04/Jun/25 1:23 PM,25/Jun/25 10:25 AM,,,0,"h3. 1. Use Case Description

*As a new or returning user*, I want to securely sign in to AIOS Link using my Google account
*so that* I can quickly access my personalized dashboard after selecting my role (User or Merchant).

----

h3. 2. User Workflow (Step-by-Step)

# User opens the app and taps on *“Sign in with Google.”*
# System triggers Google OAuth sign-in flow.
# User completes Google authentication.
# System verifies the Google account and checks if the email is already associated with a role:
#* If *role is not registered* → show role selection screen (User or Merchant).
#* If *role is already set* → proceed to role validation.
# User selects a role (if required).
# System stores or confirms the role and signs user into the correct dashboard.
# User lands in their *User Dashboard* or *Merchant Dashboard*.

----

h3. 3. Field Definitions Table

||Field Name||Input Type||Required||Validation Rules||
|Google Email|OAuth Payload|✅|Valid Google-authenticated email|
|Role|Enum (User, Merchant)|✅ if not set|One-time required if first-time login|

----

h3. 4. Data Display Table (Card View)

||Display Field||Format Example||Notes||
|Email|[<EMAIL>|mailto:<EMAIL>]|Pulled from Google payload|
|Role Selection|Buttons or toggle|Required only on first-time login|
|Dashboard Type|User / Merchant|Controls which interface the user accesses|

----

h3. 5. Acceptance Criteria

* *AC1:* On tapping “Sign in with Google,” app must trigger standard Google OAuth flow.
* *AC2:* If email already exists and is linked with a role, auto-redirect user to respective dashboard.
* *AC3:* If email exists without a role, show role selection screen.
* *AC4:* On role selection, system must save the role to user profile and allow login.
* *AC5:* If user cancels Google login, return to login screen with toast:
_“Google sign-in cancelled.”_
* *AC6:* If email is not verified by Google, display:
_“Unable to verify email. Please use a verified Google account.”_
* *AC7:* First-time role selection is final and must be stored in backend for future logins.
* *AC8:* Role mismatch (e.g., logging in with different expected role) must redirect with message:
_“This Google account is registered under a different role.”_

----

h3. 6. System Rules

* OAuth integration must use secure Google Sign-In SDK (Android/iOS).
* Email is considered the unique identifier across AIOS Link accounts.
* A user cannot switch roles once tied to a Google account unless re-registering manually.
* Role selection UI must block access to dashboard until confirmed.
* Google session must be validated server-side to prevent spoofing.

----

h3. 7. UX Optimizations (Based on Industry UX Patterns)

* Branded “Sign in with Google” button using Google’s Material Design spec.
* Role selection screen should have simple dual card layout with icons (User / Merchant).
* Use toast to confirm successful sign-in:
_“Welcome back to AIOS Link!”_
* For unregistered Google emails, allow fallback to full sign-up flow.",,Duyen Nguyen,,712020:59b66c6e-1068-441c-9436-130289e16a26,,,,,,,,,,,,,,,,,com.atlassian.servicedesk.plugins.approvals.internal.customfield.ApprovalsCFValue@5343869f,,,,,,,,,{},,,,,,,,,,,,,,,,,,MOBILE,,,,0|i02qo7:,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,37144,AL-1,Authentication,To Do,04/Jun/25 9:22 AM
[MB - Authentication] Sign In with LinkedIn,AL-26,37308,Story,Specified,AL,AIOS Link,software,Kai Nguyễn,6214a4102d1a9d0069949c96,,Medium,,,,Duyen Nguyen,712020:59b66c6e-1068-441c-9436-130289e16a26,Duyen Nguyen,712020:59b66c6e-1068-441c-9436-130289e16a26,04/Jun/25 9:22 AM,16/Jun/25 4:10 PM,,,,0,"h3. 1. Use Case Description

*As a new or returning user*, I want to sign in to AIOS Link using my LinkedIn account
*so that* I can quickly authenticate and access my user or merchant dashboard based on my role.

----

h3. 2. User Workflow (Step-by-Step)

# User selects *“Sign in with LinkedIn”* on the login screen.
# System triggers the LinkedIn OAuth authentication flow.
# User authorizes access to their LinkedIn profile.
# System retrieves email, full name, and LinkedIn user ID.
# If this is the first time the user is signing in:
#* System checks if the email exists in the AIOS database.
#* If not, prompt user to select *User* or *Merchant* role.
#* System saves role assignment.
# If user role already exists, proceed directly to the appropriate dashboard.
# User is signed in and lands on either the *User Dashboard* or *Merchant Dashboard*.

----

h3. 3. Field Definitions Table

||Field Name||Input Type||Required||Validation Rules||
|LinkedIn Email|OAuth Payload|✅|Must be a verified LinkedIn account|
|Role|Enum (User, Merchant)|✅ if new account|Required only if account is new to AIOS|

----

h3. 4. Data Display Table (Card View)

||Display Field||Format Example||Notes||
|LinkedIn Email|[<EMAIL>|mailto:<EMAIL>]|Used to match or create account|
|Role Selection|User or Merchant|Required on first login only|
|Dashboard Routing|User / Merchant|Determined by stored role in backend|

----

h3. 5. Acceptance Criteria

* *AC1:* Selecting ""Sign in with LinkedIn"" triggers the LinkedIn OAuth process.
* *AC2:* On successful authentication, system retrieves email, name, and LinkedIn ID.
* *AC3:* If email already exists in system and role is saved, sign in user to appropriate dashboard.
* *AC4:* If this is a new email or role is not defined, prompt user to select between *User* and *Merchant*.
* *AC5:* Once selected, role is saved and user is logged in immediately.
* *AC6:* If user cancels LinkedIn login, redirect to Sign In screen and display toast:
_“LinkedIn sign-in cancelled.”_
* *AC7:* If LinkedIn profile data is incomplete or cannot be retrieved, show:
_“Unable to complete sign in. Please try again or use another method.”_
* *AC8:* Role mismatch (e.g., try *sign up again as a Merchant* using the *LinkedIn account that was previously tied to a user role*) displays:
_“This LinkedIn account is registered under a different role.”_

----

h3. 6. System Rules

* LinkedIn email must be treated as a unique identifier and stored securely.
* Sign-in tokens from LinkedIn must be validated server-side before establishing session.
* Once role is set, it cannot be changed from the frontend; admin action required.
* Only one account per LinkedIn email is permitted.
* LinkedIn session token should expire per standard OAuth rules (handled by SDK).

----

h3. 7. UX Optimizations (Based on Industry UX Patterns)

* Use LinkedIn-branded “Sign in with LinkedIn” button.
* Show role selection screen immediately after first sign-in if role is missing.
* Display LinkedIn profile name/email in role confirmation screen to build trust.
* Store return path so users are redirected to their intended destination post-login.",,Duyen Nguyen,,712020:59b66c6e-1068-441c-9436-130289e16a26,,,,,,,,,,,,,,,,,com.atlassian.servicedesk.plugins.approvals.internal.customfield.ApprovalsCFValue@1a85c671,,,,,,,,,{},,,,,,,,,,,,,,,,,,MOBILE,,,,0|i02qnz:,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,37144,AL-1,Authentication,To Do,04/Jun/25 9:22 AM
[MB - Authentication] Sign In with Apple ID,AL-25,37305,Story,Specified,AL,AIOS Link,software,Kai Nguyễn,6214a4102d1a9d0069949c96,,Medium,,,,Duyen Nguyen,712020:59b66c6e-1068-441c-9436-130289e16a26,Duyen Nguyen,712020:59b66c6e-1068-441c-9436-130289e16a26,04/Jun/25 9:22 AM,16/Jun/25 4:10 PM,,,,0,"h3. 1. Use Case Description

*As a new or returning user*, I want to securely sign in to AIOS Link using my Apple ID
*so that* I can quickly access my dashboard after selecting or confirming my account role (User or Merchant).

----

h3. 2. User Workflow (Step-by-Step)

# User taps the *“Sign in with Apple”* button on the login screen.
# System triggers the Apple ID authentication flow.
# User approves authentication via Face ID / Touch ID / Apple passcode.
# System retrieves the Apple-authenticated email and user ID token.
# If account is new or role is not set:
#* Show *Role Selection screen* (User or Merchant).
#* System stores selected role.
# If account exists and role is set:
#* System validates role and redirects to the corresponding dashboard.
# User is signed in and lands on either *User Dashboard* or *Merchant Dashboard*.

----

h3. 3. Field Definitions Table

||Field Name||Input Type||Required||Validation Rules||
|Apple Email|OAuth Payload|✅|Must be retrieved from Apple ID and verified|
|Role|Enum (User, Merchant)|✅ if not set|Required on first-time sign-in, final once saved|

----

h3. 4. Data Display Table (Card View)

||Display Field||Format Example||Notes||
|Apple Email|[<EMAIL>|mailto:<EMAIL>]|May be masked by Apple|
|Role Selection|Enum selector|Only shown if user has no existing role|
|Dashboard Redirect|User / Merchant|Based on stored role value|

----

h3. 5. Acceptance Criteria

* *AC1:* Sign in must trigger Apple native OAuth process securely via device OS.
* *AC2:* Upon successful Apple authentication, system retrieves email and Apple user ID.
* *AC3:* If email already exists and is tied to a role, sign user in to that dashboard.
* *AC4:* If role is not set, prompt user with role selection screen before login proceeds.
* *AC5:* Once role is selected and confirmed, system must save role and redirect user.
* *AC6:* If user cancels Apple sign-in, return to login screen and display toast:
_“Apple sign-in cancelled.”_
* *AC7:* Masked Apple relay email should be treated as unique account identifier.
* *AC8:* If Apple authentication fails, display fallback message:
_“Apple ID could not be verified. Please try again.”_

----

h3. 6. System Rules

* Apple sign-in must be implemented using Apple Sign-In SDK for iOS and Flutter plugins.
* Email or Apple ID must be treated as unique user identifier in backend.
* Once selected, role (User or Merchant) is permanent for that account unless manually reassigned by admin.
* Face ID / Touch ID verification is handled via Apple security layer; not stored by AIOS.
* Sign-in token must be securely validated server-side before issuing session.

----

h3. 7. UX Optimizations (Based on Industry UX Patterns)

* “Sign in with Apple” button styled per Apple’s Human Interface Guidelines (white/black with icon).
* Role selection screen shown immediately after first sign-in if role is missing.
* Apple relay email is shown with info tooltip (e.g., “This is a private email assigned by Apple”).
* Fast Face ID / Touch ID sign-in on returning login attempts with cached Apple ID session.",,Duyen Nguyen,,712020:59b66c6e-1068-441c-9436-130289e16a26,,,,,,,,,,,,,,,,,com.atlassian.servicedesk.plugins.approvals.internal.customfield.ApprovalsCFValue@19a3c16e,,,,,,,,,{},,,,,,,,,,,,,,,,,,MOBILE,,,,0|i02qnr:,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,37144,AL-1,Authentication,To Do,04/Jun/25 9:22 AM
[MB - Authentication] Remember Me,AL-24,37302,Story,In Progress,AL,AIOS Link,software,Kai Nguyễn,6214a4102d1a9d0069949c96,,Medium,,,,Duyen Nguyen,712020:59b66c6e-1068-441c-9436-130289e16a26,Duyen Nguyen,712020:59b66c6e-1068-441c-9436-130289e16a26,04/Jun/25 9:21 AM,06/Jun/25 1:37 PM,,,,0,"h3. 1. Use Case Description

*As a regular user*, I want to stay signed in automatically
*so that* I can quickly access the app without entering credentials every time.

*As a merchant*, I want the option to use biometric authentication (Touch ID / Face ID)
*so that* I can log in securely without typing my password each time.

----

h3. 2. User Workflow (Step-by-Step)

h4. For Regular Users:

# User signs in using valid credentials.
# System detects role as “User”.
# Session token is stored securely and auto-login is enabled for 30 days.
# On app relaunch, the user is automatically signed in.

h4. For Merchants:

# Merchant signs in using credentials.
# System detects role as “Merchant”.
# No persistent session is created; login is required on every app open.
# In Settings, a *“Biometric Login”* toggle is shown (Touch ID / Face ID).
# If toggle is enabled, merchant can use biometric prompt instead of typing password on next login.
# If toggle is off, password login is required every time.

----

h3. 3. Field Definitions Table

||Field Name||Input Type||Required||Validation Rules||
|Role Type|String|✅|“User” or “Merchant”|
|Session Token|Encrypted|Conditional|Stored only if role is “User”|
|Biometric Toggle|Boolean|❌|Default OFF for Merchants|
|Biometric Device|Enum|✅ (if enabled)|Face ID or Touch ID availability required|

----

h3. 4. Data Display Table (Card View)

||Display Field||Format Example||Notes||
|Session State|Auto-Login (User)|Silent session token validation for users|
|Login Prompt|Face ID modal|iOS or Android native biometric screen for merchant|
|Toggle in Settings|ON/OFF|Displayed only to Merchant role|

----

h3. 5. Acceptance Criteria

* *AC1:* If role is *User*, the app automatically stores a 30-day session and skips login screen.
* *AC2:* If role is *Merchant*, app does not store session; login is required on each app launch.
* *AC3:* There is no *“Remember Me”* checkbox in the UI for either role.
* *AC4:* If role is *Merchant*, app must display *“Enable Biometric Login”* toggle in Settings.
* *AC5:* If biometric login is enabled:
** System must prompt merchant for Face ID / Touch ID on next launch.
** If biometric authentication succeeds → log in and redirect to Home.
** If biometric authentication fails → fall back to manual password login.
* *AC6:* Biometric login uses device-native APIs and is only offered if biometric hardware is available.
* *AC7:* On logout, biometric session is cleared until re-enabled in Settings.
* *AC8:* If biometric is disabled from device settings (outside app), biometric login is automatically turned off.
* *AC9:* If biometric authentication fails 3 times, fallback to password and show:
_“Authentication failed. Please enter your password.”_

----

h3. 6. System Rules

* Session tokens for *User* accounts are stored securely using OS-level keychain/keystore.
* Biometric credential (token hash) is never stored remotely.
* Biometric auth must pass platform integrity (no jailbroken/rooted device bypass allowed).
* Session and biometric tokens are device-bound and non-transferable.

----

h3. 7. UX Optimizations (Based on Industry UX Patterns)

* Biometric toggle appears *only for Merchant accounts* under Settings > Security.
* Face ID/Touch ID prompt includes fallback to ""Enter Password"".
* Users receive a subtle confirmation toast:
_“Biometric login enabled for next sign in.”_
* Auto-login for User accounts must not disrupt splash screen timing or onboarding animation.",,Duyen Nguyen,,712020:59b66c6e-1068-441c-9436-130289e16a26,,,,,,,,,,,,,,,,,com.atlassian.servicedesk.plugins.approvals.internal.customfield.ApprovalsCFValue@6f86978e,,,,,,,,,{},,,,,,,,,,,,,,,,,,MOBILE,,,,0|i02qnj:,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,37144,AL-1,Authentication,In Progress,06/Jun/25 1:37 PM
[MB - Authentication] Forgot Password,AL-23,37299,Story,In Progress,AL,AIOS Link,software,Kai Nguyễn,6214a4102d1a9d0069949c96,,Medium,,,,Duyen Nguyen,712020:59b66c6e-1068-441c-9436-130289e16a26,Duyen Nguyen,712020:59b66c6e-1068-441c-9436-130289e16a26,04/Jun/25 9:21 AM,06/Jun/25 1:37 PM,,,,0,"h3. 1. Use Case Description

As a user who forgot my password, I want to reset it using a secure OTP sent via email
so that I can regain access to my account safely.
Additionally, if I try to log in but my account is still pending, the system should prompt me to verify via OTP before access.

----

h3. 2. User Workflow (Step-by-Step)

h4. 🟦 Forgot Password Flow

*Step 1:* User taps “Forgot Password” on the login screen.
→ Enters email for verification. If valid (whether {{active}} or {{pending}}), an OTP is sent.

*Step 2:* User enters the *6-digit OTP*.

* OTP expires in *30 minutes*.
* Max resend: *5/day* (independent of signup OTP).
* Resend becomes available *60 seconds* after previous request.

*Step 3:* User enters *new password* and confirms it.

* Password must meet validation rules and differ from previous password.

*Step 4:* System confirms password change.
→ User is redirected to the Sign In screen.

----

h4. 🟦 Login Attempt with Pending Verification

If user logs in with correct credentials but account status is {{pending_verification}}:

* System sends a new OTP with same policy as above.
* User must enter OTP to complete account activation.
* On success, status becomes {{active}} and user enters dashboard.

----

h2. 3. Field Definitions

h3. Step 1: Forgot Password – Email Entry

||Field Name||Field Type||Validation Rule||Mandatory||Description||
|Email|Text (email)|Must exist in user database|Yes|Used to send OTP if user is found|

----

h3. Step 2: OTP Verification

||Field Name||Field Type||Validation Rule||Mandatory||Description||
|OTP Code|Numeric (6)|Expires in 30 min, max resend 5/day, resend allowed after 60 sec|Yes|Verifies user's email for reset/activation|

----

h3. Step 3: Reset Password Form

||Field Name||Field Type||Validation Rule||Mandatory||Description||
|New Password|Password|≥8 characters, must include 1 uppercase, 1 lowercase, 1 number, 1 special character, ≠ old|Yes|Password to update into|
|Confirm Password|Password|Must match New Password|Yes|Validates accuracy|

----

h3. Step 4: Sign-In with Pending Account

||Field Name||Field Type||Validation Rule||Mandatory||Description||
|Email|Text (email)|Must exist and status = pending_verification|Yes|Validates reactivation trigger|
|Password|Password|Must match correct login credentials|Yes|Required to confirm identity|
|OTP Code|Numeric (6)|Sent separately from Forgot Password OTP, 30 min expiry, 5x/day|Yes|Required to activate the account|

----

h3. 4. Acceptance Criteria (AC)

* *AC1:* Forgot Password accepts any existing email and sends OTP (even if not yet verified).
* *AC2:* OTP verification uses 6-digit code valid for 30 min, resendable after 60 sec, up to 5/day.
* *AC3:* Password entry enforces complexity rules and must differ from old.
* *AC4:* Successful reset shows confirmation screen and redirects to login.
* *AC5:* On login, if account is still pending, user is redirected to email OTP verification.
* *AC6:* Login activation OTP flow uses same limits (30min expiry, 5x resend/day), tracked separately.
* *AC7:* Upon valid OTP entry from login, status is updated to active and user is logged in.
* *AC8:* After 5 failed OTP attempts, lockout is triggered for 24h for both reset and activation.

----

h3. 5. System Rules

* All OTPs are single-use, hashed in DB, and timestamped.
* Resend cooldowns (60s) and max attempts/day (5x) enforced per purpose (reset vs. activation).
* Separate counters ensure that password reset OTP flow does not interfere with sign-up or login re-verification.
* Password cannot be reused from previous credentials.",,Duyen Nguyen,,712020:59b66c6e-1068-441c-9436-130289e16a26,,,,,,,,,,,,,,,,,com.atlassian.servicedesk.plugins.approvals.internal.customfield.ApprovalsCFValue@203e839f,,,,,,,,,{},,,,,,,,,,,,,,,,,,MOBILE,,,,0|i02qnb:,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,37144,AL-1,Authentication,In Progress,06/Jun/25 1:37 PM
[MB - Authentication] Sign Up as Merchant,AL-22,37296,Story,In Progress,AL,AIOS Link,software,Kai Nguyễn,6214a4102d1a9d0069949c96,,Medium,,,,Duyen Nguyen,712020:59b66c6e-1068-441c-9436-130289e16a26,Duyen Nguyen,712020:59b66c6e-1068-441c-9436-130289e16a26,04/Jun/25 9:20 AM,24/Jun/25 3:09 PM,,,,0,"h3. 1. Use Case Description

As a new merchant, I want to register by selecting my role and verifying my email
so that I can access the AIOS Link platform with merchant tools and dashboard features.

----

h3. 2. User Workflow (Step-by-Step)

# User launches the AIOS Link mobile app.
# User taps the *“Sign Up”* button.
# System prompts user to *select a role*:
#* User
#* *Merchant*
(User selects *Merchant*)
# After selecting Merchant, the system enables the *Email input field*.
# User enters email and submits.
# System checks for existing email:
#* If already registered under any role -and- *-status = active-*-:-
#** Show error: _“Email is already registered. Please sign in or use a different email.”_
#* -If pending_verification:-
#** -If same role = merchant → redirect to verification screen-
#** -If different role → show- *-Role Conflict error-* -as above-
#* If email is valid and new → proceed to registration form
# System prompts user to enter:
#* Full Name
#* Business Name
#* EIN/DUNS
#* Password
#* Confirm Password
# On form submission:
#* Account is created with role = {{merchant}}, status = {{pending_verification}}
#* Verification email is sent
#* System redirects to *Verification Pending* screen
# -On future app entry, if merchant attempts to log in while still unverified:-
#* -System redirects to verification screen with- *-resend-* -option-
# Once verification is complete:

* System activates account
* Merchant is redirected to the Sign In screen.

----

h3. 3. Field Definitions Table

||Field Name||Field Type||Validation Rule||Mandatory||Description||
|Role|Dropdown|Must choose “Merchant”|Yes|Assigns the merchant dashboard and features|
|Email|Text (email)|Must not be registered under a different role|Yes|Unique identifier|
|Full Name|Text|3–50 characters|Yes|Name of the merchant|
|Business Name|Text|1–100 characters|Yes|Store or service name|
|EIN/DUNS|Text|9 digit numbers|Yes|*Employer Identification Number*
*Data Universal Numbering System*|
|Password|Password|≥8 characters, 1 uppercase, 1 lowercase, 1 number, 1 special character|Yes|Secure login credential|
|Confirm Password|Password|Must match Password|Yes|Password confirmation|

----

h3. 4. Acceptance Criteria (AC)

* *AC1:* Merchant must select the Merchant role before email input is shown.
* *AC2:* System checks for email duplication and 
** -If role = merchant and status = active → block with standard sign-in error-
** -If role ≠ merchant → block with- -_“Email already registered under a different role”_-
** Show error: _“Email is already registered. Please sign in or use a different email.”_
* *AC3:* If email is valid, system opens the Full Name, Business Name, EIN/DUNS, Password, Confirm Password form.
* *AC4:* On successful submission, account is saved as {{pending_verification}} and merchant role is applied.
* *AC5:* Verification email expires after 24 hours and can be resent.
* *AC6:* The resend limit is 10/day
* *-AC7:-* -Sign-in attempt before verification redirects to Verification screen.-
* *-AC8:-* -Once verified, merchant is signed into the merchant dashboard.-

----

h3. 5. System Rules

* Email is a global unique key across roles.
* Role selection locks the role for the given email identity.
* If an email is associated with a different role, signup under a new role is rejected.
* Account in {{pending_verification}} can only proceed under the original role.

----

h3. 6. UX Optimizations

* Role conflict screen:
_“Email is already registered. Please sign in or use a different email.”_
* Verification pending screen includes:
** Email summary
** Resend link
** Sign-in link",,Duyen Nguyen,Kai Nguyễn,712020:59b66c6e-1068-441c-9436-130289e16a26,6214a4102d1a9d0069949c96,,,,,,,,,,,,,,,,com.atlassian.servicedesk.plugins.approvals.internal.customfield.ApprovalsCFValue@143c7fa7,,,,,,,,,{},,,,,,,,,,,,,,,,,,MOBILE,,,,0|i02qn3:,,,,,,,,,,,,,,,,,,,,,,,,,,2025-06-17 08:21:56.701,,17/Jun/25 3:21 PM;6214a4102d1a9d0069949c96;[~accountid:712020:59b66c6e-1068-441c-9436-130289e16a26] check text _“This email is already registered under a different role. Please use the correct login.”_,17/Jun/25 3:48 PM;712020:59b66c6e-1068-441c-9436-130289e16a26;[~accountid:6214a4102d1a9d0069949c96] text changed anh,37144,AL-1,Authentication,In Progress,05/Jun/25 9:06 PM
"[MB - QR Scanner] Share Profile Link via SMS, Whatsapp, Telegram",AL-15,37253,Story,In Progress,AL,AIOS Link,software,Kai Nguyễn,6214a4102d1a9d0069949c96,,Medium,,,,Duyen Nguyen,712020:59b66c6e-1068-441c-9436-130289e16a26,Duyen Nguyen,712020:59b66c6e-1068-441c-9436-130289e16a26,03/Jun/25 3:54 PM,25/Jun/25 10:29 AM,,,,0,"h4. 1. Use Case Description

*As a user*, I want to share my profile link via messaging apps like SMS, WhatsApp, or Telegram,
*so that* others can access my AIOS Link profile instantly or be prompted to download the app if they don’t have it.

----

h4. 2. User Workflow (Step-by-Step)

# User navigates to “My QR Code” or Profile screen.
# User taps any of the “Share via” options: SMS, WhatsApp, Telegram.
# The system automatically:
#* Generates a preset message including their AIOSLink profile link
#* Opens the selected app with the message pre-filled
# User selects the recipient and sends the message manually.
# When the recipient taps the link:
#* If the AIOSLink app is installed → opens to the sender’s contact profile
#* If the app is not installed → redirects to the app store (Android/iOS)

----

h4. 3. Field Definitions

h5. 3.1 Submission Fields

||Field Name||Type||Required||Description||
|Message Content|Text|✅|Prefilled with personalized profile text|
|Profile Link|URL|✅|Unique, deep-link enabled URL|

h5. 3.2 Interaction Elements

||Element Name||Type||Required||Description||
|Share via SMS|Button|✅|Triggers native SMS intent|
|Share via WhatsApp|Button|✅|Triggers WhatsApp share intent|
|Share via Telegram|Button|✅|Triggers Telegram share intent|
|Profile Link|URL|✅|Resolved based on user ID or username|

----

h4. 4. Data Display

||Element||Example Value||Notes||
|Prefilled Message|""Hey! Here’s my AIOS Link profile – check it out: [https://aios.link/u/johndoe|https://aios.link/u/johndoe]""|Used in all 3 channels|
|Profile Link|{{https://aios.link/u/{username}}}|Deep link to profile|
|Fallback Redirect|App Store / Google Play URL|Triggered if app not installed|

----

h4. 5. Acceptance Criteria

* *AC1:* User can share their profile from Profile or QR Code screen.
* *AC2:* SMS, WhatsApp, and Telegram share buttons are visible and tappable.
* *AC3:* A prefilled message is generated with the user’s unique profile link.
* *AC4:* Tapping any share button opens the correct app (SMS, WhatsApp, Telegram).
* *AC5:* Receiver clicking the link is deep-linked into the AIOSLink app if installed.
* *AC6:* If the app is not installed, the user is redirected to the App Store or Play Store.
* *AC7:* Shared links must open the sender’s contact profile page (not home screen).
* *AC8:* Profile links must honor visibility or privacy settings.

----

h4. 6. System Rules

* Profile link is generated per user using a shortlink pattern: {{https://aios.link/u/{username}}}
* Deep links must resolve to in-app route: {{/contact/{userID}}} if app is installed
* If the app is not detected, use a fallback redirect to:
** iOS → App Store
** Android → Google Play
* Prefilled message must support localization
* Link clicks can include UTM parameters or tracking ID for analytics
* System must support device capability check (e.g., if WhatsApp is not installed, hide or disable button)

----

h4. 7. UX Optimizations

* Use recognizable app icons for SMS, WhatsApp, and Telegram (colored circle buttons)
* Prefill message in clipboard as fallback for unsupported apps
* Toast: “Opening WhatsApp…” or “Message copied to clipboard” as fallback
* Prevent double taps with loading state
* Add haptic feedback on button tap",,Duyen Nguyen,,712020:59b66c6e-1068-441c-9436-130289e16a26,,,,,,,,,,,,,,,,,com.atlassian.servicedesk.plugins.approvals.internal.customfield.ApprovalsCFValue@69044bcd,,,,,,,,,{},,,,,,,,,,,,,,,,,,MOBILE,,,,0|i02qlb:,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,37240,AL-12,QR Scanner,In Progress,25/Jun/25 10:29 AM
[MB - QR Scanner] Show My QR Code,AL-14,37248,Story,In Progress,AL,AIOS Link,software,Kai Nguyễn,6214a4102d1a9d0069949c96,,Medium,,,,Duyen Nguyen,712020:59b66c6e-1068-441c-9436-130289e16a26,Duyen Nguyen,712020:59b66c6e-1068-441c-9436-130289e16a26,03/Jun/25 3:00 PM,25/Jun/25 10:29 AM,,,,0,"h4. 1. Use Case Description

*As a user*, I want to generate and view my own QR code from my profile page,
*so that* I can allow others to scan it and instantly open my contact profile.

----

h4. 2. User Workflow (Step-by-Step)

# User navigates to their profile page and click “My QR Code” option.
# System fetches or generates a unique QR code that links to the user's profile.
# The user sees:
#* Their avatar, name, and title
#* A centered scannable QR code
#* Instructional label: “Scan this code to view my profile”
# Below the QR code:
#* A CTA button: “Save QR to Gallery”
#* A row of sharing actions (Copy Link, SMS, WhatsApp, Telegram)
# User can tap “Save QR” to store a PNG of the QR code locally.
# User can tap any share action to distribute the link with prefilled text.

----

h4. 3. Field Definitions

h5. 3.1 Submission Fields (None)

This is a display-only screen with no form submission.

h5. 3.2 Interaction Elements

||Element Name||Type||Required||Description||
|QR Code Image|Image|✅|Auto-generated visual encoding of profile URL|
|Save QR Button|Button|✅|Saves QR PNG to gallery or file system|
|Share Options|Button Row|✅|Includes: Copy Link, SMS, WhatsApp, Telegram|
|Profile Header|Display|✅|Shows user avatar, name, and title|

----

h4. 4. Data Display

||Field||Format / Example||Notes||
|Profile Name|John Doe|Pulled from user profile|
|Job Title|Product Designer at TechCorp|Optional, from profile|
|QR Code|SVG or PNG of encoded user URL|Must be high-contrast & scannable|
|Share Actions|Icon row|Standard messaging integrations|
|Save QR CTA|“Save QR to Gallery”|Saves 512×512 PNG version|

----

h4. 5. Acceptance Criteria

* *AC1:* User can open a “My QR Code” screen from their profile.
* *AC2:* System generates and displays a valid scannable QR code.
* *AC3:* QR code links to the user’s contact profile page (deep link or web fallback).
* *AC4:* QR code is embedded with brand styling (e.g., AIOSLink logo in center).
* *AC5:* User sees their avatar, name, and title above the QR.
* *AC6:* User can tap “Save QR to Gallery” and the image is saved locally.
* *AC7:* User can share their profile using Copy Link, SMS, WhatsApp, or Telegram.

----

h4. 6. System Rules

* Each user has one unique QR code tied to their public contact profile.
* The QR image should be cached for 24 hours to reduce regeneration cost.
* Image generation must be high resolution (≥512×512 px) for printing or scanning.
* QR links must include fallback to web profile if app is not installed.
* Profile links must respect visibility/privacy settings.

----

h4. 7. UX Optimizations

* QR code should auto-scale to available width without clipping.
* Use atomic button styles for Save QR and Share options.
* Share row should animate in on load (e.g., fade up).
* Disable “Save QR” if permission to write files is not granted.
* Use a native image-saving confirmation toast after success.

----",,Duyen Nguyen,,712020:59b66c6e-1068-441c-9436-130289e16a26,,,,,,,,,,,,,,,,,com.atlassian.servicedesk.plugins.approvals.internal.customfield.ApprovalsCFValue@132d8b9f,,,,,,,,,{},,,,,,,,,,,,,,,,,,MOBILE,,,,0|i02qkv:,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,37240,AL-12,QR Scanner,In Progress,25/Jun/25 10:29 AM
[MB - QR Scanner] Scan QR,AL-13,37245,Story,In Progress,AL,AIOS Link,software,Kai Nguyễn,6214a4102d1a9d0069949c96,,Medium,,,,Duyen Nguyen,712020:59b66c6e-1068-441c-9436-130289e16a26,Duyen Nguyen,712020:59b66c6e-1068-441c-9436-130289e16a26,03/Jun/25 3:00 PM,25/Jun/25 10:29 AM,,,,0,"h2. 1. Use Case Description

As a *user*, I want to scan another user’s unique QR code so that I can directly access and view their contact profile page within the app.

----

h2. 2. User Workflow (Step-by-Step)

# User navigates to ""Contacts"" or Home Dashboard
# Taps the ""Scan QR"" button (launches in-app camera)
# Points device at another user’s QR code
# System decodes the user reference and queries the contact profile endpoint
# Automatically redirects to that user's contact profile

----

h2. 3. Acceptance Criteria (AC)

* *AC1*: QR must encode a valid and unique user reference (e.g. user ID or token)
* *AC2*: System must reject invalid, expired, or malformed QR inputs with an error toast
* *AC3*: After successful scan, user is redirected to the correct profile without manual confirmation
* *AC4*: Profile page must be permission-aware (e.g. hide restricted fields)
* *AC5*: QR scan triggers appropriate toast or feedback to confirm loading state

----

h2. 4. Field Definitions Table (Submission + Filters)

||Field||Type||Required||Description||
|QR Payload|String|✅|Encoded user ID or reference token|
|Contact ID|String|✅|ID fetched from decoded QR|
|Timestamp|DateTime|✅|Time scan occurred (optional for audit)|

----

h2. 5. Data Display Table (Card View)

||Field||Example||Notes||
|Profile Name|“Danny Tran”|Full name from user’s profile|
|Job Title|“Growth Marketer at XYZ”|Occupation|
|CTA|“Send Message”|Launch chat with this contact|

----

h2. 6. Search & Filter Logic

* N/A for this feature — interaction is based on QR scan only.

----

h2. 7. System Rules

* All QR codes must be signed and issued through the AIOS backend
* Users can generate their own QR from the Contact Profile screen
* Contact Profile is view-only unless user has added them to their network
* Scanned contacts are logged as passive views (not automatic connections)

----

h2. 8. UX Optimizations (Based on Industry UX Patterns)

* In-app scanner includes soft focus and scan boundary animation
* Success feedback: Vibration + Toast ""User found. Redirecting...""
* Error feedback: Toast ""Invalid QR code. Try again.""
* Optional: Add to Contacts button visible after viewing scanned profile

----",,Duyen Nguyen,Phạm Xuân Định,712020:59b66c6e-1068-441c-9436-130289e16a26,63e3536328cddcc707748a66,,,,,,,,,,,,,,,,com.atlassian.servicedesk.plugins.approvals.internal.customfield.ApprovalsCFValue@7e99f77d,,,,,,,,,{},,,,,,,,,,,,,,,,,,MOBILE,,,,0|i02qkn:,,,,,,,,,,,,,,,,,,,,,,,,,,2025-06-16 09:11:40.821,,16/Jun/25 4:11 PM;63e3536328cddcc707748a66;Donnie,,37240,AL-12,QR Scanner,In Progress,25/Jun/25 10:29 AM
[MB-Events] Set Up Ticket,AL-11,37206,Story,In Progress,AL,AIOS Link,software,Kai Nguyễn,6214a4102d1a9d0069949c96,,Medium,,,,Duyen Nguyen,712020:59b66c6e-1068-441c-9436-130289e16a26,Duyen Nguyen,712020:59b66c6e-1068-441c-9436-130289e16a26,03/Jun/25 10:03 AM,25/Jun/25 10:20 AM,,,,0,,,Duyen Nguyen,,712020:59b66c6e-1068-441c-9436-130289e16a26,,,,,,,,,,,,,,,,,com.atlassian.servicedesk.plugins.approvals.internal.customfield.ApprovalsCFValue@74a5ec30,,,,,,,,,{},,,,,,,,,,,,,,,,,,MOBILE,,,,0|i02qjz:,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,37186,AL-4,Events,In Progress,25/Jun/25 10:20 AM
[MB-Events] Cancel Event,AL-10,37203,Story,In Progress,AL,AIOS Link,software,Kai Nguyễn,6214a4102d1a9d0069949c96,,Medium,,,,Duyen Nguyen,712020:59b66c6e-1068-441c-9436-130289e16a26,Duyen Nguyen,712020:59b66c6e-1068-441c-9436-130289e16a26,03/Jun/25 10:02 AM,25/Jun/25 10:20 AM,,,,0,"h3. 1. Use Case Description

*As a host*, I want to cancel an event after it has been published,
*so that* I can notify all attendees and prevent future participation, while still retaining access to the event's history and status in the system.

----

h3. 2. User Workflow (Step-by-Step)

# Host navigates to “My Events” tab and selects an active event.
# Host taps the “More” or “Manage” option and selects *Cancel Event*.
# System displays a confirmation modal outlining:
#* Event will be marked as cancelled
#* Users will be notified
#* Ticket purchases will be refunded as credit amounts (if applicable)
# Host confirms the action.
# System updates:
#* Event status → {{Cancelled}}
#* All invitees notified
#* Ticket holders refunded credits
#* Host ticket revenue reversed
# Event remains in the ""My Events"" list with a *Cancelled* tag.

----

h3. 3. Field Definitions Table

h4. 3.1 Submission Fields

||Field Name||Type||Required||Description||
|Event ID|UUID|✅|Targeted event to cancel|
|Cancellation Note|String|✅|Optional message for invitees|

h4. 3.2 Interaction Elements

||Element Name||Type||Required||Description||
|Cancel Event CTA|Button|✅|Located in Event Detail → Host only|
|Confirmation Modal|Modal|✅|Confirms irreversible cancellation|
|Status Badge|Label|✅|“Cancelled” shown on card and detail page|

----

h3. 4. Data Display Table (Card View)

||Field||Format/Example||Notes||
|Status Badge|{{Cancelled}} (grayed red label)|Consistent across list and detail screens|
|Event Title|Text|Unchanged|
|Attendee Count|“X attendees RSVP’d”|Still viewable|
|Cancelled Notification|“This event has been cancelled.”|Toast/inline banner on attendee side|
|Refund Info (Host)|“Your earnings will be deducted...”|Only for ticketed events, shown in modal|

----

h3. 5. Acceptance Criteria

* *AC1:* Only the host of an event can access the “Cancel Event” option.
* *AC2:* Selecting “Cancel Event” opens a modal confirming the action and its consequences.
* *AC3:* After confirmation, the event’s status is updated to {{Cancelled}}.
* *AC4:* The cancelled event remains visible in the host’s “My Events” tab.
* *AC5:* The event card and detail page clearly display a *Cancelled* badge.
* *AC6:* All attendees receive a system notification or toast that the event was cancelled.
* *AC7:* All actions tied to the event (e.g., RSVP, invite, share, boost) are disabled post-cancellation.
* *AC8:* The event detail page becomes read-only after cancellation.
* *AC9:* A cancelled event cannot be reactivated or edited.
* *AC10:* Cancelled events are still visible in logs and analytics.
* *AC11:* If attendees purchased tickets, refunds will be issued as credits (in USD value), and the host’s earnings from those tickets will be deducted.
* *AC12:* Refunded credits are stored in the user’s AIOS wallet and cannot be withdrawn to a bank account.

----

h3. 6. System Rules

* Cancellation is a soft delete; all data must remain queryable and audit-ready.
* Push/email notification is triggered to all RSVP’d users.
* Ticket refunds are processed as AIOS wallet credit in equivalent dollar amount.
* Hosts who received earnings will see equivalent deduction in their transaction log.
* Boost status (if active) is immediately ended without refund.
* Any reminders or future notifications are canceled automatically.

----

h3. 7. UX Optimizations

* Use a red danger-themed modal for cancellation confirmation.
* Add helpful inline warning:
“You’re about to cancel this event. This action is permanent and attendees will be notified.”
* Show refund impact in the confirmation modal if ticket purchases exist.
* Allow host to enter a brief optional message for attendees.",,Duyen Nguyen,Phạm Xuân Định,712020:59b66c6e-1068-441c-9436-130289e16a26,63e3536328cddcc707748a66,,,,,,,,,,,,,,,,com.atlassian.servicedesk.plugins.approvals.internal.customfield.ApprovalsCFValue@3a38dc5f,,,,,,,,,{},,,,,,,,,,,,,,,,,,MOBILE,,,,0|i02qjr:,,,,,,,,,,,,,,,,,,,,,,,,,,2025-06-16 07:18:06.964,,16/Jun/25 2:18 PM;63e3536328cddcc707748a66;Done,,37186,AL-4,Events,In Progress,25/Jun/25 10:20 AM
[MB-Events] Invite Attendees with RSVP,AL-9,37200,Story,In Progress,AL,AIOS Link,software,Kai Nguyễn,6214a4102d1a9d0069949c96,,Medium,,,,Duyen Nguyen,712020:59b66c6e-1068-441c-9436-130289e16a26,Duyen Nguyen,712020:59b66c6e-1068-441c-9436-130289e16a26,03/Jun/25 10:02 AM,25/Jun/25 10:20 AM,,,,0,"

h3. 1. Use Case Description

As a premium user or merchant who hosts events, I want to invite attendees from my contact or lead list and optionally attach tickets, so that I can manage RSVPs effectively without exceeding the reserved RSVP capacity.

----

h3. 2. User Workflow (Step-by-Step)

*Precondition*: The event must be either:

* A *public* event with an *RSVP limit* configured
* A *private* event (RSVP-only by default)

# After completing event creation, system prompts the host with “Invite Attendees” option.
# Host taps *“Invite Attendees”*.
# System displays contact and lead lists (in tabbed format).
# Host selects users from either or both lists.
# If the event is *ticketed*, system prompts:
#* *Option A*: “Send Invite Only” (recipient must buy ticket)
#* *Option B*: “Send Invite With Ticket” (deducts from ticket inventory)
# Host confirms selection and invite type.
# System validates selected invite count against remaining RSVP spots.
# If valid, system sends out invites and returns to Event Detail screen.
# Success toast appears: “Invitations sent successfully.”

----

h3. 3. Field Definitions Table

||Field Name||Field Type||Validation Rule||Mandatory||Description||
|Attendee Selection|Multi-Select|Must not exceed RSVP limit|Yes|Choose from Contact or Lead List|
|Invite Type (if ticketed)|Enum|Invite Only / Invite with Ticket|Conditional|Shown if event is ticketed|
|Remaining RSVP Count|System|Auto-calculated|Yes|Prevents oversending beyond RSVP cap|

----

h3. 4. Acceptance Criteria

*AC1*: Feature is only accessible for:

* Public event with RSVP Limit
* Private event (always RSVP-based)

*AC2*: Contacts and leads are shown in separate tabs with selectable checkboxes.

*AC3*: Host cannot select more invitees than available RSVP slots.

*AC4*: For ticketed events:

* Host must choose whether to attach a ticket with each invite.
* “Invite with Ticket” option deducts from ticket inventory.

*AC5*: Once invites are sent, system records each invitation with RSVP status = “Pending”.

*AC6*: System shows live RSVP counter: “7 of 30 slots remaining”

*AC7*: Success toast confirms invite: *“Invitations sent successfully.”*

*AC8*: If RSVP capacity is full, “Invite Attendees” is disabled.

*AC9*: Attempting to send more invites than RSVP slots triggers error: *“Invite limit exceeded. Adjust selection.”*

*AC10*: All invites appear in the RSVP manager of the Event Detail Host View.

----

h3. 5. System Rules

* Invite limit = RSVP limit - current RSVP count.
* “Invite with Ticket” deducts one ticket per recipient from host’s inventory.
* Ticketed event = event must be configured with ticket price post-creation.
* RSVP invites cannot be recalled or reassigned once sent.
* Duplicate invites to the same user are prevented.",,Duyen Nguyen,Phạm Xuân Định,712020:59b66c6e-1068-441c-9436-130289e16a26,63e3536328cddcc707748a66,,,,,,,,,,,,,,,,com.atlassian.servicedesk.plugins.approvals.internal.customfield.ApprovalsCFValue@4f7a3944,,,,,,,,,{},,,,,,,,,,,,,,,,,,MOBILE,,,,0|i02qjj:,,,,,,,,,,,,,,,,,,,,,,,,,,2025-06-16 07:18:36.573,,16/Jun/25 2:18 PM;63e3536328cddcc707748a66;Done,,37186,AL-4,Events,In Progress,25/Jun/25 10:20 AM
[MB-Events] View Event Detail,AL-8,37197,Story,In Progress,AL,AIOS Link,software,Kai Nguyễn,6214a4102d1a9d0069949c96,,Medium,,,,Duyen Nguyen,712020:59b66c6e-1068-441c-9436-130289e16a26,Duyen Nguyen,712020:59b66c6e-1068-441c-9436-130289e16a26,03/Jun/25 10:01 AM,25/Jun/25 10:20 AM,,,,0,"h3. 1. Use Case Description

*As a* user of the AIOS Link app,
*I want to* view full details of an event based on my access level,
*so that* I can decide to join as an attendee or manage as a host.

----

h3. 2. User Workflow (Step-by-Step)

h4. Flow A: User (Guest / Invitee)

# User accesses event via Discover feed, invite link, or My Events tab.
# System displays the Event Detail screen with:
#* Title, Date & Time, Duration, About, Tags, Visibility, Event Type, Attendee Cap
#* Avatar stack of confirmed attendees
#* *If RSVP or ticket not confirmed* → address and meeting link remain hidden.
#* *If RSVP confirmed* or ticket purchased → reveal address or meeting link
#* CTA depending on status: “Buy Ticket”, “RSVP Going”, or “Not Going”
# User confirms attendance or completes ticket purchase.
# Event details update in ""My Events"" tab.

h4. Flow B: Host

# Host navigates to *My Events* tab and selects an event.
# System displays the Event Detail screen with:
#* All fields from event creation
#* RSVP Limit (if configured)
#* RSVP Record (per contact)
#* Number of joined attendees
#* Ticket inventory count
#* Access to: “Edit Event”, “Invite More”, “Boost Event”

----

h3. 3. Field Definitions Table

||Field||Visibility||Description||
|Event Title|All|Name of the event|
|Event ID|All|System unique generated ID|
|Date & Time|All|Scheduled start time|
|Duration (optional)|All|Optional field for expected length|
|Event Type|All|In-person or Virtual|
|Address / Meeting Link|RSVP-confirmed only|Visible after joined, RSVP (private/public) or ticket purchase|
|About Event|All|Up to 4096 characters for description|
|Tags|All|Up to 10 labels (e.g., Tech, Business)|
|Visibility|All|Public or Private (via invite)|
|Attendee Limit|All|Max capacity allowed at the event|
|RSVP Limit|Host only|Reserved spots for invitees|
|Available Seats/Tickets|All|“X of Y seats remaining” for public view|
|Confirmed Attendees|All|Avatars or list of accepted users|
|RSVP Record Table|Host only|RSVP status breakdown: Going / Not Going|
|Number of Joined Users|Host only|Count of attendees with confirmed status|
|Ticket Inventory|Host only|Total tickets left (if ticketed)|
|Free/ Ticket Price|All|Ticket Sell Price|

----

h3. 4. Acceptance Criteria (AC)

* *AC1*: All users see standard fields defined in event creation.
* *AC2*: System hides Address and Meeting Link unless:
** RSVP is “Going” or ticket is purchased or joined free public event
* *AC3*: Only *hosts* see RSVP Limit count and RSVP Table.
* *AC4*: Confirmed attendees are shown as avatar stacks with “See All” link.
* *AC5*: Available seats/tickets are updated in real-time.
* *AC6*: Event Type field determines conditional display of Address or Meeting Link.
* *AC7*: Host view includes “Edit”, “Invite”, and “Boost” buttons.
* *AC8*: CTA visibility reflects event logic:
** “Buy Ticket” for ticketed public events
** “RSVP Going” or “Not Going” for invited users
* *AC9*: Address and meeting link *must not* appear to unconfirmed users under any condition.
* *AC10*: RSVP status remains hidden from guest users entirely.

----

h3. 5. System Rules

* Address and Meeting Link are *protected fields*, shown only after RSVP = Going or ticket is assigned.
* Guests cannot see RSVP limit.
* Confirmed Attendees are loaded via lazy loading.
* Ticketed Events must show real-time ticket count in guest view.
* Host’s Event Detail includes RSVPs, available tickets, and guest responses.

----

h3. 6. UX Optimizations

* Event cards on Discover feed show only high-level info: title, date, visibility, ticket icon.
* Tabs or toggles may separate public view vs host view.
* RSVP button uses color feedback on tap.
* CTA buttons change state after interaction (e.g., RSVP → Confirmed).
* Address/Meeting section reveals dynamically after RSVP logic is met.",,Duyen Nguyen,Phạm Xuân Định,712020:59b66c6e-1068-441c-9436-130289e16a26,63e3536328cddcc707748a66,,,,,,,,,,,,,,,,com.atlassian.servicedesk.plugins.approvals.internal.customfield.ApprovalsCFValue@27e2c2a7,,,,,,,,,{},,,,,,,,,,,,,,,,,,MOBILE,,,,0|i02qjb:,,,,,,,,,,,,,,,,,,,,,,,,,,2025-06-16 04:57:55.645,,16/Jun/25 11:57 AM;63e3536328cddcc707748a66;Done,,37186,AL-4,Events,In Progress,25/Jun/25 10:20 AM
[MB-Events] RSVP Event,AL-7,37194,Story,In Progress,AL,AIOS Link,software,Kai Nguyễn,6214a4102d1a9d0069949c96,,Medium,,,,Duyen Nguyen,712020:59b66c6e-1068-441c-9436-130289e16a26,Duyen Nguyen,712020:59b66c6e-1068-441c-9436-130289e16a26,03/Jun/25 10:01 AM,25/Jun/25 10:20 AM,23/Jun/25 4:20 PM,,,0,"h3. 1. Use Case Description

As a user who receives an invitation to an event, I want to respond with my attendance status so that the host can manage event participation and ticket assignment accurately.

----

h3. 2. User Workflow (Step-by-Step)

# User receives an event invite via in-app notification.
# User taps the notification.
# System redirects the user to the *My Events* tab and highlights the *RSVP prompt*.
# User is presented with the RSVP options:
#* *Going*
#* *Not Going*
# User selects an option and confirms.
# System records the RSVP response and updates the host’s RSVP list.
# If the invite included a ticket (e.g., free gift ticket from host), the ticket is added to the user’s *My Ticket* section.
# User sees a confirmation message based on their response.

----

h3. 3. Field Definitions Table

||Field Name||Field Type||Validation Rule||Mandatory||Description||
|RSVP Status|Enum|Going / Not Going|Yes|Indicates whether user will attend|
|Event ID|UUID|Must be valid event ID|Yes|Identifies the event to RSVP|
|Ticket Assigned|Boolean|Derived from invite type|Conditional|If invite included a ticket, assign automatically|

----

h3. 4. Acceptance Criteria

*AC1*: Users can only RSVP if they received a valid event invitation.
*AC2*: Tapping on invite notification directs the user to the *My Events* tab with the specific event focused.
*AC3*: User can choose either “Going” or “Not Going”; selection is final unless host re-invites.
*AC4*: System records RSVP status and updates the host-side RSVP manager.
*AC5*: If the invitation was sent with a ticket, system adds it automatically to *My Ticket* tab upon selecting “Going”.
*AC6*: RSVP action triggers success toast:

* “RSVP confirmed” (for Going)
* “You’ve declined the invitation” (for Not Going)

*AC7*: Duplicate RSVPs (responding again to the same event) are disallowed unless the host resends the invite.
*AC8*: Users cannot RSVP to events they were not invited to if the event is private.

----

h3. 5. System Rules

* Invitations carry metadata: {{has_ticket}}, {{event_id}}, {{invite_type}}.
* If {{has_ticket = true}}, system assigns a ticket upon RSVP = Going.
* Each invitation response is uniquely tracked per user and cannot be changed unless the host resets the invite.
* Private event invites are non-transferable.
* Notification link includes deep link to specific RSVP flow in My Events.",,Duyen Nguyen,Phạm Xuân Định,712020:59b66c6e-1068-441c-9436-130289e16a26,63e3536328cddcc707748a66,,,,,,,,,,,,,,,,com.atlassian.servicedesk.plugins.approvals.internal.customfield.ApprovalsCFValue@5c4d87fe,,,,,,,,,{},,,,,,,,,,,,,,,,,,MOBILE,,,,0|i02qj3:,,,,,,,,,,,,,,,,,,,,,,,,,,2025-06-16 04:57:48.789,,16/Jun/25 11:57 AM;63e3536328cddcc707748a66;Done,,37186,AL-4,Events,In Progress,25/Jun/25 10:20 AM
[MB-Events] Boost Event,AL-6,37191,Story,In Progress,AL,AIOS Link,software,Kai Nguyễn,6214a4102d1a9d0069949c96,,Medium,,,,Duyen Nguyen,712020:59b66c6e-1068-441c-9436-130289e16a26,Duyen Nguyen,712020:59b66c6e-1068-441c-9436-130289e16a26,03/Jun/25 10:00 AM,25/Jun/25 10:20 AM,,,,0,,,Duyen Nguyen,,712020:59b66c6e-1068-441c-9436-130289e16a26,,,,,,,,,,,,,,,,,com.atlassian.servicedesk.plugins.approvals.internal.customfield.ApprovalsCFValue@52e13042,,,,,,,,,{},,,,,,,,,,,,,,,,,,MOBILE,,,,0|i02qiv:,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,37186,AL-4,Events,In Progress,25/Jun/25 10:20 AM
[MB-Events] Create Event,AL-5,37188,Story,In Progress,AL,AIOS Link,software,Kai Nguyễn,6214a4102d1a9d0069949c96,,Medium,,,,Duyen Nguyen,712020:59b66c6e-1068-441c-9436-130289e16a26,Duyen Nguyen,712020:59b66c6e-1068-441c-9436-130289e16a26,03/Jun/25 10:00 AM,25/Jun/25 10:20 AM,23/Jun/25 10:12 PM,,,0,"h3. 1. Use Case Description

As a premium user or merchant, I want to create an event with flexible visibility, attendance, and ticketing options so that I can host in-person or virtual gatherings, manage RSVPs, and control attendance capacity based on my intended audience.

----

h3. 2. User Workflow (Step-by-Step)

# User navigates to the *Event* tab.
# Taps *My Events > ""+"" (Create Event)*.
# System opens a *Create Event Form*.
# User fills in required and optional fields.
# If the event is marked as:
#* *In-person* → “Address” becomes mandatory.
#* *Virtual* → “Meeting Link” becomes mandatory.
#* *Ticketed* → Upon creation, system prompts user to configure ticket price in ""My Tickets"" tab.
# User optionally sets an *RSVP Limit*:
#* RSVP slots are deducted from overall Max Attendees.
#* Reserved for invite-only flow.
# User taps *Create*.
# System validates and submits event.
# Success toast: *“Event created successfully”* appears.
# Host is redirected to ticket setup flow if ticketed event.

----

h3. 3. Field Definitions Table

h4. 📍 Required Fields

||Field Name||Field Type||Validation Rule||Description||
|Event Title|Text|Max 100 characters|Title of the event|
|Date & Time|DateTime|Must be in future|Scheduled time of event|
|Event Type|Enum|In-Person / Virtual|Determines required fields|
|About|Text Area|Max 4096 characters|Description of event|
|Visibility|Enum|Public / Private (via invite link)|Access scope|
|Max Attendees|Number|1–200|Total max attendees|
|Event Access|Enum|Free / Ticketed|Determines if ticket setup required|

h4. 📍 Optional Fields

||Field Name||Field Type||Validation Rule||Description||
|Duration|Number|Minutes|Optional event duration|
|Tags|Tag Chips|Max 10 entries|User-defined, helps filtering|
|Telegram Group Link|URL|Must be valid format|Group communication|
|WhatsApp Group Link|URL|Must be valid format|Group communication|
|RSVP Limit|Number|≤ Max Attendees|Reserved seats for invite-only guests|

h4. 📍 Conditional Fields

||Field Name||Field Type||Rule||
|Address|Text|Required if Event Type = In-Person|
|Meeting Link|URL|Required if Event Type = Virtual|
|Ticket Price|Number|Required if Event Access = Ticketed (prompted post-creation)|

----

h3. 4. Acceptance Criteria

*AC1*: Only Premium Users or Merchant roles can access Create Event.
*AC2*: Form must enforce conditional requirements based on event type and access.
*AC3*: Max attendees must be between 1 and 200.
*AC4*: If RSVP Limit is set, it is subtracted from Max Attendees and reserved exclusively for invited users.
*AC5*: For public events:

* Discover feed shows only public spots (Max - RSVP Limit).
* RSVP Limit spots do *not* convert to public RSVPs if unused or rejected.


*AC6*: For private events:

* Max Attendees = RSVP Limit.
* Attendees can only join via invitation.


*AC7*: Hosts sending invites can:

* Send RSVP invite only, or
* Send RSVP with free ticket (if ticketed event, inventory is deducted).


*AC8*: If event is ticketed, host is redirected to configure ticket post-creation.
*AC9*: Success message confirms creation and flow proceeds to setup (if applicable)
*AC10:* Host can enable Boost after creation to improve exposure (logic defined separately).
*AC11*: Ticket price is editable only before the first ticket is sold.
*AC12:* Ticketed event will not allow change of date and time & system shows warning: “Ticketed event will not allow change of date and time. Do you want to continue?”



----

h3. 5. System Rules

* Reserved RSVP spots are isolated from general pool.
* Rejected RSVPs do not open back to public.
* Private events do not appear in public feeds.
* Ticket deduction for free ticket invites applies only for ticketed events.
* A host may not exceed 200 attendees total, regardless of public/private configuration.
* Event must be uniquely titled per host on the same date/time slot.",,Duyen Nguyen,Phạm Xuân Định,712020:59b66c6e-1068-441c-9436-130289e16a26,63e3536328cddcc707748a66,,,,,,,,,,,,,,,,com.atlassian.servicedesk.plugins.approvals.internal.customfield.ApprovalsCFValue@63bc975,,,,,,,,,{},,,,,,,,,,,,,,,,,,MOBILE,,,,0|i02qin:,,,,,,,,,,,,,,,,,,,,,,,,,,2025-06-16 04:57:37.243,,16/Jun/25 11:57 AM;63e3536328cddcc707748a66;Done,,37186,AL-4,Events,In Progress,25/Jun/25 10:20 AM
[MB - Authentication] Sign In,AL-3,37149,Story,In Progress,AL,AIOS Link,software,Kai Nguyễn,6214a4102d1a9d0069949c96,,Medium,,,,Duyen Nguyen,712020:59b66c6e-1068-441c-9436-130289e16a26,Duyen Nguyen,712020:59b66c6e-1068-441c-9436-130289e16a26,02/Jun/25 8:43 PM,06/Jun/25 1:37 PM,,,,0,"h3. 1. Use Case Description

As a returning user, I want to log in using my email and password
so that I can access the correct dashboard based on my role.
If my account is not yet verified, I should be directed to activate my account via OTP before access is granted.

----

h3. 2. User Workflow (Step-by-Step)

*Step 1: Credential Submission*

* User lands on the Sign In screen.
* User enters *email* and *password*.
* System checks if account exists:
** If the account does not exist → show: _“Account not found. Please check your credentials.”_
** If account exists:
*** If *account is verified and active* → system validates role and logs in the user to the appropriate dashboard (User or Merchant).
*** If *account is found but pending verification* → redirect user to *OTP Verification screen*.

*Step 2: OTP Verification (for pending accounts)*

* System sends a *6-digit OTP* to the user’s email.
* OTP expires in *30 minutes*.
* OTP resend is allowed:
** After 60 seconds from last request
** Up to 5 times per day (tracked separately from password reset)
* User enters the OTP and taps *Verify & Login*
** If OTP is valid → status is updated to {{active}}, user is signed in
** If OTP is invalid → show error
** After 5 invalid attempts → system locks further attempts for 24 hours

----

h2. 3. Field Definitions by Step

----

h3. Step 1: Login Screen

||Field Name||Field Type||Validation Rule||Mandatory||Description||
|Email|Text (email)|Must be in valid email format|Yes|Identifies the account to be accessed|
|Password|Password|Match existing password record|Yes|Authenticates the user’s identity|

----

h3. Step 2: OTP Verification (Pending Accounts Only)

||Field Name||Field Type||Validation Rule||Mandatory||Description||
|OTP Code|Numeric (6)|Expires in 30 min, resend after 60s, max 5x/day (independent from reset count)|Yes|Required to verify and activate user|

----

h2. 4. Acceptance Criteria

* AC1: User must input valid email and password to attempt login.
* AC2: If credentials do not match any account, show: _“Account not found.”_
* AC3: If credentials match but account is {{pending_verification}}, redirect to OTP screen.
* AC4: System sends a 6-digit OTP to the registered email for unverified accounts.
* AC5: OTP expires in 30 minutes, can be resent after 60 seconds, and up to 5 times/day.
* AC6: On successful OTP submission:
** Account status is updated to {{active}}
** User is logged in to correct dashboard based on stored role
* AC7: Incorrect OTP shows error: _“Incorrect code. Please try again.”_
* AC8: After 5 failed OTP attempts, user is blocked from retrying for 24 hours.
* AC9: If account is active and credentials are valid, user is logged in directly.

----

h2. 5. System Rules

* Email is the unique identifier for sign-in.
* Role is attached to the account at registration and cannot be altered at sign-in.
* OTP used for activation is single-purpose and distinct from forgot password OTPs.
* OTP lifecycle (expiry, resend, max attempts) is enforced per user account.
* Blocked accounts or those exceeding OTP retry limits will not be allowed to continue until timer resets.",,Duyen Nguyen,,712020:59b66c6e-1068-441c-9436-130289e16a26,,,,,,,,,,,,,,,,,com.atlassian.servicedesk.plugins.approvals.internal.customfield.ApprovalsCFValue@68c0bc1,,,,,,,,,{},,,,,,,,,,,,,,,,,,MOBILE,,,,0|i02qhj:,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,37144,AL-1,Authentication,In Progress,06/Jun/25 1:37 PM
[MB - Authentication] Sign Up as User,AL-2,37146,Story,In Progress,AL,AIOS Link,software,Kai Nguyễn,6214a4102d1a9d0069949c96,,Medium,,,,Duyen Nguyen,712020:59b66c6e-1068-441c-9436-130289e16a26,Duyen Nguyen,712020:59b66c6e-1068-441c-9436-130289e16a26,02/Jun/25 8:42 PM,17/Jun/25 3:19 PM,,,,0,"h3. 1. Use Case Description

As a new user, I want to select my role, register with my email, and activate my account via an OTP
so that I can securely access the AIOS Link platform with the appropriate dashboard and permissions.

----

h3. 2. User Workflow (Step-by-Step)

*Step 1: Role Selection*

* User opens the AIOS Link mobile app.
* User taps “Sign Up”.
* System presents a screen to choose between:
** User
** Merchant
* User selects one role and continues.

*Step 2: Email Entry & Validation*

* User enters their email address.
* System checks if the email already exists:
** If the email is not yet registered → proceed to Step 3.
** If already registered and verified → show: “This email is already registered. Please sign in.”
** -If already registered but not verified → redirect to OTP activation screen with a message: “This account hasn’t been verified. Please complete verification.”-

*Step 3: Account Details*

* User enters Full Name, Password, and Confirm Password.
* Password must meet validation rules.
* Upon submission, the system creates a temporary user record with {{pending_verification}} status.

*Step 4: OTP Email Activation*

* System sends a 6-digit OTP to the user’s email.
* OTP expires in 30 minutes.
* Max resend allowed is 5 times per day (counted separately from Forgot Password flow).
* Resend is enabled after 60 seconds from previous request.
* User enters OTP and submits for verification.

*Step 5: Account Activation Completion*

* If OTP is valid, system updates account status to {{active}}.
* User is redirected to the Sign In screen.

----

h2. 3. Field Definitions 

h3. Step 1: Role Selection

||Field Name||Field Type||Validation Rule||Mandatory||Description||
|Role|Dropdown|One of: User, Merchant|Yes|Determines access level and permissions|

----

h3. Step 2: Email Entry

||Field Name||Field Type||Validation Rule||Mandatory||Description||
|Email|Text (email)|Valid email format|Yes|Used to identify and verify the user|

----

h3. Step 3: Account Information

||Field Name||Field Type||Validation Rule||Mandatory||Description||
|Full Name|Text|3–50 characters|Yes|User's display name|
|Password|Password|Minimum 8 characters, must include 1 uppercase, 1 lowercase, 1 number, 1 special|Yes|For secure login|
|Confirm Password|Password|Must match Password|Yes|Confirms password input|

----

h3. Step 4: OTP Verification

||Field Name||Field Type||Validation Rule||Mandatory||Description||
|OTP Code|Numeric (6)|Expires in 30 minutes, max resend 5x/day, resend available after 60 seconds|Yes|Used to activate the account|

----

h2. 4. Acceptance Criteria

* AC1: User must select a role before proceeding to email entry.
* AC2: If the email is already registered and active, block further registration.
* -AC3: If email is registered but not verified, redirect to OTP verification screen.-
* AC4: Email entry must validate format and uniqueness.
* AC5: Full Name, Password, and Confirm Password must meet all validation rules.
* AC6: On successful form submission, system sends OTP and creates a pending account.
* AC7: OTP expires in 30 minutes; max resend 5 times/day; resend available after 60 seconds.
* AC8: Successful OTP verification changes account status to active and redirects to Sign In.
* AC9: Unverified accounts cannot bypass OTP screen to reach the dashboard.

----

h2. 5. System Rules

* Email is the unique identifier for all users.
* Role is locked at account creation and cannot be changed post-registration.
* OTP for activation is stored with expiry, hashed, and single-use.
* OTP resend and attempts are counted separately from the Forgot Password feature.
* Accounts with pending verification are not granted dashboard access until activated.",,Duyen Nguyen,,712020:59b66c6e-1068-441c-9436-130289e16a26,,,,,,,,,,,,,,,,,com.atlassian.servicedesk.plugins.approvals.internal.customfield.ApprovalsCFValue@5d838ad0,,,,,,,,,{},,,,,,,,,,,,,,,,,,MOBILE,,,,0|i02qhb:,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,37144,AL-1,Authentication,In Progress,05/Jun/25 9:07 PM
