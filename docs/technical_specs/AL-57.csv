Summary,Issue key,Issue id,Issue Type,Status,Project key,Project name,Project type,Project lead,Project lead id,Project description,Priority,Resolution,Assignee,Assignee Id,Reporter,Reporter Id,Creator,Creator Id,Created,Updated,Last Viewed,Resolved,Due date,Votes,Labels,Description,Environment,Watchers,Watchers,Watchers Id,Watchers Id,Original estimate,Remaining Estimate,Time Spent,Work Ratio,Σ Original Estimate,Σ Remaining Estimate,Σ Time Spent,Security Level,Custom field (% Complete),Custom field (Actual end),Custom field (Actual start),Custom field (Affected services),Custom field (App),Custom field (App),Custom field (App),Custom field (Approvals),Custom field (Bug Type),Custom field (Build Version),Custom field (Build Version),Custom field (Category),Custom field (Change reason),Custom field (Change risk),Custom field (Change type),Custom field (Company size),Custom field (Development),Custom field (Email),Custom field (Environment),Custom field (Environment),Custom field (Environment),Custom field (Epic Color),Custom field (Epic Name),Custom field (Epic Status),Custom field (Goals),Custom field (Impact),Custom field (Issue color),Custom field (Locked forms),Custom field (OS),Custom field (Open forms),Custom field (Operational categorization),Custom field (Pending reason),Custom field (Phone number),Custom field (Platform),Custom field (Platform),Custom field (Product categorization),Custom field (Project overview key),Custom field (Project overview status),Custom field (Rank),Custom field (Request Type),Custom field (Request language),Custom field (Request participants),Custom field (Responders),Satisfaction rating,Custom field (Satisfaction date),Custom field (Sentiment),Custom field (Start date),Custom field (Story Points),Custom field (Story point estimate),Custom field (Submitted forms),Custom field (Target end),Custom field (Target start),Custom field (Team),Custom field (Test Result),Custom field (Test User Picker),Custom field (Time to first response),Custom field (Time to resolution),Custom field (Total forms),Custom field (Type),Custom field (Urgency),Custom field (Version),Custom field (Vulnerability),Custom field (Website),Custom field (Work category),Custom field ([CHART] Date of First Response),Custom field ([CHART] Time in Status),Comment,Comment,Parent,Parent key,Parent summary,Status Category,Status Category Changed
[MB - Contacts] Contact Map View,AL-57,37431,Story,Specified,AL,AIOS Link,software,Kai Nguyễn,6214a4102d1a9d0069949c96,,Medium,,,,Duyen Nguyen,712020:59b66c6e-1068-441c-9436-130289e16a26,Duyen Nguyen,712020:59b66c6e-1068-441c-9436-130289e16a26,04/Jun/25 4:46 PM,05/Jun/25 11:06 AM,04/Jul/25 3:22 PM,,,0,,"h3. 1. Use Case Description

As a user, I want to view other nearby contacts who are currently available and have visibility enabled
so that I can explore connections geographically and reach out to people near me in real time.

----

h3. 2. User Workflow (Step-by-Step)

# User navigates to the *Contacts tab*.
# User taps the *“Map”* tab in the Connections section.
# System checks if the user has enabled:
#* *Availability to connect*
#* *Location visibility*
# If either setting is disabled:
#* System displays a blocking prompt:
_“To use the map view, please turn on your visibility and availability to connect.”_
#* User can tap *“Update Settings”* or *“Not Now”*
# If user taps *“Update Settings”*:
#* Availability and visibility toggles are activated.
#* System reloads the Map View.
# If user taps *“Not Now”*, they are returned to the previous tab; Map View is not accessible.
# Once visibility is active, Map View loads:
#* The user appears at their own location pin (labeled ""You"").
#* Other nearby users are shown only if:
#** They are marked *Available to connect*
#** They have *visibility enabled*
# User can tap on another contact’s pin to open a mini profile with options (e.g., message, view full profile).

----

h3. 3. Field Definitions Table

||Field Name||Field Type||Validation Rule||Mandatory||Description||
|Availability Toggle|Boolean|True to activate map|Yes|User must be available to appear or view on map|
|Visibility Toggle|Boolean|True to activate map|Yes|Location visibility must be enabled|
|User Location|GeoPoint|Accurate to city-level or better|Yes|User’s current location|
|Visible Contacts List|List|Filtered by availability + visibility|Yes|Other users to be plotted on the map|

----

h3. 4. Data Display Table (Map View)

||Element||Format Example||Notes||
|User Pin|“You” + avatar|Always visible if settings enabled|
|Other Contact Pins|Avatar only|Visible only if contact is available + visible|
|Mini Profile Pop-up|“Sarah Johnson – 500m away”|Includes actions: message, view profile|
|Status Toggle UI|“Available to connect” toggle with Update button|Controls map access|

----

h3. 5. Acceptance Criteria (AC)

* *AC1:* If either Availability or Visibility is turned off, system must block access to Map View.
* *AC2:* Blocking prompt must contain message and two buttons: “Update Settings” and “Not Now.”
* *AC3:* Selecting “Update Settings” enables both toggles and reloads the Map View.
* *AC4:* Selecting “Not Now” redirects user to the previous tab and does not load the map.
* *AC5:* Once access is granted, system displays user location centered on map.
* *AC6:* Nearby contact pins are only shown if they also have visibility + availability enabled.
* *AC7:* Tapping another user’s pin shows a mini profile card.
* *AC8:* User's location pin should remain updated while in map view (with permissions enabled).
* *AC9:* If user turns off visibility or availability while on the map, system exits map with alert:
_“You must be visible and available to stay on Map View.”_

----

h3. 6. System Rules

* Availability and Visibility settings are stored in user profile.
* Users with either setting off are fully excluded from the Map View experience (in both directions).
* Location data is retrieved from the device and must use permission-controlled APIs.
* Map updates are throttled to reduce performance impact (e.g., every 15 seconds or on move).
* Only contacts within a 25km radius are shown (configurable threshold).

----

h3. 7. UX Optimizations

* Use soft transition overlays for onboarding prompt.
* Animated map pins when new users appear nearby.
* Fallback UI:
_“No nearby contacts available right now. Try again later.”_
* Location denied scenario:
_“Please enable location access in your device settings to use this feature.”_",,Duyen Nguyen,,712020:59b66c6e-1068-441c-9436-130289e16a26,,,,,,,,,,,,,,,,,com.atlassian.servicedesk.plugins.approvals.internal.customfield.ApprovalsCFValue@7d4223ca,,,,,,,,,{},,,,,,,,,,,,,,,,,,MOBILE,,,,0|i02qsn:,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,37256,AL-17,Contacts,To Do,04/Jun/25 4:46 PM
[WEB - User Management] Create User,AL-73,37769,Story,In Progress,AL,AIOS Link,software,Kai Nguyễn,6214a4102d1a9d0069949c96,,Medium,,,,Duyen Nguyen,712020:59b66c6e-1068-441c-9436-130289e16a26,Duyen Nguyen,712020:59b66c6e-1068-441c-9436-130289e16a26,09/Jun/25 4:25 PM,25/Jun/25 5:52 PM,01/Jul/25 3:38 PM,,,0,Dev,"1. Use Case Description

As an Admin, I want to create a user account and optionally associate them with an organization (if applicable), so that the account is properly linked to business entities and complies with role-based permissions and ownership rules.

----

h3. 2. User Workflow (Step-by-Step)

# Admin logs into the AIOS Link Web Portal.
# Admin navigates to *User Management > Users*.
# Admin clicks the *“Create User”* button.
# System displays the *Create User form*.
# Admin completes the user details including role and all mandatory fields.
# Below the role dropdown, system displays the *Organization Selection section*:
#* If role = *User*, organization linking is optional and can be skipped.
#* If role = *Merchant*, system enforces selection of an organization.
#* If an organization is already owned by another user, its selection card is disabled (grayed out).
# Admin selects an available organization for Merchant user.
# Admin clicks *Save*.
# System validates all fields and:
#* Assigns the user as *Owner* of selected organization if role = Merchant.
#* Sets account status = {{pending_activation}}.
# System sends activation OTP email to the user.
# A success toast is displayed: “User created successfully.”

----

h3. 3. Field Definitions Table

||Field Name||Field Type||Validation Rule||Mandatory||Description||
|Full Name|Text Input|Max 50 characters|Yes|User's legal name|
|Email|Email Input|Valid format, must not already exist|Yes|Unique identifier|
|Phone Number|Text Input|Valid phone number|Yes|For contact & identity|
|Password|Password Input|≥8 chars, 1 uppercase, 1 lowercase, 1 number, 1 special char|Yes|Temporary system password|
|Role|Dropdown|One of: [User, Merchant]|Yes|Defines permission scope|
|Organization|Dropdown/List|Select from available organizations (disabled if owned)|Conditional*|Required if role = Merchant|
|Job Title|Text Input|Max 50 characters|Yes|User's title or professional role|
|Gender|Enum|Male, Female, Other|Yes|Self-reported gender|
|Industry|Text Input|Max 50 characters|No|Field of work|
|Date of Birth|Date Picker|Valid date|No|Used for profile completion|
|Address|Text Area|Max 100 characters|No|Private|
|Bio|Text Area|Max 500 characters|No|Public summary|
|Skills & Interests|Tag Selector|Up to 15 entries|No|User interests or specialties|
|LinkedIn URL|URL Input|Must be valid LinkedIn profile|No|Clickable icon|
|Telegram Link|Text Input|Must be valid handle format|No|Contact channel|
|WhatsApp Link|Text Input|Must be valid URL format|No|Contact channel|
|Profile Picture|Image Upload|JPG/PNG, max 2MB, camera allowed|No|Visible in profile|

* Conditional: Required only if role = Merchant

----

h3. 4. Acceptance Criteria

* *AC1*: Admin can only create a user if all mandatory fields are valid.
* *AC2*: If role = Merchant, an organization *must* be selected.
* *AC3*: Organization dropdown only lists *organizations without owners*.
* *AC4*: Cards for already-owned organizations are visible but *grayed out and disabled*.
* *AC5*: If role = User, system allows skipping the organization field.
* *AC6*: On successful form submission:
** User is created with status = {{pending_activation}}
** Activation OTP is sent to email.
* *AC7*: User is shown in the user list with correct role and organization (if applicable).
* *AC8*: System stores the user-role-organization mapping persistently.
* *AC9*: If merchant user is linked to an org, they are automatically set as “Owner.”
* *AC10*: A success toast appears: “User created successfully.”

----

h3. 5. System Rules

* Email is the primary unique identifier.
* Each *organization can have only one Owner*.
* Users can only log into *mobile app*, not web portal.
* OTP is valid for 30 minutes and resending is allowed after 60s, max 5 per day.
* Created user cannot access the dashboard until activation is complete.
* Audit logs store record of which admin created the user, timestamp, and assigned role/org.

----

h3. 6. UX Optimizations

* Organization selection uses *card grid with search + disabled states* for owned orgs.
* Display tooltip:
“This organization already has an owner and cannot be selected.”
* Role selection dynamically toggles the organization field visibility and rules.
* Save button remains disabled until required conditions (incl. role/org logic) are satisfied.
* Toasts and inline error messages guide completion without form reload.",,Duyen Nguyen,,712020:59b66c6e-1068-441c-9436-130289e16a26,,,,,,,,,,,,,,,,,com.atlassian.servicedesk.plugins.approvals.internal.customfield.ApprovalsCFValue@59db63a3,,,,,,,,,{},,,,,,,,,,,,,,,,,,WEB,,,,0|i02qh2:,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,37761,AL-70,User Management,In Progress,12/Jun/25 10:45 AM
[MB - Account Settings] Set Visibility,AL-114,38253,Story,Specified,AL,AIOS Link,software,Kai Nguyễn,6214a4102d1a9d0069949c96,,Medium,,,,Duyen Nguyen,712020:59b66c6e-1068-441c-9436-130289e16a26,Duyen Nguyen,712020:59b66c6e-1068-441c-9436-130289e16a26,12/Jun/25 3:34 PM,01/Jul/25 1:38 PM,,,,0,Dev,"

h3. 1. Use Case Description

As a user on AIOS Link, I want to toggle my visibility on or off so that I can control whether my profile appears on the map view when other users perform a contact search.

----

h3. 2. User Workflow (Step-by-Step)

# User navigates to *Settings > Privacy > Visibility*.
# System displays a single toggle option:
#* *“Show me on map when others search for contacts nearby”*
# User enables or disables the toggle.
# System saves the setting immediately.
# A confirmation toast is shown:
#* If enabled: _“You are now visible on the map.”_
#* If disabled: _“Your location is hidden from the map.”_

----

h3. 3. Field Definitions Table

||Field Name||Input Type||Required||Validation Rules||
|Map Visibility|Toggle|✅|On = visible on map; Off = hidden|

----

h3. 4. Data Display Table

||Display Field||Format Example||Notes||
|Visibility Toggle|[On/Off] Switch|Current setting state|
|Description|“Your profile will appear on the contact map when enabled.”|Below toggle|

----

h3. 5. Acceptance Criteria

* *AC1*: Visibility toggle appears in Privacy settings.
* *AC2*: Toggle is enabled by default for new users.
* *AC3*: Toggling OFF removes the user from map-based contact search results.
* *AC4*: Toggling ON includes user in real-time map view (based on location).
* *AC5*: Toast confirms setting update immediately upon toggle.

----

h3. 6. System Rules

* Visibility setting is stored in user profile metadata.
* When OFF, the system excludes the user’s geo-coordinates from any map results.
* Toggle state is respected in real-time by the map-based contact search feature.
* This toggle does not affect profile visibility in non-map views (e.g., direct search).

----

h3. 7. UX Optimizations

* Show location pin icon next to the toggle label.
* Tooltip or “i” icon explains: “When enabled, your location will appear on the map to other users.”
* Disable location sync if visibility is OFF to conserve background updates.",,Duyen Nguyen,Phạm Xuân Định,712020:59b66c6e-1068-441c-9436-130289e16a26,63e3536328cddcc707748a66,,,,,,,,,,,,,,,,com.atlassian.servicedesk.plugins.approvals.internal.customfield.ApprovalsCFValue@4b884bd8,,,,,,,,,{},,,,,,,,,,,,,,,,,,MOBILE,,,,0|i02rjb:,,,,,,,,,,,,,,,,,,,,,,,,,,2025-07-01 03:35:27.958,,01/Jul/25 10:35 AM;63e3536328cddcc707748a66;checked,01/Jul/25 1:38 PM;63e3536328cddcc707748a66;Done,37260,AL-21,Account Settings,To Do,12/Jun/25 3:34 PM
