Summary,Issue key,Issue id,Issue Type,Status,Project key,Project name,Project type,Project lead,Project lead id,Project description,Priority,Resolution,Assignee,Assignee Id,Reporter,Reporter Id,Creator,Creator Id,Created,Updated,Last Viewed,Resolved,Due date,Votes,Description,Environment,Watchers,Watchers Id,Original estimate,Remaining Estimate,Time Spent,Work Ratio,Σ Original Estimate,Σ Remaining Estimate,Σ Time Spent,Security Level,Custom field (% Complete),Custom field (Actual end),Custom field (Actual start),Custom field (Affected services),Custom field (App),Custom field (Approvals),Custom field (Category),Custom field (Change reason),Custom field (Change risk),Custom field (Change type),Custom field (Company size),Custom field (Development),Custom field (Email),Custom field (Environment),Custom field (Epic Color),Custom field (Epic Name),Custom field (Epic Status),Custom field (Goals),Custom field (Impact),Custom field (Issue color),Custom field (Locked forms),Custom field (Open forms),Custom field (Operational categorization),Custom field (Pending reason),Custom field (Phone number),Custom field (Platform),Custom field (Platform),Custom field (Product categorization),Custom field (Project overview key),Custom field (Project overview status),Custom field (Rank),Custom field (Request Type),Custom field (Request language),Custom field (Request participants),Custom field (Responders),Satisfaction rating,Custom field (Satisfaction date),Custom field (Sentiment),Custom field (Start date),Custom field (Story Points),Custom field (Story point estimate),Custom field (Submitted forms),Custom field (Target end),Custom field (Target start),Custom field (Team),Custom field (Test Result),Custom field (Test User Picker),Custom field (Time to first response),Custom field (Time to resolution),Custom field (Total forms),Custom field (Urgency),Custom field (Version),Custom field (Vulnerability),Custom field (Website),Custom field (Work category),Custom field ([CHART] Date of First Response),Custom field ([CHART] Time in Status),Parent,Parent key,Parent summary,Status Category,Status Category Changed
[MB - Storefront] Sell Orders List,AL-107,38220,Story,Specified,AL,AIOS Link,software,Kai Nguyễn,6214a4102d1a9d0069949c96,,Medium,,,,Duyen Nguyen,712020:59b66c6e-1068-441c-9436-130289e16a26,Duyen Nguyen,712020:59b66c6e-1068-441c-9436-130289e16a26,12/Jun/25 10:26 AM,12/Jun/25 11:46 AM,,,,0,"h3. 1. Use Case Description

As a merchant user on the mobile platform, I want to view a list of orders placed for my marketplace products so that I can track their status and manage fulfillment.

----

h3. 2. User Workflow

# Merchant navigates to *My Storefront* > *Sell Orders*.
# System displays a paginated list of orders sorted by most recent first.
# Merchant can:
#* Enter a search keyword
#* Open filters and refine by status, product, or date
# Tapping a row opens the *Sell Order Detail* screen for that order.

----

h3. 3. Search and Filter Rules

h4. A. Search Rules

* Match against:
** *Order Number*
** *Customer Name*
** *Product Name*
* Matching is case-insensitive and supports partial text.

h4. B. Filter Rules

||Filter Name||Type||Options||
|Order Status|Dropdown|{{New}}, {{Confirmed}}, {{In Delivery}}, {{Completed}}, {{Canceled}}|
|Order Date Range|Date Picker|Start and End dates (Start ≤ End)|
|Product|Dropdown|List of the merchant's own marketplace products|

----

h3. 4. Field Definitions

h4. Interaction Elements

||Element||Type||Description||
|Search Input|Text Field|Always visible|
|Filter Button|Button|Opens the filter modal|
|Order Row Link|Tap Target|Opens Sell Order Details|

h4. Display Fields (List Row Format)

||Field Name||Data Type||Format||Description||
|Order Number|Text|Plaintext|Unique order identifier|
|Customer Name|Text|Plaintext|Name of buyer|
|Product Summary|Text|“[Product Name] +X more”|First product name and total items|
|Total Price (USD)|Currency|$XX.XX|Includes tax if seller-enabled|
|Status|Badge|Label|Current status of order|
|Order Date|Date|YYYY-MM-DD|Date when order was placed|

----

h3. 5. Acceptance Criteria

* *AC1*: Display order number, customer name, product summary, total price (USD), status, and order date.
* *AC2*: Search works across Order Number, Product Name, and Customer Name.
* *AC3*: Filter modal enables multi-field filtering as per defined options.
* *AC4*: Tapping a row opens corresponding Sell Order Details.
* *AC5*: Orders are loaded in pages of 20 with infinite scroll or pagination.

----

h3. 6. System Rules

* All prices displayed in *USD*, tax included if applicable per product.
* List is sorted by {{order_created_at DESC}}.
* Search and filter states persist when returning from the detail screen.
* Order data is refreshed every 30 seconds automatically.
* Canceled and completed orders are not editable and only viewable.

----

h3. 7. UX Optimizations

* Sticky search bar while scrolling through orders.
* Skeleton loader on scroll for seamless pagination.
* Highlight row on tap to show interactivity.
* Empty state message: _“No orders match your filters.”_",,Duyen Nguyen,712020:59b66c6e-1068-441c-9436-130289e16a26,,,,,,,,,,,,,,com.atlassian.servicedesk.plugins.approvals.internal.customfield.ApprovalsCFValue@39c53f6f,,,,,,{},,,,,,,,,,,,,,,MOBILE,,,,0|i02rg7:,,,,,,,,,,,,,,,,,,,,,,,,,,,37258,AL-19,Storefront,To Do,12/Jun/25 10:26 AM
[MB - Storefront] Seller view Sell Order Details,AL-106,38217,Story,Specified,AL,AIOS Link,software,Kai Nguyễn,6214a4102d1a9d0069949c96,,Medium,,,,Duyen Nguyen,712020:59b66c6e-1068-441c-9436-130289e16a26,Duyen Nguyen,712020:59b66c6e-1068-441c-9436-130289e16a26,12/Jun/25 10:26 AM,12/Jun/25 11:27 AM,,,,0,"h3. 1. Use Case Description

As a merchant user on the mobile platform, I want to view detailed information about a sell order so that I can fulfill it accurately or take further actions.

----

h3. 2. User Workflow

# Merchant opens the *My Storefront > Orders* tab.
# Taps on an order to view its *Sell Order Details*.
# System displays:
#* Order metadata (number, date, status)
#* Customer info (name, contact, delivery address)
#* List of purchased items with quantity and unit price
#* Final total in *USD* (includes tax if applicable)
# Merchant may:
#* Tap *Cancel Order* (if order is cancellable)
#* Tap *Contact Buyer* (opens in-app chat)
# Merchant taps *Back* to return to Sell Order List.

----

h3. 3. Field Definitions

h4. Interaction Elements

||Element Name||Type||Visibility Condition||Description||
|Cancel Order Button|Button|If order is not completed|Launches cancel flow|
|Contact Buyer|Button|Always|Opens chat or default communication app|
|Back Button|Button|Always|Returns to Sell Order List|

----

h3. 4. Data Display

h4. Summary Fields

||Field Name||Data Type||Display When Empty||Format||Description||
|Order Number|Text|""--""|Plaintext|Unique identifier|
|Customer Name|Text|""--""|Full name|Name of buyer|
|Shipping Address|Text|""--""|Multiline Text|Delivery destination|
|Phone Number|Integer|“--”| |Buyer’s mobile or contact number|
|Order Status|Badge|""--""|New / Processing / Completed / Cancelled|Current order state|
|Order Date|Date|""--""|YYYY-MM-DD|Date order was placed|
|Total Price (USD)|Amount|""--""|$XX.XX|Final amount including tax|

h4. Product Table

||Column Name||Data Type||Description||
|Product Name|Text|Name of the purchased product|
|Unit Price (USD)|Amount|Price per unit in USD (incl. tax if set)|
|Quantity|Number|Number of units ordered|

----

h3. 5. Acceptance Criteria

* *AC1*: Display all order details, customer info, and product breakdown in USD.
* *AC2*: Show Cancel Order button only for eligible statuses (e.g., Pending, Processing).
* *AC3*: Show Contact Buyer button with valid communication channel.
* *AC4*: Tap on Back returns to Sell Order List.
* *AC5*: Tax is reflected in Total Price if the product had {{tax_enabled = true}}.

----

h3. 6. System Rules

* Order details fetched live via API upon opening the screen.
* Cancellation and contact buttons trigger respective backend workflows.
* Tax applied per product level, not globally.
* Cancellable states include: {{new}}, {{processing}}. Completed or cancelled orders cannot be modified.
* Inventory and fulfillment status must stay synced with sell order actions.

----

h3. 7. UX Optimizations

* Sticky action buttons (Cancel, Contact) for thumb-friendly access.
* Skeleton loaders during data fetch.
* Display buyer contact method icon (chat or email) for visual clarity.
* Status badge color-coded (e.g., green for Completed, red for Cancelled).",,Duyen Nguyen,712020:59b66c6e-1068-441c-9436-130289e16a26,,,,,,,,,,,,,,com.atlassian.servicedesk.plugins.approvals.internal.customfield.ApprovalsCFValue@704af711,,,,,,{},,,,,,,,,,,,,,,MOBILE,,,,0|i02rfz:,,,,,,,,,,,,,,,,,,,,,,,,,,,37258,AL-19,Storefront,To Do,12/Jun/25 10:26 AM
[MB - Storefront] Seller Cancel Sell Order,AL-105,38214,Story,Specified,AL,AIOS Link,software,Kai Nguyễn,6214a4102d1a9d0069949c96,,Medium,,,,Duyen Nguyen,712020:59b66c6e-1068-441c-9436-130289e16a26,Duyen Nguyen,712020:59b66c6e-1068-441c-9436-130289e16a26,12/Jun/25 10:25 AM,12/Jun/25 10:54 AM,,,,0,"h3. 1. Use Case Description

As a merchant on the AIOS Link mobile platform, I want the ability to cancel an active sell order—before it is completed—so that inventory, buyer communication, and payment flows are properly updated.

----

h3. 2. User Workflow

# User navigates to the *Sell Order Details* screen via their *My Storefront > Orders* tab.
# User taps the *Cancel Order* button.
# System opens a *confirmation modal* requiring a cancellation reason.
# User provides a reason and taps *Confirm Cancellation*.
# System performs the following:
#* Updates order status to {{Canceled}}
#* Reverses inventory deduction
#* Initiates refund if payment has been captured
#* Logs cancellation event
#* Sends *in-app notification* and *email* to the buyer
# User is returned to Sell Order Details screen with updated status.

----

h3. 3. Field Definitions

h4. Input Fields

||Field Name||Type||Required||Validation||Description||
|Cancellation Reason|Text Area|✅|5–200 characters|Required reason for canceling the order|

h4. Interaction Elements

||Element Name||Type||Condition||Description||
|Cancel Order Button|Button|Shown on Sell Order Details if not completed|Opens cancellation modal|
|Confirmation Modal|Dialog|On tapping Cancel Order|Requires cancellation reason and confirm/cancel|
|Confirm Button|Button|Enabled if reason valid|Executes cancellation logic|
|Back/Dismiss Button|Button|Always visible in modal|Closes modal without taking action|

----

h3. 4. Data Display

||Element||Format||Description||
|Confirmation Message|Text|“Are you sure you want to cancel this order?”|
|Success Toast|Text|“Order canceled successfully.”|
|Notification Title|Text|“Order Canceled”|
|Notification Body|Text|“Your order #[order_id] has been canceled by the seller.”|
|Email Subject|Text|“Your AIOS Link order has been canceled”|
|Email Body|Text|“Order #[order_id] has been canceled. Reason: [reason].”|

----

h3. 5. Acceptance Criteria

* *AC1*: Cancel Order button is visible for eligible orders (not yet completed or canceled).
* *AC2*: Modal enforces cancellation reason before enabling Confirm.
* *AC3*: Status updates to {{Canceled}} only after confirmation.
* *AC4*: Refund is processed immediately if payment was already captured.
* *AC5*: Buyer is notified via push and email.
* *AC6*: Seller is returned to the details view with status visibly updated.

----

h3. 6. System Rules

* Only orders with status {{Pending}} or {{Processing}} are eligible for cancellation.
* Refunds are processed through the original Nuvei payment method before notifications are sent.
* Cancellation event must be audit-logged with:
** Seller ID
** Order ID
** Timestamp
** Reason
* Inventory units for the product(s) are restored.
* After cancellation, product availability is updated accordingly.

----

h3. 7. UX Optimizations

* Autofocus into the cancellation reason text area.
* Disable Confirm button until valid reason is entered.
* Show spinner while cancellation processes.
* Toast on success: _“Order canceled”_
* Optional: pre-fill recent cancellation reasons (future enhancement).

----",,Duyen Nguyen,712020:59b66c6e-1068-441c-9436-130289e16a26,,,,,,,,,,,,,,com.atlassian.servicedesk.plugins.approvals.internal.customfield.ApprovalsCFValue@437b565a,,,,,,{},,,,,,,,,,,,,,,MOBILE,,,,0|i02rfr:,,,,,,,,,,,,,,,,,,,,,,,,,,,37258,AL-19,Storefront,To Do,12/Jun/25 10:25 AM
[MB - Marketplace] Product List,AL-102,38204,Story,Specified,AL,AIOS Link,software,Kai Nguyễn,6214a4102d1a9d0069949c96,,Medium,,,,Duyen Nguyen,712020:59b66c6e-1068-441c-9436-130289e16a26,Duyen Nguyen,712020:59b66c6e-1068-441c-9436-130289e16a26,12/Jun/25 9:44 AM,12/Jun/25 10:56 AM,,,,0,"h3. 1. Use Case Description

As a user on the mobile platform, I want to browse marketplace products with search and filter options so that I can quickly find items to purchase.

----

h3. 2. User Workflow

# User taps *Marketplace* from the bottom navigation bar.
# System loads a scrollable list of available products with:
#* Search input
#* Filter button
# User:
#* Enters a *search keyword* to refine results
#* Or taps the *Filter* button to configure search filters
# Product list updates dynamically based on inputs.
# User scrolls to explore listings and taps any *Product Card* to open *Product Details*.

----

h3. 3. Search and Filter Logic

h4. A. Search Rules

||Rule||Description||
|Fields searched|Product Name, Category, Merchant Name|
|Match behavior|Case-insensitive, partial match|

h4. B. Filter Options

||Filter Name||Type||Options||Description||
|Category|Multi-select|Based on available categories|Filter by product type|
|Price Range (USD)|Min/Max Number|Currency: USD|Filter by USD selling price|
|Merchant Rating|Dropdown|5★, 4★+, 3★+|Show products from well-rated sellers|
|In Stock Only|Toggle|On / Off|Exclude out-of-stock items|

----

h3. 4. Field Definitions

h4. Interaction Elements

||Element||Type||Description||
|Search Box|Text Input|Allows keyword search|
|Filter Button|Button|Opens filter modal|
|Product Card|Tap Target|Opens detailed view of the selected product|
|Load More Trigger|Infinite Scroll|Loads next batch of products|

----

h3. 5. Data Display (Product Card Format)

||Field Name||Data Type||Format||Notes||
|Thumbnail|Image|1:1 Aspect Ratio|Product image or placeholder|
|Product Name|Text|Plaintext|Max 2 lines, bolded|
|Price (USD)|Currency|$XX.XX|Includes tax if enabled by seller|
|Category|Text|Plaintext|Shown in tag or chip format|
|Merchant Name|Text|Plaintext|Linked to merchant profile|
|Rating|Badge|X.X / 5.0|Average rating with star icon|

----

h3. 6. Acceptance Criteria

* *AC1*: Product list displays cards with image, name, price (USD), category, merchant, and rating.
* *AC2*: Search works across Product Name, Category, and Merchant Name.
* *AC3*: Filter panel includes Category, Price (USD), Rating, and In Stock toggle.
* *AC4*: Infinite scroll loads 20 products per batch.
* *AC5*: Tapping a card opens full *Marketplace Product Detail*.
* *AC6*: All prices shown in *USD*, tax included if {{tax_enabled = true}} by the merchant.

----

h3. 7. System Rules

* Listings are sorted by relevance to search first, then by latest publish date.
* Filters persist until reset or cleared by the user.
* List updates in real-time on search input or filter change.
* Listings with quantity = 0 are hidden if “In Stock Only” is toggled ON.

----

h3. 8. UX Optimizations

* Sticky search bar remains visible during scroll.
* Skeleton loaders show while loading product batches.
* “No Results” message appears when no listings match the search/filter.
* Swipe-down gesture refreshes product list.
* Display “Only X left” if stock is critically low (≤5 units).",,Duyen Nguyen,712020:59b66c6e-1068-441c-9436-130289e16a26,,,,,,,,,,,,,,com.atlassian.servicedesk.plugins.approvals.internal.customfield.ApprovalsCFValue@5355371d,,,,,,{},,,,,,,,,,,,,,,MOBILE,,,,0|i02rev:,,,,,,,,,,,,,,,,,,,,,,,,,,,37259,AL-20,Marketplace,To Do,12/Jun/25 9:44 AM
[MB - Marketplace] Product Details,AL-101,38201,Story,Specified,AL,AIOS Link,software,Kai Nguyễn,6214a4102d1a9d0069949c96,,Medium,,,,Duyen Nguyen,712020:59b66c6e-1068-441c-9436-130289e16a26,Duyen Nguyen,712020:59b66c6e-1068-441c-9436-130289e16a26,12/Jun/25 9:44 AM,12/Jun/25 10:49 AM,,,,0,"h3. 1. Use Case Description

As a user on the mobile platform, I want to view detailed product information with a buy option so that I can decide whether to purchase the item.

----

h3. 2. User Workflow

# User browses the *Marketplace Product List* and taps a product.
# System navigates to the *Product Detail* screen.
# Screen displays:
#* Product images (carousel)
#* Name, description, price (in USD), quantity selector
#* Merchant name (linked to Storefront)
#* Stock availability, rating (if any)
# User selects quantity
# User may:
#* Tap *Add to Cart* → Item added to cart with confirmation toast
#* Tap *Buy Now* → Navigated to prefilled *Checkout* screen

----

h3. 3. Field Definitions

h4. Input Fields

||Field Name||Type||Required||Validation||Description||
|Quantity|Stepper|✅|Integer (1–9999)|Number of units to purchase|

h4. Interaction Elements

||Element Name||Type||Condition||Description||
|Add to Cart Button|Button|Enabled if Quantity > 0 and stock available|Adds item(s) to cart|
|Buy Now Button|Button|Enabled if Quantity > 0 and stock available|Goes directly to Checkout|
|Image Carousel|Carousel|Always|Swipe through product images|
|Merchant Store Link|Link|Always|Opens merchant's storefront|

----

h3. 4. Data Display Table

||Field Name||Data Type||Display When Empty||Format||Description||
|Product Name|Text|""--""|Plaintext|Title of the product|
|Price (USD)|Currency|""--""|$XX.XX|Final unit price (USD), tax included if set|
|Description|Text|""--""|Multiline text|Full product description|
|Stock Status|Badge|""--""|In Stock/Out of Stock|Availability tag|
|Merchant Name|Text Link|""--""|Plaintext|Merchant name, links to their Storefront|
|Rating|Number|""-""|X.X / 5.0|Average customer rating|

----

h3. 5. Acceptance Criteria

* *AC1*: Product Details screen must show all core info: image, name, description, price (USD), merchant name, stock status, rating.
* *AC2*: Quantity stepper is limited between 1–99.
* *AC3*: Add to Cart stores item in cart and shows toast: _“Item added to cart”_.
* *AC4*: Buy Now pre-fills checkout with product, quantity, and price.
* *AC5*: Buttons are disabled if stock = 0.
* *AC6*: If merchant has enabled tax, price shown includes tax amount.

----

h3. 6. System Rules

* All prices displayed in *USD*.
* Final price includes *merchant-defined tax* if {{tax_enabled = true}}.
* Button visibility is governed by real-time stock status from backend.
* Product rating is calculated from verified buyer reviews only.
* System logs view activity for personalization and analytics.

----

h3. 7. UX Optimizations

* Display “Only X left” if stock ≤ 5.
* Show related products section under description.
* Image carousel auto-zooms on tap with swipe navigation.
* Include “Verified Seller” badge next to merchant name (if applicable).
* Animate confirmation toast on Add to Cart action.",,Duyen Nguyen,712020:59b66c6e-1068-441c-9436-130289e16a26,,,,,,,,,,,,,,com.atlassian.servicedesk.plugins.approvals.internal.customfield.ApprovalsCFValue@310217d3,,,,,,{},,,,,,,,,,,,,,,MOBILE,,,,0|i02ren:,,,,,,,,,,,,,,,,,,,,,,,,,,,37259,AL-20,Marketplace,To Do,12/Jun/25 9:44 AM
[MB - Marketplace] Checkout,AL-99,38195,Story,Specified,AL,AIOS Link,software,Kai Nguyễn,6214a4102d1a9d0069949c96,,Medium,,,,Duyen Nguyen,712020:59b66c6e-1068-441c-9436-130289e16a26,Duyen Nguyen,712020:59b66c6e-1068-441c-9436-130289e16a26,12/Jun/25 9:38 AM,12/Jun/25 10:54 AM,,,,0,"h3. 1. Use Case Description

As a user on the mobile platform, I want to finalize my purchase by reviewing cart items, entering delivery details, and submitting payment so that I can complete the order. When my cart contains products from multiple merchants, the checkout should create separate orders for each merchant with their own delivery information.

----

h3. 2. User Workflow

# User taps *Checkout* from the Cart screen.
# If items come from multiple merchants, system groups them by merchant and shows a separate delivery section for each order.
#* Each section includes item list, price in USD, any tax if applicable, and shipping.
# User enters or confirms:
#* Full Name
#* Phone Number
#* Delivery Address
#* Payment Method (via Nuvei)
# User reviews total amounts (item + tax + shipping).
# Taps *Place Order*.
# System processes payment and confirms with unique order IDs per merchant.

----

h3. 3. Field Definitions

h4. Input Fields

||Field Name||Type||Validation Rule||Mandatory||Description||
|Full Name|Text|1–50 characters|Yes|Recipient’s full name|
|Phone Number|Phone|Valid mobile format|Yes|Contact number|
|Delivery Address|Text Area|5–200 characters|Yes|Shipping address|
|Payment Method|Dropdown|Nuvei required|Yes|Selected payment method|
|Notes|Text Area|0–200 characters|No|Optional delivery note|

----

h3. 4. Data Display (Summary)

||Data Name||Type||Display When Empty||Format||Description||
|Order Summary|Card|Hidden|Text|Item list with quantity and USD price|
|Subtotal|Amount|“--”|USD|Price × quantity|
|Tax|Amount|“$0.00” if not set|USD|% of subtotal (only if seller applies)|
|Shipping Fee|Amount|“--”|USD|Merchant-based shipping cost|
|Total Cost|Amount|“--”|USD|Subtotal + tax + shipping|
|Order Confirmation|Toast|Hidden|Text|Message shown upon success|

----

h3. 5. Acceptance Criteria

* *AC1*: All required fields must be valid to enable *Place Order*.
* *AC2*: Total includes seller-defined tax if {{tax_enabled = true}}.
* *AC3*: Nuvei handles payment; on success, unique order numbers are returned per merchant.
* *AC4*: “Edit Cart” link navigates back without clearing entered data.
* *AC5*: Split checkout by merchant, each requiring its own delivery data.
* *AC6*: Confirmation screen displays all created order numbers.

----

h3. 6. System Rules

* All monetary fields are stored and displayed in *USD* with two decimal precision.
* If seller has {{tax_enabled}}, apply {{tax_percentage}} to their item subtotal.
* Each seller group results in one order object.
* Shipping rates are fetched dynamically based on delivery location.
* Orders are created only after Nuvei payment is confirmed.

----

h3. 7. UX Optimizations

* Autofill saved delivery info if available.
* Real-time tax and total update as inputs change.
* Spinner during Nuvei processing.
* Push and email sent after successful order placement.
* Show “Tax included by [Merchant Name]” label if applicable.",,Duyen Nguyen,712020:59b66c6e-1068-441c-9436-130289e16a26,,,,,,,,,,,,,,com.atlassian.servicedesk.plugins.approvals.internal.customfield.ApprovalsCFValue@7de7b97c,,,,,,{},,,,,,,,,,,,,,,MOBILE,,,,0|i02re7:,,,,,,,,,,,,,,,,,,,,,,,,,,,37259,AL-20,Marketplace,To Do,12/Jun/25 9:38 AM
[MB - Marketplace] Cart Management,AL-98,38192,Story,Specified,AL,AIOS Link,software,Kai Nguyễn,6214a4102d1a9d0069949c96,,Medium,,,,Duyen Nguyen,712020:59b66c6e-1068-441c-9436-130289e16a26,Duyen Nguyen,712020:59b66c6e-1068-441c-9436-130289e16a26,12/Jun/25 9:38 AM,12/Jun/25 10:00 AM,,,,0,"h3. 1. Use Case Description

As a user on the mobile platform, I want to manage items in my cart so that I can adjust quantities or remove products before checking out.

h3. 2. User Workflow

* Step 1: User opens the *Cart* from the navigation menu.
* Step 2: System displays a list of cart items with quantity steppers, subtotal per item, and overall total.
* Step 3: User adjusts quantity using the stepper or taps *Remove* to delete an item.
* Step 4: System updates totals instantly and disables Checkout if the cart becomes empty.
* Step 5: User taps *Checkout* to proceed when finished editing.

h3. 3. Field Definitions

h4. Input Fields

||Field Name||Field Type||Validation Rule||Mandatory||Description||
|Quantity|Stepper|1–9999|Yes|Adjusts units per product|

h4. Interaction Elements

||Element Name||Type||Trigger Condition||Description||
|Remove Button|Icon Button|Always visible|Deletes item from cart|
|Checkout Button|Button|Enabled when cart has items|Navigates to Checkout screen|

h3. 4. Data Display (Summary)

||Data Name||Data Type||Display When Empty||Format||Description||
|Product Name|Text|“–”|Plaintext|Name of product in cart|
|Price|Amount|“–”|USD|Unit price|
|Subtotal|Amount|“–”|USD|Quantity × price|
|Cart Total|Amount|“0”|USD|Sum of all subtotals|

h3. 5. Acceptance Criteria

* AC1: Cart shows each item with Quantity stepper, Price, and Subtotal.
* AC2: Changing quantity updates Subtotal and Cart Total immediately.
* AC3: Remove Button deletes the item and updates totals.
* AC4: Checkout Button is disabled when cart is empty.

h3. 6. System Rules

* Maximum quantity per item is limited by available stock.
* Cart data persists across sessions for logged-in users.
* Cart totals recalculate after each modification.

h3. 7. UX Optimizations

* Swipe left on an item to reveal the Remove action.
* Show micro-animation on total amount change.
* Provide “Continue Shopping” link when cart is empty.",,Duyen Nguyen,712020:59b66c6e-1068-441c-9436-130289e16a26,,,,,,,,,,,,,,com.atlassian.servicedesk.plugins.approvals.internal.customfield.ApprovalsCFValue@494a03c7,,,,,,{},,,,,,,,,,,,,,,MOBILE,,,,0|i02rdz:,,,,,,,,,,,,,,,,,,,,,,,,,,,37259,AL-20,Marketplace,To Do,12/Jun/25 9:38 AM
[MB - Marketplace] Add to Cart,AL-97,38190,Story,Specified,AL,AIOS Link,software,Kai Nguyễn,6214a4102d1a9d0069949c96,,Medium,,,,Duyen Nguyen,712020:59b66c6e-1068-441c-9436-130289e16a26,Duyen Nguyen,712020:59b66c6e-1068-441c-9436-130289e16a26,12/Jun/25 9:34 AM,12/Jun/25 9:52 AM,,,,0,"h3. 
1. Use Case Description

As a user on the mobile platform, I want to add products to my shopping cart so that I can review and purchase multiple items in one checkout.

h3. 2. User Workflow

* Step 1: User views a product on the *Product Details* screen.
* Step 2: User selects quantity and taps *Add to Cart*.
* Step 3: System confirms the action with a toast message and updates the cart badge in the navigation.
* Step 4: User may continue browsing or open the *Cart* from the navigation menu.

h3. 3. Field Definitions

h4. Input Fields

||Field Name||Field Type||Validation Rule||Mandatory||Description||
|Quantity|Stepper|1–9999|Yes|Number of units to add|

h4. Interaction Elements

||Element Name||Type||Trigger Condition||Description||
|Add to Cart Button|Button|Enabled when Quantity > 0|Adds selected quantity to cart|
|Cart Badge|Icon Badge|Visible when cart not empty|Shows total item count|

h3. 4. Data Display (Summary)

||Data Name||Data Type||Display When Empty||Format||Description||
|Confirmation Toast|Toast|Hidden|Plaintext|Appears after item added|
|Cart Item Count|Number|“0”|Numeric|Total items in cart|

h3. 5. Acceptance Criteria

* AC1: Add to Cart must respect the quantity stepper value.
* AC2: Confirmation toast appears after the item is added.
* AC3: Cart Badge updates with the new total item count.
* AC4: System prevents adding quantity exceeding available stock.

h3. 6. System Rules

* Cart items are stored locally and synced to the backend when the user is online.
* If the same product is added again, quantity increments rather than creating a new line item.
* Cart state persists across sessions for logged-in users.

h3. 7. UX Optimizations

* Shake animation on the cart badge when items are added.
* Provide undo action in the toast for 5 seconds.",,Duyen Nguyen,712020:59b66c6e-1068-441c-9436-130289e16a26,,,,,,,,,,,,,,com.atlassian.servicedesk.plugins.approvals.internal.customfield.ApprovalsCFValue@6666de9d,,,,,,{},,,,,,,,,,,,,,,MOBILE,,,,0|i02rdr:,,,,,,,,,,,,,,,,,,,,,,,,,,,37259,AL-20,Marketplace,To Do,12/Jun/25 9:34 AM
[WEB - Marketplace] Put Product to Marketplace,AL-85,37948,Story,Specified,AL,AIOS Link,software,Kai Nguyễn,6214a4102d1a9d0069949c96,,Medium,,,,Duyen Nguyen,712020:59b66c6e-1068-441c-9436-130289e16a26,Duyen Nguyen,712020:59b66c6e-1068-441c-9436-130289e16a26,10/Jun/25 7:35 PM,10/Jun/25 7:36 PM,,,,0,"h1. Put Product to Marketplace Requirement Specification

h3. 1. Use Case Description

As an admin on the web platform, I want to create a marketplace listing for a product that is not yet listed so that customers can purchase it through the platform.

h3. 2. User Workflow

* Step 1: Admin opens the *Product Details* page for a product not currently in the marketplace.
* Step 2: Admin taps *Put to Marketplace* within the Marketplace section.
* Step 3: System displays a form requesting listing information.
* Step 4: Admin enters Price and chooses whether to enable Tax. If Tax is enabled, a Tax Percentage field appears.
* Step 5: Admin taps *Save*. System validates input and creates the marketplace listing.
* Step 6: Product details update to show marketplace information including the listing price and date.

h3. 3. Field Definitions

h4. Input Fields

||Field Name||Field Type||Validation Rule||Mandatory||Description||
|Price|Amount|> 0|Yes|Listing price in VND|
|Enable Tax|Toggle|On or Off|Yes|Determines if tax applies|
|Tax Percentage|Number|0–100, shown only when Enable Tax = On|Conditional|Percentage tax applied|

h4. Interaction Elements

||Element Name||Type||Trigger Condition||Description||
|Put to Marketplace Button|Button|Visible when product not listed|Opens listing form|
|Save Button|Button|Enabled when form valid|Creates marketplace listing|
|Discard Button|Button|Always visible|Cancels and closes form|

h3. 4. Data Display (Summary)

||Data Name||Data Type||Display When Empty||Format||Description||
|Success Message|Toast|Hidden|Plaintext|Shown when listing created|
|Error Message|Text|Hidden|Plaintext|Displays validation errors|

h3. 5. Acceptance Criteria

* AC1: Put to Marketplace button appears only for products not yet listed.
* AC2: Listing form requires Price and optionally Tax Percentage when tax is enabled.
* AC3: On successful save, product becomes visible in the Marketplace Product List and the Product Details page shows the marketplace section.
* AC4: Discard button closes the form without saving.
* AC5: System records the listing date automatically at creation.

h3. 6. System Rules

* Price must be a positive value in USD.
* Tax Percentage must be a whole number between 0 and 100 when enabled.
* Listing date is stored in UTC and displayed according to admin timezone.
* Audit log captures admin ID and timestamp for the listing action.

h3. 7. UX Optimizations

* Autofocus on the Price field when the form opens.
* Display a live preview of total price including tax while editing.
* Show spinner on Save while the request processes.",,Duyen Nguyen,712020:59b66c6e-1068-441c-9436-130289e16a26,,,,,,,,,,,,,,com.atlassian.servicedesk.plugins.approvals.internal.customfield.ApprovalsCFValue@7929556f,,,,,,{},,,,,,,,,,,,,,,WEB,,,,0|i02r7r:,,,,,,,,,,,,,,,,,,,,,,,,,,,37259,AL-20,Marketplace,To Do,10/Jun/25 7:35 PM
[WEB - Marketplace] Product List,AL-84,37945,Story,Specified,AL,AIOS Link,software,Kai Nguyễn,6214a4102d1a9d0069949c96,,Medium,,,,Duyen Nguyen,712020:59b66c6e-1068-441c-9436-130289e16a26,Duyen Nguyen,712020:59b66c6e-1068-441c-9436-130289e16a26,10/Jun/25 7:24 PM,10/Jun/25 7:24 PM,,,,0,"h1. Marketplace Product List Requirement Specification

*Menu Path:* Marketplace > Product List

h3. 1. Use Case Description

As an admin on the web platform, I want to view all products currently listed in the marketplace so that I can manage prices and remove listings when needed.

h3. 2. User Workflow

* Step 1: Admin opens *Marketplace > Product List* from the Web Admin Module dashboard.
* Step 2: System displays a searchable table listing marketplace products with pagination.
* Step 3: Admin uses the search box or filters to locate specific products.
* Step 4: Admin clicks a row (excluding links) to open the *Product Details* page.
* Step 5: Admin clicks the Owner name link within the row to open the *Business Profile* page.
* Step 6: Admin clicks the *Actions* gear icon in the row and chooses *Update* or *Remove from Marketplace* from the popover.
* Step 7: System confirms the action and updates the listing accordingly.

h3. 3. Search and Filter Rules

h4. Search Rules

* Partial, case-insensitive match on *Product Name*, *Category*, *Manufacturer*, and *Business Name*.

h4. Filter Rules

* *Category*: Multi-select dropdown of marketplace product categories.
* *Manufacturer*: Dropdown listing all manufacturers.
* *Business*: Dropdown of business names.
* *Price Range*: Min/Max numeric fields to filter by listing price.
* *Listing Date Range*: Date range picker to filter by listing date.

h3. 4. Field Definitions

h4. Interaction Elements

||Element Name||Type||Trigger Condition||Description||
|Row Link|Row Click|Always enabled|Navigates to Product Details page|
|Business Link|Text Link|Always visible|Opens Business Profile page|
|Actions Gear Icon|Icon Button|Always visible|Opens popover with Update and Remove options|

h3. 5. Data Display (Summary)

||Data Name||Data Type||Display When Empty||Format||Description||
|Product Name|Text|""--""|Plaintext|Name of the product|
|Owner|Text Link|""--""|Plaintext|Business name of owner|
|Price|Amount|""--""|VND|Listing price|
|Category|Text|""--""|Plaintext|Product category|
|Manufacturer|Text|""--""|Plaintext|Manufacturer brand|
|Listing Date|Date|""--""|YYYY-MM-DD|Date product was listed|
|Actions|Gear Icon|Hidden|N/A|Popover with Update or Remove options|

h3. 6. Acceptance Criteria

* AC1: Table must display columns for Product Name, Owner, Price, Category, Manufacturer, Listing Date, and Actions.
* AC2: Clicking a row opens Product Details; clicking the Owner link opens the Business Profile page.
* AC3: Search returns matching results on Product Name, Category, Manufacturer, or Business Name.
* AC4: Filters narrow results by Category, Manufacturer, Business, Price Range, and Listing Date Range.
* AC5: Update and Remove actions trigger confirmation and reflect changes immediately in the table.
* AC6: Table loads with pagination of 25 marketplace products per page by default.

h3. 7. System Rules

* Remove action requires confirmation and is only allowed when the product has no active orders.
* Table data is sorted by Listing Date (newest first) by default.
* Update action must check for concurrent changes to avoid conflicts.

h3. 8. UX Optimizations

* Keep search box focused after returning from details pages.
* Highlight rows on hover to indicate clickability.
* Disable the gear icon menu while processing and show a spinner.
* Remember last applied filters when admin returns to the screen.
* Provide a tooltip for the gear icon to clarify its purpose.",,Duyen Nguyen,712020:59b66c6e-1068-441c-9436-130289e16a26,,,,,,,,,,,,,,com.atlassian.servicedesk.plugins.approvals.internal.customfield.ApprovalsCFValue@208857ab,,,,,,{},,,,,,,,,,,,,,,WEB,,,,0|i02r7j:,,,,,,,,,,,,,,,,,,,,,,,,,,,37259,AL-20,Marketplace,To Do,10/Jun/25 7:24 PM
[WEB - Marketplace] Remove Product from Marketplace,AL-35,37368,Story,Specified,AL,AIOS Link,software,Kai Nguyễn,6214a4102d1a9d0069949c96,,Medium,,,,Duyen Nguyen,712020:59b66c6e-1068-441c-9436-130289e16a26,Duyen Nguyen,712020:59b66c6e-1068-441c-9436-130289e16a26,04/Jun/25 2:15 PM,10/Jun/25 7:42 PM,,,,0,"h1. Remove Product from Marketplace Requirement Specification

h3. 1. Use Case Description

As an admin on the web platform, I want to remove a product listing from the marketplace so that it no longer appears for purchase while preserving historical orders.

h3. 2. User Workflow

* Step 1: Admin opens the *Actions* gear icon in the Marketplace Product List row and selects *Remove from Marketplace*, or taps *Remove* inside the Product Details page.
* Step 2: System displays a confirmation modal explaining that the product will disappear from the marketplace and active carts but remain in existing orders.
* Step 3: Admin confirms the removal.
* Step 4: System removes the listing, updates the Marketplace Product List, and shows a success message.

h3. 3. Field Definitions

h4. Interaction Elements

||Element Name||Type||Trigger Condition||Description||
|Remove Button|Button|Visible in Product Details page|Initiates removal workflow|
|Remove Option|Popover Item|Available under Actions gear icon|Initiates removal workflow|
|Confirmation Modal|Dialog|Shown after Remove pressed|Asks admin to confirm removal|
|Confirm Removal|Button|Enabled within modal|Confirms removal of listing|
|Cancel Removal|Button|Enabled within modal|Dismisses modal without changes|

h3. 4. Data Display (Summary)

||Data Name||Data Type||Display When Empty||Format||Description||
|Success Message|Toast|Hidden|Plaintext|Displayed when product is removed|
|Error Message|Text|Hidden|Plaintext|Shows validation or server errors|

h3. 5. Acceptance Criteria

* AC1: Remove option is available from both the Marketplace Product List and Product Details page.
* AC2: Confirmation modal clearly states that removed products disappear from the marketplace and carts but remain in all orders.
* AC3: After confirmation, product no longer appears in the Marketplace Product List.
* AC4: Success message appears after a successful removal.
* AC5: System prevents removal if the product has active unfulfilled orders and shows an error message.

h3. 6. System Rules

* Removal only updates marketplace listing status; original product record remains intact.
* Cart entries referencing the removed product are deleted immediately.
* Audit log records admin ID and timestamp of removal action.

h3. 7. UX Optimizations

* Prefill modal with product name for clarity.
* Display spinner inside modal while processing removal.
* Return focus to previous table row after successful removal.",,Duyen Nguyen,712020:59b66c6e-1068-441c-9436-130289e16a26,,,,,,,,,,,,,,com.atlassian.servicedesk.plugins.approvals.internal.customfield.ApprovalsCFValue@472b1f69,,,,,,{},,,,,,,,,,,,,,,WEB,,,,0|i02qlq:y,,,,,,,,,,,,,,,,,,,,,,,,,,,37259,AL-20,Marketplace,To Do,04/Jun/25 2:15 PM
