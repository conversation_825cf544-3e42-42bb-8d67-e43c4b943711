{"type":"heading","attrs":{"level":3},"content":[{"type":"text","text":"1. Use Case Description"}]}, {"type":"paragraph","content":[{"type":"text","text":"As a user, I want to edit and update my personal and professional profile information"},{"type":"hardBreak"},{"type":"text","text":"so that I can keep my data current and control how I appear to others on AIOS Link."}]}, {"type":"rule"}, {"type":"heading","attrs":{"level":3},"content":[{"type":"text","text":"2. User Workflow (Step-by-Step)"}]}, {"type":"orderedList","attrs":{"order":1},"content":[{"type":"listItem","content":[{"type":"paragraph","content":[{"type":"text","text":"User opens the "},{"type":"text","text":"Profile tab","marks":[{"type":"strong"}]},{"type":"text","text":" and taps "},{"type":"text","text":"“Edit Profile”","marks":[{"type":"strong"}]},{"type":"text","text":"."}]}]},{"type":"listItem","content":[{"type":"paragraph","content":[{"type":"text","text":"System loads the "},{"type":"text","text":"Edit Profile","marks":[{"type":"strong"}]},{"type":"text","text":" form with current user values populated."}]}]},{"type":"listItem","content":[{"type":"paragraph","content":[{"type":"text","text":"User modifies one or more fields including name, bio, social links, or phone/email."}]}]},{"type":"listItem","content":[{"type":"paragraph","content":[{"type":"text","text":"For phone or email changes:"}]},{"type":"bulletList","content":[{"type":"listItem","content":[{"type":"paragraph","content":[{"type":"text","text":"System triggers "},{"type":"text","text":"OTP verification flow","marks":[{"type":"strong"}]},{"type":"text","text":" for the new value."}]}]}]}]},{"type":"listItem","content":[{"type":"paragraph","content":[{"type":"text","text":"User optionally updates avatar via file picker "},{"type":"text","text":"or uses camera","marks":[{"type":"strong"}]},{"type":"text","text":" to take a new photo."}]}]},{"type":"listItem","content":[{"type":"paragraph","content":[{"type":"text","text":"User taps "},{"type":"text","text":"Save","marks":[{"type":"strong"}]},{"type":"text","text":"."}]}]},{"type":"listItem","content":[{"type":"paragraph","content":[{"type":"text","text":"System validates inputs, processes any pending OTP flows, and saves changes."}]}]},{"type":"listItem","content":[{"type":"paragraph","content":[{"type":"text","text":"Updated data is reflected in the My Profile view."}]}]},{"type":"listItem","content":[{"type":"paragraph","content":[{"type":"text","text":"Success toast appears: "},{"type":"text","text":"“Profile updated successfully.”","marks":[{"type":"em"}]}]}]}]}, {"type":"rule"}, {"type":"heading","attrs":{"level":3},"content":[{"type":"text","text":"3. Field Definitions Table (Updated)"}]}, {"type":"table","attrs":{"isNumberColumnEnabled":false,"layout":"center","localId":"d8c92c8f-e94c-403d-bc55-9ada5349bd9c"},"content":[{"type":"tableRow","content":[{"type":"tableHeader","attrs":{},"content":[{"type":"paragraph","content":[{"type":"text","text":"Field Name"}]}]},{"type":"tableHeader","attrs":{},"content":[{"type":"paragraph","content":[{"type":"text","text":"Field Type"}]}]},{"type":"tableHeader","attrs":{},"content":[{"type":"paragraph","content":[{"type":"text","text":"Validation Rule"}]}]},{"type":"tableHeader","attrs":{},"content":[{"type":"paragraph","content":[{"type":"text","text":"Mandatory"}]}]},{"type":"tableHeader","attrs":{},"content":[{"type":"paragraph","content":[{"type":"text","text":"Visibility"}]}]}]},{"type":"tableRow","content":[{"type":"tableCell","attrs":{},"content":[{"type":"paragraph","content":[{"type":"text","text":"Full Name"}]}]},{"type":"tableCell","attrs":{},"content":[{"type":"paragraph","content":[{"type":"text","text":"Text Input"}]}]},{"type":"tableCell","attrs":{},"content":[{"type":"paragraph","content":[{"type":"text","text":"Max 50 characters"}]}]},{"type":"tableCell","attrs":{},"content":[{"type":"paragraph","content":[{"type":"text","text":"Yes"}]}]},{"type":"tableCell","attrs":{},"content":[{"type":"paragraph","content":[{"type":"text","text":"Public"}]}]}]},{"type":"tableRow","content":[{"type":"tableCell","attrs":{},"content":[{"type":"paragraph","content":[{"type":"text","text":"Email"}]}]},{"type":"tableCell","attrs":{},"content":[{"type":"paragraph","content":[{"type":"text","text":"Email Input"}]}]},{"type":"tableCell","attrs":{},"content":[{"type":"paragraph","content":[{"type":"text","text":"Valid format, "},{"type":"text","text":"OTP verification if changed","marks":[{"type":"strong"}]}]}]},{"type":"tableCell","attrs":{},"content":[{"type":"paragraph","content":[{"type":"text","text":"Yes"}]}]},{"type":"tableCell","attrs":{},"content":[{"type":"paragraph","content":[{"type":"text","text":"Private (self only)"}]}]}]},{"type":"tableRow","content":[{"type":"tableCell","attrs":{},"content":[{"type":"paragraph","content":[{"type":"text","text":"Phone Number"}]}]},{"type":"tableCell","attrs":{},"content":[{"type":"paragraph","content":[{"type":"text","text":"Phone Input"}]}]},{"type":"tableCell","attrs":{},"content":[{"type":"paragraph","content":[{"type":"text","text":"Valid format, "},{"type":"text","text":"OTP verification if changed","marks":[{"type":"strong"}]}]}]},{"type":"tableCell","attrs":{},"content":[{"type":"paragraph","content":[{"type":"text","text":"Yes"}]}]},{"type":"tableCell","attrs":{},"content":[{"type":"paragraph","content":[{"type":"text","text":"Private (self only)"}]}]}]},{"type":"tableRow","content":[{"type":"tableCell","attrs":{},"content":[{"type":"paragraph","content":[{"type":"text","text":"Position"}]}]},{"type":"tableCell","attrs":{},"content":[{"type":"paragraph","content":[{"type":"text","text":"Text Input"}]}]},{"type":"tableCell","attrs":{},"content":[{"type":"paragraph","content":[{"type":"text","text":"Max 50 characters"}]}]},{"type":"tableCell","attrs":{},"content":[{"type":"paragraph","content":[{"type":"text","text":"Yes"}]}]},{"type":"tableCell","attrs":{},"content":[{"type":"paragraph","content":[{"type":"text","text":"Public"}]}]}]},{"type":"tableRow","content":[{"type":"tableCell","attrs":{},"content":[{"type":"paragraph","content":[{"type":"text","text":"Gender"}]}]},{"type":"tableCell","attrs":{},"content":[{"type":"paragraph","content":[{"type":"text","text":"Enum"}]}]},{"type":"tableCell","attrs":{},"content":[{"type":"paragraph","content":[{"type":"text","text":"Male, Female, Other"}]}]},{"type":"tableCell","attrs":{},"content":[{"type":"paragraph","content":[{"type":"text","text":"Yes"}]}]},{"type":"tableCell","attrs":{},"content":[{"type":"paragraph","content":[{"type":"text","text":"Public"}]}]}]},{"type":"tableRow","content":[{"type":"tableCell","attrs":{},"content":[{"type":"paragraph","content":[{"type":"text","text":"Industry"}]}]},{"type":"tableCell","attrs":{},"content":[{"type":"paragraph","content":[{"type":"text","text":"Text Input"}]}]},{"type":"tableCell","attrs":{},"content":[{"type":"paragraph","content":[{"type":"text","text":"Max 50 characters"}]}]},{"type":"tableCell","attrs":{},"content":[{"type":"paragraph","content":[{"type":"text","text":"No"}]}]},{"type":"tableCell","attrs":{},"content":[{"type":"paragraph","content":[{"type":"text","text":"Public"}]}]}]},{"type":"tableRow","content":[{"type":"tableCell","attrs":{},"content":[{"type":"paragraph","content":[{"type":"text","text":"Date of Birth"}]}]},{"type":"tableCell","attrs":{},"content":[{"type":"paragraph","content":[{"type":"text","text":"Date Picker"}]}]},{"type":"tableCell","attrs":{},"content":[{"type":"paragraph","content":[{"type":"text","text":"Must be valid date"}]}]},{"type":"tableCell","attrs":{},"content":[{"type":"paragraph","content":[{"type":"text","text":"No"}]}]},{"type":"tableCell","attrs":{},"content":[{"type":"paragraph","content":[{"type":"text","text":"Private"}]}]}]},{"type":"tableRow","content":[{"type":"tableCell","attrs":{},"content":[{"type":"paragraph","content":[{"type":"text","text":"Address"}]}]},{"type":"tableCell","attrs":{},"content":[{"type":"paragraph","content":[{"type":"text","text":"Text Area"}]}]},{"type":"tableCell","attrs":{},"content":[{"type":"paragraph","content":[{"type":"text","text":"Max 100 characters"}]}]},{"type":"tableCell","attrs":{},"content":[{"type":"paragraph","content":[{"type":"text","text":"No"}]}]},{"type":"tableCell","attrs":{},"content":[{"type":"paragraph","content":[{"type":"text","text":"Private"}]}]}]},{"type":"tableRow","content":[{"type":"tableCell","attrs":{},"content":[{"type":"paragraph","content":[{"type":"text","text":"Bio"}]}]},{"type":"tableCell","attrs":{},"content":[{"type":"paragraph","content":[{"type":"text","text":"Text Area"}]}]},{"type":"tableCell","attrs":{},"content":[{"type":"paragraph","content":[{"type":"text","text":"Max 500 characters","marks":[{"type":"strong"}]}]}]},{"type":"tableCell","attrs":{},"content":[{"type":"paragraph","content":[{"type":"text","text":"No"}]}]},{"type":"tableCell","attrs":{},"content":[{"type":"paragraph","content":[{"type":"text","text":"Public"}]}]}]},{"type":"tableRow","content":[{"type":"tableCell","attrs":{},"content":[{"type":"paragraph","content":[{"type":"text","text":"Skills & Interests"}]}]},{"type":"tableCell","attrs":{},"content":[{"type":"paragraph","content":[{"type":"text","text":"Tag Selector"}]}]},{"type":"tableCell","attrs":{},"content":[{"type":"paragraph","content":[{"type":"text","text":"Max 15 entries","marks":[{"type":"strong"}]},{"type":"text","text":", predefined or custom"}]}]},{"type":"tableCell","attrs":{},"content":[{"type":"paragraph","content":[{"type":"text","text":"No"}]}]},{"type":"tableCell","attrs":{},"content":[{"type":"paragraph","content":[{"type":"text","text":"Public"}]}]}]},{"type":"tableRow","content":[{"type":"tableCell","attrs":{},"content":[{"type":"paragraph","content":[{"type":"text","text":"Profile Picture"}]}]},{"type":"tableCell","attrs":{},"content":[{"type":"paragraph","content":[{"type":"text","text":"Image Upload"}]}]},{"type":"tableCell","attrs":{},"content":[{"type":"paragraph","content":[{"type":"text","text":"JPG/PNG, 2MB max, cropper enabled, "},{"type":"text","text":"camera option","marks":[{"type":"strong"}]}]}]},{"type":"tableCell","attrs":{},"content":[{"type":"paragraph","content":[{"type":"text","text":"No"}]}]},{"type":"tableCell","attrs":{},"content":[{"type":"paragraph","content":[{"type":"text","text":"Public"}]}]}]},{"type":"tableRow","content":[{"type":"tableCell","attrs":{},"content":[{"type":"paragraph","content":[{"type":"text","text":"LinkedIn URL"}]}]},{"type":"tableCell","attrs":{},"content":[{"type":"paragraph","content":[{"type":"text","text":"URL Input"}]}]},{"type":"tableCell","attrs":{},"content":[{"type":"paragraph","content":[{"type":"text","text":"Must be valid LinkedIn profile URL"}]}]},{"type":"tableCell","attrs":{},"content":[{"type":"paragraph","content":[{"type":"text","text":"No"}]}]},{"type":"tableCell","attrs":{},"content":[{"type":"paragraph","content":[{"type":"text","text":"Public (icon only)"}]}]}]},{"type":"tableRow","content":[{"type":"tableCell","attrs":{},"content":[{"type":"paragraph","content":[{"type":"text","text":"Telegram Link"}]}]},{"type":"tableCell","attrs":{},"content":[{"type":"paragraph","content":[{"type":"text","text":"Username"}]}]},{"type":"tableCell","attrs":{},"content":[{"type":"paragraph","content":[{"type":"text","text":"Must match Telegram handle format"}]}]},{"type":"tableCell","attrs":{},"content":[{"type":"paragraph","content":[{"type":"text","text":"No"}]}]},{"type":"tableCell","attrs":{},"content":[{"type":"paragraph","content":[{"type":"text","text":"Public (icon only)"}]}]}]},{"type":"tableRow","content":[{"type":"tableCell","attrs":{},"content":[{"type":"paragraph","content":[{"type":"text","text":"WhatsApp Link"}]}]},{"type":"tableCell","attrs":{},"content":[{"type":"paragraph","content":[{"type":"text","text":"Phone-to-URL"}]}]},{"type":"tableCell","attrs":{},"content":[{"type":"paragraph","content":[{"type":"text","text":"Must match valid WhatsApp link format"}]}]},{"type":"tableCell","attrs":{},"content":[{"type":"paragraph","content":[{"type":"text","text":"No"}]}]},{"type":"tableCell","attrs":{},"content":[{"type":"paragraph","content":[{"type":"text","text":"Public (icon only)"}]}]}]}]}, {"type":"rule"}, {"type":"heading","attrs":{"level":3},"content":[{"type":"text","text":"4. Data Display Table (Grouped Fields)"}]}, {"type":"table","attrs":{"isNumberColumnEnabled":false,"layout":"center","localId":"ebcd1f22-af5a-4da4-a278-d86e8e21135e"},"content":[{"type":"tableRow","content":[{"type":"tableHeader","attrs":{},"content":[{"type":"paragraph","content":[{"type":"text","text":"Section"}]}]},{"type":"tableHeader","attrs":{},"content":[{"type":"paragraph","content":[{"type":"text","text":"Fields Included"}]}]}]},{"type":"tableRow","content":[{"type":"tableCell","attrs":{},"content":[{"type":"paragraph","content":[{"type":"text","text":"Personal Info"}]}]},{"type":"tableCell","attrs":{},"content":[{"type":"paragraph","content":[{"type":"text","text":"Name, Gender, DOB, Address"}]}]}]},{"type":"tableRow","content":[{"type":"tableCell","attrs":{},"content":[{"type":"paragraph","content":[{"type":"text","text":"Contact Info"}]}]},{"type":"tableCell","attrs":{},"content":[{"type":"paragraph","content":[{"type":"text","text":"Phone (editable + OTP), Email (editable + OTP)"}]}]}]},{"type":"tableRow","content":[{"type":"tableCell","attrs":{},"content":[{"type":"paragraph","content":[{"type":"text","text":"Professional Info"}]}]},{"type":"tableCell","attrs":{},"content":[{"type":"paragraph","content":[{"type":"text","text":"Position, Industry, Skills & Interests, Bio"}]}]}]},{"type":"tableRow","content":[{"type":"tableCell","attrs":{},"content":[{"type":"paragraph","content":[{"type":"text","text":"Profile Media"}]}]},{"type":"tableCell","attrs":{},"content":[{"type":"paragraph","content":[{"type":"text","text":"Avatar with upload & camera option"}]}]}]},{"type":"tableRow","content":[{"type":"tableCell","attrs":{},"content":[{"type":"paragraph","content":[{"type":"text","text":"Social Links"}]}]},{"type":"tableCell","attrs":{},"content":[{"type":"paragraph","content":[{"type":"text","text":"LinkedIn, Telegram, WhatsApp"}]}]}]}]}, {"type":"rule"}, {"type":"heading","attrs":{"level":3},"content":[{"type":"text","text":"5. Acceptance Criteria (AC)"}]}, {"type":"bulletList","content":[{"type":"listItem","content":[{"type":"paragraph","content":[{"type":"text","text":"AC1:","marks":[{"type":"strong"}]},{"type":"text","text":" All profile fields are editable from the Edit Profile screen."}]}]},{"type":"listItem","content":[{"type":"paragraph","content":[{"type":"text","text":"AC2:","marks":[{"type":"strong"}]},{"type":"text","text":" Changing email or phone number triggers OTP verification before save is allowed."}]}]},{"type":"listItem","content":[{"type":"paragraph","content":[{"type":"text","text":"AC3:","marks":[{"type":"strong"}]},{"type":"text","text":" Avatar can be updated using gallery "},{"type":"text","text":"or camera","marks":[{"type":"strong"}]},{"type":"text","text":" (mobile only)."}]}]},{"type":"listItem","content":[{"type":"paragraph","content":[{"type":"text","text":"AC4:","marks":[{"type":"strong"}]},{"type":"text","text":" Bio input accepts up to "},{"type":"text","text":"500 characters","marks":[{"type":"strong"}]},{"type":"text","text":" with live counter."}]}]},{"type":"listItem","content":[{"type":"paragraph","content":[{"type":"text","text":"AC5:","marks":[{"type":"strong"}]},{"type":"text","text":" Skills & Interests accepts up to "},{"type":"text","text":"15 entries","marks":[{"type":"strong"}]},{"type":"text","text":" with tag chip UI."}]}]},{"type":"listItem","content":[{"type":"paragraph","content":[{"type":"text","text":"AC6:","marks":[{"type":"strong"}]},{"type":"text","text":" After successful validation and OTP checks (if applicable), all updates are saved and reflected in the profile."}]}]},{"type":"listItem","content":[{"type":"paragraph","content":[{"type":"text","text":"AC7:","marks":[{"type":"strong"}]},{"type":"text","text":" Toast message appears after successful save:"},{"type":"hardBreak"},{"type":"text","text":"“Profile updated successfully.”","marks":[{"type":"em"}]}]}]},{"type":"listItem","content":[{"type":"paragraph","content":[{"type":"text","text":"AC8:","marks":[{"type":"strong"}]},{"type":"text","text":" System stores a "},{"type":"text","text":"last_updated","marks":[{"type":"code"}]},{"type":"text","text":" timestamp for audit and tracking."}]}]},{"type":"listItem","content":[{"type":"paragraph","content":[{"type":"text","text":"AC9:","marks":[{"type":"strong"}]},{"type":"text","text":" If user attempts to leave screen with unsaved changes, a confirmation modal is shown:"},{"type":"hardBreak"},{"type":"text","text":"“You have unsaved changes. Discard or continue editing?”","marks":[{"type":"em"}]}]}]}]}, {"type":"rule"}, {"type":"heading","attrs":{"level":3},"content":[{"type":"text","text":"6. System Rules"}]}, {"type":"bulletList","content":[{"type":"listItem","content":[{"type":"paragraph","content":[{"type":"text","text":"Phone and Email fields are editable only with OTP flow (using backend auth service)."}]}]},{"type":"listItem","content":[{"type":"paragraph","content":[{"type":"text","text":"All avatar uploads must support "},{"type":"text","text":"camera access","marks":[{"type":"strong"}]},{"type":"text","text":", "},{"type":"text","text":"crop","marks":[{"type":"strong"}]},{"type":"text","text":", and "},{"type":"text","text":"compression","marks":[{"type":"strong"}]},{"type":"text","text":"."}]}]},{"type":"listItem","content":[{"type":"paragraph","content":[{"type":"text","text":"Maximum one profile update every 60 seconds per user (rate limit)."}]}]},{"type":"listItem","content":[{"type":"paragraph","content":[{"type":"text","text":"Social icons displayed only if corresponding field is filled."}]}]}]}, {"type":"rule"}, {"type":"heading","attrs":{"level":3},"content":[{"type":"text","text":"7. UX Optimizations"}]}, {"type":"bulletList","content":[{"type":"listItem","content":[{"type":"paragraph","content":[{"type":"text","text":"Phone & Email fields show subtle "},{"type":"text","text":"“verify”","marks":[{"type":"strong"}]},{"type":"text","text":" tag when edited."}]}]},{"type":"listItem","content":[{"type":"paragraph","content":[{"type":"text","text":"Avatar upload shows “Take Photo” or “Choose from Gallery” prompt."}]}]},{"type":"listItem","content":[{"type":"paragraph","content":[{"type":"text","text":"Invalid links or handles show red border and helper text."}]}]},{"type":"listItem","content":[{"type":"paragraph","content":[{"type":"text","text":"OTP modal reuses existing UI component from Sign In / Forgot Password."}]}]},{"type":"listItem","content":[{"type":"paragraph","content":[{"type":"text","text":"Sticky Save button appears when any changes are detected."}]}]}]}