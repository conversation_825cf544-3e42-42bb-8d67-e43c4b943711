Summary,Issue key,Issue id,Issue Type,Status,Project key,Project name,Project type,Project lead,Project lead id,Project description,Priority,Resolution,Assignee,Assignee Id,Reporter,Reporter Id,Creator,Creator Id,Created,Updated,Last Viewed,Resolved,Due date,Votes,Description,Environment,Watchers,Watchers Id,Original estimate,Remaining Estimate,Time Spent,Work Ratio,Σ Original Estimate,Σ Remaining Estimate,Σ Time Spent,Security Level,Custom field (% Complete),Custom field (Actual end),Custom field (Actual start),Custom field (Affected services),Custom field (App),Custom field (Approvals),Custom field (Category),Custom field (Change reason),Custom field (Change risk),Custom field (Change type),Custom field (Company size),Custom field (Development),Custom field (Email),Custom field (Environment),Custom field (Epic Color),Custom field (Epic Name),Custom field (Epic Status),Custom field (Goals),Custom field (Impact),Custom field (Issue color),Custom field (Locked forms),Custom field (Open forms),Custom field (Operational categorization),Custom field (Pending reason),Custom field (Phone number),Custom field (Platform),Custom field (Platform),Custom field (Product categorization),Custom field (Project overview key),Custom field (Project overview status),Custom field (Rank),Custom field (Request Type),Custom field (Request language),Custom field (Request participants),Custom field (Responders),Satisfaction rating,Custom field (Satisfaction date),Custom field (Sentiment),Custom field (Start date),Custom field (Story Points),Custom field (Story point estimate),Custom field (Submitted forms),Custom field (Target end),Custom field (Target start),Custom field (Team),Custom field (Test Result),Custom field (Test User Picker),Custom field (Time to first response),Custom field (Time to resolution),Custom field (Total forms),Custom field (Urgency),Custom field (Version),Custom field (Vulnerability),Custom field (Website),Custom field (Work category),Custom field ([CHART] Date of First Response),Custom field ([CHART] Time in Status),Parent,Parent key,Parent summary,Status Category,Status Category Changed
[MB - Meeting Scheduling] Delete Meeting,AL-96,38155,Story,Specified,AL,AIOS Link,software,Kai Nguyễn,6214a4102d1a9d0069949c96,,Medium,,,,Duyen Nguyen,712020:59b66c6e-1068-441c-9436-130289e16a26,Duyen Nguyen,712020:59b66c6e-1068-441c-9436-130289e16a26,11/Jun/25 10:05 PM,15/Jun/25 11:01 PM,,,,0,"h3. 1. Use Case Description

As a user who initiated a meeting, I want to delete it so that it no longer appears in either user’s schedule, and the other party is clearly notified.

----

h3. 2. User Workflow (Step-by-Step)

# User opens a meeting they initiated from the *Schedule* view.
# User taps *Delete Meeting*.
# System shows a confirmation modal:
_“Deleting this meeting will cancel it for both parties. Continue?”_
# User confirms deletion.
# System removes the meeting from both users’ schedules.
# System sends a notification to the other party:
_“Your meeting with [User] has been canceled.”_

----

h3. 3. Field Definitions

h4. Interaction Elements

||Element Name||Type||Description||
|Delete Meeting|Button|Opens confirmation modal|
|Confirm Delete|Button|Confirms cancellation|
|Cancel Delete|Button|Dismisses without action|

----

h3. 4. Data Display

||Data Field||Data Type||Description||
|Deletion Notice|Notification|Sent to recipient|
|Success Toast|Toast|“Meeting deleted” confirmation to sender|

----

h3. 5. Acceptance Criteria

* *AC1*: Only the sender can delete the meeting.
* *AC2*: Confirmation modal appears before deletion.
* *AC3*: After deletion, meeting is removed from both schedules.
* *AC4*: Recipient is notified about the cancellation.
* *AC5*: Deleted meetings are removed from the Schedule screen immediately.

----

h3. 6. System Rules

* Deleted meetings are hard-removed from both users' schedules.
* Deletion is logged with user ID and timestamp.
* Notifications are pushed and stored in the recipient’s notification center.

----

h3. 7. UX Optimizations

* Use red button for destructive action.
* Show recipient’s name in confirmation modal.
* Optionally allow deletion reason (future enhancement).
* Scroll to top of Schedule after deletion confirmation.",,Duyen Nguyen,712020:59b66c6e-1068-441c-9436-130289e16a26,,,,,,,,,,,,,,com.atlassian.servicedesk.plugins.approvals.internal.customfield.ApprovalsCFValue@7a4d2caf,,,,,,{},,,,,,,,,,,,,,,MOBILE,,,,0|i02rdb:,,,,,,,,,,,,,,,,,,,,,,,,,,,37636,AL-68,Meeting Scheduling,To Do,11/Jun/25 10:05 PM
[MB - Meeting Scheduling] Invite to Meet,AL-69,37638,Story,Specified,AL,AIOS Link,software,Kai Nguyễn,6214a4102d1a9d0069949c96,,Medium,,,,Duyen Nguyen,712020:59b66c6e-1068-441c-9436-130289e16a26,Duyen Nguyen,712020:59b66c6e-1068-441c-9436-130289e16a26,06/Jun/25 2:25 PM,15/Jun/25 11:00 PM,,,,0,"h3. 1. Use Case Description

As a mobile user, I want to invite any contact to meet by specifying a date, time, and meeting method (in person or virtual), so that I can coordinate meetings easily.

----

h3. 2. User Workflow (Step-by-Step)

h4. A. From Home Dashboard

# User taps the *“Invite to Meet”* CTA on the home dashboard.
# System displays the *full contact list*.
# User selects a contact.
# User proceeds to the *Meeting Setup Screen*:
#* Selects *Meeting Type*: In Person or Virtual
#* Chooses *Date* via date picker
#* Chooses *Time* via time picker
#* If In Person → enters *Address*
#* If Virtual → enters *Meeting Link*
# User taps *Send Invite*.
# System sends the invitation to the recipient and shows a confirmation.

h4. B. From Contact Profile

# User views a contact’s profile.
# User taps *“Invite to Meet”*.
# System opens the *Meeting Setup Screen* as described above.
# User completes and submits the invite.

----

h3. 3. Field Definitions Table

h4. Input Fields

||Field Name||Field Type||Required||Validation Rules||Description||
|Contact|Selector|✅|Must be from user's contact/lead list|Person to invite|
|Meeting Type|Radio Button|✅|“In Person” or “Virtual”|Format of the meeting|
|Date|Date Picker|✅|Must be today or future|Scheduled date|
|Time|Time Picker|✅|Any valid 24-hour format|Scheduled time|
|Address|Text Field|✅ if In Person|5–100 characters|Physical meeting location|
|Meeting Link|URL Input|✅ if Virtual|Must be a valid URL|Link to video meeting|

h4. Interaction Elements

||Element Name||Type||Trigger Condition||Description||
|Invite to Meet CTA|Button|On home dashboard|Starts the invite workflow|
|Contact Profile CTA|Button|On individual profile|Opens the meeting setup screen|
|Send Invite Button|Button|Enabled when form is complete|Sends the invitation|

----

h3. 4. Data Display Table (Card View)

||Display Field||Data Type||Display When Empty||Format Example||Description||
|Contact Name|Text|“Not selected”|“John D.”|Name of the invite recipient|
|Meeting Type|Badge|“TBD”|“In Person” / “Virtual”|Selected meeting format|
|Date & Time|Datetime|“Not Scheduled”|“Jun 14, 15:00”|Meeting date and time|
|Address/Link|Text/URL|“Not Provided”|Location or URL|Based on meeting type|

----

h3. 5. Acceptance Criteria

* *AC1*: User must be able to invite any contact without availability constraints.
* *AC2*: Invite requires meeting type, date, time, and corresponding location/link.
* *AC3*: Tapping “Send Invite” delivers a complete meeting request to the recipient.
* *AC4*: Recipient receives notification and can view invite details.
* *AC5*: System displays a success message after the invite is sent.

----

h3. 6. System Rules

* No availability check is performed; all contacts are always selectable.
* Invite metadata is stored with sender ID, recipient ID, meeting type, date/time, and status=pending.
* Invite is delivered via in-app notification (and optionally email or push).
* Sender cannot send duplicate invites with the same date/time/contact combination.

----

h3. 7. UX Optimizations

* Full contact list shown alphabetically.
* Autofocus on date after contact is selected.
* Show confirmation summary: _“Invite [Contact Name] to meet on [Date, Time] via [Type]?”_
* Toast: _“Invite sent to [Name] successfully.”_
* Meeting type icons help visually distinguish in-person vs virtual in preview.",,Duyen Nguyen,712020:59b66c6e-1068-441c-9436-130289e16a26,,,,,,,,,,,,,,com.atlassian.servicedesk.plugins.approvals.internal.customfield.ApprovalsCFValue@5951fd07,,,,,,{},,,,,,,,,,,,,,,MOBILE,,,,0|i02qxz:,,,,,,,,,,,,,,,,,,,,,,,,,,,37636,AL-68,Meeting Scheduling,To Do,06/Jun/25 2:25 PM
[MB - Meeting Scheduling] Edit Meeting,AL-38,37377,Story,Specified,AL,AIOS Link,software,Kai Nguyễn,6214a4102d1a9d0069949c96,,Medium,,,,Duyen Nguyen,712020:59b66c6e-1068-441c-9436-130289e16a26,Duyen Nguyen,712020:59b66c6e-1068-441c-9436-130289e16a26,04/Jun/25 2:37 PM,15/Jun/25 11:00 PM,12/Jun/25 1:37 PM,,,0,"h3. 1. Use Case Description

As a user, I want to request an update to the date, time, or location of a confirmed meeting so that I can adjust the meeting schedule if necessary. The change should only apply if the other party agrees.

----

h3. 2. User Workflow (Step-by-Step)

# User opens a confirmed meeting from the *Schedule* view.
# User taps *Edit Meeting*.
# System displays the current meeting details in editable form:
#* Meeting Date (Date Picker)
#* Meeting Time (Time Picker)
#* Location or Link (Text input)
# User submits updated info and taps *Send Update Request*.
# System sends a notification to the recipient with:
#* “You have a request to reschedule your meeting with [Sender Name].”
#* Options: *Accept Change* or *Decline Change*
# If recipient *accepts*:
#* Meeting is updated for both users.
#* Confirmation notification is sent to both parties.
# If recipient *declines* or takes no action within 24 hours:
#* Meeting remains unchanged.
#* Sender is notified: “Your update request was declined / expired.”

----

h3. 3. Field Definitions

h4. Input Fields

||Field Name||Type||Required||Description||
|Date|Date Picker|✅|Proposed new meeting date|
|Time|Time Picker|✅|Proposed new meeting time|
|Location/Link|Text Input|✅|Updated address or virtual meeting link|

h4. Interaction Elements

||Element Name||Type||Description||
|Edit Meeting Button|Button|Opens editable form|
|Send Update Request|Button|Submits updated fields for confirmation|
|Accept / Decline Buttons|Button|Displayed in recipient's notification|

----

h3. 4. Data Display

||Data Field||Data Type||Description||
|Pending Update|Badge|Shown on meetings awaiting confirmation|
|Change History|Text|Logs timestamp and fields changed|
|Confirmation Toast|Toast|Shown after user sends or accepts update|

----

h3. 5. Acceptance Criteria

* *AC1*: Only meetings with status “Accepted” can be updated.
* *AC2*: Update applies only after recipient confirms.
* *AC3*: System notifies both users on outcome (accepted, declined, expired).
* *AC4*: Pending requests disable further edits until resolved.

----

h3. 6. System Rules

* Only the original sender may initiate updates.
* Recipients can only accept or decline; they cannot edit.
* Audit log stores all updates and responses.

----

h3. 7. UX Optimizations

* Display status tag: _“Pending Update”_
* Inline preview of proposed changes in recipient notification
* Show countdown until request expires
* Offer “Withdraw Update” button to sender",,Duyen Nguyen,712020:59b66c6e-1068-441c-9436-130289e16a26,,,,,,,,,,,,,,com.atlassian.servicedesk.plugins.approvals.internal.customfield.ApprovalsCFValue@2c297978,,,,,,{},,,,,,,,,,,,,,,MOBILE,,,,0|i02qlq:zr,,,,,,,,,,,,,,,,,,,,,,,,,,,37636,AL-68,Meeting Scheduling,To Do,04/Jun/25 2:37 PM
[MB - Meeting Scheduling] View Schedule,AL-37,37374,Story,Specified,AL,AIOS Link,software,Kai Nguyễn,6214a4102d1a9d0069949c96,,Medium,,,,Duyen Nguyen,712020:59b66c6e-1068-441c-9436-130289e16a26,Duyen Nguyen,712020:59b66c6e-1068-441c-9436-130289e16a26,04/Jun/25 2:16 PM,15/Jun/25 10:59 PM,,,,0,"h3. 1. Use Case Description

As a user, I want to view all my upcoming meetings, events, and booked services in a single schedule view, so that I can manage my time efficiently and keep track of my commitments within the app.

----

h3. 2. User Workflow (Step-by-Step)

* Step 1: User navigates to the *Schedule* section from dashboard shortcut.
* Step 2: System loads a consolidated view of all future appointments including:
** Confirmed *Meetings* (via Invite to Meet)
** Registered *Events*
** Confirmed *Booked Services* (from Marketplace or Gifting)
* Step 3: User scrolls through upcoming items sorted by *date & time*.
* Step 4: Tapping on any item opens a *detail view* (e.g., contact, time, location, notes).
* Step 5: If applicable, user may *cancel* or *reschedule* (based on event/service rules).

----

h3. 3. Field Definitions Table

h4. Data Fields (Schedule Entries)

||Field Name||Field Type||Required||Description||
|Schedule Type|Enum|✅|One of: Meeting, Event, Booked Service|
|Date & Time|Datetime|✅|Scheduled time of the activity|
|Title|Text|✅|Activity title or subject|
|Related Contact|User Link|✅ if Meeting|Who the meeting or service is with|
|Location / Link|Text/URL|✅|Address or virtual meeting link|
|Status|Badge|✅|Confirmed, Cancelled, Rescheduled, Completed|

----

h3. 4. Data Display Table (Card View)

||Data Name||Data Type||Display When Empty||Format||Description||
|Date Header|Text|“No events yet”|“Today”, “Tomorrow”|Group label by day|
|Schedule Card|Card|Hidden|Structured card|Displays icon, title, time, location|
|Type Icon|Icon|Hidden|Event/Meeting/Service|Visual cue for entry type|
|Status Tag|Badge|“Pending”|e.g., Confirmed|Status color-coded|

----

h3. 5. Acceptance Criteria

* *AC1*: Schedule screen must display all confirmed future meetings, events, and services.
* *AC2*: Entries are grouped and sorted by date in ascending order.
* *AC3*: Each card must show time, type icon, title, and location/link.
* *AC4*: Tapping a card opens detail view with full metadata.
* *AC5*: System must exclude cancelled or expired items by default (filter optional).
* *AC6*: Supports scrollable list with lazy loading for long schedules.

----

h3. 6. System Rules

* Schedule data is synced in real time with event, meeting, and service modules.
* Past entries are not shown unless user activates a “History” toggle.
* Cancelled items must be retained in backend but excluded from default view.
* All times are normalized to user’s device timezone.

----

h3. 7. UX Optimizations

* Sticky day headers (e.g., “Today”, “This Week”) while scrolling.
* Use icons or colored borders to differentiate between Meeting, Event, and Service.
* Optionally allow “Add to Calendar” or export (ICS) for Pro/Premium users.
* Empty state message: _“No scheduled activities. Start by inviting or booking!”_
* Quick filters at the top: [All] [Meetings] [Events] [Services]

----",,Duyen Nguyen,712020:59b66c6e-1068-441c-9436-130289e16a26,,,,,,,,,,,,,,com.atlassian.servicedesk.plugins.approvals.internal.customfield.ApprovalsCFValue@3d3246af,,,,,,{},,,,,,,,,,,,,,,MOBILE,,,,0|i02qlq:zi,,,,,,,,,,,,,,,,,,,,,,,,,,,37636,AL-68,Meeting Scheduling,To Do,04/Jun/25 2:16 PM
[MB - Meeting Scheduling] Accept/Reject Invite to Meet,AL-33,37362,Story,Specified,AL,AIOS Link,software,Kai Nguyễn,6214a4102d1a9d0069949c96,,Medium,,,,Duyen Nguyen,712020:59b66c6e-1068-441c-9436-130289e16a26,Duyen Nguyen,712020:59b66c6e-1068-441c-9436-130289e16a26,04/Jun/25 2:12 PM,15/Jun/25 10:58 PM,,,,0,"h3. 1. Use Case Description

As a user who receives a meeting invitation, I want to be able to accept or reject the invite, so that I can control my availability and confirm or decline meetings while notifying the inviter accordingly.

----

h3. 2. User Workflow (Step-by-Step)

# User receives an *Invite to Meet* notification or sees the invite in the inbox/activity feed.
# User taps the invitation to view the meeting details (date, time, contact, meeting type).
# User chooses either:
#* *Accept Invite* → Meeting is added to user’s schedule, and a confirmation is sent to the inviter.
#* *Reject Invite* → A rejection message is sent to the inviter; no meeting is created.
# System displays confirmation toast and returns to previous screen.

----

h3. 3. Field Definitions Table

h4. Interaction Elements

||Element Name||Type||Trigger Condition||Description||
|Accept Invite|Button|Always visible|Confirms the meeting and adds to schedule|
|Reject Invite|Button|Always visible|Declines the invite and notifies inviter|
|Invite Details|Card View|On invitation open|Displays time, location/link, sender, type|

----

h3. 4. Data Display Table (Card View)

||Data Name||Data Type||Display When Empty||Format||Description||
|Meeting Type|Text|“TBD”|“In Person”/“Virtual”|Displayed on invite detail screen|
|Meeting Location|Text/Link|“Not Provided”|Address or URL|Based on invite type|
|Sender Name|Text|“Unknown”|Plaintext|Who sent the invite|
|Date & Time|Datetime|“Pending”|Relative format|Scheduled time of meeting|

----

h3. 5. Acceptance Criteria

* *AC1*: User must be able to accept or reject an invitation from both push notification and in-app interface.
* *AC2*: Accepting an invite automatically creates a calendar/schedule entry for the recipient.
* *AC3*: Inviter receives a confirmation notification upon acceptance.
* *AC4*: Rejecting an invite sends a rejection notification to the inviter.
* *AC5*: No schedule is created if invite is rejected.
* *AC6*: User cannot accept an expired or revoked invite (system should handle gracefully).

----

h3. 6. System Rules

* Schedule entries are bi-directional: both users see the confirmed meeting.
* Notifications must indicate invite status (“Accepted”, “Rejected”) and show sender/recipient.
* Once an invite is responded to, response buttons are disabled or replaced with status tag.
* All responses are logged in the system for audit trail.

----

h3. 7. UX Optimizations

* Use icons for Accept (✓) and Reject (✕) with clear color cues (green/red).
* Confirmation toast:
** On accept: _“You’ve accepted the invite. Meeting added to your schedule.”_
** On reject: _“You’ve declined the invite. Notification sent to [Sender Name].”_
* Inline feedback: “Awaiting response” badge replaced with “Accepted” or “Rejected”.
* Allow tapping sender’s name to view their profile before deciding.
* Provide optional comments on rejection (future enhancement).",,Duyen Nguyen,712020:59b66c6e-1068-441c-9436-130289e16a26,,,,,,,,,,,,,,com.atlassian.servicedesk.plugins.approvals.internal.customfield.ApprovalsCFValue@3260420f,,,,,,{},,,,,,,,,,,,,,,MOBILE,,,,0|i02qlq:v,,,,,,,,,,,,,,,,,,,,,,,,,,,37636,AL-68,Meeting Scheduling,To Do,04/Jun/25 2:12 PM
[MB - Meeting Scheduling] Meeting Details,AL-32,37359,Story,Specified,AL,AIOS Link,software,Kai Nguyễn,6214a4102d1a9d0069949c96,,Medium,,,,Duyen Nguyen,712020:59b66c6e-1068-441c-9436-130289e16a26,Duyen Nguyen,712020:59b66c6e-1068-441c-9436-130289e16a26,04/Jun/25 2:10 PM,15/Jun/25 10:58 PM,,,,0,"h3. 1. Use Case Description

As a mobile user, I want to view all relevant details of a scheduled meeting so that I can confirm the time, location, and participants, and take further actions like updating or canceling the meeting if I am the inviter.

----

h3. 2. User Workflow (Step-by-Step)

# User opens the *Schedule* screen.
# User taps on a scheduled meeting.
# System navigates to the *Meeting Detail* screen displaying:
#* Meeting Title (e.g., “Meeting with John D.”)
#* Meeting Type: In Person / Virtual
#* Date & Time
#* Location (or virtual meeting link)
#* Status: Scheduled / Pending Change / Cancelled
#* Inviter and Invitee names
#* Action buttons (based on user role and meeting state)
# If user is the *inviter*:
#* Can tap *Edit Meeting* (if accepted and not pending change)
#* Can tap *Delete Meeting*
# If user is the *recipient* and a change request is pending:
#* Can tap *Accept Change* or *Decline Change*
# User taps *Back* to return to the Schedule screen.

----

h3. 3. Field Definitions

||Field Name||Data Type||Description||
|Meeting Type|Badge|“In Person” or “Virtual”|
|Meeting Date|Date|Scheduled meeting date|
|Meeting Time|Time|Scheduled time|
|Location / Link|Text / URL|Address or virtual meeting link|
|Status|Badge|Scheduled / Pending Change / Cancelled|
|Participants|Text|Names of inviter and invitee|

----

h3. 4. Interaction Elements

||Element Name||Type||Visible When||Description||
|Edit Meeting|Button|If user is inviter & meeting is confirmed|Opens update flow|
|Delete Meeting|Button|If user is inviter|Opens delete confirmation modal|
|Accept Change|Button|If user is invitee & update pending|Accepts the proposed changes|
|Decline Change|Button|If user is invitee & update pending|Rejects the proposed changes|
|Back|Button|Always visible|Returns to the Schedule screen|

----

h3. 5. Acceptance Criteria

* *AC1*: Meeting Detail must display all fields: type, date, time, location/link, participants, and status.
* *AC2*: Edit and Delete buttons appear only for the user who created the invite.
* *AC3*: Accept/Decline Change appears only when an update is pending and user is the recipient.
* *AC4*: Back button navigates cleanly back to previous screen.
* *AC5*: Virtual meeting links must be tappable and open in browser or app.

----

h3. 6. System Rules

* Data is loaded based on meeting ID and real-time status.
* Meeting status reflects latest update request or cancellation.
* If the meeting is expired or cancelled, all actions are disabled.
* Any changes made from this screen must sync with the Schedule module.

----

h3. 7. UX Optimizations

* Use calendar and clock icons next to date/time.
* Format date/time in local user timezone.
* Show profile photos next to participant names.
* Highlight pending changes in yellow badge.
* Add copy/share icon next to virtual link. ",,Duyen Nguyen,712020:59b66c6e-1068-441c-9436-130289e16a26,,,,,,,,,,,,,,com.atlassian.servicedesk.plugins.approvals.internal.customfield.ApprovalsCFValue@55e51f82,,,,,,{},,,,,,,,,,,,,,,MOBILE,,,,0|i02qlq:r,,,,,,,,,,,,,,,,,,,,,,,,,,,37636,AL-68,Meeting Scheduling,To Do,04/Jun/25 2:10 PM
