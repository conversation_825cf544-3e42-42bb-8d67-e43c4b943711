Summary,Issue key,Issue id,Issue Type,Status,Project key,Project name,Project type,Project lead,Project lead id,Project description,Priority,Resolution,Assignee,Assignee Id,Reporter,Reporter Id,Creator,Creator Id,Created,Updated,Last Viewed,Resolved,Due date,Votes,Description,Environment,Watchers,Watchers Id,Original estimate,Remaining Estimate,Time Spent,Work Ratio,Σ Original Estimate,Σ Remaining Estimate,Σ Time Spent,Security Level,Custom field (% Complete),Custom field (Actual end),Custom field (Actual start),Custom field (Affected services),Custom field (App),Custom field (Approvals),Custom field (Category),Custom field (Change reason),Custom field (Change risk),Custom field (Change type),Custom field (Company size),Custom field (Development),Custom field (Email),Custom field (Environment),Custom field (Epic Color),Custom field (Epic Name),Custom field (Epic Status),Custom field (Goals),Custom field (Impact),Custom field (Issue color),Custom field (Locked forms),Custom field (Open forms),Custom field (Operational categorization),Custom field (Pending reason),Custom field (Phone number),Custom field (Platform),Custom field (Platform),Custom field (Product categorization),Custom field (Project overview key),Custom field (Project overview status),Custom field (Rank),Custom field (Request Type),Custom field (Request language),Custom field (Request participants),Custom field (Responders),Satisfaction rating,Custom field (Satisfaction date),Custom field (Sentiment),Custom field (Start date),Custom field (Story Points),Custom field (Story point estimate),Custom field (Submitted forms),Custom field (Target end),Custom field (Target start),Custom field (Team),Custom field (Test Result),Custom field (Test User Picker),Custom field (Time to first response),Custom field (Time to resolution),Custom field (Total forms),Custom field (Urgency),Custom field (Version),Custom field (Vulnerability),Custom field (Website),Custom field (Work category),Custom field ([CHART] Date of First Response),Custom field ([CHART] Time in Status),Parent,Parent key,Parent summary,Status Category,Status Category Changed
[MB - Inventory] Edit Product,AL-95,38105,Story,Specified,AL,AIOS Link,software,Kai Nguyễn,6214a4102d1a9d0069949c96,,Medium,,,,Duyen Nguyen,712020:59b66c6e-1068-441c-9436-130289e16a26,Duyen Nguyen,712020:59b66c6e-1068-441c-9436-130289e16a26,11/Jun/25 1:25 PM,11/Jun/25 3:52 PM,,,,0,"h3. 1. Use Case Description

As a user on the mobile platform, I want to update the details of a product in my inventory so that I can keep inventory data accurate and up to date.

----

h3. 2. User Workflow

# User navigates to *Inventory Product Details*.
# User taps *Update Product*.
# System displays an editable form pre-filled with current product details:
#* Product Name
#* Category
#* Cost (USD)
#* Quantity
#* Description (optional)
#* Photo (optional)
# User updates one or more fields.
# User taps *Save Changes*.
# System validates inputs, updates the record, and returns to the Product Details screen with confirmation.

----

h3. 3. Field Definitions

h4. Input Fields

||Field Name||Field Type||Validation Rule||Mandatory||Description||
|Product Name|Text|1–100 characters|Yes|Name of the product|
|Category|Dropdown|Must select existing category|Yes|Product classification|
|Cost (USD)|Number|Must be > 0|Yes|Product price in *USD*|
|Quantity|Integer|Must be ≥ 0|Yes|Number of units available|
|Description|Text Area|Up to 500 characters|No|Product details|
|Photo|Image Upload|JPG/PNG ≤ 5MB|No|Optional image for product|

h4. Interaction Elements

||Element Name||Type||Trigger Condition||Description||
|Save Changes Button|Button|Enabled when form is valid|Saves changes to the product|
|Cancel Button|Button|Always visible|Returns to Inventory Product Details|

----

h3. 4. Data Display (Summary)

||Data Field||Data Type||Display When Empty||Format||Description||
|Cost (USD)|Amount|“--”|$X.XX|Cost in USD|
|Quantity|Integer|“--”|Numeric|Number of available units|
|Success Message|Toast|Hidden|Text|“Product updated successfully”|
|Error Message|Text|Hidden|Text|Validation or API failure|

----

h3. 5. Acceptance Criteria

* *AC1*: Form includes editable fields for Product Name, Category, Cost (USD), Quantity, optional Description, and Photo.
* *AC2*: Save Changes button is disabled until all required fields are valid.
* *AC3*: Cost must be a positive number in USD; Quantity must be a non-negative integer.
* *AC4*: On success, product updates are reflected immediately in the Product Details screen.
* *AC5*: Cancel returns user without applying any changes.

----

h3. 6. System Rules

* Product Name must remain unique within the user's inventory.
* Cost is stored in USD with up to two decimal places.
* Quantity updates are synced to backend and used for stock-level logic.
* Updated timestamp is recorded automatically.
* Photo replacement is optional and resized before upload.

----

h3. 7. UX Optimizations

* Modified fields highlighted before submission.
* Real-time validation on Cost and Quantity.
* Currency label “USD” shown inline next to price field.
* Retain form values if validation fails.
* Spinner displayed during save operation.",,Duyen Nguyen,712020:59b66c6e-1068-441c-9436-130289e16a26,,,,,,,,,,,,,,com.atlassian.servicedesk.plugins.approvals.internal.customfield.ApprovalsCFValue@5a0214c6,,,,,,{},,,,,,,,,,,,,,,MOBILE,,,,0|i02rav:,,,,,,,,,,,,,,,,,,,,,,,,,,,38088,AL-89,Inventory,To Do,11/Jun/25 1:25 PM
[MB - Inventory] Publish Product to Marketplace,AL-94,38102,Story,Specified,AL,AIOS Link,software,Kai Nguyễn,6214a4102d1a9d0069949c96,,Medium,,,,Duyen Nguyen,712020:59b66c6e-1068-441c-9436-130289e16a26,Duyen Nguyen,712020:59b66c6e-1068-441c-9436-130289e16a26,11/Jun/25 1:24 PM,11/Jun/25 3:49 PM,,,,0,"h3. 1. Use Case Description

As a user on the mobile platform, I want to list a product from my inventory on the marketplace so that other users can purchase it.

h3. 2. User Workflow

* Step 1: From *Inventory Product Details*, user taps *Put to Marketplace* for a product not currently listed.
* Step 2: System displays a form requesting listing information.
* Step 3: User enters Listing Price and optionally enables Tax. If Tax is enabled, a Tax Percentage field appears.
* Step 4: User taps *Save*. System validates inputs and creates the marketplace listing.
* Step 5: Product details update to show marketplace information including listing price and listing date.

h3. 3. Field Definitions

h4. Input Fields

||Field Name||Field Type||Validation Rule||Mandatory||Description||
|Listing Price|Amount|> 0|Yes|Selling price in USD|
|Enable Tax|Toggle|On or Off|Yes|Determines if tax applies|
|Tax Percentage|Number|0–100, shown only when Enable Tax = On|Conditional|Percentage tax applied|

h4. Interaction Elements

||Element Name||Type||Trigger Condition||Description||
|Put to Marketplace Button|Button|Visible when product not listed|Opens listing form|
|Save Button|Button|Enabled when form valid|Creates marketplace listing|
|Discard Button|Button|Always visible|Cancels and closes form|

h3. 4. Data Display (Summary)

||Data Name||Data Type||Display When Empty||Format||Description||
|Success Message|Toast|Hidden|Plaintext|Shown when listing created|
|Error Message|Text|Hidden|Plaintext|Displays validation errors|

h3. 5. Acceptance Criteria

* AC1: Put to Marketplace button appears only for products not yet listed.
* AC2: Listing form requires Listing Price and optionally Tax Percentage when tax is enabled.
* AC3: On successful save, product becomes visible in the Marketplace Product List and Inventory status updates to {{Marketplace}}.
* AC4: Discard button closes the form without saving.
* AC5: System records the listing date automatically at creation.

h3. 6. System Rules

* Listing Price must be a positive value in USD.
* Tax Percentage must be a whole number between 0 and 100 when enabled.
* Listing date is stored in UTC and displayed according to user timezone.
* Audit log captures user ID and timestamp for the listing action.

h3. 7. UX Optimizations

* Autofocus on the Listing Price field when the form opens.
* Display a live preview of total price including tax while editing.
* Show spinner on Save while the request processes.",,Duyen Nguyen,712020:59b66c6e-1068-441c-9436-130289e16a26,,,,,,,,,,,,,,com.atlassian.servicedesk.plugins.approvals.internal.customfield.ApprovalsCFValue@4a4451d,,,,,,{},,,,,,,,,,,,,,,MOBILE,,,,0|i02ran:,,,,,,,,,,,,,,,,,,,,,,,,,,,38088,AL-89,Inventory,To Do,11/Jun/25 1:24 PM
[MB - Inventory] Product List,AL-93,38099,Story,Specified,AL,AIOS Link,software,Kai Nguyễn,6214a4102d1a9d0069949c96,,Medium,,,,Duyen Nguyen,712020:59b66c6e-1068-441c-9436-130289e16a26,Duyen Nguyen,712020:59b66c6e-1068-441c-9436-130289e16a26,11/Jun/25 1:23 PM,11/Jun/25 3:45 PM,,,,0,"h3. 1. Use Case Description

As a user on the mobile platform, I want to view a list of my inventory products so that I can manage inventory and monitor stock levels effectively.

----

h3. 2. User Workflow

# User taps *Inventory* from the main menu.
# System loads the *Inventory Product List* view.
# Each product row displays:
#* Thumbnail
#* Product Name
#* Category
#* Cost (USD)
#* Quantity
#* Status
# User may:
#* Search or filter the list
#* Tap a product row to view details
#* Pull to refresh the data

----

h3. 3. Search and Filter Rules

h4. Search

* Case-insensitive partial match on *Product Name* and *Category*

h4. Filter

* *Category* (multi-select)
* *Status* ({{Draft}}, {{Active}}, {{Marketplace}})
* *Price Range* (min/max in *USD*)

----

h3. 4. Field Definitions

h4. Display Fields

||Field Name||Data Type||Display When Empty||Format||Description||
|Thumbnail|Image|Placeholder image|1:1 Ratio|Product photo|
|Product Name|Text|“--”|Plaintext|Inventory product title|
|Category|Text|“--”|Plaintext|Product category label|
|Cost (USD)|Amount|“--”|USD|Product cost in *US Dollars*|
|Quantity|Integer|“--”|Numeric|Number of units in inventory|
|Status|Badge|“--”|Draft/Marketplace|Inventory status|

h4. Interaction Elements

||Element Name||Type||Trigger Condition||Description||
|Search Box|Input|Always visible|Filter by Product Name or Category|
|Filter Button|Button|Always visible|Opens filter modal|
|Product Row|Tap|Always active|Opens Product Details view|
|Pull to Refresh|Gesture|Top of list|Reloads product list|

----

h3. 5. Acceptance Criteria

* *AC1*: Product rows must display Cost in USD and Quantity per item.
* *AC2*: Search and filters continue to function with quantity and cost.
* *AC3*: Pull to refresh fetches the most current data.
* *AC4*: Cost and quantity columns are formatted cleanly and consistently.
* *AC5*: Product row opens the correct Product Details screen.

----

h3. 6. Search and Filter Rules

h4. A. Search Rules

||Rule Type||Logic Description||
|Keyword Match|Case-insensitive partial match on *Product Name* or *Category*|

h4. B. Filter Rules

||Filter Name||Type||Values / Options||Behavior||
|*Category*|Multi-select|From system-defined categories|Filters results by selected categories|
|*Status*|Dropdown|{{Draft}}, {{Marketplace}}|Filters results by publish status|
|*Price Range*|Min/Max fields|Numeric input in *USD*|Filters results by product cost|

h3. 
7. System Rules

* Cost is stored and displayed in *USD* (rounded to 2 decimals).
* Quantity must be a non-negative integer pulled from product record.
* List is sorted by updated date (descending).
* Filters persist until cleared by the user.

----

h3. 8. UX Optimizations

* Quantity column uses numeric right-aligned layout.
* Cost column prefixed with “$” and shown with 2 decimal points.
* Sticky search bar at the top during scroll.
* Skeleton cards shown during loading.
* Empty state message: _“No products found. Add one to get started.”_",,Duyen Nguyen,712020:59b66c6e-1068-441c-9436-130289e16a26,,,,,,,,,,,,,,com.atlassian.servicedesk.plugins.approvals.internal.customfield.ApprovalsCFValue@18d9278b,,,,,,{},,,,,,,,,,,,,,,MOBILE,,,,0|i02raf:,,,,,,,,,,,,,,,,,,,,,,,,,,,38088,AL-89,Inventory,To Do,11/Jun/25 1:24 PM
[MB - Inventory] Product Details,AL-92,38096,Story,Specified,AL,AIOS Link,software,Kai Nguyễn,6214a4102d1a9d0069949c96,,Medium,,,,Duyen Nguyen,712020:59b66c6e-1068-441c-9436-130289e16a26,Duyen Nguyen,712020:59b66c6e-1068-441c-9436-130289e16a26,11/Jun/25 1:23 PM,11/Jun/25 3:34 PM,,,,0,"h3. 1. Use Case Description

As a user on the mobile platform, I want to view complete information about a product in my inventory—including quantity—so that I can verify inventory levels and take actions like updating, deleting, or listing the product.

----

h3. 2. User Workflow

# User selects a product from the *Inventory Product List*.
# System navigates to the *Inventory Product Details* screen.
# Screen displays all product data fields:
#* Product Name
#* Category
#* Cost (in USD)
#* Quantity
#* Description
#* Status
# User may:
#* Tap *Update Product* to modify data
#* Tap *Put to Marketplace* (if eligible)
#* Tap *Delete Product*
#* Tap *Back* to return to list view

----

h3. 3. Field Definitions

h4. Display Fields

||Field Name||Data Type||Display When Empty||Format||Description||
|Product Name|Text|“--”|Plaintext|Product title|
|Category|Text|“--”|Plaintext|Category label|
|Cost (USD)|Amount|“--”|USD|Product price in USD|
|*Quantity*|Number|“--”|Integer|Available stock quantity|
|Description|Text|“--”|Plaintext|Additional product info|
|Status|Badge|“--”|Draft/Active/Marketplace|Current inventory state|

h4. Interaction Elements

||Element Name||Type||Trigger Condition||Description||
|Update Product Button|Button|Always visible|Navigates to Update Product form|
|Put to Marketplace Button|Button|Visible when status = Active|Opens listing form|
|Delete Product Button|Button|Always visible|Initiates deletion confirmation|
|Back Button|Button|Always visible|Returns to Inventory Product List|

----

h3. 4. Data Display (Card View)

||Data Field||Data Type||Format||Description||
|Quantity|Integer|e.g., “15”|Number of units in inventory|

----

h3. 5. Acceptance Criteria

* *AC1*: Quantity must be displayed alongside other product fields.
* *AC2*: If quantity is not available (corrupt or null), show ""--"".
* *AC3*: Quantity is fetched and displayed in read-only format.
* *AC4*: All update or delete actions continue to function as before.

----

h3. 6. System Rules

* Quantity is stored per product and retrieved on detail view load.
* Quantity value is updated through the Update Product workflow.
* Quantity must be a non-negative integer.

----

h3. 7. UX Optimizations

* Quantity field shown in “General Information” section.
* Field is styled consistently with Cost and Category.
* Skeleton loader shown until quantity is loaded.",,Duyen Nguyen,712020:59b66c6e-1068-441c-9436-130289e16a26,,,,,,,,,,,,,,com.atlassian.servicedesk.plugins.approvals.internal.customfield.ApprovalsCFValue@162a715c,,,,,,{},,,,,,,,,,,,,,,MOBILE,,,,0|i02ra7:,,,,,,,,,,,,,,,,,,,,,,,,,,,38088,AL-89,Inventory,To Do,11/Jun/25 1:23 PM
[MB - Inventory] Delete Product,AL-91,38093,Story,Specified,AL,AIOS Link,software,Kai Nguyễn,6214a4102d1a9d0069949c96,,Medium,,,,Duyen Nguyen,712020:59b66c6e-1068-441c-9436-130289e16a26,Duyen Nguyen,712020:59b66c6e-1068-441c-9436-130289e16a26,11/Jun/25 1:23 PM,11/Jun/25 2:20 PM,,,,0,"h3. 1. Use Case Description

As a user on the mobile platform, I want to remove a product from my inventory so that it no longer appears in my list, while ensuring that existing orders referencing it are preserved.

h3. 2. User Workflow

* Step 1: From *Inventory Product Details*, user taps *Delete Product*.
* Step 2: System displays a confirmation modal explaining that deletion is only allowed if the product has no active marketplace listings or unfulfilled orders.
* Step 3: User confirms deletion.
* Step 4: System deletes the product record and returns to the Inventory Product List with a success message.

h3. 3. Field Definitions

h4. Interaction Elements

||Element Name||Type||Trigger Condition||Description||
|Delete Product Button|Button|Always visible on details screen|Opens confirmation modal|
|Confirmation Modal|Dialog|Shown after Delete pressed|Explains data constraint and asks for confirmation|
|Confirm Delete|Button|Enabled within modal|Confirms deletion|
|Cancel Delete|Button|Enabled within modal|Dismisses modal without changes|

h3. 4. Data Display (Summary)

||Data Name||Data Type||Display When Empty||Format||Description||
|Success Message|Toast|Hidden|Plaintext|Displayed when product is deleted|
|Error Message|Text|Hidden|Plaintext|Shown if data constraint prevents deletion|

h3. 5. Acceptance Criteria

* AC1: Delete Product button is available from Inventory Product Details.
* AC2: Confirmation modal clearly states that products with active marketplace listings or unfulfilled orders cannot be deleted.
* AC3: If constraints are met, product record is removed and success message appears.
* AC4: If deletion is blocked, error message explains why.
* AC5: After deletion, user returns to the Inventory Product List with the item removed.

h3. 6. System Rules

* Backend checks for marketplace listings and unfulfilled orders before deletion.
* Deleted products are permanently removed from the user’s inventory but remain in historical order records.
* Audit log records user ID and timestamp of deletion.

h3. 7. UX Optimizations

* Prefill modal with product name for clarity.
* Display spinner inside modal while processing deletion.
* Return focus to the previous list position after successful deletion.",,Duyen Nguyen,712020:59b66c6e-1068-441c-9436-130289e16a26,,,,,,,,,,,,,,com.atlassian.servicedesk.plugins.approvals.internal.customfield.ApprovalsCFValue@5c44f37b,,,,,,{},,,,,,,,,,,,,,,MOBILE,,,,0|i02r9z:,,,,,,,,,,,,,,,,,,,,,,,,,,,38088,AL-89,Inventory,To Do,11/Jun/25 1:23 PM
[MB-Inventory] Create Product,AL-90,38090,Story,Specified,AL,AIOS Link,software,Kai Nguyễn,6214a4102d1a9d0069949c96,,Medium,,,,Duyen Nguyen,712020:59b66c6e-1068-441c-9436-130289e16a26,Duyen Nguyen,712020:59b66c6e-1068-441c-9436-130289e16a26,11/Jun/25 1:22 PM,11/Jun/25 2:06 PM,11/Jun/25 5:29 PM,,,0,"h3. 1. Use Case Description

As a user on the mobile platform, I want to add a new product to my inventory by entering its details, including quantity and pricing in USD, so that I can manage inventory items and prepare them for potential marketplace listings.

----

h3. 2. User Workflow

# User opens *Inventory* and taps *Add Product*.
# System displays a form with fields for Product Name, Category, Cost (in USD), Quantity, optional Description, and optional Photo.
# User fills in the form.
# User taps *Create Product*. System validates all mandatory fields.
# On success, system adds the product to the Inventory Product List and shows a confirmation.

----

h3. 3. Field Definitions

h4. Input Fields

||Field Name||Field Type||Validation Rule||Mandatory||Description||
|Product Name|Text|1–100 characters|Yes|Name of the product|
|Category|Dropdown|Must select existing category|Yes|Product category|
|Cost (USD)|Number|Positive value|Yes|Base cost in *USD*|
|Quantity|Integer|Must be ≥ 0|Yes|Inventory quantity available|
|Description|Text Area|Up to 500 characters|No|Optional product notes|
|Photo|Image Upload|JPG/PNG ≤ 5MB|No|Optional product image|

h4. Interaction Elements

||Element Name||Type||Trigger Condition||Description||
|Add Product Button|Button|Always visible|Opens the create product form|
|Create Product Button|Button|Enabled when form is valid|Submits and saves the new product|
|Cancel Button|Button|Always visible|Returns to Inventory Product List|

----

h3. 4. Data Display (Summary)

||Data Name||Data Type||Display When Empty||Format||Description||
|Success Message|Toast|Hidden|Text|“Product created successfully”|
|Error Message|Text|Hidden|Text|Form validation or API error message|

----

h3. 5. Acceptance Criteria

* *AC1*: Form must include Product Name, Category, Cost (in USD), and Quantity.
* *AC2*: Create Product button is disabled until all mandatory fields are valid.
* *AC3*: On success, product appears in Inventory Product List.
* *AC4*: Cancel button returns user to list without saving.
* *AC5*: Quantity must be a non-negative whole number.

----

h3. 6. System Rules

* Product Name must be unique per user inventory.
* Cost is stored in USD with two decimal places.
* Quantity is stored as a positive integer.
* Photo is resized before upload to optimize storage.
* Created timestamp is automatically generated.

----

h3. 7. UX Optimizations

* Autofocus on Product Name field when form loads.
* Live validation for cost and quantity fields.
* Retain input values if form validation fails.
* Show unit label “USD” next to cost field.
* Spinner displayed during save operation.",,Duyen Nguyen,712020:59b66c6e-1068-441c-9436-130289e16a26,,,,,,,,,,,,,,com.atlassian.servicedesk.plugins.approvals.internal.customfield.ApprovalsCFValue@7aeec943,,,,,,{},,,,,,,,,,,,,,,MOBILE,,,,0|i02r9r:,,,,,,,,,,,,,,,,,,,,,,,,,,,38088,AL-89,Inventory,To Do,11/Jun/25 1:22 PM
