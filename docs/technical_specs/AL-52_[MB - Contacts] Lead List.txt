{"type":"heading","attrs":{"level":3},"content":[{"type":"text","text":"1. Use Case Description"}]}, {"type":"paragraph","content":[{"type":"text","text":"As a user, I want to view and manage my lead contacts separately from my general contact list"},{"type":"hardBreak"},{"type":"text","text":"so that I can track referral, follow-up, and engagement actions with higher intent contacts."}]}, {"type":"rule"}, {"type":"heading","attrs":{"level":3},"content":[{"type":"text","text":"2. User Workflow (Step-by-Step)"}]}, {"type":"orderedList","attrs":{"order":1},"content":[{"type":"listItem","content":[{"type":"paragraph","content":[{"type":"text","text":"User navigates to the "},{"type":"text","text":"Contacts","marks":[{"type":"strong"}]},{"type":"text","text":" tab from the dashboard."}]}]},{"type":"listItem","content":[{"type":"paragraph","content":[{"type":"text","text":"User taps the "},{"type":"text","text":"“Lead”","marks":[{"type":"strong"}]},{"type":"text","text":" section/tab to view all contacts previously marked as leads."}]}]},{"type":"listItem","content":[{"type":"paragraph","content":[{"type":"text","text":"On the Lead List screen, user can:"}]},{"type":"bulletList","content":[{"type":"listItem","content":[{"type":"paragraph","content":[{"type":"text","text":"Scroll and browse lead cards."}]}]},{"type":"listItem","content":[{"type":"paragraph","content":[{"type":"text","text":"Tap the "},{"type":"text","text":"“+” (Convert to Lead)","marks":[{"type":"strong"}]},{"type":"text","text":" button."}]}]}]}]},{"type":"listItem","content":[{"type":"paragraph","content":[{"type":"text","text":"System displays a modal where user can select one or more contacts to convert into leads."}]}]},{"type":"listItem","content":[{"type":"paragraph","content":[{"type":"text","text":"User confirms the selection."}]}]},{"type":"listItem","content":[{"type":"paragraph","content":[{"type":"text","text":"Selected contacts are added to the lead list and displayed as lead cards."}]}]},{"type":"listItem","content":[{"type":"paragraph","content":[{"type":"text","text":"Each lead card includes:"}]},{"type":"bulletList","content":[{"type":"listItem","content":[{"type":"paragraph","content":[{"type":"text","text":"Refer icon","marks":[{"type":"strong"}]},{"type":"text","text":" → refer this lead to another contact."}]}]},{"type":"listItem","content":[{"type":"paragraph","content":[{"type":"text","text":"Add Note icon","marks":[{"type":"strong"}]},{"type":"text","text":" → open note editor for this lead."}]}]},{"type":"listItem","content":[{"type":"paragraph","content":[{"type":"text","text":"Remove icon","marks":[{"type":"strong"}]},{"type":"text","text":" → remove the lead (returns contact to Contact List)."}]}]}]}]}]}, {"type":"rule"}, {"type":"heading","attrs":{"level":3},"content":[{"type":"text","text":"3. Field Definitions Table"}]}, {"type":"table","attrs":{"isNumberColumnEnabled":false,"layout":"center","localId":"c50693a9-7a22-4ecc-90a7-8a38283cf6e6"},"content":[{"type":"tableRow","content":[{"type":"tableHeader","attrs":{},"content":[{"type":"paragraph","content":[{"type":"text","text":"Field Name"}]}]},{"type":"tableHeader","attrs":{},"content":[{"type":"paragraph","content":[{"type":"text","text":"Input Type"}]}]},{"type":"tableHeader","attrs":{},"content":[{"type":"paragraph","content":[{"type":"text","text":"Validation Rule"}]}]},{"type":"tableHeader","attrs":{},"content":[{"type":"paragraph","content":[{"type":"text","text":"Mandatory"}]}]},{"type":"tableHeader","attrs":{},"content":[{"type":"paragraph","content":[{"type":"text","text":"Description"}]}]}]},{"type":"tableRow","content":[{"type":"tableCell","attrs":{},"content":[{"type":"paragraph","content":[{"type":"text","text":"Lead Source Contact"}]}]},{"type":"tableCell","attrs":{},"content":[{"type":"paragraph","content":[{"type":"text","text":"List"}]}]},{"type":"tableCell","attrs":{},"content":[{"type":"paragraph","content":[{"type":"text","text":"Contact must already exist"}]}]},{"type":"tableCell","attrs":{},"content":[{"type":"paragraph","content":[{"type":"text","text":"Yes"}]}]},{"type":"tableCell","attrs":{},"content":[{"type":"paragraph","content":[{"type":"text","text":"Contacts eligible for conversion to lead"}]}]}]},{"type":"tableRow","content":[{"type":"tableCell","attrs":{},"content":[{"type":"paragraph","content":[{"type":"text","text":"Lead Notes"}]}]},{"type":"tableCell","attrs":{},"content":[{"type":"paragraph","content":[{"type":"text","text":"Text"}]}]},{"type":"tableCell","attrs":{},"content":[{"type":"paragraph","content":[{"type":"text","text":"Max 500 characters"}]}]},{"type":"tableCell","attrs":{},"content":[{"type":"paragraph","content":[{"type":"text","text":"No"}]}]},{"type":"tableCell","attrs":{},"content":[{"type":"paragraph","content":[{"type":"text","text":"Optional notes associated with each lead"}]}]}]},{"type":"tableRow","content":[{"type":"tableCell","attrs":{},"content":[{"type":"paragraph","content":[{"type":"text","text":"Action Icons"}]}]},{"type":"tableCell","attrs":{},"content":[{"type":"paragraph","content":[{"type":"text","text":"Button Row"}]}]},{"type":"tableCell","attrs":{},"content":[{"type":"paragraph","content":[{"type":"text","text":"Refer, Note, Remove"}]}]},{"type":"tableCell","attrs":{},"content":[{"type":"paragraph","content":[{"type":"text","text":"Yes"}]}]},{"type":"tableCell","attrs":{},"content":[{"type":"paragraph","content":[{"type":"text","text":"Quick actions available for each lead"}]}]}]}]}, {"type":"rule"}, {"type":"heading","attrs":{"level":3},"content":[{"type":"text","text":"4. Data Display Table (Card View)"}]}, {"type":"table","attrs":{"isNumberColumnEnabled":false,"layout":"center","localId":"8f6838e0-48df-482a-9e04-6b9d44d9c5c8","width":356},"content":[{"type":"tableRow","content":[{"type":"tableHeader","attrs":{},"content":[{"type":"paragraph","content":[{"type":"text","text":"Field Name"}]}]},{"type":"tableHeader","attrs":{},"content":[{"type":"paragraph","content":[{"type":"text","text":"Display Type"}]}]},{"type":"tableHeader","attrs":{},"content":[{"type":"paragraph","content":[{"type":"text","text":"Notes"}]}]}]},{"type":"tableRow","content":[{"type":"tableCell","attrs":{},"content":[{"type":"paragraph","content":[{"type":"text","text":"Lead Name"}]}]},{"type":"tableCell","attrs":{},"content":[{"type":"paragraph","content":[{"type":"text","text":"Bold Text"}]}]},{"type":"tableCell","attrs":{},"content":[{"type":"paragraph","content":[{"type":"text","text":"Full name from contact profile"}]}]}]},{"type":"tableRow","content":[{"type":"tableCell","attrs":{},"content":[{"type":"paragraph","content":[{"type":"text","text":"Last Interaction"}]}]},{"type":"tableCell","attrs":{},"content":[{"type":"paragraph","content":[{"type":"text","text":"Subtext"}]}]},{"type":"tableCell","attrs":{},"content":[{"type":"paragraph","content":[{"type":"text","text":"“Gift sent 2w ago”, “Message sent 1w ago”, etc."}]}]}]},{"type":"tableRow","content":[{"type":"tableCell","attrs":{},"content":[{"type":"paragraph","content":[{"type":"text","text":"Lead Avatar"}]}]},{"type":"tableCell","attrs":{},"content":[{"type":"paragraph","content":[{"type":"text","text":"Circular Image"}]}]},{"type":"tableCell","attrs":{},"content":[{"type":"paragraph","content":[{"type":"text","text":"Profile image pulled from contact card"}]}]}]},{"type":"tableRow","content":[{"type":"tableCell","attrs":{},"content":[{"type":"paragraph","content":[{"type":"text","text":"Action Buttons"}]}]},{"type":"tableCell","attrs":{},"content":[{"type":"paragraph","content":[{"type":"text","text":"3 Icons"}]}]},{"type":"tableCell","attrs":{},"content":[{"type":"paragraph","content":[{"type":"text","text":"Refer, Add Note, Remove (returns to contact list)"}]}]}]}]}, {"type":"rule"}, {"type":"heading","attrs":{"level":3},"content":[{"type":"text","text":"5. Acceptance Criteria (AC)"}]}, {"type":"bulletList","content":[{"type":"listItem","content":[{"type":"paragraph","content":[{"type":"text","text":"AC1:","marks":[{"type":"strong"}]},{"type":"text","text":" Lead list must be accessible via the Contacts tab → Lead sub-tab."}]}]},{"type":"listItem","content":[{"type":"paragraph","content":[{"type":"text","text":"AC2:","marks":[{"type":"strong"}]},{"type":"text","text":" “Convert to Lead” button must open a modal with multi-select contact picker."}]}]},{"type":"listItem","content":[{"type":"paragraph","content":[{"type":"text","text":"AC3:","marks":[{"type":"strong"}]},{"type":"text","text":" Only contacts not already in the lead list are available for conversion."}]}]},{"type":"listItem","content":[{"type":"paragraph","content":[{"type":"text","text":"AC4:","marks":[{"type":"strong"}]},{"type":"text","text":" On confirmation, selected contacts are added to the lead list and shown in the UI."}]}]},{"type":"listItem","content":[{"type":"paragraph","content":[{"type":"text","text":"AC5:","marks":[{"type":"strong"}]},{"type":"text","text":" Tapping the "},{"type":"text","text":"Refer","marks":[{"type":"strong"}]},{"type":"text","text":" icon opens the referral modal with the lead pre-filled."}]}]},{"type":"listItem","content":[{"type":"paragraph","content":[{"type":"text","text":"AC6:","marks":[{"type":"strong"}]},{"type":"text","text":" Tapping "},{"type":"text","text":"Add Note","marks":[{"type":"strong"}]},{"type":"text","text":" opens the lead-specific note editor."}]}]},{"type":"listItem","content":[{"type":"paragraph","content":[{"type":"text","text":"AC7:","marks":[{"type":"strong"}]},{"type":"text","text":" Tapping "},{"type":"text","text":"Remove","marks":[{"type":"strong"}]},{"type":"text","text":" removes the lead from the list and places the contact back into the general Contacts list."}]}]},{"type":"listItem","content":[{"type":"paragraph","content":[{"type":"text","text":"AC8:","marks":[{"type":"strong"}]},{"type":"text","text":" System must prevent duplicate conversion of the same contact into lead more than once."}]}]},{"type":"listItem","content":[{"type":"paragraph","content":[{"type":"text","text":"AC9:","marks":[{"type":"strong"}]},{"type":"text","text":" Changes to lead list are synced with the backend and persist across sessions."}]}]},{"type":"listItem","content":[{"type":"paragraph","content":[{"type":"text","text":"AC10:","marks":[{"type":"strong"}]},{"type":"text","text":" When a contact is converted to a lead or removed back to contact list, any existing notes must be preserved and moved with them."}]}]},{"type":"listItem","content":[{"type":"paragraph","content":[{"type":"text","text":"AC11:","marks":[{"type":"strong"}]},{"type":"text","text":" Once a contact is converted to a lead, it must no longer appear in the general Contact List until removed from the Lead List."}]}]}]}, {"type":"rule"}, {"type":"heading","attrs":{"level":3},"content":[{"type":"text","text":"6. System Rules"}]}, {"type":"bulletList","content":[{"type":"listItem","content":[{"type":"paragraph","content":[{"type":"text","text":"A contact can exist in only one list at a time: either "},{"type":"text","text":"Contacts","marks":[{"type":"strong"}]},{"type":"text","text":" or "},{"type":"text","text":"Leads","marks":[{"type":"strong"}]},{"type":"text","text":"."}]}]},{"type":"listItem","content":[{"type":"paragraph","content":[{"type":"text","text":"Removing a lead does not delete the contact; it returns them to the Contact List."}]}]},{"type":"listItem","content":[{"type":"paragraph","content":[{"type":"text","text":"No export, drag, swipe, or sort is included in MVP scope."}]}]},{"type":"listItem","content":[{"type":"paragraph","content":[{"type":"text","text":"All actions on lead (note, refer, remove) should use existing atomic components."}]}]}]}, {"type":"rule"}, {"type":"heading","attrs":{"level":3},"content":[{"type":"text","text":"7. UX Optimizations"}]}, {"type":"bulletList","content":[{"type":"listItem","content":[{"type":"paragraph","content":[{"type":"text","text":"“Convert to Lead” CTA uses primary color and icon for visibility."}]}]},{"type":"listItem","content":[{"type":"paragraph","content":[{"type":"text","text":"Lead cards visually distinct from general contacts (e.g., shadow or border variant)."}]}]},{"type":"listItem","content":[{"type":"paragraph","content":[{"type":"text","text":"Timestamps for last action auto-populated (e.g., last message sent, note added)."}]}]},{"type":"listItem","content":[{"type":"paragraph","content":[{"type":"text","text":"Use animation or toast:"}]},{"type":"bulletList","content":[{"type":"listItem","content":[{"type":"paragraph","content":[{"type":"text","text":"“3 contacts added to Lead List.”","marks":[{"type":"em"}]}]}]},{"type":"listItem","content":[{"type":"paragraph","content":[{"type":"text","text":"“Contact removed from Lead List.”","marks":[{"type":"em"}]}]}]}]}]}]}