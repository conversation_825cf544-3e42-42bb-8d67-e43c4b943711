<!DOCTYPE html>
<html>
<head>
    <title>[#AL-52] [MB - Contacts] Lead List</title>
    <meta http-equiv="Content-Type" Content="application/vnd.ms-word; charset=UTF-8">
        <style type="text/css">

.tableBorder, .grid
{
    background-color: #fff;
    width: 100%;
    border-collapse: collapse;
}

.tableBorder td, .grid td
{
    vertical-align: top;
    padding: 2px;
    border: 1px solid #ccc;
}

.noPadding
{
    padding: 0 !important;
}

h3 .subText
{
    font-size: 60%;
    font-weight: normal;
}

.tabLabel
{
    font-weight: bold;
    border: 1px solid #ccc;
    border-bottom:none;
    padding: 2px;
    display: inline;
}

td.blank
{
    padding: 0;
    margin: 0;
}

.blank td
{
    border: none;
}

#descriptionArea
{
    margin: 0;
    padding: 2px;
    border: 1px solid #ccc;
}

hr
{
    border-top:1px solid #aaa;
}

hr.fullcontent
{
  height: 15px;
  padding: 10px 0;
  background: #fff url('https://exnodes.atlassian.net/images/icons/hr.gif') no-repeat scroll center;
}

</style>

</head>
<body>

<table class="tableBorder" cellpadding="0" cellspacing="0" border="0" width="100%">
    <tr>
        <td bgcolor="#f0f0f0" width="100%" colspan="2" valign="top">
                            <b><a id="parent_issue_summary" href="https://exnodes.atlassian.net/browse/AL-17">Contacts</a></b>
                <span style="font-size: 9px">(<a id="parent_issue_key" href="https://exnodes.atlassian.net/browse/AL-17">AL-17</a>)</span>
                <h3 class="formtitle"><img src="https://exnodes.atlassian.net/images/icons/link_out_bot.gif" width="16" height="16" />
                        [AL-52]&nbsp;<a href="https://exnodes.atlassian.net/browse/AL-52">[MB - Contacts] Lead List</a>
            <span class="subText">
               Created: 04/Jun/25                   &nbsp;Updated: 26/Jun/25

                                            </span>
            </h3>
        </td>
    </tr>
    <tr>
        <td width="20%"><b>Status:</b></td>
        <td width="80%">In Progress</td>
    </tr>
    <tr>
        <td width="20%"><b>Project:</b></td>
        <td width="80%"><a href="https://exnodes.atlassian.net/secure/BrowseProject.jspa?id=10842">AIOS Link</a></td>
    </tr>

        <tr>
            <td><b>Components:</b></td>
            <td>
                            None
                </td>
    </tr>
    

        <tr>
            <td><b>Affects versions:</b></td>
            <td>
                            None
                </td>
    </tr>
    

        <tr>
            <td><b>Fix versions:</b></td>
            <td>
                            None
                </td>
    </tr>
    
    
            <td width="20%"><b>Parent:</b></td>
        <td width="80%"><a href="https://exnodes.atlassian.net/browse/AL-17">Contacts</a></td>
    </table>

<br />
<table class="grid" cellpadding="0" cellspacing="0" border="0" width="100%">
    <tr>
        <td bgcolor="#f0f0f0" valign="top" width="20%">
            <b>Type:</b>
        </td>
        <td bgcolor="#ffffff" valign="top"  width="30%" >
            Story
        </td>

                    <td bgcolor="#f0f0f0">
                <b>Priority:</b>
            </td>
            <td bgcolor="#ffffff" valign="top" nowrap>
                Medium
            </td>
            </tr>
    <tr>
                        <td bgcolor="#f0f0f0" valign="top" width="20%">
                <b>Reporter:</b>
            </td>
            <td bgcolor="#ffffff" valign="top"  width="30%" >
                                        
                
        
            <a class="user-hover" rel="712020:59b66c6e-1068-441c-9436-130289e16a26"
                id="word_reporter_712020:59b66c6e-1068-441c-9436-130289e16a26"
                href="https://exnodes.atlassian.net/secure/ViewProfile.jspa?accountId=712020%3A59b66c6e-1068-441c-9436-130289e16a26">
                    Duyen Nguyen
                </a>

                            </td>
        
                    <td bgcolor="#f0f0f0" width="20%">
                <b>Assignee:</b>
            </td>
            <td bgcolor="#ffffff" valign="top" nowrap  width="30%" >
                                    Unassigned
                            </td>
            </tr>
    	<tr>
		<td bgcolor="#f0f0f0" width="20%">
			<b>Resolution:</b>
		</td>
		<td bgcolor="#ffffff" valign="top" width="30%" nowrap>
            				Unresolved
                    </td>
                    <td bgcolor="#f0f0f0" width="20%">
                <b>Votes:</b>
            </td>
            <td bgcolor="#ffffff" valign="top" width="30%" nowrap>
                0
            </td>
        
    </tr>
    
        <tr>
        <td bgcolor="#f0f0f0" width="20%">
            <b>Labels:</b>
        </td>
        <td id="labels-37416-value" class="value" bgcolor="#ffffff" valign="top" colspan="3" nowrap>
                            Dev                    </td>
    </tr>
    
    	<tr>
        		<td bgcolor="#f0f0f0" width="20%"><b>Remaining Estimate:</b></td>
        <td bgcolor="#ffffff" valign="top" nowrap width="80%" colspan="3">
                            Not Specified
            		</td>
    </tr>
    <tr>
                <td bgcolor="#f0f0f0" width="20%"><b>Time Spent:</b></td>
		<td bgcolor="#ffffff" valign="top" nowrap width="80%" colspan="3">
                            Not Specified
            		</td>
	</tr>
    <tr>
                <td bgcolor="#f0f0f0" width="20%"><b>Original estimate:</b></td>
		<td bgcolor="#ffffff" valign="top" nowrap width="80%" colspan="3">
                            Not Specified
            		</td>
    </tr>
    
    </table>



    <br />

    	<table class="grid" cellpadding="0" cellspacing="0" border="0" width="100%">
            

        
    


    
                        <tr>
                <td bgcolor="#f0f0f0" width="20%" valign="top"><b>Sprint:</b></td>
                <td id="customfield_10020-37416-value" class="value" bgcolor="#ffffff" width="80%"></td>
            </tr>
                                <tr>
                <td bgcolor="#f0f0f0" width="20%" valign="top"><b>Platform:</b></td>
                <td id="customfield_10687-37416-value" class="value" bgcolor="#ffffff" width="80%">    MOBILE</td>
            </tr>
            </table>

    <br/>

    <table cellpadding="2" cellspacing="0" border="0" width="100%" align="center">
    <tr>
        <td bgcolor="#bbbbbb" width="1%" nowrap align="center">
            &nbsp;<font color="#ffffff"><b>Description</b></font>&nbsp;
        </td>
        <td>&nbsp;</td>
    </tr>
    </table>

    <table cellpadding="0" cellspacing="0" border="0" width="100%">
    <tr>
        <td id="descriptionArea">
            <h3><a name="1.UseCaseDescription"></a>1. Use Case Description</h3>

<p>As a user, I want to view and manage my lead contacts separately from my general contact list<br/>
so that I can track referral, follow-up, and engagement actions with higher intent contacts.</p>

<hr />

<h3><a name="2.UserWorkflow%28StepbyStep%29"></a>2. User Workflow (Step-by-Step)</h3>

<ol>
	<li>User navigates to the <b>Contacts</b> tab from the dashboard.</li>
	<li>User taps the <b>“Lead”</b> section/tab to view all contacts previously marked as leads.</li>
	<li>On the Lead List screen, user can:
	<ul>
		<li>Scroll and browse lead cards.</li>
		<li>Tap the <b>“+” (Convert to Lead)</b> button.</li>
	</ul>
	</li>
	<li>System displays a modal where user can select one or more contacts to convert into leads.</li>
	<li>User confirms the selection.</li>
	<li>Selected contacts are added to the lead list and displayed as lead cards.</li>
	<li>Each lead card includes:
	<ul>
		<li><b>Refer icon</b> → refer this lead to another contact.</li>
		<li><b>Add Note icon</b> → open note editor for this lead.</li>
		<li><b>Remove icon</b> → remove the lead (returns contact to Contact List).</li>
	</ul>
	</li>
</ol>


<hr />

<h3><a name="3.FieldDefinitionsTable"></a>3. Field Definitions Table</h3>

<div class='table-wrap'>
<table class='confluenceTable'><tbody>
<tr>
<th class='confluenceTh'>Field Name</th>
<th class='confluenceTh'>Input Type</th>
<th class='confluenceTh'>Validation Rule</th>
<th class='confluenceTh'>Mandatory</th>
<th class='confluenceTh'>Description</th>
</tr>
<tr>
<td class='confluenceTd'>Lead Source Contact</td>
<td class='confluenceTd'>List</td>
<td class='confluenceTd'>Contact must already exist</td>
<td class='confluenceTd'>Yes</td>
<td class='confluenceTd'>Contacts eligible for conversion to lead</td>
</tr>
<tr>
<td class='confluenceTd'>Lead Notes</td>
<td class='confluenceTd'>Text</td>
<td class='confluenceTd'>Max 500 characters</td>
<td class='confluenceTd'>No</td>
<td class='confluenceTd'>Optional notes associated with each lead</td>
</tr>
<tr>
<td class='confluenceTd'>Action Icons</td>
<td class='confluenceTd'>Button Row</td>
<td class='confluenceTd'>Refer, Note, Remove</td>
<td class='confluenceTd'>Yes</td>
<td class='confluenceTd'>Quick actions available for each lead</td>
</tr>
</tbody></table>
</div>


<hr />

<h3><a name="4.DataDisplayTable%28CardView%29"></a>4. Data Display Table (Card View)</h3>

<div class='table-wrap'>
<table class='confluenceTable'><tbody>
<tr>
<th class='confluenceTh'>Field Name</th>
<th class='confluenceTh'>Display Type</th>
<th class='confluenceTh'>Notes</th>
</tr>
<tr>
<td class='confluenceTd'>Lead Name</td>
<td class='confluenceTd'>Bold Text</td>
<td class='confluenceTd'>Full name from contact profile</td>
</tr>
<tr>
<td class='confluenceTd'>Last Interaction</td>
<td class='confluenceTd'>Subtext</td>
<td class='confluenceTd'>“Gift sent 2w ago”, “Message sent 1w ago”, etc.</td>
</tr>
<tr>
<td class='confluenceTd'>Lead Avatar</td>
<td class='confluenceTd'>Circular Image</td>
<td class='confluenceTd'>Profile image pulled from contact card</td>
</tr>
<tr>
<td class='confluenceTd'>Action Buttons</td>
<td class='confluenceTd'>3 Icons</td>
<td class='confluenceTd'>Refer, Add Note, Remove (returns to contact list)</td>
</tr>
</tbody></table>
</div>


<hr />

<h3><a name="5.AcceptanceCriteria%28AC%29"></a>5. Acceptance Criteria (AC)</h3>

<ul>
	<li><b>AC1:</b> Lead list must be accessible via the Contacts tab → Lead sub-tab.</li>
	<li><b>AC2:</b> “Convert to Lead” button must open a modal with multi-select contact picker.</li>
	<li><b>AC3:</b> Only contacts not already in the lead list are available for conversion.</li>
	<li><b>AC4:</b> On confirmation, selected contacts are added to the lead list and shown in the UI.</li>
	<li><b>AC5:</b> Tapping the <b>Refer</b> icon opens the referral modal with the lead pre-filled.</li>
	<li><b>AC6:</b> Tapping <b>Add Note</b> opens the lead-specific note editor.</li>
	<li><b>AC7:</b> Tapping <b>Remove</b> removes the lead from the list and places the contact back into the general Contacts list.</li>
	<li><b>AC8:</b> System must prevent duplicate conversion of the same contact into lead more than once.</li>
	<li><b>AC9:</b> Changes to lead list are synced with the backend and persist across sessions.</li>
	<li><b>AC10:</b> When a contact is converted to a lead or removed back to contact list, any existing notes must be preserved and moved with them.</li>
	<li><b>AC11:</b> Once a contact is converted to a lead, it must no longer appear in the general Contact List until removed from the Lead List.</li>
</ul>


<hr />

<h3><a name="6.SystemRules"></a>6. System Rules</h3>

<ul>
	<li>A contact can exist in only one list at a time: either <b>Contacts</b> or <b>Leads</b>.</li>
	<li>Removing a lead does not delete the contact; it returns them to the Contact List.</li>
	<li>No export, drag, swipe, or sort is included in MVP scope.</li>
	<li>All actions on lead (note, refer, remove) should use existing atomic components.</li>
</ul>


<hr />

<h3><a name="7.UXOptimizations"></a>7. UX Optimizations</h3>

<ul>
	<li>“Convert to Lead” CTA uses primary color and icon for visibility.</li>
	<li>Lead cards visually distinct from general contacts (e.g., shadow or border variant).</li>
	<li>Timestamps for last action auto-populated (e.g., last message sent, note added).</li>
	<li>Use animation or toast:
	<ul>
		<li><em>“3 contacts added to Lead List.”</em></li>
		<li><em>“Contact removed from Lead List.”</em></li>
	</ul>
	</li>
</ul>

            <br/>
        </td>
    </tr>
    </table>

Generated at Thu Jun 26 04:16:23 UTC 2025 by Kid Nguyễn using Jira 1001.0.0-SNAPSHOT#100285-rev:e2e3f38099261bb500217a742ea82f5528ab8db0.

</body>
</html>