import pytest
from sqlalchemy import create_engine
from sqlalchemy.orm import sessionmaker
from datetime import datetime, timedelta, time

from src.utils.base_model import Base
from src.user.models import User, UserProfile
from src.user.service import update_availability_service, get_availability_service
from src.user.schemas import AvailabilityUpdateSchema
from src.utils.enum import AvailabilityStatusEnum, AvailabilityTagsEnum, UserRoleEnum

@pytest.fixture
def db_session():
    engine = create_engine("sqlite:///:memory:")
    Session = sessionmaker(bind=engine)
    Base.metadata.create_all(engine)
    session = Session()
    try:
        yield session
    finally:
        session.close()

def _create_user(session):
    user = User(email="<EMAIL>", password="x", user_type=UserRoleEnum.BUSINESS)
    session.add(user)
    session.commit()
    profile = UserProfile(user_id=user.id)
    session.add(profile)
    session.commit()
    return user


def test_update_and_get_availability_success(db_session):
    user = _create_user(db_session)
    data = AvailabilityUpdateSchema(
        availability_time={"from_time": "09:00", "to_time": "17:00"},
        availability_tags=[AvailabilityTagsEnum.COFFEE],
        availability_status=AvailabilityStatusEnum.AVAILABLE,
        availability_message=None,
    )
    result = update_availability_service(db_session, user.id, data)
    assert result["availability_status"] == AvailabilityStatusEnum.AVAILABLE
    profile = db_session.query(UserProfile).filter(UserProfile.user_id == user.id).first()
    assert profile.availability_start_time == time(9, 0)
    assert profile.availability_end_time == time(17, 0)
    assert profile.availability_tags == [AvailabilityTagsEnum.COFFEE.value]
    assert profile.availability_tags_expired_at is not None


def test_update_availability_invalid_time(db_session):
    user = _create_user(db_session)
    data = AvailabilityUpdateSchema(
        availability_time={"from_time": "18:00", "to_time": "09:00"},
        availability_status=AvailabilityStatusEnum.AVAILABLE,
    )
    with pytest.raises(Exception):
        update_availability_service(db_session, user.id, data)


def test_get_availability_clears_expired_tags(db_session):
    user = _create_user(db_session)
    profile = db_session.query(UserProfile).filter(UserProfile.user_id == user.id).first()
    profile.availability_tags = [AvailabilityTagsEnum.COFFEE.value]
    profile.availability_tags_expired_at = datetime.utcnow() - timedelta(hours=1)
    db_session.add(profile)
    db_session.commit()
    result = get_availability_service(db_session, user.id)
    assert result["availability_tags"] is None
    assert profile.availability_tags is None
