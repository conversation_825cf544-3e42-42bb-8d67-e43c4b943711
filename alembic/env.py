from logging.config import fileConfig

from sqlalchemy import engine_from_config
from sqlalchemy import pool
import os
from dotenv import load_dotenv
from alembic import context
from src.utils.base_model import Base
from src.user.models import User, UserProfile, Token, UserIntegration,UserNotificationSettings,UserSubscriptionSettings,UserWebLinkSettings,BusinessProfile, UserOTP
from src.event.models import Event, EventInteraction, EventInvitation
from src.news.models import News
from src.contact.models import Contact
from src.chat.models import ChatRoom, ChatRoomMember, Message
from src.product.models import Category, Product
from src.gift.models import GiftCategory, Gift, UserGift
from src.notification_history.models import SMSHistory, Notification, FCMToken, UserTopic, Topic, NotificationAction, NotificationConfig
from src.payment.models import Transaction, Invoice, Receipt
from src.ticket_sale.models import TicketOrder
from src.ticket.models import Ticket
from src.lead.models import Lead
from src.organization.models import Organization
from src.cart.models import Cart, CartItem
from src.order.models import Order
from src.meeting.models import Meeting
from src.meeting.models import MeetingParticipant
from src.contact.models import ContactReferralLog
from src.contact.models import ContactDismissSuggestion
# from src.notification_history.models import SMSHistory
# this is the Alembic Config object, which provides
# access to the values within the .ini file in use.
config = context.config
load_dotenv()
# Interpret the config file for Python logging.
# This line sets up loggers basically.
if config.config_file_name is not None:
    fileConfig(config.config_file_name)

# add your model's MetaData object here
# for 'autogenerate' support
# from myapp import mymodel
# target_metadata = mymodel.Base.metadata
target_metadata = Base.metadata
# other values from the config, defined by the needs of env.py,
# can be acquired:
# my_important_option = config.get_main_option("my_important_option")
# ... etc.
db_user = os.getenv("DATABASE_USER")
db_password = os.getenv("DATABASE_PASSWORD")
db_host = os.getenv("DATABASE_HOST")
db_port = os.getenv("DATABASE_PORT")
db_name = os.getenv("DATABASE_NAME")

database_url = f"mysql+pymysql://general_super_admin:<EMAIL>:3306/aioslink_db"
# database_url = f"********************************************************"      
config.set_main_option("sqlalchemy.url", database_url)

def run_migrations_offline() -> None:
    """Run migrations in 'offline' mode.

    This configures the context with just a URL
    and not an Engine, though an Engine is acceptable
    here as well.  By skipping the Engine creation
    we don't even need a DBAPI to be available.

    Calls to context.execute() here emit the given string to the
    script output.

    """
    url = config.get_main_option("sqlalchemy.url")
    context.configure(
        url=url,
        target_metadata=target_metadata,
        literal_binds=True,
        dialect_opts={"paramstyle": "named"},
    )

    with context.begin_transaction():
        context.run_migrations()


def run_migrations_online() -> None:
    """Run migrations in 'online' mode.

    In this scenario we need to create an Engine
    and associate a connection with the context.

    """
    connectable = engine_from_config(
        config.get_section(config.config_ini_section, {}),
        prefix="sqlalchemy.",
        poolclass=pool.NullPool,
    )
    with connectable.connect() as connection:
        context.configure(
            connection=connection, target_metadata=target_metadata
        )

        with context.begin_transaction():
            context.run_migrations()


if context.is_offline_mode():
    run_migrations_offline()
else:
    run_migrations_online()
