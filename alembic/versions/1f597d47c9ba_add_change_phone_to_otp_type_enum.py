"""add_change_phone_to_otp_type_enum

Revision ID: 1f597d47c9ba
Revises: 378d4ae26613
Create Date: 2025-06-27 11:05:08.297841

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = '1f597d47c9ba'
down_revision: Union[str, None] = '378d4ae26613'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    # Add CHANGE_PHONE to OTPTypeEnum
    op.execute("ALTER TABLE user_otps MODIFY COLUMN otp_type ENUM('USER_VERIFICATION', 'FORGOT_PASSWORD', 'CHANGE_EMAIL', 'CHANGE_PHONE') NOT NULL")
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    # Remove CHANGE_PHONE from OTPTypeEnum
    op.execute("ALTER TABLE user_otps MODIFY COLUMN otp_type ENUM('USER_VERIFICATION', 'FORGOT_PASSWORD', 'CHANGE_EMAIL') NOT NULL")
    # ### end Alembic commands ###
