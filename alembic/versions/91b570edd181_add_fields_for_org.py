"""add fields for org

Revision ID: 91b570edd181
Revises: ca9607813f5d
Create Date: 2025-06-25 04:59:06.189698

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import mysql

# revision identifiers, used by Alembic.
revision: str = '91b570edd181'
down_revision: Union[str, None] = 'ca9607813f5d'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('organization', sa.Column('established_at', sa.DateTime(), nullable=True))
    op.alter_column('organization', 'owner_id',
               existing_type=mysql.INTEGER(),
               nullable=True)
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.alter_column('organization', 'owner_id',
               existing_type=mysql.INTEGER(),
               nullable=False)
    op.drop_column('organization', 'established_at')
    # ### end Alembic commands ###
