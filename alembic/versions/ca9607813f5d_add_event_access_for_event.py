"""add event_access for event

Revision ID: ca9607813f5d
Revises: eb04b8c84790
Create Date: 2025-06-23 10:04:52.414623

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import mysql

# revision identifiers, used by Alembic.
revision: str = 'ca9607813f5d'
down_revision: Union[str, None] = 'eb04b8c84790'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('event', sa.Column('event_access', sa.Enum('FREE', 'TICKETED', name='eventaccessenum'), nullable=False))
    op.drop_column('ticket', 'ticket_type')
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('ticket', sa.Column('ticket_type', mysql.ENUM('FREE', 'TICKETED', collation='utf8mb4_general_ci'), nullable=False))
    op.drop_column('event', 'event_access')
    # ### end Alembic commands ###
