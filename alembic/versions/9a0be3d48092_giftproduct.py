"""GiftProduct

Revision ID: 9a0be3d48092
Revises: 3e318a3000f6
Create Date: 2025-06-27 08:14:45.412385

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import mysql

# revision identifiers, used by Alembic.
revision: str = '9a0be3d48092'
down_revision: Union[str, None] = '3e318a3000f6'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('user_gift', sa.Column('order_id', sa.Integer(), nullable=True))
    op.drop_constraint('user_gift_ibfk_4', 'user_gift', type_='foreignkey')
    op.create_foreign_key(None, 'user_gift', 'order', ['order_id'], ['id'])
    op.drop_column('user_gift', 'order_item_id')
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('user_gift', sa.Column('order_item_id', mysql.INTEGER(), autoincrement=False, nullable=True))
    op.drop_constraint(None, 'user_gift', type_='foreignkey')
    op.create_foreign_key('user_gift_ibfk_4', 'user_gift', 'order_item', ['order_item_id'], ['id'])
    op.drop_column('user_gift', 'order_id')
    # ### end Alembic commands ###
