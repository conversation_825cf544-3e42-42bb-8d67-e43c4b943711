"""add_chat_room_fields_only

Revision ID: d66029db17aa
Revises: eb464d83f1b0
Create Date: 2025-07-07 11:50:03.803965

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = 'd66029db17aa'
down_revision: Union[str, None] = 'eb464d83f1b0'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('chat_room', sa.Column('theme', sa.String(length=50), nullable=True))
    op.add_column('chat_room_member', sa.Column('nick_name', sa.String(length=100), nullable=True))
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('chat_room_member', 'nick_name')
    op.drop_column('chat_room', 'theme')
    # ### end Alembic commands ###
