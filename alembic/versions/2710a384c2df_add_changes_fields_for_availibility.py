"""add changes fields for availibility

Revision ID: 2710a384c2df
Revises: 103f24c278bc
Create Date: 2025-06-27 04:18:37.328043

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = '2710a384c2df'
down_revision: Union[str, None] = '103f24c278bc'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('userprofile', sa.Column('enabled_availability', sa.<PERSON>an(), nullable=True))
    op.add_column('userprofile', sa.Column('availability', sa.JSON(), nullable=True))
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('userprofile', 'availability')
    op.drop_column('userprofile', 'enabled_availability')
    # ### end Alembic commands ###
