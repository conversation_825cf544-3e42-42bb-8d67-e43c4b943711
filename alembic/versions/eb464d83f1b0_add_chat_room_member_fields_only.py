"""add_chat_room_member_fields_only

Revision ID: eb464d83f1b0
Revises: fe293e8b35df
Create Date: 2025-07-07 11:02:00.643242

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = 'eb464d83f1b0'
down_revision: Union[str, None] = 'e9a9657dd57e'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('chat_room_member', sa.Column('role', sa.Enum('ADMIN', 'MEMBER', name='memberroleenum'), nullable=True))
    op.add_column('chat_room_member', sa.Column('status', sa.Enum('PENDING', 'ACCEPTED', 'REJECTED', 'BLOCKED', name='memberstatusenum'), nullable=True))
    op.add_column('chat_room_member', sa.Column('joined_at', sa.DateTime(), nullable=True))
    op.add_column('chat_room_member', sa.Column('invited_by', sa.Integer(), nullable=True))
    op.add_column('chat_room_member', sa.Column('invited_at', sa.DateTime(), nullable=True))
    op.add_column('chat_room_member', sa.Column('accepted_at', sa.DateTime(), nullable=True))
    op.create_foreign_key(None, 'chat_room_member', 'user', ['invited_by'], ['id'])
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_constraint(None, 'chat_room_member', type_='foreignkey')
    op.drop_column('chat_room_member', 'accepted_at')
    op.drop_column('chat_room_member', 'invited_at')
    op.drop_column('chat_room_member', 'invited_by')
    op.drop_column('chat_room_member', 'joined_at')
    op.drop_column('chat_room_member', 'status')
    op.drop_column('chat_room_member', 'role')
    # ### end Alembic commands ###
