"""add type for event

Revision ID: e7b211349d91
Revises: 2d89c8593fbe
Create Date: 2025-06-26 09:16:09.698563

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import mysql

# revision identifiers, used by Alembic.
revision: str = 'e7b211349d91'
down_revision: Union[str, None] = '2d89c8593fbe'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('event_invitation', sa.Column('type', sa.Enum('INVITE_ONLY', 'INVITE_WITH_TICKET', name='eventinvitationenum'), nullable=True))
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('event_invitation', 'type')
    # ### end Alembic commands ###
