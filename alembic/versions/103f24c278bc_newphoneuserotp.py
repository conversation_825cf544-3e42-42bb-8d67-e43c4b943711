"""NewPhoneUserOTP

Revision ID: 103f24c278bc
Revises: 1f597d47c9ba
Create Date: 2025-06-27 04:12:18.196034

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = '103f24c278bc'
down_revision: Union[str, None] = '1f597d47c9ba'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('user_otps', sa.Column('new_phone', sa.String(length=20), nullable=True))
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('user_otps', 'new_phone')
    # ### end Alembic commands ###
