"""add is_active for org

Revision ID: 84dcf155648c
Revises: 91b570edd181
Create Date: 2025-06-26 03:58:48.949383

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = '84dcf155648c'
down_revision: Union[str, None] = '91b570edd181'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_foreign_key(None, 'event_invitation', 'event', ['event_id'], ['id'])
    op.create_foreign_key(None, 'event_invitation', 'contact', ['contact_id'], ['id'])
    op.add_column('organization', sa.Column('is_active', sa.<PERSON>(), nullable=False))
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('organization', 'is_active')
    op.drop_constraint(None, 'event_invitation', type_='foreignkey')
    op.drop_constraint(None, 'event_invitation', type_='foreignkey')
    # ### end Alembic commands ###
