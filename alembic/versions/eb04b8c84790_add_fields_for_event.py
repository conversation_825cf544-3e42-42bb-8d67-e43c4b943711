"""add fields for event

Revision ID: eb04b8c84790
Revises: 0f1f1284547b
Create Date: 2025-06-23 09:33:16.394785

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import mysql

# revision identifiers, used by Alembic.
revision: str = 'eb04b8c84790'
down_revision: Union[str, None] = '0f1f1284547b'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('event', sa.Column('rsvp_limit', sa.Integer(), nullable=True))
    op.add_column('ticket', sa.Column('ticket_type', sa.Enum('FREE', 'TICKETED', name='tickettypeenum'), nullable=False))
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('ticket', 'ticket_type')
    op.drop_column('event', 'rsvp_limit')
    # ### end Alembic commands ###
