"""Meeting invite multiple users

Revision ID: 8ac538e55b46
Revises: 84dcf155648c
Create Date: 2025-06-26 08:31:48.739282

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import mysql

# revision identifiers, used by Alembic.
revision: str = '8ac538e55b46'
down_revision: Union[str, None] = '84dcf155648c'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_constraint('meeting_ibfk_2', 'meeting', type_='foreignkey')
    op.drop_column('meeting', 'invitee_id')
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('meeting', sa.Column('invitee_id', mysql.INTEGER(), autoincrement=False, nullable=False))
    op.create_foreign_key('meeting_ibfk_2', 'meeting', 'user', ['invitee_id'], ['id'])
    # ### end Alembic commands ###
