"""add more fields in userprofile

Revision ID: 2be2d22e3129
Revises: fdb616793f4a
Create Date: 2025-06-06 14:24:45.869927

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import mysql

# revision identifiers, used by Alembic.
revision: str = '2be2d22e3129'
down_revision: Union[str, None] = 'fdb616793f4a'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('userprofile', sa.Column('title', sa.String(length=255), nullable=True))
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('userprofile', 'title')
    # ### end Alembic commands ###
