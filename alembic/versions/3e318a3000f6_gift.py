"""Gift

Revision ID: 3e318a3000f6
Revises: 16e7549ede93
Create Date: 2025-06-27 07:23:38.067053

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = '3e318a3000f6'
down_revision: Union[str, None] = '16e7549ede93'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('user_gift', sa.Column('gift_type', sa.Enum('TICKET', 'PRODUCT', name='gifttypeenum'), nullable=False))
    op.add_column('user_gift', sa.Column('ticket_order_id', sa.Integer(), nullable=True))
    op.add_column('user_gift', sa.Column('order_item_id', sa.Integer(), nullable=True))
    op.add_column('user_gift', sa.Column('status', sa.Enum('PENDING', 'SENT', 'DELIVERED', 'REDEEMED', 'CANCELLED', 'EXPIRED', 'ACCEPTED', 'REJECTED', name='giftstatusenum'), nullable=True))
    op.create_foreign_key(None, 'user_gift', 'order_item', ['order_item_id'], ['id'])
    op.create_foreign_key(None, 'user_gift', 'ticket_order', ['ticket_order_id'], ['id'])
    op.add_column('userprofile', sa.Column('is_visibled_map', sa.Boolean(), nullable=True))
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('userprofile', 'is_visibled_map')
    op.drop_constraint(None, 'user_gift', type_='foreignkey')
    op.drop_constraint(None, 'user_gift', type_='foreignkey')
    op.drop_column('user_gift', 'status')
    op.drop_column('user_gift', 'order_item_id')
    op.drop_column('user_gift', 'ticket_order_id')
    op.drop_column('user_gift', 'gift_type')
    # ### end Alembic commands ###
