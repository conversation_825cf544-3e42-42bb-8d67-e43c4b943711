"""Product add listed at

Revision ID: 16e7549ede93
Revises: 2710a384c2df
Create Date: 2025-06-27 04:44:58.598195

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = '16e7549ede93'
down_revision: Union[str, None] = '2710a384c2df'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('product', sa.Column('listed_at', sa.DateTime(), nullable=True))
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('product', 'listed_at')
    # ### end Alembic commands ###
