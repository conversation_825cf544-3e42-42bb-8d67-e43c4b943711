"""add_change_email_to_otp_type_enum

Revision ID: 378d4ae26613
Revises: d66673a92842
Create Date: 2025-06-27 10:42:31.780725

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = '378d4ae26613'
down_revision: Union[str, None] = 'd66673a92842'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    # Add CHANGE_EMAIL to OTPTypeEnum
    op.execute("ALTER TABLE user_otps MODIFY COLUMN otp_type ENUM('USER_VERIFICATION', 'FORGOT_PASSWORD', 'CHANGE_EMAIL') NOT NULL")
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    # Remove CHANGE_EMAIL from OTPTypeEnum
    op.execute("ALTER TABLE user_otps MODIFY COLUMN otp_type ENUM('USER_VERIFICATION', 'FORGOT_PASSWORD') NOT NULL")
    # ### end Alembic commands ###
