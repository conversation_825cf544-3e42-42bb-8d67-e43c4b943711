"""Add status Gift

Revision ID: 8f703a554ba1
Revises: d66029db17aa
Create Date: 2025-07-07 10:44:36.155775

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import mysql

# revision identifiers, used by Alembic.
revision: str = '8f703a554ba1'
down_revision: Union[str, None] = 'd66029db17aa'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_index('uq_report_user_event', table_name='report_event')
    op.drop_table('report_event')
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table('report_event',
    sa.Column('user_id', mysql.INTEGER(), autoincrement=False, nullable=False),
    sa.Column('event_id', mysql.INTEGER(), autoincrement=False, nullable=False),
    sa.Column('reason', mysql.ENUM('SPAM', 'MISLEADING', 'SAFETY', 'OTHER'), nullable=False),
    sa.Column('comment', mysql.TEXT(collation='utf8mb4_general_ci'), nullable=True),
    sa.Column('status', mysql.ENUM('OUTSTANDING', 'RESOLVED'), nullable=False),
    sa.Column('resolved_by', mysql.INTEGER(), autoincrement=False, nullable=True),
    sa.Column('resolved_at', mysql.DATETIME(), nullable=True),
    sa.Column('id', mysql.INTEGER(), autoincrement=True, nullable=False),
    sa.Column('created_at', mysql.DATETIME(), nullable=True),
    sa.Column('updated_at', mysql.DATETIME(), nullable=True),
    sa.Column('is_deleted', mysql.TINYINT(display_width=1), autoincrement=False, nullable=True),
    sa.ForeignKeyConstraint(['event_id'], ['event.id'], name='report_event_ibfk_2'),
    sa.ForeignKeyConstraint(['resolved_by'], ['user.id'], name='report_event_ibfk_3'),
    sa.ForeignKeyConstraint(['user_id'], ['user.id'], name='report_event_ibfk_1'),
    sa.PrimaryKeyConstraint('id'),
    mysql_collate='utf8mb4_general_ci',
    mysql_default_charset='utf8mb4',
    mysql_engine='InnoDB'
    )
    op.create_index('uq_report_user_event', 'report_event', ['user_id', 'event_id'], unique=True)
    # ### end Alembic commands ###
