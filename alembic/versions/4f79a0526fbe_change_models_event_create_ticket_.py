"""change models event, create ticket, ticketorder, payment modules

Revision ID: 4f79a0526fbe
Revises: 2be2d22e3129
Create Date: 2025-06-09 16:19:18.695889

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = '4f79a0526fbe'
down_revision: Union[str, None] = '2be2d22e3129'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table('transaction',
    sa.Column('amount', sa.Float(), nullable=False),
    sa.Column('payment_method', sa.String(length=50), nullable=False),
    sa.Column('transaction_date', sa.DateTime(), nullable=True),
    sa.Column('payload', sa.JSON(), nullable=True),
    sa.Column('id', sa.Integer(), autoincrement=True, nullable=False),
    sa.Column('created_at', sa.DateTime(), nullable=True),
    sa.Column('updated_at', sa.DateTime(), nullable=True),
    sa.Column('is_deleted', sa.Boolean(), nullable=True),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_transaction_id'), 'transaction', ['id'], unique=False)
    op.create_table('invoice',
    sa.Column('user_id', sa.Integer(), nullable=False),
    sa.Column('total_amount', sa.Float(), nullable=False),
    sa.Column('issued_at', sa.DateTime(), nullable=True),
    sa.Column('payload', sa.JSON(), nullable=True),
    sa.Column('uuid', sa.String(length=36), nullable=False),
    sa.Column('id', sa.Integer(), autoincrement=True, nullable=False),
    sa.Column('created_at', sa.DateTime(), nullable=True),
    sa.Column('updated_at', sa.DateTime(), nullable=True),
    sa.Column('is_deleted', sa.Boolean(), nullable=True),
    sa.ForeignKeyConstraint(['user_id'], ['user.id'], ),
    sa.PrimaryKeyConstraint('id'),
    sa.UniqueConstraint('uuid')
    )
    op.create_index(op.f('ix_invoice_id'), 'invoice', ['id'], unique=False)
    op.create_table('receipt',
    sa.Column('transaction_id', sa.Integer(), nullable=False),
    sa.Column('amount', sa.Float(), nullable=False),
    sa.Column('issued_at', sa.DateTime(), nullable=True),
    sa.Column('payload', sa.JSON(), nullable=True),
    sa.Column('uuid', sa.String(length=36), nullable=False),
    sa.Column('id', sa.Integer(), autoincrement=True, nullable=False),
    sa.Column('created_at', sa.DateTime(), nullable=True),
    sa.Column('updated_at', sa.DateTime(), nullable=True),
    sa.Column('is_deleted', sa.Boolean(), nullable=True),
    sa.ForeignKeyConstraint(['transaction_id'], ['transaction.id'], ),
    sa.PrimaryKeyConstraint('id'),
    sa.UniqueConstraint('uuid')
    )
    op.create_index(op.f('ix_receipt_id'), 'receipt', ['id'], unique=False)
    op.create_table('ticket',
    sa.Column('event_id', sa.Integer(), nullable=False),
    sa.Column('name', sa.String(length=255), nullable=False),
    sa.Column('description', sa.Text(), nullable=True),
    sa.Column('price', sa.Float(), nullable=False),
    sa.Column('quantity', sa.Integer(), nullable=False),
    sa.Column('start_sale', sa.DateTime(), nullable=True),
    sa.Column('end_sale', sa.DateTime(), nullable=True),
    sa.Column('id', sa.Integer(), autoincrement=True, nullable=False),
    sa.Column('created_at', sa.DateTime(), nullable=True),
    sa.Column('updated_at', sa.DateTime(), nullable=True),
    sa.Column('is_deleted', sa.Boolean(), nullable=True),
    sa.ForeignKeyConstraint(['event_id'], ['event.id'], ),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_ticket_id'), 'ticket', ['id'], unique=False)
    op.create_table('ticket_order',
    sa.Column('ticket_id', sa.Integer(), nullable=False),
    sa.Column('user_id', sa.Integer(), nullable=False),
    sa.Column('quantity', sa.Integer(), nullable=False),
    sa.Column('total_price', sa.Float(), nullable=False),
    sa.Column('purchased_at', sa.DateTime(), nullable=True),
    sa.Column('payment_status', sa.Enum('PENDING', 'PAID', 'FAILED', name='paymentstatusenum'), nullable=True),
    sa.Column('id', sa.Integer(), autoincrement=True, nullable=False),
    sa.Column('created_at', sa.DateTime(), nullable=True),
    sa.Column('updated_at', sa.DateTime(), nullable=True),
    sa.Column('is_deleted', sa.Boolean(), nullable=True),
    sa.ForeignKeyConstraint(['ticket_id'], ['ticket.id'], ),
    sa.ForeignKeyConstraint(['user_id'], ['user.id'], ),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_ticket_order_id'), 'ticket_order', ['id'], unique=False)
    op.create_table('invoice_ticket_order',
    sa.Column('invoice_id', sa.Integer(), nullable=False),
    sa.Column('ticket_order_id', sa.Integer(), nullable=False),
    sa.ForeignKeyConstraint(['invoice_id'], ['invoice.id'], ),
    sa.ForeignKeyConstraint(['ticket_order_id'], ['ticket_order.id'], ),
    sa.PrimaryKeyConstraint('invoice_id', 'ticket_order_id')
    )
    op.create_table('ticket_order_transaction',
    sa.Column('ticket_order_id', sa.Integer(), nullable=False),
    sa.Column('transaction_id', sa.Integer(), nullable=False),
    sa.ForeignKeyConstraint(['ticket_order_id'], ['ticket_order.id'], ),
    sa.ForeignKeyConstraint(['transaction_id'], ['transaction.id'], ),
    sa.PrimaryKeyConstraint('ticket_order_id', 'transaction_id')
    )
    op.add_column('event', sa.Column('meeting_link', sa.String(length=256), nullable=True))
    op.add_column('event', sa.Column('tags', sa.JSON(), nullable=True))
    op.add_column('event', sa.Column('attendee_limit', sa.Integer(), nullable=True))
    op.add_column('event', sa.Column('attendee_count', sa.Integer(), nullable=True))
    op.add_column('event', sa.Column('telegram_group_link', sa.String(length=256), nullable=True))
    op.add_column('event', sa.Column('whatsapp_group_link', sa.String(length=256), nullable=True))
    op.create_index(op.f('ix_lead_id'), 'lead', ['id'], unique=False)
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_index(op.f('ix_lead_id'), table_name='lead')
    op.drop_column('event', 'whatsapp_group_link')
    op.drop_column('event', 'telegram_group_link')
    op.drop_column('event', 'attendee_count')
    op.drop_column('event', 'attendee_limit')
    op.drop_column('event', 'tags')
    op.drop_column('event', 'meeting_link')
    op.drop_table('ticket_order_transaction')
    op.drop_table('invoice_ticket_order')
    op.drop_index(op.f('ix_ticket_order_id'), table_name='ticket_order')
    op.drop_table('ticket_order')
    op.drop_index(op.f('ix_ticket_id'), table_name='ticket')
    op.drop_table('ticket')
    op.drop_index(op.f('ix_receipt_id'), table_name='receipt')
    op.drop_table('receipt')
    op.drop_index(op.f('ix_invoice_id'), table_name='invoice')
    op.drop_table('invoice')
    op.drop_index(op.f('ix_transaction_id'), table_name='transaction')
    op.drop_table('transaction')
    # ### end Alembic commands ###
