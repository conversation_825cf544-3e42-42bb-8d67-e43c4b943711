"""add lead table

Revision ID: fdb616793f4a
Revises: bdcf9cb96675
Create Date: 2025-06-06 00:00:00.000000
"""
from typing import Sequence, Union
from alembic import op
import sqlalchemy as sa

revision: str = 'fdb616793f4a'
down_revision: Union[str, None] = 'bdcf9cb96675'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None

def upgrade() -> None:
    op.create_table(
        'lead',
        sa.Column('id', sa.Integer(), primary_key=True),
        sa.Column('created_at', sa.DateTime(), nullable=True),
        sa.Column('updated_at', sa.DateTime(), nullable=True),
        sa.Column('is_deleted', sa.Bo<PERSON>an(), nullable=True),
        sa.Column('user_id', sa.Integer(), nullable=False),
        sa.Column('contact_id', sa.Integer(), nullable=False),
        sa.Column('notes', sa.Text(), nullable=True),
        sa.ForeignKeyConstraint(['user_id'], ['user.id']),
        sa.ForeignKeyConstraint(['contact_id'], ['contact.id']),
        sa.UniqueConstraint('user_id', 'contact_id', name='uq_user_contact_lead'),
    )


def downgrade() -> None:
    op.drop_table('lead')
