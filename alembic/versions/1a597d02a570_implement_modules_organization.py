"""implement modules organization

Revision ID: 1a597d02a570
Revises: 4f79a0526fbe
Create Date: 2025-06-11 15:46:06.319372

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = '1a597d02a570'
down_revision: Union[str, None] = '4f79a0526fbe'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('organization', sa.Column('logo', sa.String(length=255), nullable=True))
    op.add_column('organization', sa.Column('owner_id', sa.Integer(), nullable=False))
    op.add_column('user', sa.Column('organization_id', sa.Integer(), nullable=True))
    op.create_foreign_key(None, 'user', 'organization', ['organization_id'], ['id'])
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_constraint(None, 'user', type_='foreignkey')
    op.drop_column('user', 'organization_id')
    op.drop_column('organization', 'owner_id')
    op.drop_column('organization', 'logo')
    # ### end Alembic commands ###
