"""initial migrations

Revision ID: c05f6a8cb385
Revises: 
Create Date: 2025-06-05 15:03:26.142812

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = 'c05f6a8cb385'
down_revision: Union[str, None] = None
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table('category',
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('name', sa.String(length=255), nullable=False),
    sa.Column('description', sa.Text(), nullable=True),
    sa.Column('parent_id', sa.Integer(), nullable=True),
    sa.Column('created_at', sa.DateTime(), nullable=True),
    sa.Column('updated_at', sa.DateTime(), nullable=True),
    sa.Column('is_deleted', sa.<PERSON>(), nullable=True),
    sa.ForeignKeyConstraint(['parent_id'], ['category.id'], ),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_table('gift_category',
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('name', sa.String(length=255), nullable=False),
    sa.Column('description', sa.Text(), nullable=True),
    sa.Column('created_at', sa.DateTime(), nullable=True),
    sa.Column('updated_at', sa.DateTime(), nullable=True),
    sa.Column('is_deleted', sa.Boolean(), nullable=True),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_gift_category_id'), 'gift_category', ['id'], unique=False)
    op.create_table('news',
    sa.Column('title', sa.String(length=256), nullable=False),
    sa.Column('short_description', sa.Text(), nullable=True),
    sa.Column('content', sa.Text(), nullable=True),
    sa.Column('banner', sa.Text(), nullable=True),
    sa.Column('images', sa.JSON(), nullable=True),
    sa.Column('posted_date', sa.DateTime(), nullable=False),
    sa.Column('id', sa.Integer(), autoincrement=True, nullable=False),
    sa.Column('created_at', sa.DateTime(), nullable=True),
    sa.Column('updated_at', sa.DateTime(), nullable=True),
    sa.Column('is_deleted', sa.Boolean(), nullable=True),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_news_id'), 'news', ['id'], unique=False)
    op.create_index(op.f('ix_news_posted_date'), 'news', ['posted_date'], unique=False)
    op.create_index(op.f('ix_news_title'), 'news', ['title'], unique=False)
    op.create_table('sms_history',
    sa.Column('payload_data', sa.JSON(), nullable=True),
    sa.Column('phone_number', sa.String(length=256), nullable=True),
    sa.Column('message', sa.Text(), nullable=True),
    sa.Column('id', sa.Integer(), autoincrement=True, nullable=False),
    sa.Column('created_at', sa.DateTime(), nullable=True),
    sa.Column('updated_at', sa.DateTime(), nullable=True),
    sa.Column('is_deleted', sa.Boolean(), nullable=True),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_sms_history_id'), 'sms_history', ['id'], unique=False)
    op.create_table('topic',
    sa.Column('name', sa.String(length=255), nullable=True),
    sa.Column('description', sa.String(length=255), nullable=True),
    sa.Column('id', sa.Integer(), autoincrement=True, nullable=False),
    sa.Column('created_at', sa.DateTime(), nullable=True),
    sa.Column('updated_at', sa.DateTime(), nullable=True),
    sa.Column('is_deleted', sa.Boolean(), nullable=True),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_topic_id'), 'topic', ['id'], unique=False)
    op.create_index(op.f('ix_topic_name'), 'topic', ['name'], unique=False)
    op.create_table('user',
    sa.Column('phone_number', sa.String(length=255), nullable=True),
    sa.Column('user_name', sa.String(length=50), nullable=True),
    sa.Column('email', sa.String(length=50), nullable=True),
    sa.Column('password', sa.Text(), nullable=True),
    sa.Column('avatar', sa.Text(), nullable=True),
    sa.Column('user_type', sa.Enum('ADMIN', 'USER', 'MERCHANT', name='userroleenum'), nullable=False),
    sa.Column('is_active', sa.Boolean(), nullable=True),
    sa.Column('is_admin', sa.Boolean(), nullable=True),
    sa.Column('last_login', sa.DateTime(), nullable=True),
    sa.Column('qr_code', sa.Text(), nullable=True),
    sa.Column('id', sa.Integer(), autoincrement=True, nullable=False),
    sa.Column('created_at', sa.DateTime(), nullable=True),
    sa.Column('updated_at', sa.DateTime(), nullable=True),
    sa.Column('is_deleted', sa.Boolean(), nullable=True),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_user_email'), 'user', ['email'], unique=True)
    op.create_index(op.f('ix_user_id'), 'user', ['id'], unique=False)
    op.create_index(op.f('ix_user_phone_number'), 'user', ['phone_number'], unique=False)
    op.create_index(op.f('ix_user_user_name'), 'user', ['user_name'], unique=False)
    op.create_table('businessprofile',
    sa.Column('user_id', sa.Integer(), nullable=False),
    sa.Column('business_name', sa.String(length=255), nullable=True),
    sa.Column('business_ein', sa.String(length=255), nullable=True),
    sa.Column('business_address', sa.String(length=255), nullable=True),
    sa.Column('business_description', sa.Text(), nullable=True),
    sa.Column('opening_hours', sa.JSON(), nullable=True),
    sa.Column('industry_tags', sa.JSON(), nullable=True),
    sa.Column('about', sa.Text(), nullable=True),
    sa.Column('services', sa.JSON(), nullable=True),
    sa.Column('id', sa.Integer(), autoincrement=True, nullable=False),
    sa.Column('created_at', sa.DateTime(), nullable=True),
    sa.Column('updated_at', sa.DateTime(), nullable=True),
    sa.Column('is_deleted', sa.Boolean(), nullable=True),
    sa.ForeignKeyConstraint(['user_id'], ['user.id'], ),
    sa.PrimaryKeyConstraint('id'),
    sa.UniqueConstraint('user_id')
    )
    op.create_index(op.f('ix_businessprofile_id'), 'businessprofile', ['id'], unique=False)
    op.create_table('chat_room',
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('name', sa.String(length=255), nullable=True),
    sa.Column('is_group', sa.Boolean(), nullable=True),
    sa.Column('created_by', sa.Integer(), nullable=False),
    sa.Column('created_at', sa.DateTime(), nullable=True),
    sa.Column('room_name', sa.String(length=255), nullable=True),
    sa.Column('room_avatar', sa.String(length=255), nullable=True),
    sa.Column('updated_at', sa.DateTime(), nullable=True),
    sa.Column('is_deleted', sa.Boolean(), nullable=True),
    sa.ForeignKeyConstraint(['created_by'], ['user.id'], ),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_table('contact',
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('user_id', sa.Integer(), nullable=False),
    sa.Column('user_contact_id', sa.Integer(), nullable=True),
    sa.Column('nickname', sa.String(length=100), nullable=True),
    sa.Column('notes', sa.Text(), nullable=True),
    sa.Column('tags', sa.Text(), nullable=True),
    sa.Column('is_favorite', sa.Boolean(), nullable=True),
    sa.Column('last_contact_date', sa.DateTime(), nullable=True),
    sa.Column('created_at', sa.DateTime(), nullable=True),
    sa.Column('updated_at', sa.DateTime(), nullable=True),
    sa.Column('is_deleted', sa.Boolean(), nullable=True),
    sa.ForeignKeyConstraint(['user_contact_id'], ['user.id'], ),
    sa.ForeignKeyConstraint(['user_id'], ['user.id'], ),
    sa.PrimaryKeyConstraint('id'),
    sa.UniqueConstraint('user_id', 'user_contact_id', name='uq_user_contact')
    )
    op.create_table('contact_requests',
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('from_user_id', sa.Integer(), nullable=True),
    sa.Column('to_user_id', sa.Integer(), nullable=True),
    sa.Column('status', sa.Enum('PENDING', 'APPROVED', 'REJECTED', name='contactrequeststatusenum'), nullable=True),
    sa.Column('message', sa.Text(), nullable=True),
    sa.Column('created_at', sa.DateTime(), nullable=True),
    sa.Column('updated_at', sa.DateTime(), nullable=True),
    sa.Column('is_deleted', sa.Boolean(), nullable=True),
    sa.ForeignKeyConstraint(['from_user_id'], ['user.id'], ),
    sa.ForeignKeyConstraint(['to_user_id'], ['user.id'], ),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_contact_requests_id'), 'contact_requests', ['id'], unique=False)
    op.create_table('event',
    sa.Column('title', sa.String(length=256), nullable=False),
    sa.Column('description', sa.Text(), nullable=True),
    sa.Column('start_time', sa.DateTime(), nullable=False),
    sa.Column('end_time', sa.DateTime(), nullable=False),
    sa.Column('event_type', sa.Enum('MEETING', 'CALL', 'TASK', 'WEBINAR', 'ONEONONE', 'REMINDER', 'OTHER', name='eventtypeenum'), nullable=False),
    sa.Column('status', sa.Enum('UPCOMING', 'SCHEDULED', 'IN_PROGRESS', 'COMPLETED', 'CANCELLED', 'POSTPONED', name='eventstatusenum'), nullable=False),
    sa.Column('location', sa.String(length=256), nullable=True),
    sa.Column('notes', sa.Text(), nullable=True),
    sa.Column('lattitude', sa.Float(), nullable=True),
    sa.Column('longitude', sa.Float(), nullable=True),
    sa.Column('linked_entity_type', sa.Enum('USER', 'CONTACT', 'LISTING', name='entitytypeenum'), nullable=True),
    sa.Column('is_public', sa.Boolean(), nullable=True),
    sa.Column('created_by', sa.Integer(), nullable=False),
    sa.Column('id', sa.Integer(), autoincrement=True, nullable=False),
    sa.Column('created_at', sa.DateTime(), nullable=True),
    sa.Column('updated_at', sa.DateTime(), nullable=True),
    sa.Column('is_deleted', sa.Boolean(), nullable=True),
    sa.ForeignKeyConstraint(['created_by'], ['user.id'], ),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_event_id'), 'event', ['id'], unique=False)
    op.create_table('fcm_tokens',
    sa.Column('device_id', sa.String(length=255), nullable=True),
    sa.Column('fcm_token', sa.String(length=255), nullable=True),
    sa.Column('user_id', sa.Integer(), nullable=True),
    sa.Column('id', sa.Integer(), autoincrement=True, nullable=False),
    sa.Column('created_at', sa.DateTime(), nullable=True),
    sa.Column('updated_at', sa.DateTime(), nullable=True),
    sa.Column('is_deleted', sa.Boolean(), nullable=True),
    sa.ForeignKeyConstraint(['user_id'], ['user.id'], ),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_fcm_tokens_device_id'), 'fcm_tokens', ['device_id'], unique=False)
    op.create_index(op.f('ix_fcm_tokens_fcm_token'), 'fcm_tokens', ['fcm_token'], unique=False)
    op.create_index(op.f('ix_fcm_tokens_id'), 'fcm_tokens', ['id'], unique=False)
    op.create_table('gift',
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('name', sa.String(length=255), nullable=False),
    sa.Column('image', sa.String(length=255), nullable=False),
    sa.Column('description', sa.Text(), nullable=True),
    sa.Column('category_id', sa.Integer(), nullable=False),
    sa.Column('created_at', sa.DateTime(), nullable=True),
    sa.Column('updated_at', sa.DateTime(), nullable=True),
    sa.Column('is_deleted', sa.Boolean(), nullable=True),
    sa.ForeignKeyConstraint(['category_id'], ['gift_category.id'], ),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_gift_id'), 'gift', ['id'], unique=False)
    op.create_table('notifications',
    sa.Column('title', sa.String(length=255), nullable=True),
    sa.Column('content', sa.String(length=255), nullable=True),
    sa.Column('user_id', sa.Integer(), nullable=True),
    sa.Column('is_read', sa.Boolean(), nullable=True),
    sa.Column('id', sa.Integer(), autoincrement=True, nullable=False),
    sa.Column('created_at', sa.DateTime(), nullable=True),
    sa.Column('updated_at', sa.DateTime(), nullable=True),
    sa.Column('is_deleted', sa.Boolean(), nullable=True),
    sa.ForeignKeyConstraint(['user_id'], ['user.id'], ),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_notifications_id'), 'notifications', ['id'], unique=False)
    op.create_index(op.f('ix_notifications_title'), 'notifications', ['title'], unique=False)
    op.create_table('product',
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('name', sa.String(length=255), nullable=False),
    sa.Column('description', sa.Text(), nullable=True),
    sa.Column('price', sa.Float(), nullable=False),
    sa.Column('thumbnail', sa.String(length=255), nullable=True),
    sa.Column('type', sa.Enum('PRODUCT', 'SERVICE', name='producttypeenum'), nullable=True),
    sa.Column('category_id', sa.Integer(), nullable=False),
    sa.Column('user_id', sa.Integer(), nullable=False),
    sa.Column('created_at', sa.DateTime(), nullable=True),
    sa.Column('updated_at', sa.DateTime(), nullable=True),
    sa.Column('is_deleted', sa.Boolean(), nullable=True),
    sa.ForeignKeyConstraint(['category_id'], ['category.id'], ),
    sa.ForeignKeyConstraint(['user_id'], ['user.id'], ),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_table('token',
    sa.Column('access_token', sa.Text(), nullable=False),
    sa.Column('refresh_token', sa.Text(), nullable=False),
    sa.Column('is_expired', sa.Boolean(), nullable=True),
    sa.Column('expire_at', sa.DateTime(), nullable=True),
    sa.Column('ip_address', sa.Text(), nullable=True),
    sa.Column('user_agent', sa.Text(), nullable=True),
    sa.Column('device_id', sa.Text(), nullable=True),
    sa.Column('user_id', sa.Integer(), nullable=True),
    sa.Column('id', sa.Integer(), autoincrement=True, nullable=False),
    sa.Column('created_at', sa.DateTime(), nullable=True),
    sa.Column('updated_at', sa.DateTime(), nullable=True),
    sa.Column('is_deleted', sa.Boolean(), nullable=True),
    sa.ForeignKeyConstraint(['user_id'], ['user.id'], ),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_token_id'), 'token', ['id'], unique=False)
    op.create_table('user_integration',
    sa.Column('user_id', sa.Integer(), nullable=True),
    sa.Column('google_calendar', sa.Boolean(), nullable=True),
    sa.Column('google_access_token', sa.Text(), nullable=True),
    sa.Column('google_refresh_token', sa.Text(), nullable=True),
    sa.Column('apple_access_token', sa.Text(), nullable=True),
    sa.Column('apple_refresh_token', sa.Text(), nullable=True),
    sa.Column('linkedin_connection', sa.Boolean(), nullable=True),
    sa.Column('linkedin_access_token', sa.Text(), nullable=True),
    sa.Column('linkedin_refresh_token', sa.Text(), nullable=True),
    sa.Column('id', sa.Integer(), autoincrement=True, nullable=False),
    sa.Column('created_at', sa.DateTime(), nullable=True),
    sa.Column('updated_at', sa.DateTime(), nullable=True),
    sa.Column('is_deleted', sa.Boolean(), nullable=True),
    sa.ForeignKeyConstraint(['user_id'], ['user.id'], ),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_user_integration_id'), 'user_integration', ['id'], unique=False)
    op.create_table('user_notification_settings',
    sa.Column('user_id', sa.Integer(), nullable=True),
    sa.Column('rsvp_reminders', sa.Boolean(), nullable=True),
    sa.Column('contact_nearby_alerts', sa.Boolean(), nullable=True),
    sa.Column('follow_up_nudges', sa.Boolean(), nullable=True),
    sa.Column('gift_delivery_confirmations', sa.Boolean(), nullable=True),
    sa.Column('id', sa.Integer(), autoincrement=True, nullable=False),
    sa.Column('created_at', sa.DateTime(), nullable=True),
    sa.Column('updated_at', sa.DateTime(), nullable=True),
    sa.Column('is_deleted', sa.Boolean(), nullable=True),
    sa.ForeignKeyConstraint(['user_id'], ['user.id'], ),
    sa.PrimaryKeyConstraint('id'),
    sa.UniqueConstraint('user_id')
    )
    op.create_index(op.f('ix_user_notification_settings_id'), 'user_notification_settings', ['id'], unique=False)
    op.create_table('user_otps',
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('user_id', sa.Integer(), nullable=False),
    sa.Column('otp_code', sa.String(length=6), nullable=False),
    sa.Column('otp_type', sa.Enum('USER_VERIFICATION', 'FORGOT_PASSWORD', name='otptypeenum'), nullable=False),
    sa.Column('is_used', sa.Boolean(), nullable=True),
    sa.Column('expires_at', sa.DateTime(), nullable=False),
    sa.Column('resend_count', sa.Integer(), nullable=True),
    sa.Column('created_at', sa.DateTime(), nullable=True),
    sa.Column('updated_at', sa.DateTime(), nullable=True),
    sa.Column('is_deleted', sa.Boolean(), nullable=True),
    sa.ForeignKeyConstraint(['user_id'], ['user.id'], ),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_user_otps_id'), 'user_otps', ['id'], unique=False)
    op.create_table('user_subscription_settings',
    sa.Column('user_id', sa.Integer(), nullable=True),
    sa.Column('current_plan', sa.String(length=50), nullable=True),
    sa.Column('billing_cycle', sa.String(length=50), nullable=True),
    sa.Column('next_billing_date', sa.DateTime(), nullable=True),
    sa.Column('team_members_limit', sa.Integer(), nullable=True),
    sa.Column('id', sa.Integer(), autoincrement=True, nullable=False),
    sa.Column('created_at', sa.DateTime(), nullable=True),
    sa.Column('updated_at', sa.DateTime(), nullable=True),
    sa.Column('is_deleted', sa.Boolean(), nullable=True),
    sa.ForeignKeyConstraint(['user_id'], ['user.id'], ),
    sa.PrimaryKeyConstraint('id'),
    sa.UniqueConstraint('user_id')
    )
    op.create_index(op.f('ix_user_subscription_settings_id'), 'user_subscription_settings', ['id'], unique=False)
    op.create_table('user_topic',
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('topic_id', sa.Integer(), nullable=False),
    sa.Column('user_id', sa.Integer(), nullable=False),
    sa.Column('created_at', sa.DateTime(), nullable=True),
    sa.Column('updated_at', sa.DateTime(), nullable=True),
    sa.Column('is_deleted', sa.Boolean(), nullable=True),
    sa.ForeignKeyConstraint(['topic_id'], ['topic.id'], ),
    sa.ForeignKeyConstraint(['user_id'], ['user.id'], ),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_user_topic_id'), 'user_topic', ['id'], unique=False)
    op.create_table('user_web_link_settings',
    sa.Column('user_id', sa.Integer(), nullable=True),
    sa.Column('public_profile_link', sa.String(length=255), nullable=True),
    sa.Column('active_invite_pages', sa.Text(), nullable=True),
    sa.Column('gift_claim_link', sa.String(length=255), nullable=True),
    sa.Column('id', sa.Integer(), autoincrement=True, nullable=False),
    sa.Column('created_at', sa.DateTime(), nullable=True),
    sa.Column('updated_at', sa.DateTime(), nullable=True),
    sa.Column('is_deleted', sa.Boolean(), nullable=True),
    sa.ForeignKeyConstraint(['user_id'], ['user.id'], ),
    sa.PrimaryKeyConstraint('id'),
    sa.UniqueConstraint('gift_claim_link'),
    sa.UniqueConstraint('public_profile_link'),
    sa.UniqueConstraint('user_id')
    )
    op.create_index(op.f('ix_user_web_link_settings_id'), 'user_web_link_settings', ['id'], unique=False)
    op.create_table('userprofile',
    sa.Column('full_name', sa.String(length=255), nullable=True),
    sa.Column('gender', sa.String(length=255), nullable=True),
    sa.Column('date_of_birth', sa.Date(), nullable=True),
    sa.Column('social_media_links', sa.JSON(), nullable=True),
    sa.Column('bio', sa.Text(), nullable=True),
    sa.Column('notes', sa.Text(), nullable=True),
    sa.Column('public_profile', sa.Boolean(), nullable=True),
    sa.Column('address', sa.String(length=255), nullable=True),
    sa.Column('lattitude', sa.Float(), nullable=True),
    sa.Column('longitude', sa.Float(), nullable=True),
    sa.Column('skill_interests', sa.JSON(), nullable=True),
    sa.Column('industry_tags', sa.JSON(), nullable=True),
    sa.Column('user_id', sa.Integer(), nullable=False),
    sa.Column('id', sa.Integer(), autoincrement=True, nullable=False),
    sa.Column('created_at', sa.DateTime(), nullable=True),
    sa.Column('updated_at', sa.DateTime(), nullable=True),
    sa.Column('is_deleted', sa.Boolean(), nullable=True),
    sa.ForeignKeyConstraint(['user_id'], ['user.id'], ),
    sa.PrimaryKeyConstraint('id'),
    sa.UniqueConstraint('user_id')
    )
    op.create_index(op.f('ix_userprofile_id'), 'userprofile', ['id'], unique=False)
    op.create_table('chat_room_member',
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('room_id', sa.Integer(), nullable=False),
    sa.Column('user_id', sa.Integer(), nullable=False),
    sa.Column('created_at', sa.DateTime(), nullable=True),
    sa.Column('updated_at', sa.DateTime(), nullable=True),
    sa.Column('is_deleted', sa.Boolean(), nullable=True),
    sa.ForeignKeyConstraint(['room_id'], ['chat_room.id'], ),
    sa.ForeignKeyConstraint(['user_id'], ['user.id'], ),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_table('event_interaction',
    sa.Column('event_id', sa.Integer(), nullable=True),
    sa.Column('user_id', sa.Integer(), nullable=True),
    sa.Column('interaction_type', sa.String(length=50), nullable=True),
    sa.Column('description', sa.Text(), nullable=True),
    sa.Column('date', sa.DateTime(), nullable=True),
    sa.Column('id', sa.Integer(), autoincrement=True, nullable=False),
    sa.Column('created_at', sa.DateTime(), nullable=True),
    sa.Column('updated_at', sa.DateTime(), nullable=True),
    sa.Column('is_deleted', sa.Boolean(), nullable=True),
    sa.ForeignKeyConstraint(['event_id'], ['event.id'], ),
    sa.ForeignKeyConstraint(['user_id'], ['user.id'], ),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_event_interaction_id'), 'event_interaction', ['id'], unique=False)
    op.create_table('event_invitation',
    sa.Column('event_id', sa.Integer(), nullable=False),
    sa.Column('contact_id', sa.Integer(), nullable=False),
    sa.Column('status', sa.Enum('PENDING', 'ACCEPTED', 'DECLINED', 'MAYBE', 'NOT_RESPONDED', name='rsvpstatusenum'), nullable=True),
    sa.Column('response_message', sa.Text(), nullable=True),
    sa.Column('number_of_guests', sa.Integer(), nullable=True),
    sa.Column('dietary_restrictions', sa.Text(), nullable=True),
    sa.Column('additional_notes', sa.Text(), nullable=True),
    sa.Column('response_date', sa.DateTime(), nullable=True),
    sa.Column('response_note', sa.Text(), nullable=True),
    sa.Column('uuid', sa.String(length=256), nullable=False),
    sa.Column('is_public', sa.Boolean(), nullable=True),
    sa.Column('id', sa.Integer(), autoincrement=True, nullable=False),
    sa.Column('created_at', sa.DateTime(), nullable=True),
    sa.Column('updated_at', sa.DateTime(), nullable=True),
    sa.Column('is_deleted', sa.Boolean(), nullable=True),
    sa.ForeignKeyConstraint(['contact_id'], ['contact.id'], ),
    sa.ForeignKeyConstraint(['event_id'], ['event.id'], ),
    sa.PrimaryKeyConstraint('id'),
    sa.UniqueConstraint('event_id', 'contact_id', name='uq_event_contact_rsvp')
    )
    op.create_index(op.f('ix_event_invitation_id'), 'event_invitation', ['id'], unique=False)
    op.create_table('message',
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('room_id', sa.Integer(), nullable=False),
    sa.Column('sender_id', sa.Integer(), nullable=False),
    sa.Column('content', sa.Text(), nullable=False),
    sa.Column('message_type', sa.Enum('TEXT', 'FILE', name='messagetypeenum'), nullable=True),
    sa.Column('created_at', sa.DateTime(), nullable=True),
    sa.Column('updated_at', sa.DateTime(), nullable=True),
    sa.Column('is_deleted', sa.Boolean(), nullable=True),
    sa.ForeignKeyConstraint(['room_id'], ['chat_room.id'], ),
    sa.ForeignKeyConstraint(['sender_id'], ['user.id'], ),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_table('user_gift',
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('sender_id', sa.Integer(), nullable=False),
    sa.Column('receiver_id', sa.Integer(), nullable=False),
    sa.Column('gift_id', sa.Integer(), nullable=False),
    sa.Column('message', sa.Text(), nullable=True),
    sa.Column('sent_at', sa.DateTime(), nullable=True),
    sa.Column('created_at', sa.DateTime(), nullable=True),
    sa.Column('updated_at', sa.DateTime(), nullable=True),
    sa.Column('is_deleted', sa.Boolean(), nullable=True),
    sa.ForeignKeyConstraint(['gift_id'], ['gift.id'], ),
    sa.ForeignKeyConstraint(['receiver_id'], ['user.id'], ),
    sa.ForeignKeyConstraint(['sender_id'], ['user.id'], ),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_user_gift_id'), 'user_gift', ['id'], unique=False)
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_index(op.f('ix_user_gift_id'), table_name='user_gift')
    op.drop_table('user_gift')
    op.drop_table('message')
    op.drop_index(op.f('ix_event_invitation_id'), table_name='event_invitation')
    op.drop_table('event_invitation')
    op.drop_index(op.f('ix_event_interaction_id'), table_name='event_interaction')
    op.drop_table('event_interaction')
    op.drop_table('chat_room_member')
    op.drop_index(op.f('ix_userprofile_id'), table_name='userprofile')
    op.drop_table('userprofile')
    op.drop_index(op.f('ix_user_web_link_settings_id'), table_name='user_web_link_settings')
    op.drop_table('user_web_link_settings')
    op.drop_index(op.f('ix_user_topic_id'), table_name='user_topic')
    op.drop_table('user_topic')
    op.drop_index(op.f('ix_user_subscription_settings_id'), table_name='user_subscription_settings')
    op.drop_table('user_subscription_settings')
    op.drop_index(op.f('ix_user_otps_id'), table_name='user_otps')
    op.drop_table('user_otps')
    op.drop_index(op.f('ix_user_notification_settings_id'), table_name='user_notification_settings')
    op.drop_table('user_notification_settings')
    op.drop_index(op.f('ix_user_integration_id'), table_name='user_integration')
    op.drop_table('user_integration')
    op.drop_index(op.f('ix_token_id'), table_name='token')
    op.drop_table('token')
    op.drop_table('product')
    op.drop_index(op.f('ix_notifications_title'), table_name='notifications')
    op.drop_index(op.f('ix_notifications_id'), table_name='notifications')
    op.drop_table('notifications')
    op.drop_index(op.f('ix_gift_id'), table_name='gift')
    op.drop_table('gift')
    op.drop_index(op.f('ix_fcm_tokens_id'), table_name='fcm_tokens')
    op.drop_index(op.f('ix_fcm_tokens_fcm_token'), table_name='fcm_tokens')
    op.drop_index(op.f('ix_fcm_tokens_device_id'), table_name='fcm_tokens')
    op.drop_table('fcm_tokens')
    op.drop_index(op.f('ix_event_id'), table_name='event')
    op.drop_table('event')
    op.drop_index(op.f('ix_contact_requests_id'), table_name='contact_requests')
    op.drop_table('contact_requests')
    op.drop_table('contact')
    op.drop_table('chat_room')
    op.drop_index(op.f('ix_businessprofile_id'), table_name='businessprofile')
    op.drop_table('businessprofile')
    op.drop_index(op.f('ix_user_user_name'), table_name='user')
    op.drop_index(op.f('ix_user_phone_number'), table_name='user')
    op.drop_index(op.f('ix_user_id'), table_name='user')
    op.drop_index(op.f('ix_user_email'), table_name='user')
    op.drop_table('user')
    op.drop_index(op.f('ix_topic_name'), table_name='topic')
    op.drop_index(op.f('ix_topic_id'), table_name='topic')
    op.drop_table('topic')
    op.drop_index(op.f('ix_sms_history_id'), table_name='sms_history')
    op.drop_table('sms_history')
    op.drop_index(op.f('ix_news_title'), table_name='news')
    op.drop_index(op.f('ix_news_posted_date'), table_name='news')
    op.drop_index(op.f('ix_news_id'), table_name='news')
    op.drop_table('news')
    op.drop_index(op.f('ix_gift_category_id'), table_name='gift_category')
    op.drop_table('gift_category')
    op.drop_table('category')
    # ### end Alembic commands ###
