"""add table contact_dismiss_suggestion

Revision ID: d5d26968b8b0
Revises: 9a0be3d48092
Create Date: 2025-07-01 08:56:08.517228

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = 'd5d26968b8b0'
down_revision: Union[str, None] = '9a0be3d48092'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table('contact_dismiss_suggestion',
    sa.Column('id', sa.Integer(), nullable=False),
    sa.<PERSON>umn('user_id', sa.Integer(), nullable=False),
    sa.<PERSON>umn('user_dismiss_id', sa.Integer(), nullable=False),
    sa.<PERSON>umn('dismissed_to', sa.DateTime(), nullable=False),
    sa.Column('created_at', sa.DateTime(), nullable=True),
    sa.Column('updated_at', sa.DateTime(), nullable=True),
    sa.Column('is_deleted', sa.Boolean(), nullable=True),
    sa.PrimaryKeyConstraint('id')
    )
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_table('contact_dismiss_suggestion')
    # ### end Alembic commands ###
