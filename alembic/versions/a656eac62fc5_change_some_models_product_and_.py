"""change some models product and organization

Revision ID: a656eac62fc5
Revises: 1a597d02a570
Create Date: 2025-06-12 13:34:58.383758

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import mysql

# revision identifiers, used by Alembic.
revision: str = 'a656eac62fc5'
down_revision: Union[str, None] = '1a597d02a570'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('product', sa.Column('quantity', sa.Integer(), nullable=False))
    op.add_column('product', sa.Column('organization_id', sa.Integer(), nullable=False))
    op.create_unique_constraint('uq_organization_product_name', 'product', ['organization_id', 'name'])
    op.drop_constraint('product_ibfk_2', 'product', type_='foreignkey')
    op.create_foreign_key(None, 'product', 'organization', ['organization_id'], ['id'])
    op.drop_column('product', 'user_id')
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('product', sa.Column('user_id', mysql.INTEGER(), autoincrement=False, nullable=False))
    op.drop_constraint(None, 'product', type_='foreignkey')
    op.create_foreign_key('product_ibfk_2', 'product', 'user', ['user_id'], ['id'])
    op.drop_constraint('uq_organization_product_name', 'product', type_='unique')
    op.drop_column('product', 'organization_id')
    op.drop_column('product', 'quantity')
    # ### end Alembic commands ###
