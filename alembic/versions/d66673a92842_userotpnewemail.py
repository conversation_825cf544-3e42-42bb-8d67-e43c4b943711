"""UserOTPNewEmail

Revision ID: d66673a92842
Revises: e7b211349d91
Create Date: 2025-06-27 03:12:46.163918

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = 'd66673a92842'
down_revision: Union[str, None] = 'e7b211349d91'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('user_otps', sa.Column('new_email', sa.String(length=50), nullable=True))
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('user_otps', 'new_email')
    # ### end Alembic commands ###
