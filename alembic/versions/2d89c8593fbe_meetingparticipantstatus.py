"""MeetingParticipantStatus

Revision ID: 2d89c8593fbe
Revises: 8ac538e55b46
Create Date: 2025-06-26 08:49:07.727592

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = '2d89c8593fbe'
down_revision: Union[str, None] = '8ac538e55b46'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('meeting_participant', sa.Column('status', sa.Enum('INVITED', 'ACCEPTED', 'REJECTED', name='meetingparticipantstatusenum'), nullable=False))
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('meeting_participant', 'status')
    # ### end Alembic commands ###
