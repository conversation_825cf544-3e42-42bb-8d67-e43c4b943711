"""add biometric fields

Revision ID: 09a32b03606a
Revises: d5d26968b8b0
Create Date: 2025-07-02 10:32:21.678577

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = '09a32b03606a'
down_revision: Union[str, None] = 'd5d26968b8b0'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('user', sa.Column('enabled_touch_id', sa.<PERSON>an(), server_default=sa.text('0'), nullable=True))
    op.add_column('user', sa.Column('enabled_face_id', sa.<PERSON>(), server_default=sa.text('0'), nullable=True))
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('user', 'enabled_face_id')
    op.drop_column('user', 'enabled_touch_id')
    # ### end Alembic commands ###
