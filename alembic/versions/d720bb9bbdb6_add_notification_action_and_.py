"""add notification_action and notification_config

Revision ID: d720bb9bbdb6
Revises: 93b9d9c23a74
Create Date: 2025-07-03 11:19:30.640918

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import mysql

# revision identifiers, used by Alembic.
revision: str = 'd720bb9bbdb6'
down_revision: Union[str, None] = '93b9d9c23a74'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table('notification_action',
    sa.Column('module', sa.String(length=32), nullable=False),
    sa.Column('trigger_event', sa.String(length=64), nullable=False),
    sa.Column('is_merchant_only', sa.<PERSON>(), nullable=True),
    sa.Column('is_system_forced', sa.<PERSON>(), nullable=True),
    sa.Column('description', sa.Text(), nullable=True),
    sa.Column('default_push', sa.<PERSON>(), nullable=True),
    sa.Column('default_email', sa.Boolean(), nullable=True),
    sa.Column('id', sa.Integer(), autoincrement=True, nullable=False),
    sa.Column('created_at', sa.DateTime(), nullable=True),
    sa.Column('updated_at', sa.DateTime(), nullable=True),
    sa.Column('is_deleted', sa.Boolean(), nullable=True),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_notification_action_id'), 'notification_action', ['id'], unique=False)
    op.create_table('notification_config',
    sa.Column('user_id', sa.Integer(), nullable=False),
    sa.Column('action_id', sa.Integer(), nullable=False),
    sa.Column('push_enabled', sa.Boolean(), nullable=False),
    sa.Column('email_enabled', sa.Boolean(), nullable=False),
    sa.Column('id', sa.Integer(), autoincrement=True, nullable=False),
    sa.Column('created_at', sa.DateTime(), nullable=True),
    sa.Column('updated_at', sa.DateTime(), nullable=True),
    sa.Column('is_deleted', sa.Boolean(), nullable=True),
    sa.ForeignKeyConstraint(['action_id'], ['notification_action.id'], ),
    sa.ForeignKeyConstraint(['user_id'], ['user.id'], ),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_notification_config_id'), 'notification_config', ['id'], unique=False)
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_index(op.f('ix_notification_config_id'), table_name='notification_config')
    op.drop_table('notification_config')
    op.drop_index(op.f('ix_notification_action_id'), table_name='notification_action')
    op.drop_table('notification_action')
    # ### end Alembic commands ###
