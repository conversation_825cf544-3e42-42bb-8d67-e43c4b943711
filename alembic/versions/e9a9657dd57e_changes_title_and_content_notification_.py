"""changes title and content notification to json

Revision ID: e9a9657dd57e
Revises: d720bb9bbdb6
Create Date: 2025-07-04 04:25:31.090736

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import mysql

# revision identifiers, used by Alembic.
revision: str = 'e9a9657dd57e'
down_revision: Union[str, None] = 'd720bb9bbdb6'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_index('ix_notifications_title', table_name='notifications')
    op.alter_column('notifications', 'title',
               existing_type=mysql.VARCHAR(collation='utf8mb4_general_ci', length=255),
               type_=sa.JSON(),
               existing_nullable=True)
    op.alter_column('notifications', 'content',
               existing_type=mysql.VARCHAR(collation='utf8mb4_general_ci', length=255),
               type_=sa.JSON(),
               existing_nullable=True)
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.alter_column('notifications', 'content',
               existing_type=sa.JSON(),
               type_=mysql.VARCHAR(collation='utf8mb4_general_ci', length=255),
               existing_nullable=True)
    op.alter_column('notifications', 'title',
               existing_type=sa.JSON(),
               type_=mysql.VARCHAR(collation='utf8mb4_general_ci', length=255),
               existing_nullable=True)
    op.create_index('ix_notifications_title', 'notifications', ['title'], unique=False)
    # ### end Alembic commands ###
