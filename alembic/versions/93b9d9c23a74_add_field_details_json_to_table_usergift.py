"""add field details json to table UserGift

Revision ID: 93b9d9c23a74
Revises: 09a32b03606a
Create Date: 2025-07-03 07:44:10.221266

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = '93b9d9c23a74'
down_revision: Union[str, None] = '09a32b03606a'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('user_gift', sa.Column('details', sa.JSON(), nullable=True))
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('user_gift', 'details')
    # ### end Alembic commands ###
