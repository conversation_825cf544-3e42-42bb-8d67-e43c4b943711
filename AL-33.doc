<!DOCTYPE html>
<html>
<head>
    <title>[#AL-33] [MB - Meeting Scheduling] Accept/Reject Invite to Meet</title>
    <meta http-equiv="Content-Type" Content="application/vnd.ms-word; charset=UTF-8">
        <style type="text/css">

.tableBorder, .grid
{
    background-color: #fff;
    width: 100%;
    border-collapse: collapse;
}

.tableBorder td, .grid td
{
    vertical-align: top;
    padding: 2px;
    border: 1px solid #ccc;
}

.noPadding
{
    padding: 0 !important;
}

h3 .subText
{
    font-size: 60%;
    font-weight: normal;
}

.tabLabel
{
    font-weight: bold;
    border: 1px solid #ccc;
    border-bottom:none;
    padding: 2px;
    display: inline;
}

td.blank
{
    padding: 0;
    margin: 0;
}

.blank td
{
    border: none;
}

#descriptionArea
{
    margin: 0;
    padding: 2px;
    border: 1px solid #ccc;
}

hr
{
    border-top:1px solid #aaa;
}

hr.fullcontent
{
  height: 15px;
  padding: 10px 0;
  background: #fff url('https://exnodes.atlassian.net/images/icons/hr.gif') no-repeat scroll center;
}

</style>

</head>
<body>

<table class="tableBorder" cellpadding="0" cellspacing="0" border="0" width="100%">
    <tr>
        <td bgcolor="#f0f0f0" width="100%" colspan="2" valign="top">
                            <b><a id="parent_issue_summary" href="https://exnodes.atlassian.net/browse/AL-68">Meeting Scheduling</a></b>
                <span style="font-size: 9px">(<a id="parent_issue_key" href="https://exnodes.atlassian.net/browse/AL-68">AL-68</a>)</span>
                <h3 class="formtitle"><img src="https://exnodes.atlassian.net/images/icons/link_out_bot.gif" width="16" height="16" />
                        [AL-33]&nbsp;<a href="https://exnodes.atlassian.net/browse/AL-33">[MB - Meeting Scheduling] Accept/Reject Invite to Meet</a>
            <span class="subText">
               Created: 04/Jun/25                   &nbsp;Updated: 25/Jun/25

                                            </span>
            </h3>
        </td>
    </tr>
    <tr>
        <td width="20%"><b>Status:</b></td>
        <td width="80%">In Progress</td>
    </tr>
    <tr>
        <td width="20%"><b>Project:</b></td>
        <td width="80%"><a href="https://exnodes.atlassian.net/secure/BrowseProject.jspa?id=10842">AIOS Link</a></td>
    </tr>

        <tr>
            <td><b>Components:</b></td>
            <td>
                            None
                </td>
    </tr>
    

        <tr>
            <td><b>Affects versions:</b></td>
            <td>
                            None
                </td>
    </tr>
    

        <tr>
            <td><b>Fix versions:</b></td>
            <td>
                            None
                </td>
    </tr>
    
    
            <td width="20%"><b>Parent:</b></td>
        <td width="80%"><a href="https://exnodes.atlassian.net/browse/AL-68">Meeting Scheduling</a></td>
    </table>

<br />
<table class="grid" cellpadding="0" cellspacing="0" border="0" width="100%">
    <tr>
        <td bgcolor="#f0f0f0" valign="top" width="20%">
            <b>Type:</b>
        </td>
        <td bgcolor="#ffffff" valign="top"  width="30%" >
            Story
        </td>

                    <td bgcolor="#f0f0f0">
                <b>Priority:</b>
            </td>
            <td bgcolor="#ffffff" valign="top" nowrap>
                Medium
            </td>
            </tr>
    <tr>
                        <td bgcolor="#f0f0f0" valign="top" width="20%">
                <b>Reporter:</b>
            </td>
            <td bgcolor="#ffffff" valign="top"  width="30%" >
                                        
                
        
            <a class="user-hover" rel="712020:59b66c6e-1068-441c-9436-130289e16a26"
                id="word_reporter_712020:59b66c6e-1068-441c-9436-130289e16a26"
                href="https://exnodes.atlassian.net/secure/ViewProfile.jspa?accountId=712020%3A59b66c6e-1068-441c-9436-130289e16a26">
                    Duyen Nguyen
                </a>

                            </td>
        
                    <td bgcolor="#f0f0f0" width="20%">
                <b>Assignee:</b>
            </td>
            <td bgcolor="#ffffff" valign="top" nowrap  width="30%" >
                                    Unassigned
                            </td>
            </tr>
    	<tr>
		<td bgcolor="#f0f0f0" width="20%">
			<b>Resolution:</b>
		</td>
		<td bgcolor="#ffffff" valign="top" width="30%" nowrap>
            				Unresolved
                    </td>
                    <td bgcolor="#f0f0f0" width="20%">
                <b>Votes:</b>
            </td>
            <td bgcolor="#ffffff" valign="top" width="30%" nowrap>
                0
            </td>
        
    </tr>
    
        <tr>
        <td bgcolor="#f0f0f0" width="20%">
            <b>Labels:</b>
        </td>
        <td id="labels-37362-value" class="value" bgcolor="#ffffff" valign="top" colspan="3" nowrap>
                            None
                    </td>
    </tr>
    
    	<tr>
        		<td bgcolor="#f0f0f0" width="20%"><b>Remaining Estimate:</b></td>
        <td bgcolor="#ffffff" valign="top" nowrap width="80%" colspan="3">
                            Not Specified
            		</td>
    </tr>
    <tr>
                <td bgcolor="#f0f0f0" width="20%"><b>Time Spent:</b></td>
		<td bgcolor="#ffffff" valign="top" nowrap width="80%" colspan="3">
                            Not Specified
            		</td>
	</tr>
    <tr>
                <td bgcolor="#f0f0f0" width="20%"><b>Original estimate:</b></td>
		<td bgcolor="#ffffff" valign="top" nowrap width="80%" colspan="3">
                            Not Specified
            		</td>
    </tr>
    
    </table>



    <br />

    	<table class="grid" cellpadding="0" cellspacing="0" border="0" width="100%">
            

        
    


    
                        <tr>
                <td bgcolor="#f0f0f0" width="20%" valign="top"><b>Sprint:</b></td>
                <td id="customfield_10020-37362-value" class="value" bgcolor="#ffffff" width="80%"></td>
            </tr>
                                <tr>
                <td bgcolor="#f0f0f0" width="20%" valign="top"><b>Platform:</b></td>
                <td id="customfield_10687-37362-value" class="value" bgcolor="#ffffff" width="80%">    MOBILE</td>
            </tr>
            </table>

    <br/>

    <table cellpadding="2" cellspacing="0" border="0" width="100%" align="center">
    <tr>
        <td bgcolor="#bbbbbb" width="1%" nowrap align="center">
            &nbsp;<font color="#ffffff"><b>Description</b></font>&nbsp;
        </td>
        <td>&nbsp;</td>
    </tr>
    </table>

    <table cellpadding="0" cellspacing="0" border="0" width="100%">
    <tr>
        <td id="descriptionArea">
            <h3><a name="1.UseCaseDescription"></a>1. Use Case Description</h3>

<p>As a user who receives a meeting invitation, I want to be able to accept or reject the invite, so that I can control my availability and confirm or decline meetings while notifying the inviter accordingly.</p>

<hr />

<h3><a name="2.UserWorkflow%28StepbyStep%29"></a>2. User Workflow (Step-by-Step)</h3>

<ol>
	<li>User receives an <b>Invite to Meet</b> notification or sees the invite in the inbox/activity feed.</li>
	<li>User taps the invitation to view the meeting details (date, time, contact, meeting type).</li>
	<li>User chooses either:
	<ul>
		<li><b>Accept Invite</b> → Meeting is added to user’s schedule, and a confirmation is sent to the inviter.</li>
		<li><b>Reject Invite</b> → A rejection message is sent to the inviter; no meeting is created.</li>
	</ul>
	</li>
	<li>System displays confirmation toast and returns to previous screen.</li>
</ol>


<hr />

<h3><a name="3.FieldDefinitionsTable"></a>3. Field Definitions Table</h3>

<h4><a name="InteractionElements"></a>Interaction Elements</h4>

<div class='table-wrap'>
<table class='confluenceTable'><tbody>
<tr>
<th class='confluenceTh'>Element Name</th>
<th class='confluenceTh'>Type</th>
<th class='confluenceTh'>Trigger Condition</th>
<th class='confluenceTh'>Description</th>
</tr>
<tr>
<td class='confluenceTd'>Accept Invite</td>
<td class='confluenceTd'>Button</td>
<td class='confluenceTd'>Always visible</td>
<td class='confluenceTd'>Confirms the meeting and adds to schedule</td>
</tr>
<tr>
<td class='confluenceTd'>Reject Invite</td>
<td class='confluenceTd'>Button</td>
<td class='confluenceTd'>Always visible</td>
<td class='confluenceTd'>Declines the invite and notifies inviter</td>
</tr>
<tr>
<td class='confluenceTd'>Invite Details</td>
<td class='confluenceTd'>Card View</td>
<td class='confluenceTd'>On invitation open</td>
<td class='confluenceTd'>Displays time, location/link, sender, type</td>
</tr>
</tbody></table>
</div>


<hr />

<h3><a name="4.DataDisplayTable%28CardView%29"></a>4. Data Display Table (Card View)</h3>

<div class='table-wrap'>
<table class='confluenceTable'><tbody>
<tr>
<th class='confluenceTh'>Data Name</th>
<th class='confluenceTh'>Data Type</th>
<th class='confluenceTh'>Display When Empty</th>
<th class='confluenceTh'>Format</th>
<th class='confluenceTh'>Description</th>
</tr>
<tr>
<td class='confluenceTd'>Meeting Type</td>
<td class='confluenceTd'>Text</td>
<td class='confluenceTd'>“TBD”</td>
<td class='confluenceTd'>“In Person”/“Virtual”</td>
<td class='confluenceTd'>Displayed on invite detail screen</td>
</tr>
<tr>
<td class='confluenceTd'>Meeting Location</td>
<td class='confluenceTd'>Text/Link</td>
<td class='confluenceTd'>“Not Provided”</td>
<td class='confluenceTd'>Address or URL</td>
<td class='confluenceTd'>Based on invite type</td>
</tr>
<tr>
<td class='confluenceTd'>Sender Name</td>
<td class='confluenceTd'>Text</td>
<td class='confluenceTd'>“Unknown”</td>
<td class='confluenceTd'>Plaintext</td>
<td class='confluenceTd'>Who sent the invite</td>
</tr>
<tr>
<td class='confluenceTd'>Date &amp; Time</td>
<td class='confluenceTd'>Datetime</td>
<td class='confluenceTd'>“Pending”</td>
<td class='confluenceTd'>Relative format</td>
<td class='confluenceTd'>Scheduled time of meeting</td>
</tr>
</tbody></table>
</div>


<hr />

<h3><a name="5.AcceptanceCriteria"></a>5. Acceptance Criteria</h3>

<ul>
	<li><b>AC1</b>: User must be able to accept or reject an invitation from both push notification and in-app interface.</li>
	<li><b>AC2</b>: Accepting an invite automatically creates a calendar/schedule entry for the recipient.</li>
	<li><b>AC3</b>: Inviter receives a confirmation notification upon acceptance.</li>
	<li><b>AC4</b>: Rejecting an invite sends a rejection notification to the inviter.</li>
	<li><b>AC5</b>: No schedule is created if invite is rejected.</li>
	<li><b>AC6</b>: User cannot accept an expired or revoked invite (system should handle gracefully).</li>
</ul>


<hr />

<h3><a name="6.SystemRules"></a>6. System Rules</h3>

<ul>
	<li>Schedule entries are bi-directional: both users see the confirmed meeting.</li>
	<li>Notifications must indicate invite status (“Accepted”, “Rejected”) and show sender/recipient.</li>
	<li>Once an invite is responded to, response buttons are disabled or replaced with status tag.</li>
	<li>All responses are logged in the system for audit trail.</li>
</ul>


<hr />

<h3><a name="7.UXOptimizations"></a>7. UX Optimizations</h3>

<ul>
	<li>Use icons for Accept (✓) and Reject (✕) with clear color cues (green/red).</li>
	<li>Confirmation toast:
	<ul>
		<li>On accept: <em>“You’ve accepted the invite. Meeting added to your schedule.”</em></li>
		<li>On reject: <em>“You’ve declined the invite. Notification sent to <span class="error">&#91;Sender Name&#93;</span>.”</em></li>
	</ul>
	</li>
	<li>Inline feedback: “Awaiting response” badge replaced with “Accepted” or “Rejected”.</li>
	<li>Allow tapping sender’s name to view their profile before deciding.</li>
	<li>Provide optional comments on rejection (future enhancement).</li>
</ul>

            <br/>
        </td>
    </tr>
    </table>

Generated at Thu Jun 26 09:22:22 UTC 2025 by Kid Nguyễn using Jira 1001.0.0-SNAPSHOT#100285-rev:e2e3f38099261bb500217a742ea82f5528ab8db0.

</body>
</html>