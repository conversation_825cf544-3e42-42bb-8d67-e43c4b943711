<!DOCTYPE html>
<html>
<head>
    <title>[#AL-50] [MB - Contacts] Refer contact to another user</title>
    <meta http-equiv="Content-Type" Content="application/vnd.ms-word; charset=UTF-8">
        <style type="text/css">

.tableBorder, .grid
{
    background-color: #fff;
    width: 100%;
    border-collapse: collapse;
}

.tableBorder td, .grid td
{
    vertical-align: top;
    padding: 2px;
    border: 1px solid #ccc;
}

.noPadding
{
    padding: 0 !important;
}

h3 .subText
{
    font-size: 60%;
    font-weight: normal;
}

.tabLabel
{
    font-weight: bold;
    border: 1px solid #ccc;
    border-bottom:none;
    padding: 2px;
    display: inline;
}

td.blank
{
    padding: 0;
    margin: 0;
}

.blank td
{
    border: none;
}

#descriptionArea
{
    margin: 0;
    padding: 2px;
    border: 1px solid #ccc;
}

hr
{
    border-top:1px solid #aaa;
}

hr.fullcontent
{
  height: 15px;
  padding: 10px 0;
  background: #fff url('https://exnodes.atlassian.net/images/icons/hr.gif') no-repeat scroll center;
}

</style>

</head>
<body>

<table class="tableBorder" cellpadding="0" cellspacing="0" border="0" width="100%">
    <tr>
        <td bgcolor="#f0f0f0" width="100%" colspan="2" valign="top">
                            <b><a id="parent_issue_summary" href="https://exnodes.atlassian.net/browse/AL-17">Contacts</a></b>
                <span style="font-size: 9px">(<a id="parent_issue_key" href="https://exnodes.atlassian.net/browse/AL-17">AL-17</a>)</span>
                <h3 class="formtitle"><img src="https://exnodes.atlassian.net/images/icons/link_out_bot.gif" width="16" height="16" />
                        [AL-50]&nbsp;<a href="https://exnodes.atlassian.net/browse/AL-50">[MB - Contacts] Refer contact to another user</a>
            <span class="subText">
               Created: 04/Jun/25                   &nbsp;Updated: 25/Jun/25

                                            </span>
            </h3>
        </td>
    </tr>
    <tr>
        <td width="20%"><b>Status:</b></td>
        <td width="80%">In Progress</td>
    </tr>
    <tr>
        <td width="20%"><b>Project:</b></td>
        <td width="80%"><a href="https://exnodes.atlassian.net/secure/BrowseProject.jspa?id=10842">AIOS Link</a></td>
    </tr>

        <tr>
            <td><b>Components:</b></td>
            <td>
                            None
                </td>
    </tr>
    

        <tr>
            <td><b>Affects versions:</b></td>
            <td>
                            None
                </td>
    </tr>
    

        <tr>
            <td><b>Fix versions:</b></td>
            <td>
                            None
                </td>
    </tr>
    
    
            <td width="20%"><b>Parent:</b></td>
        <td width="80%"><a href="https://exnodes.atlassian.net/browse/AL-17">Contacts</a></td>
    </table>

<br />
<table class="grid" cellpadding="0" cellspacing="0" border="0" width="100%">
    <tr>
        <td bgcolor="#f0f0f0" valign="top" width="20%">
            <b>Type:</b>
        </td>
        <td bgcolor="#ffffff" valign="top"  width="30%" >
            Story
        </td>

                    <td bgcolor="#f0f0f0">
                <b>Priority:</b>
            </td>
            <td bgcolor="#ffffff" valign="top" nowrap>
                Medium
            </td>
            </tr>
    <tr>
                        <td bgcolor="#f0f0f0" valign="top" width="20%">
                <b>Reporter:</b>
            </td>
            <td bgcolor="#ffffff" valign="top"  width="30%" >
                                        
                
        
            <a class="user-hover" rel="712020:59b66c6e-1068-441c-9436-130289e16a26"
                id="word_reporter_712020:59b66c6e-1068-441c-9436-130289e16a26"
                href="https://exnodes.atlassian.net/secure/ViewProfile.jspa?accountId=712020%3A59b66c6e-1068-441c-9436-130289e16a26">
                    Duyen Nguyen
                </a>

                            </td>
        
                    <td bgcolor="#f0f0f0" width="20%">
                <b>Assignee:</b>
            </td>
            <td bgcolor="#ffffff" valign="top" nowrap  width="30%" >
                                    Unassigned
                            </td>
            </tr>
    	<tr>
		<td bgcolor="#f0f0f0" width="20%">
			<b>Resolution:</b>
		</td>
		<td bgcolor="#ffffff" valign="top" width="30%" nowrap>
            				Unresolved
                    </td>
                    <td bgcolor="#f0f0f0" width="20%">
                <b>Votes:</b>
            </td>
            <td bgcolor="#ffffff" valign="top" width="30%" nowrap>
                0
            </td>
        
    </tr>
    
        <tr>
        <td bgcolor="#f0f0f0" width="20%">
            <b>Labels:</b>
        </td>
        <td id="labels-37410-value" class="value" bgcolor="#ffffff" valign="top" colspan="3" nowrap>
                            None
                    </td>
    </tr>
    
    	<tr>
        		<td bgcolor="#f0f0f0" width="20%"><b>Remaining Estimate:</b></td>
        <td bgcolor="#ffffff" valign="top" nowrap width="80%" colspan="3">
                            Not Specified
            		</td>
    </tr>
    <tr>
                <td bgcolor="#f0f0f0" width="20%"><b>Time Spent:</b></td>
		<td bgcolor="#ffffff" valign="top" nowrap width="80%" colspan="3">
                            Not Specified
            		</td>
	</tr>
    <tr>
                <td bgcolor="#f0f0f0" width="20%"><b>Original estimate:</b></td>
		<td bgcolor="#ffffff" valign="top" nowrap width="80%" colspan="3">
                            Not Specified
            		</td>
    </tr>
    
    </table>



    <br />

    	<table class="grid" cellpadding="0" cellspacing="0" border="0" width="100%">
            

        
    


    
                        <tr>
                <td bgcolor="#f0f0f0" width="20%" valign="top"><b>Sprint:</b></td>
                <td id="customfield_10020-37410-value" class="value" bgcolor="#ffffff" width="80%"></td>
            </tr>
                                <tr>
                <td bgcolor="#f0f0f0" width="20%" valign="top"><b>Platform:</b></td>
                <td id="customfield_10687-37410-value" class="value" bgcolor="#ffffff" width="80%">    MOBILE</td>
            </tr>
            </table>

    <br/>

    <table cellpadding="2" cellspacing="0" border="0" width="100%" align="center">
    <tr>
        <td bgcolor="#bbbbbb" width="1%" nowrap align="center">
            &nbsp;<font color="#ffffff"><b>Description</b></font>&nbsp;
        </td>
        <td>&nbsp;</td>
    </tr>
    </table>

    <table cellpadding="0" cellspacing="0" border="0" width="100%">
    <tr>
        <td id="descriptionArea">
            <h3><a name="1.UseCaseDescription"></a>1. Use Case Description</h3>

<p>As a user, I want to refer one of my contacts to other users in my network<br/>
so that they can view the referred profile and choose to connect—only if the referred contact allows referrals.</p>

<hr />

<h3><a name="2.UserWorkflow%28StepbyStep%29"></a>2. User Workflow (Step-by-Step)</h3>

<h4><a name="%F0%9F%9F%A6FlowA%3AReferviaContactsTab"></a>🟦 Flow A: Refer via Contacts Tab</h4>

<ol>
	<li>User navigates to the <b>Contacts tab</b>.</li>
	<li>Each contact card includes a <b>Refer icon</b>.</li>
	<li>User taps the <b>Refer icon</b> on a specific contact.</li>
	<li>System opens a <b>modal</b> titled “Refer this Contact to...”</li>
	<li>User sees a searchable list of their own contacts and selects <b>one or more</b> recipients.</li>
	<li>System verifies that the selected contact has <b>referral permission enabled</b>.</li>
	<li>If allowed, referral is sent, and a <b>notification</b> is delivered to each recipient.</li>
	<li>Recipients see the referral in their <b>Notification Center</b>.</li>
	<li>Tapping the notification opens the referred user’s profile.</li>
	<li>Recipient may tap <b>“Add to Contact”</b> to initiate connection.</li>
</ol>


<h4><a name="%F0%9F%9F%A6FlowB%3AReferviaContactProfile"></a>🟦 Flow B: Refer via Contact Profile</h4>

<ol>
	<li>User views a specific contact’s profile.</li>
	<li>User taps <b>“Refer”</b> button.</li>
	<li>System opens the same <b>multi-select modal</b> for selecting recipient contacts.</li>
	<li>Remaining steps match Flow A.</li>
</ol>


<hr />

<h3><a name="3.FieldDefinitionsTable"></a>3. Field Definitions Table</h3>

<div class='table-wrap'>
<table class='confluenceTable'><tbody>
<tr>
<th class='confluenceTh'>Field Name</th>
<th class='confluenceTh'>Field Type</th>
<th class='confluenceTh'>Validation Rule</th>
<th class='confluenceTh'>Mandatory</th>
<th class='confluenceTh'>Description</th>
</tr>
<tr>
<td class='confluenceTd'>Contact to Refer</td>
<td class='confluenceTd'>Object (User ID)</td>
<td class='confluenceTd'>Must exist in sender’s Contact List</td>
<td class='confluenceTd'>Yes</td>
<td class='confluenceTd'>The person being referred</td>
</tr>
<tr>
<td class='confluenceTd'>Recipients</td>
<td class='confluenceTd'>Multi-select</td>
<td class='confluenceTd'>Must exist in sender’s Contact List</td>
<td class='confluenceTd'>Yes</td>
<td class='confluenceTd'>One or more contacts who will receive referral</td>
</tr>
</tbody></table>
</div>


<hr />

<h3><a name="4.DataDisplayTable%28Notification%29"></a>4. Data Display Table (Notification)</h3>

<div class='table-wrap'>
<table class='confluenceTable'><tbody>
<tr>
<th class='confluenceTh'>Field</th>
<th class='confluenceTh'>Example</th>
<th class='confluenceTh'>Notes</th>
</tr>
<tr>
<td class='confluenceTd'>Title</td>
<td class='confluenceTd'>“Sarah has referred you a new contact”</td>
<td class='confluenceTd'>Standard format</td>
</tr>
<tr>
<td class='confluenceTd'>Action CTA</td>
<td class='confluenceTd'>Tap → View referred contact profile</td>
<td class='confluenceTd'>Navigates to limited-view profile screen</td>
</tr>
</tbody></table>
</div>


<hr />

<h3><a name="5.AcceptanceCriteria%28AC%29"></a>5. Acceptance Criteria (AC)</h3>

<ul>
	<li><b>AC1:</b> Refer icon must be shown on all contact cards and contact profiles.</li>
	<li><b>AC2:</b> Clicking Refer opens a modal with a searchable multi-select list of the sender’s contacts.</li>
	<li><b>AC3:</b> Referrals are only allowed if the <b>referred contact has referral permission enabled</b>.</li>
	<li><b>AC4:</b> If the referred contact has disabled referral sharing, display error:<br/>
<em>“This contact does not allow referrals.”</em></li>
	<li><b>AC5:</b> System prevents referring a user who is already in the recipient's contact list.</li>
	<li><b>AC6:</b> Tapping referral notification opens the referred user’s <b>public profile</b>.</li>
	<li><b>AC7:</b> Recipient can choose to <b>Add to Contact</b> from the profile view.</li>
	<li><b>AC8:</b> Referral does <b>not</b> automatically add contact for either party.</li>
	<li><b>AC9:</b> A toast confirms success:<br/>
<em>“<span class="error">&#91;Name&#93;</span> was referred to 2 people.”</em></li>
	<li><b>AC10:</b> Duplicate referral to the same user pair within 24 hours is not allowed.</li>
</ul>


<hr />

<h3><a name="6.SystemRules"></a>6. System Rules</h3>

<ul>
	<li>Referral opt-out is toggled by each user under <b>Privacy Settings &gt; Referral Visibility</b>.</li>
	<li>If toggle is off, the user <b>cannot be selected</b> in referral flows.</li>
	<li>No referral history is stored beyond delivery metadata.</li>
	<li>Referrals cannot be recalled once sent.</li>
	<li>Blocked users cannot refer or be referred.</li>
</ul>


<hr />

<h3><a name="7.UXOptimizations"></a>7. UX Optimizations</h3>

<ul>
	<li>Modal grays out and disables non-eligible recipients or referrals.</li>
	<li>Tooltip for disabled refer button:<br/>
<em>“This contact has disabled referrals.”</em></li>
	<li>Suggest most active contacts at top of picker modal.</li>
	<li>Avatar + name shown in notification with quick CTA.</li>
</ul>

            <br/>
        </td>
    </tr>
    </table>

Generated at Thu Jun 26 09:59:39 UTC 2025 by Kid Nguyễn using Jira 1001.0.0-SNAPSHOT#100285-rev:e2e3f38099261bb500217a742ea82f5528ab8db0.

</body>
</html>