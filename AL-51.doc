<!DOCTYPE html>
<html>
<head>
    <title>[#AL-51] [MB - Contacts] Contact List</title>
    <meta http-equiv="Content-Type" Content="application/vnd.ms-word; charset=UTF-8">
        <style type="text/css">

.tableBorder, .grid
{
    background-color: #fff;
    width: 100%;
    border-collapse: collapse;
}

.tableBorder td, .grid td
{
    vertical-align: top;
    padding: 2px;
    border: 1px solid #ccc;
}

.noPadding
{
    padding: 0 !important;
}

h3 .subText
{
    font-size: 60%;
    font-weight: normal;
}

.tabLabel
{
    font-weight: bold;
    border: 1px solid #ccc;
    border-bottom:none;
    padding: 2px;
    display: inline;
}

td.blank
{
    padding: 0;
    margin: 0;
}

.blank td
{
    border: none;
}

#descriptionArea
{
    margin: 0;
    padding: 2px;
    border: 1px solid #ccc;
}

hr
{
    border-top:1px solid #aaa;
}

hr.fullcontent
{
  height: 15px;
  padding: 10px 0;
  background: #fff url('https://exnodes.atlassian.net/images/icons/hr.gif') no-repeat scroll center;
}

</style>

</head>
<body>

<table class="tableBorder" cellpadding="0" cellspacing="0" border="0" width="100%">
    <tr>
        <td bgcolor="#f0f0f0" width="100%" colspan="2" valign="top">
                            <b><a id="parent_issue_summary" href="https://exnodes.atlassian.net/browse/AL-17">Contacts</a></b>
                <span style="font-size: 9px">(<a id="parent_issue_key" href="https://exnodes.atlassian.net/browse/AL-17">AL-17</a>)</span>
                <h3 class="formtitle"><img src="https://exnodes.atlassian.net/images/icons/link_out_bot.gif" width="16" height="16" />
                        [AL-51]&nbsp;<a href="https://exnodes.atlassian.net/browse/AL-51">[MB - Contacts] Contact List</a>
            <span class="subText">
               Created: 04/Jun/25                   &nbsp;Updated: 25/Jun/25

                                            </span>
            </h3>
        </td>
    </tr>
    <tr>
        <td width="20%"><b>Status:</b></td>
        <td width="80%">In Progress</td>
    </tr>
    <tr>
        <td width="20%"><b>Project:</b></td>
        <td width="80%"><a href="https://exnodes.atlassian.net/secure/BrowseProject.jspa?id=10842">AIOS Link</a></td>
    </tr>

        <tr>
            <td><b>Components:</b></td>
            <td>
                            None
                </td>
    </tr>
    

        <tr>
            <td><b>Affects versions:</b></td>
            <td>
                            None
                </td>
    </tr>
    

        <tr>
            <td><b>Fix versions:</b></td>
            <td>
                            None
                </td>
    </tr>
    
    
            <td width="20%"><b>Parent:</b></td>
        <td width="80%"><a href="https://exnodes.atlassian.net/browse/AL-17">Contacts</a></td>
    </table>

<br />
<table class="grid" cellpadding="0" cellspacing="0" border="0" width="100%">
    <tr>
        <td bgcolor="#f0f0f0" valign="top" width="20%">
            <b>Type:</b>
        </td>
        <td bgcolor="#ffffff" valign="top"  width="30%" >
            Story
        </td>

                    <td bgcolor="#f0f0f0">
                <b>Priority:</b>
            </td>
            <td bgcolor="#ffffff" valign="top" nowrap>
                Medium
            </td>
            </tr>
    <tr>
                        <td bgcolor="#f0f0f0" valign="top" width="20%">
                <b>Reporter:</b>
            </td>
            <td bgcolor="#ffffff" valign="top"  width="30%" >
                                        
                
        
            <a class="user-hover" rel="712020:59b66c6e-1068-441c-9436-130289e16a26"
                id="word_reporter_712020:59b66c6e-1068-441c-9436-130289e16a26"
                href="https://exnodes.atlassian.net/secure/ViewProfile.jspa?accountId=712020%3A59b66c6e-1068-441c-9436-130289e16a26">
                    Duyen Nguyen
                </a>

                            </td>
        
                    <td bgcolor="#f0f0f0" width="20%">
                <b>Assignee:</b>
            </td>
            <td bgcolor="#ffffff" valign="top" nowrap  width="30%" >
                                    Unassigned
                            </td>
            </tr>
    	<tr>
		<td bgcolor="#f0f0f0" width="20%">
			<b>Resolution:</b>
		</td>
		<td bgcolor="#ffffff" valign="top" width="30%" nowrap>
            				Unresolved
                    </td>
                    <td bgcolor="#f0f0f0" width="20%">
                <b>Votes:</b>
            </td>
            <td bgcolor="#ffffff" valign="top" width="30%" nowrap>
                0
            </td>
        
    </tr>
    
        <tr>
        <td bgcolor="#f0f0f0" width="20%">
            <b>Labels:</b>
        </td>
        <td id="labels-37413-value" class="value" bgcolor="#ffffff" valign="top" colspan="3" nowrap>
                            None
                    </td>
    </tr>
    
    	<tr>
        		<td bgcolor="#f0f0f0" width="20%"><b>Remaining Estimate:</b></td>
        <td bgcolor="#ffffff" valign="top" nowrap width="80%" colspan="3">
                            Not Specified
            		</td>
    </tr>
    <tr>
                <td bgcolor="#f0f0f0" width="20%"><b>Time Spent:</b></td>
		<td bgcolor="#ffffff" valign="top" nowrap width="80%" colspan="3">
                            Not Specified
            		</td>
	</tr>
    <tr>
                <td bgcolor="#f0f0f0" width="20%"><b>Original estimate:</b></td>
		<td bgcolor="#ffffff" valign="top" nowrap width="80%" colspan="3">
                            Not Specified
            		</td>
    </tr>
    
    </table>



    <br />

    	<table class="grid" cellpadding="0" cellspacing="0" border="0" width="100%">
            

        
    


    
                        <tr>
                <td bgcolor="#f0f0f0" width="20%" valign="top"><b>Sprint:</b></td>
                <td id="customfield_10020-37413-value" class="value" bgcolor="#ffffff" width="80%"></td>
            </tr>
                                <tr>
                <td bgcolor="#f0f0f0" width="20%" valign="top"><b>Platform:</b></td>
                <td id="customfield_10687-37413-value" class="value" bgcolor="#ffffff" width="80%">    MOBILE</td>
            </tr>
            </table>

    <br/>

    <table cellpadding="2" cellspacing="0" border="0" width="100%" align="center">
    <tr>
        <td bgcolor="#bbbbbb" width="1%" nowrap align="center">
            &nbsp;<font color="#ffffff"><b>Description</b></font>&nbsp;
        </td>
        <td>&nbsp;</td>
    </tr>
    </table>

    <table cellpadding="0" cellspacing="0" border="0" width="100%">
    <tr>
        <td id="descriptionArea">
            <h3><a name="1.UseCaseDescription"></a>1. Use Case Description</h3>

<p>As a logged-in user, I want to view, filter, and interact with my list of contacts so that I can manage relationships, refer users, and message individuals efficiently.</p>

<hr />

<h3><a name="2.UserWorkflow"></a>2. User Workflow</h3>

<ul>
	<li>Step 1: User navigates to the <b>Contacts</b> module from the dashboard.</li>
	<li>Step 2: System displays the list of contacts segmented by role (User / Merchant).</li>
	<li>Step 3: User scrolls through the contact list.</li>
	<li>Step 4: For each contact card, user can:
	<ul>
		<li>Tap <b>Refer</b> icon to initiate a referral</li>
		<li>Tap <b>Add Note</b> icon to add contextual information</li>
		<li>Tap <b>Delete</b> icon to remove the contact</li>
		<li>Tap anywhere else on the card to view contact details</li>
		<li>Tap <b>Message</b> icon to open message module</li>
	</ul>
	</li>
	<li>Step 5: System updates or redirects based on selected interaction.</li>
</ul>


<hr />

<h3><a name="3.FieldDefinitions"></a>3. Field Definitions</h3>

<h4><a name="InteractionElements"></a>Interaction Elements</h4>

<div class='table-wrap'>
<table class='confluenceTable'><tbody>
<tr>
<th class='confluenceTh'>Element Name</th>
<th class='confluenceTh'>Type</th>
<th class='confluenceTh'>Description</th>
</tr>
<tr>
<td class='confluenceTd'>Refer Icon</td>
<td class='confluenceTd'>IconTap</td>
<td class='confluenceTd'>Opens contact referral modal</td>
</tr>
<tr>
<td class='confluenceTd'>Add Note Icon</td>
<td class='confluenceTd'>IconTap</td>
<td class='confluenceTd'>Opens note editor for selected contact</td>
</tr>
<tr>
<td class='confluenceTd'>Delete Icon</td>
<td class='confluenceTd'>IconTap</td>
<td class='confluenceTd'>Deletes contact with confirmation modal</td>
</tr>
<tr>
<td class='confluenceTd'>Message Icon</td>
<td class='confluenceTd'>IconTap</td>
<td class='confluenceTd'>Opens message module with selected contact</td>
</tr>
<tr>
<td class='confluenceTd'>Contact Card</td>
<td class='confluenceTd'>CardTap</td>
<td class='confluenceTd'>Navigates to the full profile/contact detail page</td>
</tr>
</tbody></table>
</div>


<hr />

<h3><a name="4.DataDisplay"></a>4. Data Display</h3>

<div class='table-wrap'>
<table class='confluenceTable'><tbody>
<tr>
<th class='confluenceTh'>Data Name</th>
<th class='confluenceTh'>Data Type</th>
<th class='confluenceTh'>Display When Empty</th>
<th class='confluenceTh'>Format</th>
<th class='confluenceTh'>Description</th>
</tr>
<tr>
<td class='confluenceTd'>Contact Name</td>
<td class='confluenceTd'>Text</td>
<td class='confluenceTd'>“Unnamed Contact”</td>
<td class='confluenceTd'>Full name</td>
<td class='confluenceTd'>User's or merchant's registered name</td>
</tr>
<tr>
<td class='confluenceTd'>Avatar</td>
<td class='confluenceTd'>Image</td>
<td class='confluenceTd'>Default icon</td>
<td class='confluenceTd'>Circle image</td>
<td class='confluenceTd'>Contact’s profile picture</td>
</tr>
<tr>
<td class='confluenceTd'>Action Icons</td>
<td class='confluenceTd'>Icons</td>
<td class='confluenceTd'>Hidden</td>
<td class='confluenceTd'>Row of 3 buttons</td>
<td class='confluenceTd'>Refer, Add Note, Delete</td>
</tr>
</tbody></table>
</div>


<hr />

<h3><a name="5.AcceptanceCriteria"></a>5. Acceptance Criteria</h3>

<ul>
	<li><b>AC1:</b> Contacts must be loaded and shown as cards with avatar, name, position and last interaction (met at X event)                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                            and role.</li>
	<li><b>AC2:</b> Tapping the <b>Refer</b> icon must open the referral modal pre-filled with contact data.</li>
	<li><b>AC3:</b> Tapping the <b>Add Note</b> icon must open a note editor linked to the selected contact.</li>
	<li><b>AC4:</b> Tapping the <b>Delete</b> icon must open a confirmation modal before removing the contact.</li>
	<li><b>AC5:</b> Tapping the <b>Message</b> icon must navigate to the message module.</li>
	<li><b>AC6:</b> Contact cards must not support drag, swipe, or export actions.</li>
	<li><b>AC7:</b> Contacts deleted from the list must not appear in the next refresh or session.</li>
</ul>


<hr />

<h3><a name="6.SystemRules"></a>6. System Rules</h3>

<ul>
	<li>Contact list must be cached for session duration and updated with pull-to-refresh.</li>
	<li>Message icon must trigger navigation to Message Module with selected contact preloaded.</li>
	<li>Deletion must update both local view and backend reference (via soft delete flag).</li>
	<li>Notes are stored per user and not visible to the contact.</li>
	<li>No export, share, or download option is available in this module.</li>
</ul>


<hr />

<h3><a name="7.UXOptimizations"></a>7. UX Optimizations</h3>

<ul>
	<li>Sticky filter bar on top when scrolling long contact lists.</li>
	<li>Subtle animation on card tap or icon press for feedback.</li>
	<li>Toast message:
	<ul>
		<li>“Contact deleted” on success</li>
		<li>“Referral sent” if referral flow is completed</li>
	</ul>
	</li>
	<li>Display avatars using lazy-load strategy to enhance performance.</li>
	<li>Use tooltips or icon labels for first-time users (e.g., “Refer”, “Note”, “Delete”).</li>
</ul>

            <br/>
        </td>
    </tr>
    </table>

    <br/>

    <table cellpadding="2" cellspacing="0" border="0" width="100%" align="center">
    <tr>
        <td bgcolor="#bbbbbb" width="1%" nowrap align="center">
            &nbsp;<font color="#ffffff"><b>Comments</b></font>&nbsp;
        </td>
        <td>&nbsp;</td>
    </tr>
    </table>

    <table cellpadding="0" cellspacing="0" border="0" width="100%" class="grid" style="margin: 0;">
                <tr id="comment-header-20230"><td bgcolor="#f0f0f0">
            Comment by  
                                                    
                
        
            <a class="user-hover" rel="************************"
                id="word_commented_************************"
                href="https://exnodes.atlassian.net/secure/ViewProfile.jspa?accountId=************************">
                    Kai Nguyễn
                </a>

                                        <font size="-2">
            [
                <font color="#336699">05/Jun/25</font>

                            ]
            </font>

        </td></tr>
        <tr id="comment-body-20230"><td bgcolor="#ffffff">
            <p>Role filter đúng không v <a href="https://exnodes.atlassian.net/secure/ViewProfile.jspa?accountId=712020%3A59b66c6e-1068-441c-9436-130289e16a26" class="user-hover" rel="712020:59b66c6e-1068-441c-9436-130289e16a26" data-account-id="712020:59b66c6e-1068-441c-9436-130289e16a26" accountid="712020:59b66c6e-1068-441c-9436-130289e16a26" rel="noreferrer">Duyen Nguyen</a> </p>
        </td></tr>
                <tr id="comment-header-20231"><td bgcolor="#f0f0f0">
            Comment by  
                                                    
                
        
            <a class="user-hover" rel="712020:59b66c6e-1068-441c-9436-130289e16a26"
                id="word_commented_712020:59b66c6e-1068-441c-9436-130289e16a26"
                href="https://exnodes.atlassian.net/secure/ViewProfile.jspa?accountId=712020%3A59b66c6e-1068-441c-9436-130289e16a26">
                    Duyen Nguyen
                </a>

                                        <font size="-2">
            [
                <font color="#336699">05/Jun/25</font>

                            ]
            </font>

        </td></tr>
        <tr id="comment-body-20231"><td bgcolor="#ffffff">
            <p><a href="https://exnodes.atlassian.net/secure/ViewProfile.jspa?accountId=************************" class="user-hover" rel="************************" data-account-id="************************" accountid="************************" rel="noreferrer">Kai Nguyễn</a> dạ đúng rồi anh</p>
        </td></tr>
            </table>
Generated at Thu Jun 26 04:50:41 UTC 2025 by Kid Nguyễn using Jira 1001.0.0-SNAPSHOT#100285-rev:e2e3f38099261bb500217a742ea82f5528ab8db0.

</body>
</html>