# AIOS-Link

## Description
**AIOS-Link** is a business relationship management application. Here are the main features:

* **Contact Management**: Store and manage contact information, interactions, and meeting history with partners.

* **Gift Giving**: Send gifts to partners and track gift status.

* **Product Management**: Manage product catalog, pricing, and status.

* **Interactions**: Record interaction history with partners (meetings, gifts, messages).

* **User Profile**: Manage personal information, company details, and visibility settings.

## Technical
The project uses FastAPI and PostgreSQL.

## Installation and Running

### Requirements
- Python 3.9+
- PostgreSQL
- Docker (optional)

### Install dependencies
```bash
pip install -r requirements.txt
```

### Run with FastAPI
```bash
cd src
uvicorn main:app --reload
```

### Run with Docker
```bash
docker-compose up -d --build
```

## Migrations
### Create new migration
```bash
alembic revision --autogenerate -m "descriptions for migrations"
```

### Update database to latest version
```bash
alembic upgrade head
```

### View migration history
```bash
alembic history
```

### Rollback migration
```bash
alembic downgrade -1
```

### Rollback to specific revision
```bash
alembic downgrade <revision_ID>
```

### Merge heads (when having multiple migration branches)
```bash
alembic merge heads
```

## Project Structure
```
.
├── alembic.ini
├── docker-compose.yml
├── Dockerfile
├── requirements.txt
├── README.md
├── alembic
│   ├── env.py
│   ├── README
│   ├── script.py.mako
│   └── versions
└── src
    ├── __init__.py
    ├── main.py
    ├── authentication
    │   ├── __init__.py
    │   ├── models.py
    │   ├── router.py
    │   ├── schemas.py
    │   └── service.py
    ├── contact
    │   ├── __init__.py
    │   ├── models.py
    │   ├── router.py
    │   ├── schemas.py
    │   └── service.py
    ├── product
    │   ├── __init__.py
    │   ├── models.py
    │   ├── router.py
    │   ├── schemas.py
    │   └── service.py
    ├── user
    │   ├── __init__.py
    │   ├── models.py
    │   ├── router.py
    │   ├── schemas.py
    │   └── service.py
    └── utils
        ├── __init__.py
        ├── base_model.py
        ├── base_schema.py
        ├── database.py
        ├── exceptions.py
        ├── pagination.py
        ├── permissions.py
        └── response.py
```

## API Documentation
After running the server, access:
- Swagger UI: `http://localhost:8000/docs`
- ReDoc: `http://localhost:8000/redoc`

## Notes
- Use Alembic for migrations instead of `Base.metadata.create_all()`
- When adding new routes, synchronize scopes by calling API `/api/routes/synchronized`
- Define API permissions:
  - `current_user: User = Depends(is_authenticated)` for APIs requiring authentication
  - `current_user: User = Depends(is_admin)` for APIs requiring admin rights
  - No definition needed for publicly accessible APIs