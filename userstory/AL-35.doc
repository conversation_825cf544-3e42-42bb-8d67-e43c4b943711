<!DOCTYPE html>
<html>
<head>
    <title>[#AL-35] [WEB - Marketplace] Remove Product from Marketplace</title>
    <meta http-equiv="Content-Type" Content="application/vnd.ms-word; charset=UTF-8">
        <style type="text/css">

.tableBorder, .grid
{
    background-color: #fff;
    width: 100%;
    border-collapse: collapse;
}

.tableBorder td, .grid td
{
    vertical-align: top;
    padding: 2px;
    border: 1px solid #ccc;
}

.noPadding
{
    padding: 0 !important;
}

h3 .subText
{
    font-size: 60%;
    font-weight: normal;
}

.tabLabel
{
    font-weight: bold;
    border: 1px solid #ccc;
    border-bottom:none;
    padding: 2px;
    display: inline;
}

td.blank
{
    padding: 0;
    margin: 0;
}

.blank td
{
    border: none;
}

#descriptionArea
{
    margin: 0;
    padding: 2px;
    border: 1px solid #ccc;
}

hr
{
    border-top:1px solid #aaa;
}

hr.fullcontent
{
  height: 15px;
  padding: 10px 0;
  background: #fff url('https://exnodes.atlassian.net/images/icons/hr.gif') no-repeat scroll center;
}

</style>

</head>
<body>

<table class="tableBorder" cellpadding="0" cellspacing="0" border="0" width="100%">
    <tr>
        <td bgcolor="#f0f0f0" width="100%" colspan="2" valign="top">
                            <b><a id="parent_issue_summary" href="https://exnodes.atlassian.net/browse/AL-20">Marketplace</a></b>
                <span style="font-size: 9px">(<a id="parent_issue_key" href="https://exnodes.atlassian.net/browse/AL-20">AL-20</a>)</span>
                <h3 class="formtitle"><img src="https://exnodes.atlassian.net/images/icons/link_out_bot.gif" width="16" height="16" />
                        [AL-35]&nbsp;<a href="https://exnodes.atlassian.net/browse/AL-35">[WEB - Marketplace] Remove Product from Marketplace</a>
            <span class="subText">
               Created: 04/Jun/25                   &nbsp;Updated: 25/Jun/25

                                            </span>
            </h3>
        </td>
    </tr>
    <tr>
        <td width="20%"><b>Status:</b></td>
        <td width="80%">In Progress</td>
    </tr>
    <tr>
        <td width="20%"><b>Project:</b></td>
        <td width="80%"><a href="https://exnodes.atlassian.net/secure/BrowseProject.jspa?id=10842">AIOS Link</a></td>
    </tr>

        <tr>
            <td><b>Components:</b></td>
            <td>
                            None
                </td>
    </tr>
    

        <tr>
            <td><b>Affects versions:</b></td>
            <td>
                            None
                </td>
    </tr>
    

        <tr>
            <td><b>Fix versions:</b></td>
            <td>
                            None
                </td>
    </tr>
    
    
            <td width="20%"><b>Parent:</b></td>
        <td width="80%"><a href="https://exnodes.atlassian.net/browse/AL-20">Marketplace</a></td>
    </table>

<br />
<table class="grid" cellpadding="0" cellspacing="0" border="0" width="100%">
    <tr>
        <td bgcolor="#f0f0f0" valign="top" width="20%">
            <b>Type:</b>
        </td>
        <td bgcolor="#ffffff" valign="top"  width="30%" >
            Story
        </td>

                    <td bgcolor="#f0f0f0">
                <b>Priority:</b>
            </td>
            <td bgcolor="#ffffff" valign="top" nowrap>
                Medium
            </td>
            </tr>
    <tr>
                        <td bgcolor="#f0f0f0" valign="top" width="20%">
                <b>Reporter:</b>
            </td>
            <td bgcolor="#ffffff" valign="top"  width="30%" >
                                        
                
        
            <a class="user-hover" rel="712020:59b66c6e-1068-441c-9436-130289e16a26"
                id="word_reporter_712020:59b66c6e-1068-441c-9436-130289e16a26"
                href="https://exnodes.atlassian.net/secure/ViewProfile.jspa?accountId=712020%3A59b66c6e-1068-441c-9436-130289e16a26">
                    Duyen Nguyen
                </a>

                            </td>
        
                    <td bgcolor="#f0f0f0" width="20%">
                <b>Assignee:</b>
            </td>
            <td bgcolor="#ffffff" valign="top" nowrap  width="30%" >
                                    Unassigned
                            </td>
            </tr>
    	<tr>
		<td bgcolor="#f0f0f0" width="20%">
			<b>Resolution:</b>
		</td>
		<td bgcolor="#ffffff" valign="top" width="30%" nowrap>
            				Unresolved
                    </td>
                    <td bgcolor="#f0f0f0" width="20%">
                <b>Votes:</b>
            </td>
            <td bgcolor="#ffffff" valign="top" width="30%" nowrap>
                0
            </td>
        
    </tr>
    
        <tr>
        <td bgcolor="#f0f0f0" width="20%">
            <b>Labels:</b>
        </td>
        <td id="labels-37368-value" class="value" bgcolor="#ffffff" valign="top" colspan="3" nowrap>
                            None
                    </td>
    </tr>
    
    	<tr>
        		<td bgcolor="#f0f0f0" width="20%"><b>Remaining Estimate:</b></td>
        <td bgcolor="#ffffff" valign="top" nowrap width="80%" colspan="3">
                            Not Specified
            		</td>
    </tr>
    <tr>
                <td bgcolor="#f0f0f0" width="20%"><b>Time Spent:</b></td>
		<td bgcolor="#ffffff" valign="top" nowrap width="80%" colspan="3">
                            Not Specified
            		</td>
	</tr>
    <tr>
                <td bgcolor="#f0f0f0" width="20%"><b>Original estimate:</b></td>
		<td bgcolor="#ffffff" valign="top" nowrap width="80%" colspan="3">
                            Not Specified
            		</td>
    </tr>
    
    </table>



    <br />

    	<table class="grid" cellpadding="0" cellspacing="0" border="0" width="100%">
            

        
    


    
                        <tr>
                <td bgcolor="#f0f0f0" width="20%" valign="top"><b>Sprint:</b></td>
                <td id="customfield_10020-37368-value" class="value" bgcolor="#ffffff" width="80%"></td>
            </tr>
                                <tr>
                <td bgcolor="#f0f0f0" width="20%" valign="top"><b>Platform:</b></td>
                <td id="customfield_10687-37368-value" class="value" bgcolor="#ffffff" width="80%">    WEB</td>
            </tr>
            </table>

    <br/>

    <table cellpadding="2" cellspacing="0" border="0" width="100%" align="center">
    <tr>
        <td bgcolor="#bbbbbb" width="1%" nowrap align="center">
            &nbsp;<font color="#ffffff"><b>Description</b></font>&nbsp;
        </td>
        <td>&nbsp;</td>
    </tr>
    </table>

    <table cellpadding="0" cellspacing="0" border="0" width="100%">
    <tr>
        <td id="descriptionArea">
            <h1><a name="RemoveProductfromMarketplaceRequirementSpecification"></a>Remove Product from Marketplace Requirement Specification</h1>

<h3><a name="1.UseCaseDescription"></a>1. Use Case Description</h3>

<p>As an admin on the web platform, I want to remove a product listing from the marketplace so that it no longer appears for purchase while preserving historical orders.</p>

<h3><a name="2.UserWorkflow"></a>2. User Workflow</h3>

<ul>
	<li>Step 1: Admin opens the <b>Actions</b> gear icon in the Marketplace Product List row and selects <b>Remove from Marketplace</b>, or taps <b>Remove</b> inside the Product Details page.</li>
	<li>Step 2: System displays a confirmation modal explaining that the product will disappear from the marketplace and active carts but remain in existing orders.</li>
	<li>Step 3: Admin confirms the removal.</li>
	<li>Step 4: System removes the listing, updates the Marketplace Product List, and shows a success message.</li>
</ul>


<h3><a name="3.FieldDefinitions"></a>3. Field Definitions</h3>

<h4><a name="InteractionElements"></a>Interaction Elements</h4>

<div class='table-wrap'>
<table class='confluenceTable'><tbody>
<tr>
<th class='confluenceTh'>Element Name</th>
<th class='confluenceTh'>Type</th>
<th class='confluenceTh'>Trigger Condition</th>
<th class='confluenceTh'>Description</th>
</tr>
<tr>
<td class='confluenceTd'>Remove Button</td>
<td class='confluenceTd'>Button</td>
<td class='confluenceTd'>Visible in Product Details page</td>
<td class='confluenceTd'>Initiates removal workflow</td>
</tr>
<tr>
<td class='confluenceTd'>Remove Option</td>
<td class='confluenceTd'>Popover Item</td>
<td class='confluenceTd'>Available under Actions gear icon</td>
<td class='confluenceTd'>Initiates removal workflow</td>
</tr>
<tr>
<td class='confluenceTd'>Confirmation Modal</td>
<td class='confluenceTd'>Dialog</td>
<td class='confluenceTd'>Shown after Remove pressed</td>
<td class='confluenceTd'>Asks admin to confirm removal</td>
</tr>
<tr>
<td class='confluenceTd'>Confirm Removal</td>
<td class='confluenceTd'>Button</td>
<td class='confluenceTd'>Enabled within modal</td>
<td class='confluenceTd'>Confirms removal of listing</td>
</tr>
<tr>
<td class='confluenceTd'>Cancel Removal</td>
<td class='confluenceTd'>Button</td>
<td class='confluenceTd'>Enabled within modal</td>
<td class='confluenceTd'>Dismisses modal without changes</td>
</tr>
</tbody></table>
</div>


<h3><a name="4.DataDisplay%28Summary%29"></a>4. Data Display (Summary)</h3>

<div class='table-wrap'>
<table class='confluenceTable'><tbody>
<tr>
<th class='confluenceTh'>Data Name</th>
<th class='confluenceTh'>Data Type</th>
<th class='confluenceTh'>Display When Empty</th>
<th class='confluenceTh'>Format</th>
<th class='confluenceTh'>Description</th>
</tr>
<tr>
<td class='confluenceTd'>Success Message</td>
<td class='confluenceTd'>Toast</td>
<td class='confluenceTd'>Hidden</td>
<td class='confluenceTd'>Plaintext</td>
<td class='confluenceTd'>Displayed when product is removed</td>
</tr>
<tr>
<td class='confluenceTd'>Error Message</td>
<td class='confluenceTd'>Text</td>
<td class='confluenceTd'>Hidden</td>
<td class='confluenceTd'>Plaintext</td>
<td class='confluenceTd'>Shows validation or server errors</td>
</tr>
</tbody></table>
</div>


<h3><a name="5.AcceptanceCriteria"></a>5. Acceptance Criteria</h3>

<ul>
	<li>AC1: Remove option is available from both the Marketplace Product List and Product Details page.</li>
	<li>AC2: Confirmation modal clearly states that removed products disappear from the marketplace and carts but remain in all orders.</li>
	<li>AC3: After confirmation, product no longer appears in the Marketplace Product List.</li>
	<li>AC4: Success message appears after a successful removal.</li>
	<li>AC5: System prevents removal if the product has active unfulfilled orders and shows an error message.</li>
</ul>


<h3><a name="6.SystemRules"></a>6. System Rules</h3>

<ul>
	<li>Removal only updates marketplace listing status; original product record remains intact.</li>
	<li>Cart entries referencing the removed product are deleted immediately.</li>
	<li>Audit log records admin ID and timestamp of removal action.</li>
</ul>


<h3><a name="7.UXOptimizations"></a>7. UX Optimizations</h3>

<ul>
	<li>Prefill modal with product name for clarity.</li>
	<li>Display spinner inside modal while processing removal.</li>
	<li>Return focus to previous table row after successful removal.</li>
</ul>

            <br/>
        </td>
    </tr>
    </table>

Generated at Fri Jun 27 02:51:47 UTC 2025 by Kid Nguyễn using Jira 1001.0.0-SNAPSHOT#100285-rev:3c7b32e5e5b342f76f058c25245725788fa6e907.

</body>
</html>