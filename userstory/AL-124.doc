<!DOCTYPE html>
<html>
<head>
    <title>[#AL-124] [WEB - Misconduct Report] Report List</title>
    <meta http-equiv="Content-Type" Content="application/vnd.ms-word; charset=UTF-8">
        <style type="text/css">

.tableBorder, .grid
{
    background-color: #fff;
    width: 100%;
    border-collapse: collapse;
}

.tableBorder td, .grid td
{
    vertical-align: top;
    padding: 2px;
    border: 1px solid #ccc;
}

.noPadding
{
    padding: 0 !important;
}

h3 .subText
{
    font-size: 60%;
    font-weight: normal;
}

.tabLabel
{
    font-weight: bold;
    border: 1px solid #ccc;
    border-bottom:none;
    padding: 2px;
    display: inline;
}

td.blank
{
    padding: 0;
    margin: 0;
}

.blank td
{
    border: none;
}

#descriptionArea
{
    margin: 0;
    padding: 2px;
    border: 1px solid #ccc;
}

hr
{
    border-top:1px solid #aaa;
}

hr.fullcontent
{
  height: 15px;
  padding: 10px 0;
  background: #fff url('https://exnodes.atlassian.net/images/icons/hr.gif') no-repeat scroll center;
}

</style>

</head>
<body>

<table class="tableBorder" cellpadding="0" cellspacing="0" border="0" width="100%">
    <tr>
        <td bgcolor="#f0f0f0" width="100%" colspan="2" valign="top">
                            <b><a id="parent_issue_summary" href="https://exnodes.atlassian.net/browse/AL-123">Misconduct Report</a></b>
                <span style="font-size: 9px">(<a id="parent_issue_key" href="https://exnodes.atlassian.net/browse/AL-123">AL-123</a>)</span>
                <h3 class="formtitle"><img src="https://exnodes.atlassian.net/images/icons/link_out_bot.gif" width="16" height="16" />
                        [AL-124]&nbsp;<a href="https://exnodes.atlassian.net/browse/AL-124">[WEB - Misconduct Report] Report List</a>
            <span class="subText">
               Created: 13/Jun/25                   &nbsp;Updated: 13/Jun/25

                                            </span>
            </h3>
        </td>
    </tr>
    <tr>
        <td width="20%"><b>Status:</b></td>
        <td width="80%">Specified</td>
    </tr>
    <tr>
        <td width="20%"><b>Project:</b></td>
        <td width="80%"><a href="https://exnodes.atlassian.net/secure/BrowseProject.jspa?id=10842">AIOS Link</a></td>
    </tr>

        <tr>
            <td><b>Components:</b></td>
            <td>
                            None
                </td>
    </tr>
    

        <tr>
            <td><b>Affects versions:</b></td>
            <td>
                            None
                </td>
    </tr>
    

        <tr>
            <td><b>Fix versions:</b></td>
            <td>
                            None
                </td>
    </tr>
    
    
            <td width="20%"><b>Parent:</b></td>
        <td width="80%"><a href="https://exnodes.atlassian.net/browse/AL-123">Misconduct Report</a></td>
    </table>

<br />
<table class="grid" cellpadding="0" cellspacing="0" border="0" width="100%">
    <tr>
        <td bgcolor="#f0f0f0" valign="top" width="20%">
            <b>Type:</b>
        </td>
        <td bgcolor="#ffffff" valign="top"  width="30%" >
            Story
        </td>

                    <td bgcolor="#f0f0f0">
                <b>Priority:</b>
            </td>
            <td bgcolor="#ffffff" valign="top" nowrap>
                Medium
            </td>
            </tr>
    <tr>
                        <td bgcolor="#f0f0f0" valign="top" width="20%">
                <b>Reporter:</b>
            </td>
            <td bgcolor="#ffffff" valign="top"  width="30%" >
                                        
                
        
            <a class="user-hover" rel="712020:59b66c6e-1068-441c-9436-130289e16a26"
                id="word_reporter_712020:59b66c6e-1068-441c-9436-130289e16a26"
                href="https://exnodes.atlassian.net/secure/ViewProfile.jspa?accountId=712020%3A59b66c6e-1068-441c-9436-130289e16a26">
                    Duyen Nguyen
                </a>

                            </td>
        
                    <td bgcolor="#f0f0f0" width="20%">
                <b>Assignee:</b>
            </td>
            <td bgcolor="#ffffff" valign="top" nowrap  width="30%" >
                                    Unassigned
                            </td>
            </tr>
    	<tr>
		<td bgcolor="#f0f0f0" width="20%">
			<b>Resolution:</b>
		</td>
		<td bgcolor="#ffffff" valign="top" width="30%" nowrap>
            				Unresolved
                    </td>
                    <td bgcolor="#f0f0f0" width="20%">
                <b>Votes:</b>
            </td>
            <td bgcolor="#ffffff" valign="top" width="30%" nowrap>
                0
            </td>
        
    </tr>
    
        <tr>
        <td bgcolor="#f0f0f0" width="20%">
            <b>Labels:</b>
        </td>
        <td id="labels-38323-value" class="value" bgcolor="#ffffff" valign="top" colspan="3" nowrap>
                            None
                    </td>
    </tr>
    
    	<tr>
        		<td bgcolor="#f0f0f0" width="20%"><b>Remaining Estimate:</b></td>
        <td bgcolor="#ffffff" valign="top" nowrap width="80%" colspan="3">
                            Not Specified
            		</td>
    </tr>
    <tr>
                <td bgcolor="#f0f0f0" width="20%"><b>Time Spent:</b></td>
		<td bgcolor="#ffffff" valign="top" nowrap width="80%" colspan="3">
                            Not Specified
            		</td>
	</tr>
    <tr>
                <td bgcolor="#f0f0f0" width="20%"><b>Original estimate:</b></td>
		<td bgcolor="#ffffff" valign="top" nowrap width="80%" colspan="3">
                            Not Specified
            		</td>
    </tr>
    
    </table>



    <br />

    	<table class="grid" cellpadding="0" cellspacing="0" border="0" width="100%">
            

        
    


    
                        <tr>
                <td bgcolor="#f0f0f0" width="20%" valign="top"><b>Sprint:</b></td>
                <td id="customfield_10020-38323-value" class="value" bgcolor="#ffffff" width="80%"></td>
            </tr>
                                <tr>
                <td bgcolor="#f0f0f0" width="20%" valign="top"><b>Platform:</b></td>
                <td id="customfield_10687-38323-value" class="value" bgcolor="#ffffff" width="80%">    WEB</td>
            </tr>
            </table>

    <br/>

    <table cellpadding="2" cellspacing="0" border="0" width="100%" align="center">
    <tr>
        <td bgcolor="#bbbbbb" width="1%" nowrap align="center">
            &nbsp;<font color="#ffffff"><b>Description</b></font>&nbsp;
        </td>
        <td>&nbsp;</td>
    </tr>
    </table>

    <table cellpadding="0" cellspacing="0" border="0" width="100%">
    <tr>
        <td id="descriptionArea">
            <h3><a name="1.UseCaseDescription"></a>1. Use Case Description</h3>

<p>As an admin, I want to view and manage all user-submitted event reports so that I can prioritize unresolved violations and take appropriate moderation actions efficiently.</p>

<hr />

<h3><a name="2.UserWorkflow%28StepbyStep%29"></a>2. User Workflow (Step-by-Step)</h3>

<ol>
	<li>Admin logs into the Web Admin Panel.</li>
	<li>Navigates to the <b>Reports &gt; Event Reports</b> section.</li>
	<li>System loads a list of reports:
	<ul>
		<li>Outstanding (unresolved) reports are displayed at the top</li>
		<li>Resolved reports are greyed out and moved to the bottom</li>
	</ul>
	</li>
	<li>Admin can:
	<ul>
		<li>Tap a report card to view its details</li>
		<li>Filter or search by event, user, or status</li>
		<li>Update the status (e.g., mark as resolved)</li>
	</ul>
	</li>
	<li>Any status update refreshes the list order in real time.</li>
</ol>


<hr />

<h3><a name="3.FieldDefinitionsTable"></a>3. Field Definitions Table</h3>

<h4><a name="InputFields"></a>Input Fields</h4>

<div class='table-wrap'>
<table class='confluenceTable'><tbody>
<tr>
<th class='confluenceTh'>Field Name</th>
<th class='confluenceTh'>Field Type</th>
<th class='confluenceTh'>Validation Rule</th>
<th class='confluenceTh'>Mandatory</th>
<th class='confluenceTh'>Description</th>
</tr>
<tr>
<td class='confluenceTd'>Report Status</td>
<td class='confluenceTd'>Dropdown</td>
<td class='confluenceTd'>Only: Outstanding, Resolved</td>
<td class='confluenceTd'>Yes</td>
<td class='confluenceTd'>Used for sorting and update action</td>
</tr>
</tbody></table>
</div>


<h4><a name="InteractionElements"></a>Interaction Elements</h4>

<div class='table-wrap'>
<table class='confluenceTable'><tbody>
<tr>
<th class='confluenceTh'>Element Name</th>
<th class='confluenceTh'>Type</th>
<th class='confluenceTh'>Trigger Condition</th>
<th class='confluenceTh'>Description</th>
</tr>
<tr>
<td class='confluenceTd'>Report Card</td>
<td class='confluenceTd'>Button</td>
<td class='confluenceTd'>Always visible</td>
<td class='confluenceTd'>Opens detailed view of a single report</td>
</tr>
<tr>
<td class='confluenceTd'>Mark Resolved</td>
<td class='confluenceTd'>Button</td>
<td class='confluenceTd'>Only if status = Outstanding</td>
<td class='confluenceTd'>Updates the report status to Resolved</td>
</tr>
<tr>
<td class='confluenceTd'>Filter Toggle</td>
<td class='confluenceTd'>Toggle</td>
<td class='confluenceTd'>Optional</td>
<td class='confluenceTd'>Filter by status, date, event, user</td>
</tr>
</tbody></table>
</div>


<hr />

<h3><a name="4.DataDisplayTable%28ReportListView%29"></a>4. Data Display Table (Report List View)</h3>

<div class='table-wrap'>
<table class='confluenceTable'><tbody>
<tr>
<th class='confluenceTh'>Data Name</th>
<th class='confluenceTh'>Data Type</th>
<th class='confluenceTh'>Display When Empty</th>
<th class='confluenceTh'>Format</th>
<th class='confluenceTh'>Description</th>
</tr>
<tr>
<td class='confluenceTd'>Report Date</td>
<td class='confluenceTd'>Date</td>
<td class='confluenceTd'>“--”</td>
<td class='confluenceTd'>YYYY-MM-DD</td>
<td class='confluenceTd'>Date report was submitted</td>
</tr>
<tr>
<td class='confluenceTd'>Event Title</td>
<td class='confluenceTd'>Text</td>
<td class='confluenceTd'>“Unnamed Event”</td>
<td class='confluenceTd'>Plaintext</td>
<td class='confluenceTd'>Event associated with the report</td>
</tr>
<tr>
<td class='confluenceTd'>Reported By</td>
<td class='confluenceTd'>Text</td>
<td class='confluenceTd'>“Anonymous”</td>
<td class='confluenceTd'>Full name or User ID</td>
<td class='confluenceTd'>User who submitted the report</td>
</tr>
<tr>
<td class='confluenceTd'>Report Status</td>
<td class='confluenceTd'>Badge</td>
<td class='confluenceTd'>“Unresolved”</td>
<td class='confluenceTd'>Outstanding/Resolved</td>
<td class='confluenceTd'>Used to control card placement and color</td>
</tr>
<tr>
<td class='confluenceTd'>Reason</td>
<td class='confluenceTd'>Text</td>
<td class='confluenceTd'>“--”</td>
<td class='confluenceTd'>One-line reason summary</td>
<td class='confluenceTd'>Admin preview of reported issue</td>
</tr>
</tbody></table>
</div>


<hr />

<h3><a name="5.AcceptanceCriteria"></a>5. Acceptance Criteria</h3>

<ul>
	<li><b>AC1</b>: Reports must be displayed in descending order by:
	<ul>
		<li>Outstanding reports first (newest first)</li>
		<li>Resolved reports second (newest first within that group)</li>
	</ul>
	</li>
	<li><b>AC2</b>: Report cards marked as Resolved are greyed out visually.</li>
	<li><b>AC3</b>: Tapping a report opens its full details and allows status update.</li>
	<li><b>AC4</b>: Reports can be filtered by date, event, or reporter.</li>
	<li><b>AC5</b>: Report status update must be logged with admin ID and timestamp.</li>
</ul>


<hr />

<h3><a name="6.SystemRules"></a>6. System Rules</h3>

<ul>
	<li>Reports are sorted server-side using <tt>status</tt> and <tt>created_at</tt>.</li>
	<li>Status field values: <tt>outstanding</tt>, <tt>resolved</tt>.</li>
	<li>Greyed styling applied only when status = <tt>resolved</tt>.</li>
	<li>Admin actions (status updates) are audit-logged.</li>
	<li>Archived reports remain visible unless filtered out explicitly.</li>
</ul>


<hr />

<h3><a name="7.UXOptimizations"></a>7. UX Optimizations</h3>

<ul>
	<li>Use red badge for "Outstanding", gray for "Resolved".</li>
	<li>Resolved reports are visually dimmed to reduce prominence.</li>
	<li>Live update of list after status change—no manual refresh.</li>
	<li>Empty state message: <em>“No reports at this time.”</em></li>
</ul>

            <br/>
        </td>
    </tr>
    </table>

Generated at Thu Jul 03 08:23:55 UTC 2025 by Kid Nguyễn using Jira 1001.0.0-SNAPSHOT#100286-rev:954e9e452cb84460b72a974aab214235167cd4a5.

</body>
</html>