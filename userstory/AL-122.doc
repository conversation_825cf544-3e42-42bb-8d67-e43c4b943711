<!DOCTYPE html>
<html>
<head>
    <title>[#AL-122] [MB - Misconduct Report] Report Event</title>
    <meta http-equiv="Content-Type" Content="application/vnd.ms-word; charset=UTF-8">
        <style type="text/css">

.tableBorder, .grid
{
    background-color: #fff;
    width: 100%;
    border-collapse: collapse;
}

.tableBorder td, .grid td
{
    vertical-align: top;
    padding: 2px;
    border: 1px solid #ccc;
}

.noPadding
{
    padding: 0 !important;
}

h3 .subText
{
    font-size: 60%;
    font-weight: normal;
}

.tabLabel
{
    font-weight: bold;
    border: 1px solid #ccc;
    border-bottom:none;
    padding: 2px;
    display: inline;
}

td.blank
{
    padding: 0;
    margin: 0;
}

.blank td
{
    border: none;
}

#descriptionArea
{
    margin: 0;
    padding: 2px;
    border: 1px solid #ccc;
}

hr
{
    border-top:1px solid #aaa;
}

hr.fullcontent
{
  height: 15px;
  padding: 10px 0;
  background: #fff url('https://exnodes.atlassian.net/images/icons/hr.gif') no-repeat scroll center;
}

</style>

</head>
<body>

<table class="tableBorder" cellpadding="0" cellspacing="0" border="0" width="100%">
    <tr>
        <td bgcolor="#f0f0f0" width="100%" colspan="2" valign="top">
                            <b><a id="parent_issue_summary" href="https://exnodes.atlassian.net/browse/AL-123">Misconduct Report</a></b>
                <span style="font-size: 9px">(<a id="parent_issue_key" href="https://exnodes.atlassian.net/browse/AL-123">AL-123</a>)</span>
                <h3 class="formtitle"><img src="https://exnodes.atlassian.net/images/icons/link_out_bot.gif" width="16" height="16" />
                        [AL-122]&nbsp;<a href="https://exnodes.atlassian.net/browse/AL-122">[MB - Misconduct Report] Report Event</a>
            <span class="subText">
               Created: 13/Jun/25                   &nbsp;Updated: 16/Jun/25

                                            </span>
            </h3>
        </td>
    </tr>
    <tr>
        <td width="20%"><b>Status:</b></td>
        <td width="80%">Specified</td>
    </tr>
    <tr>
        <td width="20%"><b>Project:</b></td>
        <td width="80%"><a href="https://exnodes.atlassian.net/secure/BrowseProject.jspa?id=10842">AIOS Link</a></td>
    </tr>

        <tr>
            <td><b>Components:</b></td>
            <td>
                            None
                </td>
    </tr>
    

        <tr>
            <td><b>Affects versions:</b></td>
            <td>
                            None
                </td>
    </tr>
    

        <tr>
            <td><b>Fix versions:</b></td>
            <td>
                            None
                </td>
    </tr>
    
    
            <td width="20%"><b>Parent:</b></td>
        <td width="80%"><a href="https://exnodes.atlassian.net/browse/AL-123">Misconduct Report</a></td>
    </table>

<br />
<table class="grid" cellpadding="0" cellspacing="0" border="0" width="100%">
    <tr>
        <td bgcolor="#f0f0f0" valign="top" width="20%">
            <b>Type:</b>
        </td>
        <td bgcolor="#ffffff" valign="top"  width="30%" >
            Story
        </td>

                    <td bgcolor="#f0f0f0">
                <b>Priority:</b>
            </td>
            <td bgcolor="#ffffff" valign="top" nowrap>
                Medium
            </td>
            </tr>
    <tr>
                        <td bgcolor="#f0f0f0" valign="top" width="20%">
                <b>Reporter:</b>
            </td>
            <td bgcolor="#ffffff" valign="top"  width="30%" >
                                        
                
        
            <a class="user-hover" rel="712020:59b66c6e-1068-441c-9436-130289e16a26"
                id="word_reporter_712020:59b66c6e-1068-441c-9436-130289e16a26"
                href="https://exnodes.atlassian.net/secure/ViewProfile.jspa?accountId=712020%3A59b66c6e-1068-441c-9436-130289e16a26">
                    Duyen Nguyen
                </a>

                            </td>
        
                    <td bgcolor="#f0f0f0" width="20%">
                <b>Assignee:</b>
            </td>
            <td bgcolor="#ffffff" valign="top" nowrap  width="30%" >
                                    Unassigned
                            </td>
            </tr>
    	<tr>
		<td bgcolor="#f0f0f0" width="20%">
			<b>Resolution:</b>
		</td>
		<td bgcolor="#ffffff" valign="top" width="30%" nowrap>
            				Unresolved
                    </td>
                    <td bgcolor="#f0f0f0" width="20%">
                <b>Votes:</b>
            </td>
            <td bgcolor="#ffffff" valign="top" width="30%" nowrap>
                0
            </td>
        
    </tr>
    
        <tr>
        <td bgcolor="#f0f0f0" width="20%">
            <b>Labels:</b>
        </td>
        <td id="labels-38319-value" class="value" bgcolor="#ffffff" valign="top" colspan="3" nowrap>
                            None
                    </td>
    </tr>
    
    	<tr>
        		<td bgcolor="#f0f0f0" width="20%"><b>Remaining Estimate:</b></td>
        <td bgcolor="#ffffff" valign="top" nowrap width="80%" colspan="3">
                            Not Specified
            		</td>
    </tr>
    <tr>
                <td bgcolor="#f0f0f0" width="20%"><b>Time Spent:</b></td>
		<td bgcolor="#ffffff" valign="top" nowrap width="80%" colspan="3">
                            Not Specified
            		</td>
	</tr>
    <tr>
                <td bgcolor="#f0f0f0" width="20%"><b>Original estimate:</b></td>
		<td bgcolor="#ffffff" valign="top" nowrap width="80%" colspan="3">
                            Not Specified
            		</td>
    </tr>
    
    </table>



    <br />

    	<table class="grid" cellpadding="0" cellspacing="0" border="0" width="100%">
            

        
    


    
                        <tr>
                <td bgcolor="#f0f0f0" width="20%" valign="top"><b>Sprint:</b></td>
                <td id="customfield_10020-38319-value" class="value" bgcolor="#ffffff" width="80%"></td>
            </tr>
                                <tr>
                <td bgcolor="#f0f0f0" width="20%" valign="top"><b>Platform:</b></td>
                <td id="customfield_10687-38319-value" class="value" bgcolor="#ffffff" width="80%">    MOBILE</td>
            </tr>
            </table>

    <br/>

    <table cellpadding="2" cellspacing="0" border="0" width="100%" align="center">
    <tr>
        <td bgcolor="#bbbbbb" width="1%" nowrap align="center">
            &nbsp;<font color="#ffffff"><b>Description</b></font>&nbsp;
        </td>
        <td>&nbsp;</td>
    </tr>
    </table>

    <table cellpadding="0" cellspacing="0" border="0" width="100%">
    <tr>
        <td id="descriptionArea">
            <h3><a name="1.UseCaseDescription"></a>1. Use Case Description</h3>

<p>As a user who has attended an event (free or ticketed), I want to report that event if it violated community guidelines, so that the admin team can review the case and take appropriate action.</p>

<hr />

<h3><a name="2.UserWorkflow%28StepbyStep%29"></a>2. User Workflow (Step-by-Step)</h3>

<ol>
	<li>User views an <b>event detail</b> from their <b>past event history</b>.</li>
	<li>If the event has concluded, a <b>“Report Event”</b> button is shown.</li>
	<li>User taps <b>Report Event</b> → system opens a form with:
	<ul>
		<li>Predefined reason dropdown (e.g., spam, misleading info, safety issue)</li>
		<li>Optional comment box</li>
	</ul>
	</li>
	<li>User submits the report.</li>
	<li>System confirms submission with toast: <em>“Your report has been submitted.”</em></li>
	<li>Report is forwarded to the <b>admin dashboard</b> on the web platform for moderation.</li>
</ol>


<hr />

<h3><a name="3.FieldDefinitionsTable"></a>3. Field Definitions Table</h3>

<div class='table-wrap'>
<table class='confluenceTable'><tbody>
<tr>
<th class='confluenceTh'>Field Name</th>
<th class='confluenceTh'>Input Type</th>
<th class='confluenceTh'>Required</th>
<th class='confluenceTh'>Validation Rules</th>
</tr>
<tr>
<td class='confluenceTd'>Reason</td>
<td class='confluenceTd'>Dropdown</td>
<td class='confluenceTd'>✅</td>
<td class='confluenceTd'>Must select one of the predefined report reasons</td>
</tr>
<tr>
<td class='confluenceTd'>Additional Notes</td>
<td class='confluenceTd'>Multiline Text</td>
<td class='confluenceTd'>❌</td>
<td class='confluenceTd'>Max 500 characters</td>
</tr>
</tbody></table>
</div>


<hr />

<h3><a name="4.DataDisplayTable%28MobileUI%29"></a>4. Data Display Table (Mobile UI)</h3>

<div class='table-wrap'>
<table class='confluenceTable'><tbody>
<tr>
<th class='confluenceTh'>Display Element</th>
<th class='confluenceTh'>Example</th>
<th class='confluenceTh'>Notes</th>
</tr>
<tr>
<td class='confluenceTd'>Report Reason</td>
<td class='confluenceTd'>“Misleading event description”</td>
<td class='confluenceTd'>Pre-filled dropdown</td>
</tr>
<tr>
<td class='confluenceTd'>Comment Field</td>
<td class='confluenceTd'>“Tell us what happened…”</td>
<td class='confluenceTd'>Optional</td>
</tr>
<tr>
<td class='confluenceTd'>Submit Button</td>
<td class='confluenceTd'>“Send Report”</td>
<td class='confluenceTd'>Disabled until a reason is selected</td>
</tr>
<tr>
<td class='confluenceTd'>Confirmation Toast</td>
<td class='confluenceTd'>“Your report has been submitted.”</td>
<td class='confluenceTd'>Shown upon successful submission</td>
</tr>
</tbody></table>
</div>


<hr />

<h3><a name="5.AcceptanceCriteria"></a>5. Acceptance Criteria</h3>

<ul>
	<li><b>AC1</b>: Report option only visible for events that have ended.</li>
	<li><b>AC2</b>: User must have attended (RSVP'd or had a ticket) to be eligible to report.</li>
	<li><b>AC3</b>: System requires reason to be selected before submitting.</li>
	<li><b>AC4</b>: All submitted reports are logged and sent to the admin web portal.</li>
	<li><b>AC5</b>: Report triggers confirmation toast after submission.</li>
</ul>


<hr />

<h3><a name="6.SystemRules"></a>6. System Rules</h3>

<ul>
	<li>Each report is linked to:
	<ul>
		<li>User ID</li>
		<li>Event ID</li>
		<li>Timestamp</li>
		<li>Selected reason</li>
		<li>Optional comment</li>
	</ul>
	</li>
	<li>Duplicate reports by the same user for the same event are blocked.</li>
	<li>Admin view includes report list with sorting and filtering by event, user, or reason.</li>
	<li>Events with ≥X reports (configurable threshold) are auto-flagged for high priority review.</li>
</ul>


<hr />

<h3><a name="7.UXOptimizations"></a>7. UX Optimizations</h3>

<ul>
	<li>Use alert icon (e.g., ⚠️) next to "Report Event" CTA</li>
	<li>Collapse comment field until a reason is selected</li>
	<li>Redirect user back to past event page after submitting</li>
	<li>Display subtle prompt: <em>“Only report if you believe this event broke our guidelines.”</em></li>
</ul>

            <br/>
        </td>
    </tr>
    </table>

Generated at Thu Jul 03 08:19:53 UTC 2025 by Kid Nguyễn using Jira 1001.0.0-SNAPSHOT#100286-rev:954e9e452cb84460b72a974aab214235167cd4a5.

</body>
</html>