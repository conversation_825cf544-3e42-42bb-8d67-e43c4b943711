<!DOCTYPE html>
<html>
<head>
    <title>[#AL-37] [MB - Meeting Scheduling] View Schedule</title>
    <meta http-equiv="Content-Type" Content="application/vnd.ms-word; charset=UTF-8">
        <style type="text/css">

.tableBorder, .grid
{
    background-color: #fff;
    width: 100%;
    border-collapse: collapse;
}

.tableBorder td, .grid td
{
    vertical-align: top;
    padding: 2px;
    border: 1px solid #ccc;
}

.noPadding
{
    padding: 0 !important;
}

h3 .subText
{
    font-size: 60%;
    font-weight: normal;
}

.tabLabel
{
    font-weight: bold;
    border: 1px solid #ccc;
    border-bottom:none;
    padding: 2px;
    display: inline;
}

td.blank
{
    padding: 0;
    margin: 0;
}

.blank td
{
    border: none;
}

#descriptionArea
{
    margin: 0;
    padding: 2px;
    border: 1px solid #ccc;
}

hr
{
    border-top:1px solid #aaa;
}

hr.fullcontent
{
  height: 15px;
  padding: 10px 0;
  background: #fff url('https://exnodes.atlassian.net/images/icons/hr.gif') no-repeat scroll center;
}

</style>

</head>
<body>

<table class="tableBorder" cellpadding="0" cellspacing="0" border="0" width="100%">
    <tr>
        <td bgcolor="#f0f0f0" width="100%" colspan="2" valign="top">
                            <b><a id="parent_issue_summary" href="https://exnodes.atlassian.net/browse/AL-68">Meeting Scheduling</a></b>
                <span style="font-size: 9px">(<a id="parent_issue_key" href="https://exnodes.atlassian.net/browse/AL-68">AL-68</a>)</span>
                <h3 class="formtitle"><img src="https://exnodes.atlassian.net/images/icons/link_out_bot.gif" width="16" height="16" />
                        [AL-37]&nbsp;<a href="https://exnodes.atlassian.net/browse/AL-37">[MB - Meeting Scheduling] View Schedule</a>
            <span class="subText">
               Created: 04/Jun/25                   &nbsp;Updated: 25/Jun/25

                                            </span>
            </h3>
        </td>
    </tr>
    <tr>
        <td width="20%"><b>Status:</b></td>
        <td width="80%">In Progress</td>
    </tr>
    <tr>
        <td width="20%"><b>Project:</b></td>
        <td width="80%"><a href="https://exnodes.atlassian.net/secure/BrowseProject.jspa?id=10842">AIOS Link</a></td>
    </tr>

        <tr>
            <td><b>Components:</b></td>
            <td>
                            None
                </td>
    </tr>
    

        <tr>
            <td><b>Affects versions:</b></td>
            <td>
                            None
                </td>
    </tr>
    

        <tr>
            <td><b>Fix versions:</b></td>
            <td>
                            None
                </td>
    </tr>
    
    
            <td width="20%"><b>Parent:</b></td>
        <td width="80%"><a href="https://exnodes.atlassian.net/browse/AL-68">Meeting Scheduling</a></td>
    </table>

<br />
<table class="grid" cellpadding="0" cellspacing="0" border="0" width="100%">
    <tr>
        <td bgcolor="#f0f0f0" valign="top" width="20%">
            <b>Type:</b>
        </td>
        <td bgcolor="#ffffff" valign="top"  width="30%" >
            Story
        </td>

                    <td bgcolor="#f0f0f0">
                <b>Priority:</b>
            </td>
            <td bgcolor="#ffffff" valign="top" nowrap>
                Medium
            </td>
            </tr>
    <tr>
                        <td bgcolor="#f0f0f0" valign="top" width="20%">
                <b>Reporter:</b>
            </td>
            <td bgcolor="#ffffff" valign="top"  width="30%" >
                                        
                
        
            <a class="user-hover" rel="712020:59b66c6e-1068-441c-9436-130289e16a26"
                id="word_reporter_712020:59b66c6e-1068-441c-9436-130289e16a26"
                href="https://exnodes.atlassian.net/secure/ViewProfile.jspa?accountId=712020%3A59b66c6e-1068-441c-9436-130289e16a26">
                    Duyen Nguyen
                </a>

                            </td>
        
                    <td bgcolor="#f0f0f0" width="20%">
                <b>Assignee:</b>
            </td>
            <td bgcolor="#ffffff" valign="top" nowrap  width="30%" >
                                    Unassigned
                            </td>
            </tr>
    	<tr>
		<td bgcolor="#f0f0f0" width="20%">
			<b>Resolution:</b>
		</td>
		<td bgcolor="#ffffff" valign="top" width="30%" nowrap>
            				Unresolved
                    </td>
                    <td bgcolor="#f0f0f0" width="20%">
                <b>Votes:</b>
            </td>
            <td bgcolor="#ffffff" valign="top" width="30%" nowrap>
                0
            </td>
        
    </tr>
    
        <tr>
        <td bgcolor="#f0f0f0" width="20%">
            <b>Labels:</b>
        </td>
        <td id="labels-37374-value" class="value" bgcolor="#ffffff" valign="top" colspan="3" nowrap>
                            None
                    </td>
    </tr>
    
    	<tr>
        		<td bgcolor="#f0f0f0" width="20%"><b>Remaining Estimate:</b></td>
        <td bgcolor="#ffffff" valign="top" nowrap width="80%" colspan="3">
                            Not Specified
            		</td>
    </tr>
    <tr>
                <td bgcolor="#f0f0f0" width="20%"><b>Time Spent:</b></td>
		<td bgcolor="#ffffff" valign="top" nowrap width="80%" colspan="3">
                            Not Specified
            		</td>
	</tr>
    <tr>
                <td bgcolor="#f0f0f0" width="20%"><b>Original estimate:</b></td>
		<td bgcolor="#ffffff" valign="top" nowrap width="80%" colspan="3">
                            Not Specified
            		</td>
    </tr>
    
    </table>



    <br />

    	<table class="grid" cellpadding="0" cellspacing="0" border="0" width="100%">
            

        
    


    
                        <tr>
                <td bgcolor="#f0f0f0" width="20%" valign="top"><b>Sprint:</b></td>
                <td id="customfield_10020-37374-value" class="value" bgcolor="#ffffff" width="80%"></td>
            </tr>
                                <tr>
                <td bgcolor="#f0f0f0" width="20%" valign="top"><b>Platform:</b></td>
                <td id="customfield_10687-37374-value" class="value" bgcolor="#ffffff" width="80%">    MOBILE</td>
            </tr>
            </table>

    <br/>

    <table cellpadding="2" cellspacing="0" border="0" width="100%" align="center">
    <tr>
        <td bgcolor="#bbbbbb" width="1%" nowrap align="center">
            &nbsp;<font color="#ffffff"><b>Description</b></font>&nbsp;
        </td>
        <td>&nbsp;</td>
    </tr>
    </table>

    <table cellpadding="0" cellspacing="0" border="0" width="100%">
    <tr>
        <td id="descriptionArea">
            <h3><a name="1.UseCaseDescription"></a>1. Use Case Description</h3>

<p>As a user, I want to view all my upcoming meetings, events, and booked services in a single schedule view, so that I can manage my time efficiently and keep track of my commitments within the app.</p>

<hr />

<h3><a name="2.UserWorkflow%28StepbyStep%29"></a>2. User Workflow (Step-by-Step)</h3>

<ul>
	<li>Step 1: User navigates to the <b>Schedule</b> section from dashboard shortcut.</li>
	<li>Step 2: System loads a consolidated view of all future appointments including:
	<ul>
		<li>Confirmed <b>Meetings</b> (via Invite to Meet)</li>
		<li>Registered <b>Events</b></li>
		<li>Confirmed <b>Booked Services</b> (from Marketplace or Gifting)</li>
	</ul>
	</li>
	<li>Step 3: User scrolls through upcoming items sorted by <b>date &amp; time</b>.</li>
	<li>Step 4: Tapping on any item opens a <b>detail view</b> (e.g., contact, time, location, notes).</li>
	<li>Step 5: If applicable, user may <b>cancel</b> or <b>reschedule</b> (based on event/service rules).</li>
</ul>


<hr />

<h3><a name="3.FieldDefinitionsTable"></a>3. Field Definitions Table</h3>

<h4><a name="DataFields%28ScheduleEntries%29"></a>Data Fields (Schedule Entries)</h4>

<div class='table-wrap'>
<table class='confluenceTable'><tbody>
<tr>
<th class='confluenceTh'>Field Name</th>
<th class='confluenceTh'>Field Type</th>
<th class='confluenceTh'>Required</th>
<th class='confluenceTh'>Description</th>
</tr>
<tr>
<td class='confluenceTd'>Schedule Type</td>
<td class='confluenceTd'>Enum</td>
<td class='confluenceTd'>✅</td>
<td class='confluenceTd'>One of: Meeting, Event, Booked Service</td>
</tr>
<tr>
<td class='confluenceTd'>Date &amp; Time</td>
<td class='confluenceTd'>Datetime</td>
<td class='confluenceTd'>✅</td>
<td class='confluenceTd'>Scheduled time of the activity</td>
</tr>
<tr>
<td class='confluenceTd'>Title</td>
<td class='confluenceTd'>Text</td>
<td class='confluenceTd'>✅</td>
<td class='confluenceTd'>Activity title or subject</td>
</tr>
<tr>
<td class='confluenceTd'>Related Contact</td>
<td class='confluenceTd'>User Link</td>
<td class='confluenceTd'>✅ if Meeting</td>
<td class='confluenceTd'>Who the meeting or service is with</td>
</tr>
<tr>
<td class='confluenceTd'>Location / Link</td>
<td class='confluenceTd'>Text/URL</td>
<td class='confluenceTd'>✅</td>
<td class='confluenceTd'>Address or virtual meeting link</td>
</tr>
<tr>
<td class='confluenceTd'>Status</td>
<td class='confluenceTd'>Badge</td>
<td class='confluenceTd'>✅</td>
<td class='confluenceTd'>Confirmed, Cancelled, Rescheduled, Completed</td>
</tr>
</tbody></table>
</div>


<hr />

<h3><a name="4.DataDisplayTable%28CardView%29"></a>4. Data Display Table (Card View)</h3>

<div class='table-wrap'>
<table class='confluenceTable'><tbody>
<tr>
<th class='confluenceTh'>Data Name</th>
<th class='confluenceTh'>Data Type</th>
<th class='confluenceTh'>Display When Empty</th>
<th class='confluenceTh'>Format</th>
<th class='confluenceTh'>Description</th>
</tr>
<tr>
<td class='confluenceTd'>Date Header</td>
<td class='confluenceTd'>Text</td>
<td class='confluenceTd'>“No events yet”</td>
<td class='confluenceTd'>“Today”, “Tomorrow”</td>
<td class='confluenceTd'>Group label by day</td>
</tr>
<tr>
<td class='confluenceTd'>Schedule Card</td>
<td class='confluenceTd'>Card</td>
<td class='confluenceTd'>Hidden</td>
<td class='confluenceTd'>Structured card</td>
<td class='confluenceTd'>Displays icon, title, time, location</td>
</tr>
<tr>
<td class='confluenceTd'>Type Icon</td>
<td class='confluenceTd'>Icon</td>
<td class='confluenceTd'>Hidden</td>
<td class='confluenceTd'>Event/Meeting/Service</td>
<td class='confluenceTd'>Visual cue for entry type</td>
</tr>
<tr>
<td class='confluenceTd'>Status Tag</td>
<td class='confluenceTd'>Badge</td>
<td class='confluenceTd'>“Pending”</td>
<td class='confluenceTd'>e.g., Confirmed</td>
<td class='confluenceTd'>Status color-coded</td>
</tr>
</tbody></table>
</div>


<hr />

<h3><a name="5.AcceptanceCriteria"></a>5. Acceptance Criteria</h3>

<ul>
	<li><b>AC1</b>: Schedule screen must display all confirmed future meetings, events, and services.</li>
	<li><b>AC2</b>: Entries are grouped and sorted by date in ascending order.</li>
	<li><b>AC3</b>: Each card must show time, type icon, title, and location/link.</li>
	<li><b>AC4</b>: Tapping a card opens detail view with full metadata.</li>
	<li><b>AC5</b>: System must exclude cancelled or expired items by default (filter optional).</li>
	<li><b>AC6</b>: Supports scrollable list with lazy loading for long schedules.</li>
</ul>


<hr />

<h3><a name="6.SystemRules"></a>6. System Rules</h3>

<ul>
	<li>Schedule data is synced in real time with event, meeting, and service modules.</li>
	<li>Past entries are not shown unless user activates a “History” toggle.</li>
	<li>Cancelled items must be retained in backend but excluded from default view.</li>
	<li>All times are normalized to user’s device timezone.</li>
</ul>


<hr />

<h3><a name="7.UXOptimizations"></a>7. UX Optimizations</h3>

<ul>
	<li>Sticky day headers (e.g., “Today”, “This Week”) while scrolling.</li>
	<li>Use icons or colored borders to differentiate between Meeting, Event, and Service.</li>
	<li>Optionally allow “Add to Calendar” or export (ICS) for Pro/Premium users.</li>
	<li>Empty state message: <em>“No scheduled activities. Start by inviting or booking!”</em></li>
	<li>Quick filters at the top: <span class="error">&#91;All&#93;</span> <span class="error">&#91;Meetings&#93;</span> <span class="error">&#91;Events&#93;</span> <span class="error">&#91;Services&#93;</span></li>
</ul>


<hr />
            <br/>
        </td>
    </tr>
    </table>

Generated at Fri Jun 27 02:51:22 UTC 2025 by Kid Nguyễn using Jira 1001.0.0-SNAPSHOT#100285-rev:3c7b32e5e5b342f76f058c25245725788fa6e907.

</body>
</html>