<!DOCTYPE html>
<html>
<head>
    <title>[#AL-112] [MB - User Profile] Change Business Details</title>
    <meta http-equiv="Content-Type" Content="application/vnd.ms-word; charset=UTF-8">
        <style type="text/css">

.tableBorder, .grid
{
    background-color: #fff;
    width: 100%;
    border-collapse: collapse;
}

.tableBorder td, .grid td
{
    vertical-align: top;
    padding: 2px;
    border: 1px solid #ccc;
}

.noPadding
{
    padding: 0 !important;
}

h3 .subText
{
    font-size: 60%;
    font-weight: normal;
}

.tabLabel
{
    font-weight: bold;
    border: 1px solid #ccc;
    border-bottom:none;
    padding: 2px;
    display: inline;
}

td.blank
{
    padding: 0;
    margin: 0;
}

.blank td
{
    border: none;
}

#descriptionArea
{
    margin: 0;
    padding: 2px;
    border: 1px solid #ccc;
}

hr
{
    border-top:1px solid #aaa;
}

hr.fullcontent
{
  height: 15px;
  padding: 10px 0;
  background: #fff url('https://exnodes.atlassian.net/images/icons/hr.gif') no-repeat scroll center;
}

</style>

</head>
<body>

<table class="tableBorder" cellpadding="0" cellspacing="0" border="0" width="100%">
    <tr>
        <td bgcolor="#f0f0f0" width="100%" colspan="2" valign="top">
                            <b><a id="parent_issue_summary" href="https://exnodes.atlassian.net/browse/AL-58">User Profile</a></b>
                <span style="font-size: 9px">(<a id="parent_issue_key" href="https://exnodes.atlassian.net/browse/AL-58">AL-58</a>)</span>
                <h3 class="formtitle"><img src="https://exnodes.atlassian.net/images/icons/link_out_bot.gif" width="16" height="16" />
                        [AL-112]&nbsp;<a href="https://exnodes.atlassian.net/browse/AL-112">[MB - User Profile] Change Business Details</a>
            <span class="subText">
               Created: 12/Jun/25                   &nbsp;Updated: 25/Jun/25

                                            </span>
            </h3>
        </td>
    </tr>
    <tr>
        <td width="20%"><b>Status:</b></td>
        <td width="80%">In Progress</td>
    </tr>
    <tr>
        <td width="20%"><b>Project:</b></td>
        <td width="80%"><a href="https://exnodes.atlassian.net/secure/BrowseProject.jspa?id=10842">AIOS Link</a></td>
    </tr>

        <tr>
            <td><b>Components:</b></td>
            <td>
                            None
                </td>
    </tr>
    

        <tr>
            <td><b>Affects versions:</b></td>
            <td>
                            None
                </td>
    </tr>
    

        <tr>
            <td><b>Fix versions:</b></td>
            <td>
                            None
                </td>
    </tr>
    
    
            <td width="20%"><b>Parent:</b></td>
        <td width="80%"><a href="https://exnodes.atlassian.net/browse/AL-58">User Profile</a></td>
    </table>

<br />
<table class="grid" cellpadding="0" cellspacing="0" border="0" width="100%">
    <tr>
        <td bgcolor="#f0f0f0" valign="top" width="20%">
            <b>Type:</b>
        </td>
        <td bgcolor="#ffffff" valign="top"  width="30%" >
            Story
        </td>

                    <td bgcolor="#f0f0f0">
                <b>Priority:</b>
            </td>
            <td bgcolor="#ffffff" valign="top" nowrap>
                Medium
            </td>
            </tr>
    <tr>
                        <td bgcolor="#f0f0f0" valign="top" width="20%">
                <b>Reporter:</b>
            </td>
            <td bgcolor="#ffffff" valign="top"  width="30%" >
                                        
                
        
            <a class="user-hover" rel="712020:59b66c6e-1068-441c-9436-130289e16a26"
                id="word_reporter_712020:59b66c6e-1068-441c-9436-130289e16a26"
                href="https://exnodes.atlassian.net/secure/ViewProfile.jspa?accountId=712020%3A59b66c6e-1068-441c-9436-130289e16a26">
                    Duyen Nguyen
                </a>

                            </td>
        
                    <td bgcolor="#f0f0f0" width="20%">
                <b>Assignee:</b>
            </td>
            <td bgcolor="#ffffff" valign="top" nowrap  width="30%" >
                                    Unassigned
                            </td>
            </tr>
    	<tr>
		<td bgcolor="#f0f0f0" width="20%">
			<b>Resolution:</b>
		</td>
		<td bgcolor="#ffffff" valign="top" width="30%" nowrap>
            				Unresolved
                    </td>
                    <td bgcolor="#f0f0f0" width="20%">
                <b>Votes:</b>
            </td>
            <td bgcolor="#ffffff" valign="top" width="30%" nowrap>
                0
            </td>
        
    </tr>
    
        <tr>
        <td bgcolor="#f0f0f0" width="20%">
            <b>Labels:</b>
        </td>
        <td id="labels-38247-value" class="value" bgcolor="#ffffff" valign="top" colspan="3" nowrap>
                            None
                    </td>
    </tr>
    
    	<tr>
        		<td bgcolor="#f0f0f0" width="20%"><b>Remaining Estimate:</b></td>
        <td bgcolor="#ffffff" valign="top" nowrap width="80%" colspan="3">
                            Not Specified
            		</td>
    </tr>
    <tr>
                <td bgcolor="#f0f0f0" width="20%"><b>Time Spent:</b></td>
		<td bgcolor="#ffffff" valign="top" nowrap width="80%" colspan="3">
                            Not Specified
            		</td>
	</tr>
    <tr>
                <td bgcolor="#f0f0f0" width="20%"><b>Original estimate:</b></td>
		<td bgcolor="#ffffff" valign="top" nowrap width="80%" colspan="3">
                            Not Specified
            		</td>
    </tr>
    
    </table>



    <br />

    	<table class="grid" cellpadding="0" cellspacing="0" border="0" width="100%">
            

        
    


    
                        <tr>
                <td bgcolor="#f0f0f0" width="20%" valign="top"><b>Sprint:</b></td>
                <td id="customfield_10020-38247-value" class="value" bgcolor="#ffffff" width="80%"></td>
            </tr>
                                <tr>
                <td bgcolor="#f0f0f0" width="20%" valign="top"><b>Platform:</b></td>
                <td id="customfield_10687-38247-value" class="value" bgcolor="#ffffff" width="80%">    MOBILE</td>
            </tr>
            </table>

    <br/>

    <table cellpadding="2" cellspacing="0" border="0" width="100%" align="center">
    <tr>
        <td bgcolor="#bbbbbb" width="1%" nowrap align="center">
            &nbsp;<font color="#ffffff"><b>Description</b></font>&nbsp;
        </td>
        <td>&nbsp;</td>
    </tr>
    </table>

    <table cellpadding="0" cellspacing="0" border="0" width="100%">
    <tr>
        <td id="descriptionArea">
            <h3><a name="1.UseCaseDescription"></a>1. Use Case Description</h3>

<p>As a merchant, I want to update my business profile so that I can keep my organizational identity and regulatory details up to date.</p>

<hr />

<h3><a name="2.UserWorkflow%28StepbyStep%29"></a>2. User Workflow (Step-by-Step)</h3>

<ol>
	<li>Merchant logs into the AIOS Link mobile app.</li>
	<li>Navigates to <b>Profile &gt; Business Settings</b>.</li>
	<li>Taps <b>Edit Organization Details</b>.</li>
	<li>System loads a pre-filled form with the current business profile.</li>
	<li>Merchant edits one or more fields (e.g., name, address).</li>
	<li>Taps <b>Save Changes</b>.</li>
	<li>System validates the fields and updates the record if successful.</li>
	<li>A toast appears: <em>“Business details updated successfully.”</em></li>
</ol>


<hr />

<h3><a name="3.FieldDefinitionsTable"></a>3. Field Definitions Table</h3>

<div class='table-wrap'>
<table class='confluenceTable'><tbody>
<tr>
<th class='confluenceTh'>Field Name</th>
<th class='confluenceTh'>Field Type</th>
<th class='confluenceTh'>Validation Rule</th>
<th class='confluenceTh'>Required</th>
<th class='confluenceTh'>Description</th>
</tr>
<tr>
<td class='confluenceTd'>Business Name</td>
<td class='confluenceTd'>Text</td>
<td class='confluenceTd'>1–100 characters, unique</td>
<td class='confluenceTd'>✅</td>
<td class='confluenceTd'>Legal name of the merchant’s business</td>
</tr>
<tr>
<td class='confluenceTd'>Business Address</td>
<td class='confluenceTd'>Text Area</td>
<td class='confluenceTd'>Max 200 characters</td>
<td class='confluenceTd'>✅</td>
<td class='confluenceTd'>Physical or registered mailing address</td>
</tr>
<tr>
<td class='confluenceTd'>EIN/DUNS Number</td>
<td class='confluenceTd'>Text</td>
<td class='confluenceTd'>Alphanumeric, must be unique</td>
<td class='confluenceTd'>✅</td>
<td class='confluenceTd'>Tax or legal registration identifier</td>
</tr>
<tr>
<td class='confluenceTd'>Date of Establishment</td>
<td class='confluenceTd'>Date Picker</td>
<td class='confluenceTd'>Past or current date</td>
<td class='confluenceTd'>✅</td>
<td class='confluenceTd'>When the business was founded</td>
</tr>
</tbody></table>
</div>


<hr />

<h3><a name="4.DataDisplayTable%28ConfirmationView%29"></a>4. Data Display Table (Confirmation View)</h3>

<div class='table-wrap'>
<table class='confluenceTable'><tbody>
<tr>
<th class='confluenceTh'>Field</th>
<th class='confluenceTh'>Example</th>
<th class='confluenceTh'>Notes</th>
</tr>
<tr>
<td class='confluenceTd'>Name</td>
<td class='confluenceTd'>“Sunrise Coffee Ltd.”</td>
<td class='confluenceTd'>Business display name</td>
</tr>
<tr>
<td class='confluenceTd'>EIN/DUNS</td>
<td class='confluenceTd'>“92-8471923”</td>
<td class='confluenceTd'>Visible in account view</td>
</tr>
<tr>
<td class='confluenceTd'>Last Updated</td>
<td class='confluenceTd'>“2025-06-14 09:35”</td>
<td class='confluenceTd'>System timestamp of last update</td>
</tr>
</tbody></table>
</div>


<hr />

<h3><a name="5.AcceptanceCriteria"></a>5. Acceptance Criteria</h3>

<ul>
	<li><b>AC1</b>: Merchant can access and update only organization-level fields.</li>
	<li><b>AC2</b>: EIN/DUNS must be unique across the system.</li>
	<li><b>AC3</b>: System must show inline errors for invalid inputs.</li>
	<li><b>AC4</b>: Save button remains disabled until all required fields are valid.</li>
	<li><b>AC5</b>: Upon success, updated values reflect immediately in profile.</li>
	<li><b>AC6</b>: Change history is logged with user ID and timestamp.</li>
</ul>


<hr />

<h3><a name="6.SystemRules"></a>6. System Rules</h3>

<ul>
	<li>Organization owner cannot be changed via mobile.</li>
	<li>EIN/DUNS must remain unique system-wide.</li>
	<li>Edit access is limited to users with merchant role.</li>
	<li>Change triggers a log entry in system audit history.</li>
</ul>


<hr />

<h3><a name="7.UXOptimizations"></a>7. UX Optimizations</h3>

<ul>
	<li>Pre-filled form with keyboard type matched per field (e.g., numeric for EIN).</li>
	<li>Real-time validation and field-specific error messages</li>
	<li>Sticky “Save Changes” button with loading spinner</li>
	<li>Success toast with optional return-to-dashboard prompt</li>
</ul>


<hr />
            <br/>
        </td>
    </tr>
    </table>

Generated at Fri Jun 27 02:25:03 UTC 2025 by Kid Nguyễn using Jira 1001.0.0-SNAPSHOT#100285-rev:3c7b32e5e5b342f76f058c25245725788fa6e907.

</body>
</html>