<!DOCTYPE html>
<html>
<head>
    <title>[#AL-94] [MB - Inventory] Publish Product to Marketplace</title>
    <meta http-equiv="Content-Type" Content="application/vnd.ms-word; charset=UTF-8">
        <style type="text/css">

.tableBorder, .grid
{
    background-color: #fff;
    width: 100%;
    border-collapse: collapse;
}

.tableBorder td, .grid td
{
    vertical-align: top;
    padding: 2px;
    border: 1px solid #ccc;
}

.noPadding
{
    padding: 0 !important;
}

h3 .subText
{
    font-size: 60%;
    font-weight: normal;
}

.tabLabel
{
    font-weight: bold;
    border: 1px solid #ccc;
    border-bottom:none;
    padding: 2px;
    display: inline;
}

td.blank
{
    padding: 0;
    margin: 0;
}

.blank td
{
    border: none;
}

#descriptionArea
{
    margin: 0;
    padding: 2px;
    border: 1px solid #ccc;
}

hr
{
    border-top:1px solid #aaa;
}

hr.fullcontent
{
  height: 15px;
  padding: 10px 0;
  background: #fff url('https://exnodes.atlassian.net/images/icons/hr.gif') no-repeat scroll center;
}

</style>

</head>
<body>

<table class="tableBorder" cellpadding="0" cellspacing="0" border="0" width="100%">
    <tr>
        <td bgcolor="#f0f0f0" width="100%" colspan="2" valign="top">
                            <b><a id="parent_issue_summary" href="https://exnodes.atlassian.net/browse/AL-89">Inventory</a></b>
                <span style="font-size: 9px">(<a id="parent_issue_key" href="https://exnodes.atlassian.net/browse/AL-89">AL-89</a>)</span>
                <h3 class="formtitle"><img src="https://exnodes.atlassian.net/images/icons/link_out_bot.gif" width="16" height="16" />
                        [AL-94]&nbsp;<a href="https://exnodes.atlassian.net/browse/AL-94">[MB - Inventory] Publish Product to Marketplace</a>
            <span class="subText">
               Created: 11/Jun/25                   &nbsp;Updated: 25/Jun/25

                                            </span>
            </h3>
        </td>
    </tr>
    <tr>
        <td width="20%"><b>Status:</b></td>
        <td width="80%">In Progress</td>
    </tr>
    <tr>
        <td width="20%"><b>Project:</b></td>
        <td width="80%"><a href="https://exnodes.atlassian.net/secure/BrowseProject.jspa?id=10842">AIOS Link</a></td>
    </tr>

        <tr>
            <td><b>Components:</b></td>
            <td>
                            None
                </td>
    </tr>
    

        <tr>
            <td><b>Affects versions:</b></td>
            <td>
                            None
                </td>
    </tr>
    

        <tr>
            <td><b>Fix versions:</b></td>
            <td>
                            None
                </td>
    </tr>
    
    
            <td width="20%"><b>Parent:</b></td>
        <td width="80%"><a href="https://exnodes.atlassian.net/browse/AL-89">Inventory</a></td>
    </table>

<br />
<table class="grid" cellpadding="0" cellspacing="0" border="0" width="100%">
    <tr>
        <td bgcolor="#f0f0f0" valign="top" width="20%">
            <b>Type:</b>
        </td>
        <td bgcolor="#ffffff" valign="top"  width="30%" >
            Story
        </td>

                    <td bgcolor="#f0f0f0">
                <b>Priority:</b>
            </td>
            <td bgcolor="#ffffff" valign="top" nowrap>
                Medium
            </td>
            </tr>
    <tr>
                        <td bgcolor="#f0f0f0" valign="top" width="20%">
                <b>Reporter:</b>
            </td>
            <td bgcolor="#ffffff" valign="top"  width="30%" >
                                        
                
        
            <a class="user-hover" rel="712020:59b66c6e-1068-441c-9436-130289e16a26"
                id="word_reporter_712020:59b66c6e-1068-441c-9436-130289e16a26"
                href="https://exnodes.atlassian.net/secure/ViewProfile.jspa?accountId=712020%3A59b66c6e-1068-441c-9436-130289e16a26">
                    Duyen Nguyen
                </a>

                            </td>
        
                    <td bgcolor="#f0f0f0" width="20%">
                <b>Assignee:</b>
            </td>
            <td bgcolor="#ffffff" valign="top" nowrap  width="30%" >
                                    Unassigned
                            </td>
            </tr>
    	<tr>
		<td bgcolor="#f0f0f0" width="20%">
			<b>Resolution:</b>
		</td>
		<td bgcolor="#ffffff" valign="top" width="30%" nowrap>
            				Unresolved
                    </td>
                    <td bgcolor="#f0f0f0" width="20%">
                <b>Votes:</b>
            </td>
            <td bgcolor="#ffffff" valign="top" width="30%" nowrap>
                0
            </td>
        
    </tr>
    
        <tr>
        <td bgcolor="#f0f0f0" width="20%">
            <b>Labels:</b>
        </td>
        <td id="labels-38102-value" class="value" bgcolor="#ffffff" valign="top" colspan="3" nowrap>
                            None
                    </td>
    </tr>
    
    	<tr>
        		<td bgcolor="#f0f0f0" width="20%"><b>Remaining Estimate:</b></td>
        <td bgcolor="#ffffff" valign="top" nowrap width="80%" colspan="3">
                            Not Specified
            		</td>
    </tr>
    <tr>
                <td bgcolor="#f0f0f0" width="20%"><b>Time Spent:</b></td>
		<td bgcolor="#ffffff" valign="top" nowrap width="80%" colspan="3">
                            Not Specified
            		</td>
	</tr>
    <tr>
                <td bgcolor="#f0f0f0" width="20%"><b>Original estimate:</b></td>
		<td bgcolor="#ffffff" valign="top" nowrap width="80%" colspan="3">
                            Not Specified
            		</td>
    </tr>
    
    </table>



    <br />

    	<table class="grid" cellpadding="0" cellspacing="0" border="0" width="100%">
            

        
    


    
                        <tr>
                <td bgcolor="#f0f0f0" width="20%" valign="top"><b>Sprint:</b></td>
                <td id="customfield_10020-38102-value" class="value" bgcolor="#ffffff" width="80%"></td>
            </tr>
                                <tr>
                <td bgcolor="#f0f0f0" width="20%" valign="top"><b>Platform:</b></td>
                <td id="customfield_10687-38102-value" class="value" bgcolor="#ffffff" width="80%">    MOBILE</td>
            </tr>
            </table>

    <br/>

    <table cellpadding="2" cellspacing="0" border="0" width="100%" align="center">
    <tr>
        <td bgcolor="#bbbbbb" width="1%" nowrap align="center">
            &nbsp;<font color="#ffffff"><b>Description</b></font>&nbsp;
        </td>
        <td>&nbsp;</td>
    </tr>
    </table>

    <table cellpadding="0" cellspacing="0" border="0" width="100%">
    <tr>
        <td id="descriptionArea">
            <h3><a name="1.UseCaseDescription"></a>1. Use Case Description</h3>

<p>As a user on the mobile platform, I want to list a product from my inventory on the marketplace so that other users can purchase it.</p>

<h3><a name="2.UserWorkflow"></a>2. User Workflow</h3>

<ul>
	<li>Step 1: From <b>Inventory Product Details</b>, user taps <b>Put to Marketplace</b> for a product not currently listed.</li>
	<li>Step 2: System displays a form requesting listing information.</li>
	<li>Step 3: User enters Listing Price and optionally enables Tax. If Tax is enabled, a Tax Percentage field appears.</li>
	<li>Step 4: User taps <b>Save</b>. System validates inputs and creates the marketplace listing.</li>
	<li>Step 5: Product details update to show marketplace information including listing price and listing date.</li>
</ul>


<h3><a name="3.FieldDefinitions"></a>3. Field Definitions</h3>

<h4><a name="InputFields"></a>Input Fields</h4>

<div class='table-wrap'>
<table class='confluenceTable'><tbody>
<tr>
<th class='confluenceTh'>Field Name</th>
<th class='confluenceTh'>Field Type</th>
<th class='confluenceTh'>Validation Rule</th>
<th class='confluenceTh'>Mandatory</th>
<th class='confluenceTh'>Description</th>
</tr>
<tr>
<td class='confluenceTd'>Listing Price</td>
<td class='confluenceTd'>Amount</td>
<td class='confluenceTd'>&gt; 0</td>
<td class='confluenceTd'>Yes</td>
<td class='confluenceTd'>Selling price in USD</td>
</tr>
<tr>
<td class='confluenceTd'>Enable Tax</td>
<td class='confluenceTd'>Toggle</td>
<td class='confluenceTd'>On or Off</td>
<td class='confluenceTd'>Yes</td>
<td class='confluenceTd'>Determines if tax applies</td>
</tr>
<tr>
<td class='confluenceTd'>Tax Percentage</td>
<td class='confluenceTd'>Number</td>
<td class='confluenceTd'>0–100, shown only when Enable Tax = On</td>
<td class='confluenceTd'>Conditional</td>
<td class='confluenceTd'>Percentage tax applied</td>
</tr>
</tbody></table>
</div>


<h4><a name="InteractionElements"></a>Interaction Elements</h4>

<div class='table-wrap'>
<table class='confluenceTable'><tbody>
<tr>
<th class='confluenceTh'>Element Name</th>
<th class='confluenceTh'>Type</th>
<th class='confluenceTh'>Trigger Condition</th>
<th class='confluenceTh'>Description</th>
</tr>
<tr>
<td class='confluenceTd'>Put to Marketplace Button</td>
<td class='confluenceTd'>Button</td>
<td class='confluenceTd'>Visible when product not listed</td>
<td class='confluenceTd'>Opens listing form</td>
</tr>
<tr>
<td class='confluenceTd'>Save Button</td>
<td class='confluenceTd'>Button</td>
<td class='confluenceTd'>Enabled when form valid</td>
<td class='confluenceTd'>Creates marketplace listing</td>
</tr>
<tr>
<td class='confluenceTd'>Discard Button</td>
<td class='confluenceTd'>Button</td>
<td class='confluenceTd'>Always visible</td>
<td class='confluenceTd'>Cancels and closes form</td>
</tr>
</tbody></table>
</div>


<h3><a name="4.DataDisplay%28Summary%29"></a>4. Data Display (Summary)</h3>

<div class='table-wrap'>
<table class='confluenceTable'><tbody>
<tr>
<th class='confluenceTh'>Data Name</th>
<th class='confluenceTh'>Data Type</th>
<th class='confluenceTh'>Display When Empty</th>
<th class='confluenceTh'>Format</th>
<th class='confluenceTh'>Description</th>
</tr>
<tr>
<td class='confluenceTd'>Success Message</td>
<td class='confluenceTd'>Toast</td>
<td class='confluenceTd'>Hidden</td>
<td class='confluenceTd'>Plaintext</td>
<td class='confluenceTd'>Shown when listing created</td>
</tr>
<tr>
<td class='confluenceTd'>Error Message</td>
<td class='confluenceTd'>Text</td>
<td class='confluenceTd'>Hidden</td>
<td class='confluenceTd'>Plaintext</td>
<td class='confluenceTd'>Displays validation errors</td>
</tr>
</tbody></table>
</div>


<h3><a name="5.AcceptanceCriteria"></a>5. Acceptance Criteria</h3>

<ul>
	<li>AC1: Put to Marketplace button appears only for products not yet listed.</li>
	<li>AC2: Listing form requires Listing Price and optionally Tax Percentage when tax is enabled.</li>
	<li>AC3: On successful save, product becomes visible in the Marketplace Product List and Inventory status updates to <tt>Marketplace</tt>.</li>
	<li>AC4: Discard button closes the form without saving.</li>
	<li>AC5: System records the listing date automatically at creation.</li>
</ul>


<h3><a name="6.SystemRules"></a>6. System Rules</h3>

<ul>
	<li>Listing Price must be a positive value in USD.</li>
	<li>Tax Percentage must be a whole number between 0 and 100 when enabled.</li>
	<li>Listing date is stored in UTC and displayed according to user timezone.</li>
	<li>Audit log captures user ID and timestamp for the listing action.</li>
</ul>


<h3><a name="7.UXOptimizations"></a>7. UX Optimizations</h3>

<ul>
	<li>Autofocus on the Listing Price field when the form opens.</li>
	<li>Display a live preview of total price including tax while editing.</li>
	<li>Show spinner on Save while the request processes.</li>
</ul>

            <br/>
        </td>
    </tr>
    </table>

Generated at Fri Jun 27 02:50:43 UTC 2025 by Kid Nguyễn using Jira 1001.0.0-SNAPSHOT#100285-rev:3c7b32e5e5b342f76f058c25245725788fa6e907.

</body>
</html>