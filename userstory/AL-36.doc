<!DOCTYPE html>
<html>
<head>
    <title>[#AL-36] [MB - Send Gift] Accept/Reject Gift</title>
    <meta http-equiv="Content-Type" Content="application/vnd.ms-word; charset=UTF-8">
        <style type="text/css">

.tableBorder, .grid
{
    background-color: #fff;
    width: 100%;
    border-collapse: collapse;
}

.tableBorder td, .grid td
{
    vertical-align: top;
    padding: 2px;
    border: 1px solid #ccc;
}

.noPadding
{
    padding: 0 !important;
}

h3 .subText
{
    font-size: 60%;
    font-weight: normal;
}

.tabLabel
{
    font-weight: bold;
    border: 1px solid #ccc;
    border-bottom:none;
    padding: 2px;
    display: inline;
}

td.blank
{
    padding: 0;
    margin: 0;
}

.blank td
{
    border: none;
}

#descriptionArea
{
    margin: 0;
    padding: 2px;
    border: 1px solid #ccc;
}

hr
{
    border-top:1px solid #aaa;
}

hr.fullcontent
{
  height: 15px;
  padding: 10px 0;
  background: #fff url('https://exnodes.atlassian.net/images/icons/hr.gif') no-repeat scroll center;
}

</style>

</head>
<body>

<table class="tableBorder" cellpadding="0" cellspacing="0" border="0" width="100%">
    <tr>
        <td bgcolor="#f0f0f0" width="100%" colspan="2" valign="top">
                            <b><a id="parent_issue_summary" href="https://exnodes.atlassian.net/browse/AL-18">Send Gift</a></b>
                <span style="font-size: 9px">(<a id="parent_issue_key" href="https://exnodes.atlassian.net/browse/AL-18">AL-18</a>)</span>
                <h3 class="formtitle"><img src="https://exnodes.atlassian.net/images/icons/link_out_bot.gif" width="16" height="16" />
                        [AL-36]&nbsp;<a href="https://exnodes.atlassian.net/browse/AL-36">[MB - Send Gift] Accept/Reject Gift</a>
            <span class="subText">
               Created: 04/Jun/25                   &nbsp;Updated: 25/Jun/25

                                            </span>
            </h3>
        </td>
    </tr>
    <tr>
        <td width="20%"><b>Status:</b></td>
        <td width="80%">In Progress</td>
    </tr>
    <tr>
        <td width="20%"><b>Project:</b></td>
        <td width="80%"><a href="https://exnodes.atlassian.net/secure/BrowseProject.jspa?id=10842">AIOS Link</a></td>
    </tr>

        <tr>
            <td><b>Components:</b></td>
            <td>
                            None
                </td>
    </tr>
    

        <tr>
            <td><b>Affects versions:</b></td>
            <td>
                            None
                </td>
    </tr>
    

        <tr>
            <td><b>Fix versions:</b></td>
            <td>
                            None
                </td>
    </tr>
    
    
            <td width="20%"><b>Parent:</b></td>
        <td width="80%"><a href="https://exnodes.atlassian.net/browse/AL-18">Send Gift</a></td>
    </table>

<br />
<table class="grid" cellpadding="0" cellspacing="0" border="0" width="100%">
    <tr>
        <td bgcolor="#f0f0f0" valign="top" width="20%">
            <b>Type:</b>
        </td>
        <td bgcolor="#ffffff" valign="top"  width="30%" >
            Story
        </td>

                    <td bgcolor="#f0f0f0">
                <b>Priority:</b>
            </td>
            <td bgcolor="#ffffff" valign="top" nowrap>
                Medium
            </td>
            </tr>
    <tr>
                        <td bgcolor="#f0f0f0" valign="top" width="20%">
                <b>Reporter:</b>
            </td>
            <td bgcolor="#ffffff" valign="top"  width="30%" >
                                        
                
        
            <a class="user-hover" rel="712020:59b66c6e-1068-441c-9436-130289e16a26"
                id="word_reporter_712020:59b66c6e-1068-441c-9436-130289e16a26"
                href="https://exnodes.atlassian.net/secure/ViewProfile.jspa?accountId=712020%3A59b66c6e-1068-441c-9436-130289e16a26">
                    Duyen Nguyen
                </a>

                            </td>
        
                    <td bgcolor="#f0f0f0" width="20%">
                <b>Assignee:</b>
            </td>
            <td bgcolor="#ffffff" valign="top" nowrap  width="30%" >
                                    Unassigned
                            </td>
            </tr>
    	<tr>
		<td bgcolor="#f0f0f0" width="20%">
			<b>Resolution:</b>
		</td>
		<td bgcolor="#ffffff" valign="top" width="30%" nowrap>
            				Unresolved
                    </td>
                    <td bgcolor="#f0f0f0" width="20%">
                <b>Votes:</b>
            </td>
            <td bgcolor="#ffffff" valign="top" width="30%" nowrap>
                0
            </td>
        
    </tr>
    
        <tr>
        <td bgcolor="#f0f0f0" width="20%">
            <b>Labels:</b>
        </td>
        <td id="labels-37371-value" class="value" bgcolor="#ffffff" valign="top" colspan="3" nowrap>
                            None
                    </td>
    </tr>
    
    	<tr>
        		<td bgcolor="#f0f0f0" width="20%"><b>Remaining Estimate:</b></td>
        <td bgcolor="#ffffff" valign="top" nowrap width="80%" colspan="3">
                            Not Specified
            		</td>
    </tr>
    <tr>
                <td bgcolor="#f0f0f0" width="20%"><b>Time Spent:</b></td>
		<td bgcolor="#ffffff" valign="top" nowrap width="80%" colspan="3">
                            Not Specified
            		</td>
	</tr>
    <tr>
                <td bgcolor="#f0f0f0" width="20%"><b>Original estimate:</b></td>
		<td bgcolor="#ffffff" valign="top" nowrap width="80%" colspan="3">
                            Not Specified
            		</td>
    </tr>
    
    </table>



    <br />

    	<table class="grid" cellpadding="0" cellspacing="0" border="0" width="100%">
            

        
    


    
                        <tr>
                <td bgcolor="#f0f0f0" width="20%" valign="top"><b>Sprint:</b></td>
                <td id="customfield_10020-37371-value" class="value" bgcolor="#ffffff" width="80%"></td>
            </tr>
                                <tr>
                <td bgcolor="#f0f0f0" width="20%" valign="top"><b>Platform:</b></td>
                <td id="customfield_10687-37371-value" class="value" bgcolor="#ffffff" width="80%">    MOBILE</td>
            </tr>
            </table>

    <br/>

    <table cellpadding="2" cellspacing="0" border="0" width="100%" align="center">
    <tr>
        <td bgcolor="#bbbbbb" width="1%" nowrap align="center">
            &nbsp;<font color="#ffffff"><b>Description</b></font>&nbsp;
        </td>
        <td>&nbsp;</td>
    </tr>
    </table>

    <table cellpadding="0" cellspacing="0" border="0" width="100%">
    <tr>
        <td id="descriptionArea">
            <h3><a name="1.UseCaseDescription"></a>1. Use Case Description</h3>

<p>As a recipient of a digital gift, I want the ability to accept or reject the gift within 14 days, so that I have control over whether to receive it and the sender can be informed of the outcome or retrieve it if unclaimed.</p>

<hr />

<h3><a name="2.UserWorkflow%28StepbyStep%29"></a>2. User Workflow (Step-by-Step)</h3>

<ol>
	<li>User receives a <b>Gift Notification</b> and sees a new item in the <b>Gift Inbox</b>.</li>
	<li>User taps on the gift to view details:
	<ul>
		<li>Sender name</li>
		<li>Gift type (ticket or product)</li>
		<li>Gift name</li>
		<li>Date received</li>
	</ul>
	</li>
	<li>User chooses one of the following:
	<ul>
		<li><b>Accept Gift</b><br/>
→ Gift is transferred to the corresponding tab:
		<ul>
			<li>Ticket → appears in “My Tickets”</li>
			<li>Product → appears in “My Digital Products”<br/>
→ Sender receives a confirmation notification.</li>
		</ul>
		</li>
		<li><b>Reject Gift</b><br/>
→ Gift is returned to the sender.<br/>
→ Sender is notified of rejection.</li>
	</ul>
	</li>
	<li>If no action is taken for <b>14 days</b>, the system:
	<ul>
		<li>Automatically returns the gift to the sender.</li>
		<li>Sends expiration notification to both sender and receiver.</li>
	</ul>
	</li>
</ol>


<hr />

<h3><a name="3.FieldDefinitionsTable"></a>3. Field Definitions Table</h3>

<h4><a name="InteractionElements"></a>Interaction Elements</h4>

<div class='table-wrap'>
<table class='confluenceTable'><tbody>
<tr>
<th class='confluenceTh'>Element Name</th>
<th class='confluenceTh'>Type</th>
<th class='confluenceTh'>Required</th>
<th class='confluenceTh'>Description</th>
</tr>
<tr>
<td class='confluenceTd'>Accept Button</td>
<td class='confluenceTd'>Button</td>
<td class='confluenceTd'>✅</td>
<td class='confluenceTd'>Confirms acceptance; adds gift to inventory</td>
</tr>
<tr>
<td class='confluenceTd'>Reject Button</td>
<td class='confluenceTd'>Button</td>
<td class='confluenceTd'>✅</td>
<td class='confluenceTd'>Declines gift; returns to sender</td>
</tr>
<tr>
<td class='confluenceTd'>Gift Detail View</td>
<td class='confluenceTd'>Card</td>
<td class='confluenceTd'>✅</td>
<td class='confluenceTd'>Displays sender, type, and expiration</td>
</tr>
</tbody></table>
</div>


<hr />

<h3><a name="4.DataDisplayTable%28CardView%29"></a>4. Data Display Table (Card View)</h3>

<div class='table-wrap'>
<table class='confluenceTable'><tbody>
<tr>
<th class='confluenceTh'>Data Field</th>
<th class='confluenceTh'>Data Type</th>
<th class='confluenceTh'>Display When Empty</th>
<th class='confluenceTh'>Format</th>
<th class='confluenceTh'>Description</th>
</tr>
<tr>
<td class='confluenceTd'>Gift Name</td>
<td class='confluenceTd'>Text</td>
<td class='confluenceTd'>“Unnamed Gift”</td>
<td class='confluenceTd'>Plaintext</td>
<td class='confluenceTd'>Name of ticket or product</td>
</tr>
<tr>
<td class='confluenceTd'>Gift Type</td>
<td class='confluenceTd'>Badge</td>
<td class='confluenceTd'>“Unknown”</td>
<td class='confluenceTd'>Ticket/Product</td>
<td class='confluenceTd'>Visual tag indicating type</td>
</tr>
<tr>
<td class='confluenceTd'>Sender Name</td>
<td class='confluenceTd'>Text</td>
<td class='confluenceTd'>“Unknown”</td>
<td class='confluenceTd'>Full Name</td>
<td class='confluenceTd'>Who sent the gift</td>
</tr>
<tr>
<td class='confluenceTd'>Expiration Time</td>
<td class='confluenceTd'>Countdown</td>
<td class='confluenceTd'>“14d”</td>
<td class='confluenceTd'>Remaining duration</td>
<td class='confluenceTd'>Time left to respond</td>
</tr>
<tr>
<td class='confluenceTd'>Gift Status</td>
<td class='confluenceTd'>Badge</td>
<td class='confluenceTd'>“Pending”</td>
<td class='confluenceTd'>Accepted/Rejected/Expired</td>
<td class='confluenceTd'>Current state of gift</td>
</tr>
</tbody></table>
</div>


<hr />

<h3><a name="5.AcceptanceCriteria"></a>5. Acceptance Criteria</h3>

<ul>
	<li><b>AC1</b>: User must be able to accept or reject any received gift from the Gift Inbox.</li>
	<li><b>AC2</b>: Accepted gifts are transferred to either the Tickets or Products section.</li>
	<li><b>AC3</b>: Rejected gifts are returned to the original sender.</li>
	<li><b>AC4</b>: Gift auto-expires and is returned to sender if no action is taken in 14 days.</li>
	<li><b>AC5</b>: Both sender and receiver are notified upon acceptance, rejection, or expiration.</li>
	<li><b>AC6</b>: User cannot accept an already expired or recalled gift.</li>
</ul>


<hr />

<h3><a name="6.SystemRules"></a>6. System Rules</h3>

<ul>
	<li>Each gift has a <tt>sent_at</tt> timestamp and a <tt>14-day response window</tt>.</li>
	<li>If gift is not accepted/rejected in 14 days:
	<ul>
		<li>Status → “Expired”</li>
		<li>Gift is removed from recipient inbox and restored to sender's inventory</li>
	</ul>
	</li>
	<li>Gift acceptance updates the ownership record (gift_id → recipient_id).</li>
	<li>Sender cannot revoke gift during active waiting period unless rejected/expired.</li>
</ul>


<hr />

<h3><a name="7.UXOptimizations"></a>7. UX Optimizations</h3>

<ul>
	<li>Countdown indicator: “Respond within X days” under each pending gift.</li>
	<li>Accept/Reject as sticky buttons in gift detail view.</li>
	<li>Toasts:
	<ul>
		<li>Accept: <em>“Gift added to your <span class="error">&#91;Tickets/Products&#93;</span>. Sender notified.”</em></li>
		<li>Reject: <em>“Gift declined and returned to <span class="error">&#91;Sender&#93;</span>.”</em></li>
		<li>Expired: <em>“Gift request expired. Returned to sender.”</em></li>
	</ul>
	</li>
	<li>Badge on Gift Inbox tab: e.g., “2 Pending Gifts”</li>
</ul>


<hr />
            <br/>
        </td>
    </tr>
    </table>

Generated at Fri Jun 27 02:51:36 UTC 2025 by Kid Nguyễn using Jira 1001.0.0-SNAPSHOT#100285-rev:3c7b32e5e5b342f76f058c25245725788fa6e907.

</body>
</html>