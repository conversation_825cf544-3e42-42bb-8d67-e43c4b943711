<!DOCTYPE html>
<html>
<head>
    <title>[#AL-39] [MB - Messages] Edit Group Info</title>
    <meta http-equiv="Content-Type" Content="application/vnd.ms-word; charset=UTF-8">
        <style type="text/css">

.tableBorder, .grid
{
    background-color: #fff;
    width: 100%;
    border-collapse: collapse;
}

.tableBorder td, .grid td
{
    vertical-align: top;
    padding: 2px;
    border: 1px solid #ccc;
}

.noPadding
{
    padding: 0 !important;
}

h3 .subText
{
    font-size: 60%;
    font-weight: normal;
}

.tabLabel
{
    font-weight: bold;
    border: 1px solid #ccc;
    border-bottom:none;
    padding: 2px;
    display: inline;
}

td.blank
{
    padding: 0;
    margin: 0;
}

.blank td
{
    border: none;
}

#descriptionArea
{
    margin: 0;
    padding: 2px;
    border: 1px solid #ccc;
}

hr
{
    border-top:1px solid #aaa;
}

hr.fullcontent
{
  height: 15px;
  padding: 10px 0;
  background: #fff url('https://exnodes.atlassian.net/images/icons/hr.gif') no-repeat scroll center;
}

</style>

</head>
<body>

<table class="tableBorder" cellpadding="0" cellspacing="0" border="0" width="100%">
    <tr>
        <td bgcolor="#f0f0f0" width="100%" colspan="2" valign="top">
                            <b><a id="parent_issue_summary" href="https://exnodes.atlassian.net/browse/AL-16">Messages</a></b>
                <span style="font-size: 9px">(<a id="parent_issue_key" href="https://exnodes.atlassian.net/browse/AL-16">AL-16</a>)</span>
                <h3 class="formtitle"><img src="https://exnodes.atlassian.net/images/icons/link_out_bot.gif" width="16" height="16" />
                        [AL-39]&nbsp;<a href="https://exnodes.atlassian.net/browse/AL-39">[MB - Messages] Edit Group Info</a>
            <span class="subText">
               Created: 04/Jun/25                   &nbsp;Updated: 04/Jun/25

                                            </span>
            </h3>
        </td>
    </tr>
    <tr>
        <td width="20%"><b>Status:</b></td>
        <td width="80%">Specified</td>
    </tr>
    <tr>
        <td width="20%"><b>Project:</b></td>
        <td width="80%"><a href="https://exnodes.atlassian.net/secure/BrowseProject.jspa?id=10842">AIOS Link</a></td>
    </tr>

        <tr>
            <td><b>Components:</b></td>
            <td>
                            None
                </td>
    </tr>
    

        <tr>
            <td><b>Affects versions:</b></td>
            <td>
                            None
                </td>
    </tr>
    

        <tr>
            <td><b>Fix versions:</b></td>
            <td>
                            None
                </td>
    </tr>
    
    
            <td width="20%"><b>Parent:</b></td>
        <td width="80%"><a href="https://exnodes.atlassian.net/browse/AL-16">Messages</a></td>
    </table>

<br />
<table class="grid" cellpadding="0" cellspacing="0" border="0" width="100%">
    <tr>
        <td bgcolor="#f0f0f0" valign="top" width="20%">
            <b>Type:</b>
        </td>
        <td bgcolor="#ffffff" valign="top"  width="30%" >
            Story
        </td>

                    <td bgcolor="#f0f0f0">
                <b>Priority:</b>
            </td>
            <td bgcolor="#ffffff" valign="top" nowrap>
                Medium
            </td>
            </tr>
    <tr>
                        <td bgcolor="#f0f0f0" valign="top" width="20%">
                <b>Reporter:</b>
            </td>
            <td bgcolor="#ffffff" valign="top"  width="30%" >
                                        
                
        
            <a class="user-hover" rel="712020:59b66c6e-1068-441c-9436-130289e16a26"
                id="word_reporter_712020:59b66c6e-1068-441c-9436-130289e16a26"
                href="https://exnodes.atlassian.net/secure/ViewProfile.jspa?accountId=712020%3A59b66c6e-1068-441c-9436-130289e16a26">
                    Duyen Nguyen
                </a>

                            </td>
        
                    <td bgcolor="#f0f0f0" width="20%">
                <b>Assignee:</b>
            </td>
            <td bgcolor="#ffffff" valign="top" nowrap  width="30%" >
                                    Unassigned
                            </td>
            </tr>
    	<tr>
		<td bgcolor="#f0f0f0" width="20%">
			<b>Resolution:</b>
		</td>
		<td bgcolor="#ffffff" valign="top" width="30%" nowrap>
            				Unresolved
                    </td>
                    <td bgcolor="#f0f0f0" width="20%">
                <b>Votes:</b>
            </td>
            <td bgcolor="#ffffff" valign="top" width="30%" nowrap>
                0
            </td>
        
    </tr>
    
        <tr>
        <td bgcolor="#f0f0f0" width="20%">
            <b>Labels:</b>
        </td>
        <td id="labels-37380-value" class="value" bgcolor="#ffffff" valign="top" colspan="3" nowrap>
                            None
                    </td>
    </tr>
    
    	<tr>
        		<td bgcolor="#f0f0f0" width="20%"><b>Remaining Estimate:</b></td>
        <td bgcolor="#ffffff" valign="top" nowrap width="80%" colspan="3">
                            Not Specified
            		</td>
    </tr>
    <tr>
                <td bgcolor="#f0f0f0" width="20%"><b>Time Spent:</b></td>
		<td bgcolor="#ffffff" valign="top" nowrap width="80%" colspan="3">
                            Not Specified
            		</td>
	</tr>
    <tr>
                <td bgcolor="#f0f0f0" width="20%"><b>Original estimate:</b></td>
		<td bgcolor="#ffffff" valign="top" nowrap width="80%" colspan="3">
                            Not Specified
            		</td>
    </tr>
    
    </table>



    <br />

    	<table class="grid" cellpadding="0" cellspacing="0" border="0" width="100%">
            

        
    


    
                        <tr>
                <td bgcolor="#f0f0f0" width="20%" valign="top"><b>Sprint:</b></td>
                <td id="customfield_10020-37380-value" class="value" bgcolor="#ffffff" width="80%"></td>
            </tr>
                                <tr>
                <td bgcolor="#f0f0f0" width="20%" valign="top"><b>Platform:</b></td>
                <td id="customfield_10687-37380-value" class="value" bgcolor="#ffffff" width="80%">    MOBILE</td>
            </tr>
            </table>

    <br/>

    <table cellpadding="2" cellspacing="0" border="0" width="100%" align="center">
    <tr>
        <td bgcolor="#bbbbbb" width="1%" nowrap align="center">
            &nbsp;<font color="#ffffff"><b>Description</b></font>&nbsp;
        </td>
        <td>&nbsp;</td>
    </tr>
    </table>

    <table cellpadding="0" cellspacing="0" border="0" width="100%">
    <tr>
        <td id="descriptionArea">
            <h4><a name="1.UseCaseDescription"></a>1. <b>Use Case Description</b></h4>

<p>As a user who is part of a group chat,<br/>
I want to edit the group name and avatar<br/>
so that the group reflects its purpose or participants more accurately.</p>

<hr />

<h4><a name="2.UserWorkflow%28StepbyStep%29"></a>2. <b>User Workflow (Step-by-Step)</b></h4>

<ol>
	<li>User navigates to the desired group chat thread.</li>
	<li>User taps the <b>Group Info</b> header or “...” menu.</li>
	<li>User selects <b>Edit Group Info</b> option.</li>
	<li>System displays a form with current <b>Group Name</b> and <b>Group Avatar</b>.</li>
	<li>User updates the group name (or leaves unchanged).</li>
	<li>User selects a new image from device or uses the camera to capture a new photo.</li>
	<li>User taps <b>Save</b>.</li>
	<li>System validates input and updates group info.</li>
	<li>All participants see updated group name and avatar in their group chat.</li>
</ol>


<hr />

<h4><a name="3.FieldDefinitionsTable"></a>3. <b>Field Definitions Table</b></h4>

<div class='table-wrap'>
<table class='confluenceTable'><tbody>
<tr>
<th class='confluenceTh'>Field Name</th>
<th class='confluenceTh'>Field Type</th>
<th class='confluenceTh'>Validation Rule</th>
<th class='confluenceTh'>Mandatory</th>
<th class='confluenceTh'>Description</th>
</tr>
<tr>
<td class='confluenceTd'>Group Name</td>
<td class='confluenceTd'>Text Input</td>
<td class='confluenceTd'>Max 50 characters</td>
<td class='confluenceTd'>Yes</td>
<td class='confluenceTd'>Editable group label. If left blank, retains previous name.</td>
</tr>
<tr>
<td class='confluenceTd'>Group Avatar</td>
<td class='confluenceTd'>Image Upload</td>
<td class='confluenceTd'>JPG/PNG, Max 2MB, square crop, or capture via camera</td>
<td class='confluenceTd'>No</td>
<td class='confluenceTd'>Optional new photo to replace group icon. Defaults to existing or stacked avatars.</td>
</tr>
</tbody></table>
</div>


<hr />

<h4><a name="4.DataDisplayTable%28GroupInfoView%29"></a>4. <b>Data Display Table (Group Info View)</b></h4>

<div class='table-wrap'>
<table class='confluenceTable'><tbody>
<tr>
<th class='confluenceTh'>Element</th>
<th class='confluenceTh'>Display Type</th>
<th class='confluenceTh'>Notes</th>
</tr>
<tr>
<td class='confluenceTd'>Group Name</td>
<td class='confluenceTd'>Header Text</td>
<td class='confluenceTd'>Display updated name immediately across app.</td>
</tr>
<tr>
<td class='confluenceTd'>Group Avatar</td>
<td class='confluenceTd'>Image/Icon</td>
<td class='confluenceTd'>Display new avatar or fallback to initials/stack.</td>
</tr>
<tr>
<td class='confluenceTd'>Member Count</td>
<td class='confluenceTd'>Subtext</td>
<td class='confluenceTd'>Reflects real-time participant number.</td>
</tr>
<tr>
<td class='confluenceTd'>Last Updated By</td>
<td class='confluenceTd'>Text</td>
<td class='confluenceTd'>"Last updated by <span class="error">&#91;User Name&#93;</span>" timestamped (optional).</td>
</tr>
</tbody></table>
</div>


<hr />

<h4><a name="5.AcceptanceCriteria%28AC%29"></a>5. <b>Acceptance Criteria (AC)</b></h4>

<ul>
	<li><b>AC1:</b> Users can tap into group settings and see current group name and avatar.</li>
	<li><b>AC2:</b> System accepts a new name if under 50 characters and not empty.</li>
	<li><b>AC3:</b> System allows avatar update via gallery or camera.</li>
	<li><b>AC4:</b> If no new data is submitted, system retains previous values.</li>
	<li><b>AC5:</b> On success, group thread UI updates name and avatar immediately.</li>
	<li><b>AC6:</b> All group participants receive a system message: “Group name/avatar updated by <span class="error">&#91;User&#93;</span>.”</li>
	<li><b>AC7:</b> Changes do not require reconfirmation from other group members.</li>
	<li><b>AC8:</b> System compresses avatar upload before saving to optimize storage.</li>
</ul>


<hr />

<h4><a name="6.SystemRules"></a>6. <b>System Rules</b></h4>

<ul>
	<li>Only group creator or admin (if role exists) can edit group info.</li>
	<li>Avatar images are resized to standard 1:1 ratio thumbnails.</li>
	<li>Change logs are not stored but reflected in group preview metadata.</li>
	<li>Multiple edits can be made, but changes are broadcast once user taps Save.</li>
</ul>


<hr />

<h4><a name="7.UXOptimizations"></a>7. <b>UX Optimizations</b></h4>

<ul>
	<li>Inline avatar editing from top header for quick access.</li>
	<li>Avatar preview with “Edit” pencil icon overlay.</li>
	<li>Name input has live character counter and validation.</li>
	<li>Toast: “Group info updated successfully.”</li>
</ul>

            <br/>
        </td>
    </tr>
    </table>

Generated at Mon Jul 07 09:31:52 UTC 2025 by Kid Nguyễn using Jira 1001.0.0-SNAPSHOT#100286-rev:7362dfe166e568bdd8002f7ad5d37bb053226fd0.

</body>
</html>