<!DOCTYPE html>
<html>
<head>
    <title>[#AL-110] [MB - User Profile] Change Email Address</title>
    <meta http-equiv="Content-Type" Content="application/vnd.ms-word; charset=UTF-8">
        <style type="text/css">

.tableBorder, .grid
{
    background-color: #fff;
    width: 100%;
    border-collapse: collapse;
}

.tableBorder td, .grid td
{
    vertical-align: top;
    padding: 2px;
    border: 1px solid #ccc;
}

.noPadding
{
    padding: 0 !important;
}

h3 .subText
{
    font-size: 60%;
    font-weight: normal;
}

.tabLabel
{
    font-weight: bold;
    border: 1px solid #ccc;
    border-bottom:none;
    padding: 2px;
    display: inline;
}

td.blank
{
    padding: 0;
    margin: 0;
}

.blank td
{
    border: none;
}

#descriptionArea
{
    margin: 0;
    padding: 2px;
    border: 1px solid #ccc;
}

hr
{
    border-top:1px solid #aaa;
}

hr.fullcontent
{
  height: 15px;
  padding: 10px 0;
  background: #fff url('https://exnodes.atlassian.net/images/icons/hr.gif') no-repeat scroll center;
}

</style>

</head>
<body>

<table class="tableBorder" cellpadding="0" cellspacing="0" border="0" width="100%">
    <tr>
        <td bgcolor="#f0f0f0" width="100%" colspan="2" valign="top">
                            <b><a id="parent_issue_summary" href="https://exnodes.atlassian.net/browse/AL-58">User Profile</a></b>
                <span style="font-size: 9px">(<a id="parent_issue_key" href="https://exnodes.atlassian.net/browse/AL-58">AL-58</a>)</span>
                <h3 class="formtitle"><img src="https://exnodes.atlassian.net/images/icons/link_out_bot.gif" width="16" height="16" />
                        [AL-110]&nbsp;<a href="https://exnodes.atlassian.net/browse/AL-110">[MB - User Profile] Change Email Address</a>
            <span class="subText">
               Created: 12/Jun/25                   &nbsp;Updated: 25/Jun/25

                                            </span>
            </h3>
        </td>
    </tr>
    <tr>
        <td width="20%"><b>Status:</b></td>
        <td width="80%">In Progress</td>
    </tr>
    <tr>
        <td width="20%"><b>Project:</b></td>
        <td width="80%"><a href="https://exnodes.atlassian.net/secure/BrowseProject.jspa?id=10842">AIOS Link</a></td>
    </tr>

        <tr>
            <td><b>Components:</b></td>
            <td>
                            None
                </td>
    </tr>
    

        <tr>
            <td><b>Affects versions:</b></td>
            <td>
                            None
                </td>
    </tr>
    

        <tr>
            <td><b>Fix versions:</b></td>
            <td>
                            None
                </td>
    </tr>
    
    
            <td width="20%"><b>Parent:</b></td>
        <td width="80%"><a href="https://exnodes.atlassian.net/browse/AL-58">User Profile</a></td>
    </table>

<br />
<table class="grid" cellpadding="0" cellspacing="0" border="0" width="100%">
    <tr>
        <td bgcolor="#f0f0f0" valign="top" width="20%">
            <b>Type:</b>
        </td>
        <td bgcolor="#ffffff" valign="top"  width="30%" >
            Story
        </td>

                    <td bgcolor="#f0f0f0">
                <b>Priority:</b>
            </td>
            <td bgcolor="#ffffff" valign="top" nowrap>
                Medium
            </td>
            </tr>
    <tr>
                        <td bgcolor="#f0f0f0" valign="top" width="20%">
                <b>Reporter:</b>
            </td>
            <td bgcolor="#ffffff" valign="top"  width="30%" >
                                        
                
        
            <a class="user-hover" rel="712020:59b66c6e-1068-441c-9436-130289e16a26"
                id="word_reporter_712020:59b66c6e-1068-441c-9436-130289e16a26"
                href="https://exnodes.atlassian.net/secure/ViewProfile.jspa?accountId=712020%3A59b66c6e-1068-441c-9436-130289e16a26">
                    Duyen Nguyen
                </a>

                            </td>
        
                    <td bgcolor="#f0f0f0" width="20%">
                <b>Assignee:</b>
            </td>
            <td bgcolor="#ffffff" valign="top" nowrap  width="30%" >
                                    Unassigned
                            </td>
            </tr>
    	<tr>
		<td bgcolor="#f0f0f0" width="20%">
			<b>Resolution:</b>
		</td>
		<td bgcolor="#ffffff" valign="top" width="30%" nowrap>
            				Unresolved
                    </td>
                    <td bgcolor="#f0f0f0" width="20%">
                <b>Votes:</b>
            </td>
            <td bgcolor="#ffffff" valign="top" width="30%" nowrap>
                0
            </td>
        
    </tr>
    
        <tr>
        <td bgcolor="#f0f0f0" width="20%">
            <b>Labels:</b>
        </td>
        <td id="labels-38241-value" class="value" bgcolor="#ffffff" valign="top" colspan="3" nowrap>
                            None
                    </td>
    </tr>
    
    	<tr>
        		<td bgcolor="#f0f0f0" width="20%"><b>Remaining Estimate:</b></td>
        <td bgcolor="#ffffff" valign="top" nowrap width="80%" colspan="3">
                            Not Specified
            		</td>
    </tr>
    <tr>
                <td bgcolor="#f0f0f0" width="20%"><b>Time Spent:</b></td>
		<td bgcolor="#ffffff" valign="top" nowrap width="80%" colspan="3">
                            Not Specified
            		</td>
	</tr>
    <tr>
                <td bgcolor="#f0f0f0" width="20%"><b>Original estimate:</b></td>
		<td bgcolor="#ffffff" valign="top" nowrap width="80%" colspan="3">
                            Not Specified
            		</td>
    </tr>
    
    </table>



    <br />

    	<table class="grid" cellpadding="0" cellspacing="0" border="0" width="100%">
            

        
    


    
                        <tr>
                <td bgcolor="#f0f0f0" width="20%" valign="top"><b>Sprint:</b></td>
                <td id="customfield_10020-38241-value" class="value" bgcolor="#ffffff" width="80%"></td>
            </tr>
                                <tr>
                <td bgcolor="#f0f0f0" width="20%" valign="top"><b>Platform:</b></td>
                <td id="customfield_10687-38241-value" class="value" bgcolor="#ffffff" width="80%">    MOBILE</td>
            </tr>
            </table>

    <br/>

    <table cellpadding="2" cellspacing="0" border="0" width="100%" align="center">
    <tr>
        <td bgcolor="#bbbbbb" width="1%" nowrap align="center">
            &nbsp;<font color="#ffffff"><b>Description</b></font>&nbsp;
        </td>
        <td>&nbsp;</td>
    </tr>
    </table>

    <table cellpadding="0" cellspacing="0" border="0" width="100%">
    <tr>
        <td id="descriptionArea">
            <h3><a name="1.UseCaseDescription"></a>1. Use Case Description</h3>

<p>As a user, I want to change my registered email address, so that I can keep my contact information up to date while ensuring account security through OTP verification.</p>

<hr />

<h3><a name="2.UserWorkflow%28StepbyStep%29"></a>2. User Workflow (Step-by-Step)</h3>

<ol>
	<li>User navigates to <b>Profile &gt; Profile Setting &gt; Change Email</b>.</li>
	<li>User enters a new email address.</li>
	<li>User taps <b>Submit</b>.</li>
	<li>System sends a <b>6-digit OTP</b> to the new email address (valid for 30 minutes).</li>
	<li>User enters the OTP code on the verification screen.</li>
	<li>If the OTP is valid → email address is updated and user is shown a success message.</li>
	<li>If OTP is invalid or expired → show error and do not change the email.</li>
</ol>


<hr />

<h3><a name="3.FieldDefinitionsTable"></a>3. Field Definitions Table</h3>

<h4><a name="3.1SubmissionFields"></a>3.1 Submission Fields</h4>

<div class='table-wrap'>
<table class='confluenceTable'><tbody>
<tr>
<th class='confluenceTh'>Field Name</th>
<th class='confluenceTh'>Input Type</th>
<th class='confluenceTh'>Required</th>
<th class='confluenceTh'>Validation Rules</th>
</tr>
<tr>
<td class='confluenceTd'>New Email</td>
<td class='confluenceTd'>Text</td>
<td class='confluenceTd'>✅</td>
<td class='confluenceTd'>Valid email format, must be unique</td>
</tr>
<tr>
<td class='confluenceTd'>OTP Code</td>
<td class='confluenceTd'>Numeric</td>
<td class='confluenceTd'>✅</td>
<td class='confluenceTd'>6-digit, expires in 30 minutes</td>
</tr>
</tbody></table>
</div>


<hr />

<h3><a name="4.DataDisplayTable"></a>4. Data Display Table</h3>

<div class='table-wrap'>
<table class='confluenceTable'><tbody>
<tr>
<th class='confluenceTh'>Display Field</th>
<th class='confluenceTh'>Format Example</th>
<th class='confluenceTh'>Notes</th>
</tr>
<tr>
<td class='confluenceTd'>Email Input</td>
<td class='confluenceTd'><a href="mailto:<EMAIL>" class="external-link" rel="nofollow noreferrer"><EMAIL></a></td>
<td class='confluenceTd'>Auto-focus field when opened</td>
</tr>
<tr>
<td class='confluenceTd'>OTP Input</td>
<td class='confluenceTd'>6-digit numeric field</td>
<td class='confluenceTd'>With countdown timer display</td>
</tr>
<tr>
<td class='confluenceTd'>Status Toast</td>
<td class='confluenceTd'>“Email updated successfully”</td>
<td class='confluenceTd'>Shown upon successful update</td>
</tr>
</tbody></table>
</div>


<hr />

<h3><a name="5.AcceptanceCriteria"></a>5. Acceptance Criteria</h3>

<ul>
	<li><b>AC1</b>: If submitted email is invalid or already registered, show: <em>“Please enter a valid, unused email address.”</em></li>
	<li><b>AC2</b>: OTP must be a 6-digit code and expire in 30 minutes.</li>
	<li><b>AC3</b>: If incorrect OTP entered, show error: <em>“Invalid or expired verification code.”</em></li>
	<li><b>AC4</b>: After 5 resend OTP attempts per day, block verification and show: <em>“You’ve reached the daily limit. Try again tomorrow.”</em></li>
	<li><b>AC5</b>: On successful OTP verification, system updates email and returns user to Account Info screen with confirmation toast.</li>
	<li><b>AC6:</b> Email will not be changed until verification step is completed.</li>
</ul>


<hr />

<h3><a name="6.SystemRules"></a>6. System Rules</h3>

<ul>
	<li>Email is only updated upon successful OTP confirmation.</li>
	<li>OTP is single-use, expires after 30 minutes, and is rate-limited to 5 attempts/day.</li>
	<li>Updated email must be unique across the platform.</li>
	<li>Verification activity is logged (user ID, IP, timestamp).</li>
</ul>


<hr />

<h3><a name="7.UXOptimizations"></a>7. UX Optimizations</h3>

<ul>
	<li>Auto-focus on email input field</li>
	<li>Mask OTP input until entry complete</li>
	<li>Display countdown timer for OTP expiration</li>
	<li>Inline validation and field-level error messages</li>
	<li>Show “Resend OTP” button (enabled after 60 seconds)</li>
</ul>

            <br/>
        </td>
    </tr>
    </table>

Generated at Fri Jun 27 02:30:17 UTC 2025 by Kid Nguyễn using Jira 1001.0.0-SNAPSHOT#100285-rev:3c7b32e5e5b342f76f058c25245725788fa6e907.

</body>
</html>