<!DOCTYPE html>
<html>
<head>
    <title>[#AL-40] [MB - Messages] Add/Remove Contact from Group Chat</title>
    <meta http-equiv="Content-Type" Content="application/vnd.ms-word; charset=UTF-8">
        <style type="text/css">

.tableBorder, .grid
{
    background-color: #fff;
    width: 100%;
    border-collapse: collapse;
}

.tableBorder td, .grid td
{
    vertical-align: top;
    padding: 2px;
    border: 1px solid #ccc;
}

.noPadding
{
    padding: 0 !important;
}

h3 .subText
{
    font-size: 60%;
    font-weight: normal;
}

.tabLabel
{
    font-weight: bold;
    border: 1px solid #ccc;
    border-bottom:none;
    padding: 2px;
    display: inline;
}

td.blank
{
    padding: 0;
    margin: 0;
}

.blank td
{
    border: none;
}

#descriptionArea
{
    margin: 0;
    padding: 2px;
    border: 1px solid #ccc;
}

hr
{
    border-top:1px solid #aaa;
}

hr.fullcontent
{
  height: 15px;
  padding: 10px 0;
  background: #fff url('https://exnodes.atlassian.net/images/icons/hr.gif') no-repeat scroll center;
}

</style>

</head>
<body>

<table class="tableBorder" cellpadding="0" cellspacing="0" border="0" width="100%">
    <tr>
        <td bgcolor="#f0f0f0" width="100%" colspan="2" valign="top">
                            <b><a id="parent_issue_summary" href="https://exnodes.atlassian.net/browse/AL-16">Messages</a></b>
                <span style="font-size: 9px">(<a id="parent_issue_key" href="https://exnodes.atlassian.net/browse/AL-16">AL-16</a>)</span>
                <h3 class="formtitle"><img src="https://exnodes.atlassian.net/images/icons/link_out_bot.gif" width="16" height="16" />
                        [AL-40]&nbsp;<a href="https://exnodes.atlassian.net/browse/AL-40">[MB - Messages] Add/Remove Contact from Group Chat</a>
            <span class="subText">
               Created: 04/Jun/25                   &nbsp;Updated: 30/Jun/25

                                            </span>
            </h3>
        </td>
    </tr>
    <tr>
        <td width="20%"><b>Status:</b></td>
        <td width="80%">Specified</td>
    </tr>
    <tr>
        <td width="20%"><b>Project:</b></td>
        <td width="80%"><a href="https://exnodes.atlassian.net/secure/BrowseProject.jspa?id=10842">AIOS Link</a></td>
    </tr>

        <tr>
            <td><b>Components:</b></td>
            <td>
                            None
                </td>
    </tr>
    

        <tr>
            <td><b>Affects versions:</b></td>
            <td>
                            None
                </td>
    </tr>
    

        <tr>
            <td><b>Fix versions:</b></td>
            <td>
                            None
                </td>
    </tr>
    
    
            <td width="20%"><b>Parent:</b></td>
        <td width="80%"><a href="https://exnodes.atlassian.net/browse/AL-16">Messages</a></td>
    </table>

<br />
<table class="grid" cellpadding="0" cellspacing="0" border="0" width="100%">
    <tr>
        <td bgcolor="#f0f0f0" valign="top" width="20%">
            <b>Type:</b>
        </td>
        <td bgcolor="#ffffff" valign="top"  width="30%" >
            Story
        </td>

                    <td bgcolor="#f0f0f0">
                <b>Priority:</b>
            </td>
            <td bgcolor="#ffffff" valign="top" nowrap>
                Medium
            </td>
            </tr>
    <tr>
                        <td bgcolor="#f0f0f0" valign="top" width="20%">
                <b>Reporter:</b>
            </td>
            <td bgcolor="#ffffff" valign="top"  width="30%" >
                                        
                
        
            <a class="user-hover" rel="712020:59b66c6e-1068-441c-9436-130289e16a26"
                id="word_reporter_712020:59b66c6e-1068-441c-9436-130289e16a26"
                href="https://exnodes.atlassian.net/secure/ViewProfile.jspa?accountId=712020%3A59b66c6e-1068-441c-9436-130289e16a26">
                    Duyen Nguyen
                </a>

                            </td>
        
                    <td bgcolor="#f0f0f0" width="20%">
                <b>Assignee:</b>
            </td>
            <td bgcolor="#ffffff" valign="top" nowrap  width="30%" >
                                    Unassigned
                            </td>
            </tr>
    	<tr>
		<td bgcolor="#f0f0f0" width="20%">
			<b>Resolution:</b>
		</td>
		<td bgcolor="#ffffff" valign="top" width="30%" nowrap>
            				Unresolved
                    </td>
                    <td bgcolor="#f0f0f0" width="20%">
                <b>Votes:</b>
            </td>
            <td bgcolor="#ffffff" valign="top" width="30%" nowrap>
                0
            </td>
        
    </tr>
    
        <tr>
        <td bgcolor="#f0f0f0" width="20%">
            <b>Labels:</b>
        </td>
        <td id="labels-37383-value" class="value" bgcolor="#ffffff" valign="top" colspan="3" nowrap>
                            None
                    </td>
    </tr>
    
    	<tr>
        		<td bgcolor="#f0f0f0" width="20%"><b>Remaining Estimate:</b></td>
        <td bgcolor="#ffffff" valign="top" nowrap width="80%" colspan="3">
                            Not Specified
            		</td>
    </tr>
    <tr>
                <td bgcolor="#f0f0f0" width="20%"><b>Time Spent:</b></td>
		<td bgcolor="#ffffff" valign="top" nowrap width="80%" colspan="3">
                            Not Specified
            		</td>
	</tr>
    <tr>
                <td bgcolor="#f0f0f0" width="20%"><b>Original estimate:</b></td>
		<td bgcolor="#ffffff" valign="top" nowrap width="80%" colspan="3">
                            Not Specified
            		</td>
    </tr>
    
    </table>



    <br />

    	<table class="grid" cellpadding="0" cellspacing="0" border="0" width="100%">
            

        
    


    
                        <tr>
                <td bgcolor="#f0f0f0" width="20%" valign="top"><b>Sprint:</b></td>
                <td id="customfield_10020-37383-value" class="value" bgcolor="#ffffff" width="80%"></td>
            </tr>
                                <tr>
                <td bgcolor="#f0f0f0" width="20%" valign="top"><b>Platform:</b></td>
                <td id="customfield_10687-37383-value" class="value" bgcolor="#ffffff" width="80%">    MOBILE</td>
            </tr>
            </table>

    <br/>

    <table cellpadding="2" cellspacing="0" border="0" width="100%" align="center">
    <tr>
        <td bgcolor="#bbbbbb" width="1%" nowrap align="center">
            &nbsp;<font color="#ffffff"><b>Description</b></font>&nbsp;
        </td>
        <td>&nbsp;</td>
    </tr>
    </table>

    <table cellpadding="0" cellspacing="0" border="0" width="100%">
    <tr>
        <td id="descriptionArea">
            <h3><a name="1.UseCaseDescription"></a>1. Use Case Description</h3>

<p>As a group chat participant, I want to add other users to the group from my contact list so that we can expand the conversation.<br/>
As a group chat admin, I want to remove users from the group so that I can manage the group’s participants effectively.</p>

<hr />

<h3><a name="2.UserWorkflow%28StepbyStep%29"></a>2. User Workflow (Step-by-Step)</h3>

<p><b>Add User Flow:</b></p>

<ol>
	<li>User enters an existing group chat.</li>
	<li>User taps on the group chat info icon or header to access participant settings.</li>
	<li>User selects “Manage  Member” &gt; + Add Member</li>
	<li>System opens contact picker screen.</li>
	<li>User selects one or more contacts.</li>
	<li>System adds selected users to the group.</li>
	<li>Invited users receive the group chat invite with Accept/Reject/Block options (handled via separate requirement).</li>
</ol>


<p><b>Remove User Flow (Admin only):</b></p>

<ol>
	<li>Admin taps on group chat info &gt; Manage Members</li>
	<li>Admin selects a member from the participants list and taps on kebab menu</li>
	<li>System shows “Remove from group” option.</li>
	<li>Admin confirms removal via modal.</li>
	<li>System removes the user from group and chat history remains.</li>
</ol>


<hr />

<h3><a name="3.FieldDefinitionsTable"></a>3. Field Definitions Table</h3>

<div class='table-wrap'>
<table class='confluenceTable'><tbody>
<tr>
<th class='confluenceTh'>Field Name</th>
<th class='confluenceTh'>Field Type</th>
<th class='confluenceTh'>Validation Rule</th>
<th class='confluenceTh'>Mandatory</th>
<th class='confluenceTh'>Description</th>
</tr>
<tr>
<td class='confluenceTd'>Add Member Button</td>
<td class='confluenceTd'>CTA Button</td>
<td class='confluenceTd'>Visible to all users</td>
<td class='confluenceTd'>Yes</td>
<td class='confluenceTd'>Initiates contact picker to add members</td>
</tr>
<tr>
<td class='confluenceTd'>Contact Picker</td>
<td class='confluenceTd'>Multi-Select UI</td>
<td class='confluenceTd'>Only shows existing contacts</td>
<td class='confluenceTd'>Yes</td>
<td class='confluenceTd'>Allows selecting multiple contacts</td>
</tr>
<tr>
<td class='confluenceTd'>Remove Member</td>
<td class='confluenceTd'>Option in Menu</td>
<td class='confluenceTd'>Visible to admin only</td>
<td class='confluenceTd'>Yes</td>
<td class='confluenceTd'>Triggers confirmation modal</td>
</tr>
<tr>
<td class='confluenceTd'>Confirmation Modal</td>
<td class='confluenceTd'>Modal</td>
<td class='confluenceTd'>Action must be confirmed</td>
<td class='confluenceTd'>Yes</td>
<td class='confluenceTd'>Prevents accidental removals</td>
</tr>
</tbody></table>
</div>


<hr />

<h3><a name="4.AcceptanceCriteria"></a>4. Acceptance Criteria</h3>

<p><b>AC1.</b> Any user in a group chat can add other users by selecting them from their contact list.<br/>
<b>AC2.</b> When a new user is added, the group chat thread appears in their message list with a pending invitation.<br/>
<b>AC3.</b> Only group admins can remove members from the group.<br/>
<b>AC4.</b> If a non-admin user attempts to remove a participant, system blocks the action.<br/>
<b>AC5.</b> Admin sees “Remove from group” option in the participant details menu.<br/>
<b>AC6.</b> Removal action requires confirmation via modal before processing.<br/>
<b>AC7.</b> Removed users are no longer able to send or receive messages in the group.<br/>
<b>AC8.</b> Group history remains visible to other participants; removed users no longer have access.<br/>
<b>AC9.</b> Toast notifications for successful add or removal:</p>

<ul>
	<li>Add: “You’ve added <span class="error">&#91;Name&#93;</span> to the group.”</li>
	<li>Remove: “<span class="error">&#91;Name&#93;</span> has been removed from the group.”<br/>
<b>AC10.</b> Removed users are not notified directly but will no longer see or access the chat.</li>
</ul>


<hr />

<h3><a name="5.SystemRules"></a>5. System Rules</h3>

<ul>
	<li>Group chat must always retain at least 1 user (admin cannot remove themselves unless a new admin is assigned).</li>
	<li>Add flow is limited to contacts only — user must exist in sender’s contact list.</li>
	<li>A user cannot be added to a group they’ve previously blocked.</li>
	<li>Removed users can be re-added later unless they’ve blocked the group.</li>
</ul>


<hr />

<h3><a name="6.UXOptimizations"></a>6. UX Optimizations</h3>

<ul>
	<li>Group info screen displays “Add Member” at the top of the participant list.</li>
	<li>Admins see a small “Admin” badge beside their name in the participant list.</li>
	<li>Remove action includes contextual info: “Are you sure you want to remove <span class="error">&#91;Name&#93;</span>?”</li>
	<li>Toasts and success states follow standard design system used in message module.</li>
</ul>

            <br/>
        </td>
    </tr>
    </table>

Generated at Fri Jul 04 04:51:46 UTC 2025 by Kid Nguyễn using Jira 1001.0.0-SNAPSHOT#100286-rev:113e78361506b7651c15e516f613bd6e1c0d08de.

</body>
</html>