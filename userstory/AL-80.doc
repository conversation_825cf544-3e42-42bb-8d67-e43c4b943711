<!DOCTYPE html>
<html>
<head>
    <title>[#AL-80] [MB - Messages] Accept/Reject/Block Group Chat</title>
    <meta http-equiv="Content-Type" Content="application/vnd.ms-word; charset=UTF-8">
        <style type="text/css">

.tableBorder, .grid
{
    background-color: #fff;
    width: 100%;
    border-collapse: collapse;
}

.tableBorder td, .grid td
{
    vertical-align: top;
    padding: 2px;
    border: 1px solid #ccc;
}

.noPadding
{
    padding: 0 !important;
}

h3 .subText
{
    font-size: 60%;
    font-weight: normal;
}

.tabLabel
{
    font-weight: bold;
    border: 1px solid #ccc;
    border-bottom:none;
    padding: 2px;
    display: inline;
}

td.blank
{
    padding: 0;
    margin: 0;
}

.blank td
{
    border: none;
}

#descriptionArea
{
    margin: 0;
    padding: 2px;
    border: 1px solid #ccc;
}

hr
{
    border-top:1px solid #aaa;
}

hr.fullcontent
{
  height: 15px;
  padding: 10px 0;
  background: #fff url('https://exnodes.atlassian.net/images/icons/hr.gif') no-repeat scroll center;
}

</style>

</head>
<body>

<table class="tableBorder" cellpadding="0" cellspacing="0" border="0" width="100%">
    <tr>
        <td bgcolor="#f0f0f0" width="100%" colspan="2" valign="top">
                            <b><a id="parent_issue_summary" href="https://exnodes.atlassian.net/browse/AL-16">Messages</a></b>
                <span style="font-size: 9px">(<a id="parent_issue_key" href="https://exnodes.atlassian.net/browse/AL-16">AL-16</a>)</span>
                <h3 class="formtitle"><img src="https://exnodes.atlassian.net/images/icons/link_out_bot.gif" width="16" height="16" />
                        [AL-80]&nbsp;<a href="https://exnodes.atlassian.net/browse/AL-80">[MB - Messages] Accept/Reject/Block Group Chat</a>
            <span class="subText">
               Created: 10/Jun/25                   &nbsp;Updated: 10/Jun/25

                                            </span>
            </h3>
        </td>
    </tr>
    <tr>
        <td width="20%"><b>Status:</b></td>
        <td width="80%">Specified</td>
    </tr>
    <tr>
        <td width="20%"><b>Project:</b></td>
        <td width="80%"><a href="https://exnodes.atlassian.net/secure/BrowseProject.jspa?id=10842">AIOS Link</a></td>
    </tr>

        <tr>
            <td><b>Components:</b></td>
            <td>
                            None
                </td>
    </tr>
    

        <tr>
            <td><b>Affects versions:</b></td>
            <td>
                            None
                </td>
    </tr>
    

        <tr>
            <td><b>Fix versions:</b></td>
            <td>
                            None
                </td>
    </tr>
    
    
            <td width="20%"><b>Parent:</b></td>
        <td width="80%"><a href="https://exnodes.atlassian.net/browse/AL-16">Messages</a></td>
    </table>

<br />
<table class="grid" cellpadding="0" cellspacing="0" border="0" width="100%">
    <tr>
        <td bgcolor="#f0f0f0" valign="top" width="20%">
            <b>Type:</b>
        </td>
        <td bgcolor="#ffffff" valign="top"  width="30%" >
            Story
        </td>

                    <td bgcolor="#f0f0f0">
                <b>Priority:</b>
            </td>
            <td bgcolor="#ffffff" valign="top" nowrap>
                Medium
            </td>
            </tr>
    <tr>
                        <td bgcolor="#f0f0f0" valign="top" width="20%">
                <b>Reporter:</b>
            </td>
            <td bgcolor="#ffffff" valign="top"  width="30%" >
                                        
                
        
            <a class="user-hover" rel="712020:59b66c6e-1068-441c-9436-130289e16a26"
                id="word_reporter_712020:59b66c6e-1068-441c-9436-130289e16a26"
                href="https://exnodes.atlassian.net/secure/ViewProfile.jspa?accountId=712020%3A59b66c6e-1068-441c-9436-130289e16a26">
                    Duyen Nguyen
                </a>

                            </td>
        
                    <td bgcolor="#f0f0f0" width="20%">
                <b>Assignee:</b>
            </td>
            <td bgcolor="#ffffff" valign="top" nowrap  width="30%" >
                                    Unassigned
                            </td>
            </tr>
    	<tr>
		<td bgcolor="#f0f0f0" width="20%">
			<b>Resolution:</b>
		</td>
		<td bgcolor="#ffffff" valign="top" width="30%" nowrap>
            				Unresolved
                    </td>
                    <td bgcolor="#f0f0f0" width="20%">
                <b>Votes:</b>
            </td>
            <td bgcolor="#ffffff" valign="top" width="30%" nowrap>
                0
            </td>
        
    </tr>
    
        <tr>
        <td bgcolor="#f0f0f0" width="20%">
            <b>Labels:</b>
        </td>
        <td id="labels-37933-value" class="value" bgcolor="#ffffff" valign="top" colspan="3" nowrap>
                            None
                    </td>
    </tr>
    
    	<tr>
        		<td bgcolor="#f0f0f0" width="20%"><b>Remaining Estimate:</b></td>
        <td bgcolor="#ffffff" valign="top" nowrap width="80%" colspan="3">
                            Not Specified
            		</td>
    </tr>
    <tr>
                <td bgcolor="#f0f0f0" width="20%"><b>Time Spent:</b></td>
		<td bgcolor="#ffffff" valign="top" nowrap width="80%" colspan="3">
                            Not Specified
            		</td>
	</tr>
    <tr>
                <td bgcolor="#f0f0f0" width="20%"><b>Original estimate:</b></td>
		<td bgcolor="#ffffff" valign="top" nowrap width="80%" colspan="3">
                            Not Specified
            		</td>
    </tr>
    
    </table>



    <br />

    	<table class="grid" cellpadding="0" cellspacing="0" border="0" width="100%">
            

        
    


    
                        <tr>
                <td bgcolor="#f0f0f0" width="20%" valign="top"><b>Sprint:</b></td>
                <td id="customfield_10020-37933-value" class="value" bgcolor="#ffffff" width="80%"></td>
            </tr>
                                <tr>
                <td bgcolor="#f0f0f0" width="20%" valign="top"><b>Platform:</b></td>
                <td id="customfield_10687-37933-value" class="value" bgcolor="#ffffff" width="80%">    MOBILE</td>
            </tr>
            </table>

    <br/>

    <table cellpadding="2" cellspacing="0" border="0" width="100%" align="center">
    <tr>
        <td bgcolor="#bbbbbb" width="1%" nowrap align="center">
            &nbsp;<font color="#ffffff"><b>Description</b></font>&nbsp;
        </td>
        <td>&nbsp;</td>
    </tr>
    </table>

    <table cellpadding="0" cellspacing="0" border="0" width="100%">
    <tr>
        <td id="descriptionArea">
            <h3><a name="1.UseCaseDescription"></a>1. Use Case Description</h3>

<p>As a user who has been added to a group chat, I want to accept or reject the invitation so that I can choose whether to join the conversation, and optionally block the group from ever re-adding me.</p>

<hr />

<h3><a name="2.UserWorkflow%28StepbyStep%29"></a>2. User Workflow (Step-by-Step)</h3>

<ol>
	<li>User receives a new group chat invitation — thread appears in Message list with “Pending Invite” tag.</li>
	<li>User taps on the group chat.</li>
	<li>System displays a full-screen prompt with 3 options:
	<ul>
		<li><b>Accept Group Chat</b></li>
		<li><b>Reject Group Chat</b></li>
		<li><b>Block Group Chat</b></li>
	</ul>
	</li>
	<li>If <b>Accept</b> → System loads full conversation history, and user joins the chat.
	<ul>
		<li>If <b>Reject</b> → Group chat thread disappears from message list.</li>
		<li>If <b>Block</b> → Group chat is removed from view, and user is permanently excluded from being re-added.</li>
	</ul>
	</li>
	<li>System shows relevant success toast or confirmation.</li>
</ol>


<hr />

<h3><a name="3.FieldDefinitionsTable"></a>3. Field Definitions Table</h3>

<div class='table-wrap'>
<table class='confluenceTable'><tbody>
<tr>
<th class='confluenceTh'>Field Name</th>
<th class='confluenceTh'>Field Type</th>
<th class='confluenceTh'>Validation Rule</th>
<th class='confluenceTh'>Mandatory</th>
<th class='confluenceTh'>Description</th>
</tr>
<tr>
<td class='confluenceTd'>Group Chat Thread</td>
<td class='confluenceTd'>Chat Item</td>
<td class='confluenceTd'>Status = "Pending Invite"</td>
<td class='confluenceTd'>Yes</td>
<td class='confluenceTd'>Appears in message module as unread thread</td>
</tr>
<tr>
<td class='confluenceTd'>Accept Button</td>
<td class='confluenceTd'>CTA Button</td>
<td class='confluenceTd'>Always visible</td>
<td class='confluenceTd'>Yes</td>
<td class='confluenceTd'>Joins chat and loads full history</td>
</tr>
<tr>
<td class='confluenceTd'>Reject Button</td>
<td class='confluenceTd'>CTA Button</td>
<td class='confluenceTd'>Always visible</td>
<td class='confluenceTd'>Yes</td>
<td class='confluenceTd'>Removes thread from message list</td>
</tr>
<tr>
<td class='confluenceTd'>Block Button</td>
<td class='confluenceTd'>CTA Button</td>
<td class='confluenceTd'>Always visible</td>
<td class='confluenceTd'>Yes</td>
<td class='confluenceTd'>Removes thread and prevents any future invitation to this chat</td>
</tr>
</tbody></table>
</div>


<hr />

<h3><a name="4.AcceptanceCriteria"></a>4. Acceptance Criteria</h3>

<p><b>AC1.</b> When a user is added to a group, the group chat appears in the message list with a “Pending Invite” indicator.<br/>
<b>AC2.</b> On tapping the group, the system must show a modal or screen with 3 clear options: Accept, Reject, Block.<br/>
<b>AC3.</b> If the user accepts, full chat history becomes visible and user joins the chat.<br/>
<b>AC4.</b> If the user rejects, the group chat disappears from their message list.<br/>
<b>AC5.</b> If the user blocks, the group chat disappears and this group ID is added to a permanent exclusion list for that user.<br/>
<b>AC6.</b> A user who has blocked a group chat cannot be re-added to that group under any circumstance.<br/>
<b>AC7.</b> Toast must confirm each action:</p>

<ul>
	<li>Accept: “You’ve joined the group chat.”</li>
	<li>Reject: “Group chat removed.”</li>
	<li>Block: “You’ve blocked this group chat.”</li>
</ul>



<p><b>AC8.</b> Group admin is not notified when a user rejects or blocks the group.</p>

<hr />

<h3><a name="5.SystemRules"></a>5. System Rules</h3>

<ul>
	<li>A group chat remains in a “Pending” state for a user until action is taken.</li>
	<li>Blocked group chat IDs must persist across sessions.</li>
	<li>Block action is final unless manually unblocked (handled in separate Blocked Groups screen if designed).</li>
	<li>Accepting adds user to participant list; rejecting or blocking does not.</li>
	<li>No system notification is sent to others regarding accept/reject/block.</li>
</ul>


<hr />

<h3><a name="6.UXOptimizations"></a>6. UX Optimizations</h3>

<ul>
	<li>"Pending Invite" badge in chat list is styled distinct from unread badge.</li>
	<li>Modal design should clearly separate the three choices with icons and brief explanation.</li>
	<li>Once accepted, show chat history with a joining message like: “You joined the group.”</li>
	<li>Blocked group chats should not trigger notification, vibration, or badge count.</li>
</ul>

            <br/>
        </td>
    </tr>
    </table>

Generated at Mon Jul 07 07:14:33 UTC 2025 by Kid Nguyễn using Jira 1001.0.0-SNAPSHOT#100286-rev:7362dfe166e568bdd8002f7ad5d37bb053226fd0.

</body>
</html>