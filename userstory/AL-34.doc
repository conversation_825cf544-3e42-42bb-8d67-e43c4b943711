<!DOCTYPE html>
<html>
<head>
    <title>[#AL-34] [MB - Send Gift] Send Gift</title>
    <meta http-equiv="Content-Type" Content="application/vnd.ms-word; charset=UTF-8">
        <style type="text/css">

.tableBorder, .grid
{
    background-color: #fff;
    width: 100%;
    border-collapse: collapse;
}

.tableBorder td, .grid td
{
    vertical-align: top;
    padding: 2px;
    border: 1px solid #ccc;
}

.noPadding
{
    padding: 0 !important;
}

h3 .subText
{
    font-size: 60%;
    font-weight: normal;
}

.tabLabel
{
    font-weight: bold;
    border: 1px solid #ccc;
    border-bottom:none;
    padding: 2px;
    display: inline;
}

td.blank
{
    padding: 0;
    margin: 0;
}

.blank td
{
    border: none;
}

#descriptionArea
{
    margin: 0;
    padding: 2px;
    border: 1px solid #ccc;
}

hr
{
    border-top:1px solid #aaa;
}

hr.fullcontent
{
  height: 15px;
  padding: 10px 0;
  background: #fff url('https://exnodes.atlassian.net/images/icons/hr.gif') no-repeat scroll center;
}

</style>

</head>
<body>

<table class="tableBorder" cellpadding="0" cellspacing="0" border="0" width="100%">
    <tr>
        <td bgcolor="#f0f0f0" width="100%" colspan="2" valign="top">
                            <b><a id="parent_issue_summary" href="https://exnodes.atlassian.net/browse/AL-18">Send Gift</a></b>
                <span style="font-size: 9px">(<a id="parent_issue_key" href="https://exnodes.atlassian.net/browse/AL-18">AL-18</a>)</span>
                <h3 class="formtitle"><img src="https://exnodes.atlassian.net/images/icons/link_out_bot.gif" width="16" height="16" />
                        [AL-34]&nbsp;<a href="https://exnodes.atlassian.net/browse/AL-34">[MB - Send Gift] Send Gift</a>
            <span class="subText">
               Created: 04/Jun/25                   &nbsp;Updated: 25/Jun/25

                                            </span>
            </h3>
        </td>
    </tr>
    <tr>
        <td width="20%"><b>Status:</b></td>
        <td width="80%">In Progress</td>
    </tr>
    <tr>
        <td width="20%"><b>Project:</b></td>
        <td width="80%"><a href="https://exnodes.atlassian.net/secure/BrowseProject.jspa?id=10842">AIOS Link</a></td>
    </tr>

        <tr>
            <td><b>Components:</b></td>
            <td>
                            None
                </td>
    </tr>
    

        <tr>
            <td><b>Affects versions:</b></td>
            <td>
                            None
                </td>
    </tr>
    

        <tr>
            <td><b>Fix versions:</b></td>
            <td>
                            None
                </td>
    </tr>
    
    
            <td width="20%"><b>Parent:</b></td>
        <td width="80%"><a href="https://exnodes.atlassian.net/browse/AL-18">Send Gift</a></td>
    </table>

<br />
<table class="grid" cellpadding="0" cellspacing="0" border="0" width="100%">
    <tr>
        <td bgcolor="#f0f0f0" valign="top" width="20%">
            <b>Type:</b>
        </td>
        <td bgcolor="#ffffff" valign="top"  width="30%" >
            Story
        </td>

                    <td bgcolor="#f0f0f0">
                <b>Priority:</b>
            </td>
            <td bgcolor="#ffffff" valign="top" nowrap>
                Medium
            </td>
            </tr>
    <tr>
                        <td bgcolor="#f0f0f0" valign="top" width="20%">
                <b>Reporter:</b>
            </td>
            <td bgcolor="#ffffff" valign="top"  width="30%" >
                                        
                
        
            <a class="user-hover" rel="712020:59b66c6e-1068-441c-9436-130289e16a26"
                id="word_reporter_712020:59b66c6e-1068-441c-9436-130289e16a26"
                href="https://exnodes.atlassian.net/secure/ViewProfile.jspa?accountId=712020%3A59b66c6e-1068-441c-9436-130289e16a26">
                    Duyen Nguyen
                </a>

                            </td>
        
                    <td bgcolor="#f0f0f0" width="20%">
                <b>Assignee:</b>
            </td>
            <td bgcolor="#ffffff" valign="top" nowrap  width="30%" >
                                    Unassigned
                            </td>
            </tr>
    	<tr>
		<td bgcolor="#f0f0f0" width="20%">
			<b>Resolution:</b>
		</td>
		<td bgcolor="#ffffff" valign="top" width="30%" nowrap>
            				Unresolved
                    </td>
                    <td bgcolor="#f0f0f0" width="20%">
                <b>Votes:</b>
            </td>
            <td bgcolor="#ffffff" valign="top" width="30%" nowrap>
                0
            </td>
        
    </tr>
    
        <tr>
        <td bgcolor="#f0f0f0" width="20%">
            <b>Labels:</b>
        </td>
        <td id="labels-37365-value" class="value" bgcolor="#ffffff" valign="top" colspan="3" nowrap>
                            None
                    </td>
    </tr>
    
    	<tr>
        		<td bgcolor="#f0f0f0" width="20%"><b>Remaining Estimate:</b></td>
        <td bgcolor="#ffffff" valign="top" nowrap width="80%" colspan="3">
                            Not Specified
            		</td>
    </tr>
    <tr>
                <td bgcolor="#f0f0f0" width="20%"><b>Time Spent:</b></td>
		<td bgcolor="#ffffff" valign="top" nowrap width="80%" colspan="3">
                            Not Specified
            		</td>
	</tr>
    <tr>
                <td bgcolor="#f0f0f0" width="20%"><b>Original estimate:</b></td>
		<td bgcolor="#ffffff" valign="top" nowrap width="80%" colspan="3">
                            Not Specified
            		</td>
    </tr>
    
    </table>



    <br />

    	<table class="grid" cellpadding="0" cellspacing="0" border="0" width="100%">
            

        
    


    
                        <tr>
                <td bgcolor="#f0f0f0" width="20%" valign="top"><b>Sprint:</b></td>
                <td id="customfield_10020-37365-value" class="value" bgcolor="#ffffff" width="80%"></td>
            </tr>
                                <tr>
                <td bgcolor="#f0f0f0" width="20%" valign="top"><b>Platform:</b></td>
                <td id="customfield_10687-37365-value" class="value" bgcolor="#ffffff" width="80%">    MOBILE</td>
            </tr>
            </table>

    <br/>

    <table cellpadding="2" cellspacing="0" border="0" width="100%" align="center">
    <tr>
        <td bgcolor="#bbbbbb" width="1%" nowrap align="center">
            &nbsp;<font color="#ffffff"><b>Description</b></font>&nbsp;
        </td>
        <td>&nbsp;</td>
    </tr>
    </table>

    <table cellpadding="0" cellspacing="0" border="0" width="100%">
    <tr>
        <td id="descriptionArea">
            <h3><a name="1.UseCaseDescription"></a>1. Use Case Description</h3>

<p>As a mobile user, I want to send a digital gift—either an event ticket or a previously purchased product—to one of my contacts, so that I can share value and strengthen relationships directly through the platform.</p>

<hr />

<h3><a name="2.UserWorkflow%28StepbyStep%29"></a>2. User Workflow (Step-by-Step)</h3>

<h4><a name="A.Workflow1%3AFromHomeDashboard"></a>A. Workflow 1: From Home Dashboard</h4>

<ol>
	<li>User taps <b>“Send Gift”</b> CTA on the home dashboard.</li>
	<li>System opens the <b>Contact/Lead List</b>.</li>
	<li>User selects a recipient.</li>
	<li>System prompts:
	<ul>
		<li><b>Choose Gift Type</b>:
		<ul>
			<li><b>Send Ticket</b></li>
			<li><b>Send Product from Marketplace</b></li>
		</ul>
		</li>
	</ul>
	</li>
	<li>If <b>Send Ticket</b> is selected → System navigates to the <b>My Tickets tab</b> under Events.
	<ul>
		<li>User browses and selects a ticket to gift.</li>
	</ul>
	</li>
	<li>If <b>Send Product</b> is selected → System navigates to the <b>My Completed Orders tab</b> under Marketplace.
	<ul>
		<li>User selects a product or service to send.</li>
	</ul>
	</li>
	<li>User confirms the selection and taps “Send.”</li>
	<li>System delivers the gift to the recipient and displays confirmation.</li>
</ol>


<h4><a name="B.Workflow2%3AFromContactProfile"></a>B. Workflow 2: From Contact Profile</h4>

<ol>
	<li>User views a contact’s profile.</li>
	<li>User taps <b>“Send Gift”</b>.</li>
	<li>Same flow continues as in Steps 4–8 above.</li>
</ol>


<hr />

<h3><a name="3.FieldDefinitionsTable"></a>3. Field Definitions Table</h3>

<h4><a name="InputFields"></a>Input Fields</h4>

<div class='table-wrap'>
<table class='confluenceTable'><tbody>
<tr>
<th class='confluenceTh'>Field Name</th>
<th class='confluenceTh'>Field Type</th>
<th class='confluenceTh'>Required</th>
<th class='confluenceTh'>Validation Rules</th>
<th class='confluenceTh'>Description</th>
</tr>
<tr>
<td class='confluenceTd'>Recipient</td>
<td class='confluenceTd'>Selector</td>
<td class='confluenceTd'>✅</td>
<td class='confluenceTd'>Must be an existing contact or lead</td>
<td class='confluenceTd'>Who will receive the gift</td>
</tr>
<tr>
<td class='confluenceTd'>Gift Type</td>
<td class='confluenceTd'>Choice Button</td>
<td class='confluenceTd'>✅</td>
<td class='confluenceTd'>“Send Ticket” or “Send Product”</td>
<td class='confluenceTd'>Defines gift type</td>
</tr>
<tr>
<td class='confluenceTd'>Ticket</td>
<td class='confluenceTd'>Item Selector</td>
<td class='confluenceTd'>✅ if Ticket selected</td>
<td class='confluenceTd'>Must be unused &amp; transferable</td>
<td class='confluenceTd'>Chosen event ticket</td>
</tr>
<tr>
<td class='confluenceTd'>Product</td>
<td class='confluenceTd'>Item Selector</td>
<td class='confluenceTd'>✅ if Product selected</td>
<td class='confluenceTd'>Must be digital &amp; fulfilled</td>
<td class='confluenceTd'>Chosen product from completed orders</td>
</tr>
</tbody></table>
</div>


<hr />

<h3><a name="4.DataDisplayTable%28CardView%29"></a>4. Data Display Table (Card View)</h3>

<div class='table-wrap'>
<table class='confluenceTable'><tbody>
<tr>
<th class='confluenceTh'>Display Field</th>
<th class='confluenceTh'>Data Type</th>
<th class='confluenceTh'>Display When Empty</th>
<th class='confluenceTh'>Format</th>
<th class='confluenceTh'>Description</th>
</tr>
<tr>
<td class='confluenceTd'>Contact Name</td>
<td class='confluenceTd'>Text</td>
<td class='confluenceTd'>“Not Selected”</td>
<td class='confluenceTd'>Full Name</td>
<td class='confluenceTd'>Gift recipient</td>
</tr>
<tr>
<td class='confluenceTd'>Gift Type</td>
<td class='confluenceTd'>Tag/Badge</td>
<td class='confluenceTd'>“Not Selected”</td>
<td class='confluenceTd'>Ticket/Product</td>
<td class='confluenceTd'>Chosen gift type</td>
</tr>
<tr>
<td class='confluenceTd'>Item Name</td>
<td class='confluenceTd'>Text</td>
<td class='confluenceTd'>“No item selected”</td>
<td class='confluenceTd'>Plaintext</td>
<td class='confluenceTd'>Ticket title or product name</td>
</tr>
<tr>
<td class='confluenceTd'>Confirmation Toast</td>
<td class='confluenceTd'>Text</td>
<td class='confluenceTd'>Hidden</td>
<td class='confluenceTd'>“Gift sent to…”</td>
<td class='confluenceTd'>Confirmation after send</td>
</tr>
</tbody></table>
</div>


<hr />

<h3><a name="5.AcceptanceCriteria"></a>5. Acceptance Criteria</h3>

<ul>
	<li><b>AC1</b>: Both workflows (Home CTA and Contact Profile) must allow access to gift selection.</li>
	<li><b>AC2</b>: User must choose between sending a ticket or product.</li>
	<li><b>AC3</b>: On choosing “Send Ticket”, system must load user’s valid tickets.</li>
	<li><b>AC4</b>: On choosing “Send Product”, system must load completed digital orders.</li>
	<li><b>AC5</b>: Only transferable, unused tickets and fulfilled digital products can be sent.</li>
	<li><b>AC6</b>: Gift is delivered digitally, and recipient receives a notification.</li>
	<li><b>AC7</b>: Sending confirmation message must appear after success.</li>
</ul>


<hr />

<h3><a name="6.SystemRules"></a>6. System Rules</h3>

<ul>
	<li>Gifts can only be sent once per order item or ticket ID.</li>
	<li>Sender must have ownership of the item being gifted.</li>
	<li>Gifting action logs are stored with gift ID, sender ID, recipient ID, and timestamp.</li>
	<li>Gifting a ticket marks it as “transferred” and prevents reuse by sender.</li>
	<li>Gifting a product sends a one-time access token or delivery to the recipient's profile.</li>
</ul>


<hr />

<h3><a name="7.UXOptimizations"></a>7. UX Optimizations</h3>

<ul>
	<li>Use product/ticket thumbnails in selection list for visual clarity.</li>
	<li>Confirmation modal: <em>“Send <span class="error">&#91;item&#93;</span> to <span class="error">&#91;contact&#93;</span>? This action cannot be undone.”</em></li>
	<li>Toast: <em>“Gift sent to <span class="error">&#91;Name&#93;</span>. They’ll receive it instantly.”</em></li>
	<li>Auto-return to home/dashboard after send.</li>
	<li>Add filters in ticket/product selection screens (e.g., “Available Only”).</li>
</ul>


<hr />
            <br/>
        </td>
    </tr>
    </table>

Generated at Fri Jun 27 02:52:05 UTC 2025 by Kid Nguyễn using Jira 1001.0.0-SNAPSHOT#100285-rev:3c7b32e5e5b342f76f058c25245725788fa6e907.

</body>
</html>