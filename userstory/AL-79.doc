<!DOCTYPE html>
<html>
<head>
    <title>[#AL-79] [MB - Messages] Leave Group Chat</title>
    <meta http-equiv="Content-Type" Content="application/vnd.ms-word; charset=UTF-8">
        <style type="text/css">

.tableBorder, .grid
{
    background-color: #fff;
    width: 100%;
    border-collapse: collapse;
}

.tableBorder td, .grid td
{
    vertical-align: top;
    padding: 2px;
    border: 1px solid #ccc;
}

.noPadding
{
    padding: 0 !important;
}

h3 .subText
{
    font-size: 60%;
    font-weight: normal;
}

.tabLabel
{
    font-weight: bold;
    border: 1px solid #ccc;
    border-bottom:none;
    padding: 2px;
    display: inline;
}

td.blank
{
    padding: 0;
    margin: 0;
}

.blank td
{
    border: none;
}

#descriptionArea
{
    margin: 0;
    padding: 2px;
    border: 1px solid #ccc;
}

hr
{
    border-top:1px solid #aaa;
}

hr.fullcontent
{
  height: 15px;
  padding: 10px 0;
  background: #fff url('https://exnodes.atlassian.net/images/icons/hr.gif') no-repeat scroll center;
}

</style>

</head>
<body>

<table class="tableBorder" cellpadding="0" cellspacing="0" border="0" width="100%">
    <tr>
        <td bgcolor="#f0f0f0" width="100%" colspan="2" valign="top">
                            <b><a id="parent_issue_summary" href="https://exnodes.atlassian.net/browse/AL-16">Messages</a></b>
                <span style="font-size: 9px">(<a id="parent_issue_key" href="https://exnodes.atlassian.net/browse/AL-16">AL-16</a>)</span>
                <h3 class="formtitle"><img src="https://exnodes.atlassian.net/images/icons/link_out_bot.gif" width="16" height="16" />
                        [AL-79]&nbsp;<a href="https://exnodes.atlassian.net/browse/AL-79">[MB - Messages] Leave Group Chat</a>
            <span class="subText">
               Created: 10/Jun/25                   &nbsp;Updated: 10/Jun/25

                                            </span>
            </h3>
        </td>
    </tr>
    <tr>
        <td width="20%"><b>Status:</b></td>
        <td width="80%">Specified</td>
    </tr>
    <tr>
        <td width="20%"><b>Project:</b></td>
        <td width="80%"><a href="https://exnodes.atlassian.net/secure/BrowseProject.jspa?id=10842">AIOS Link</a></td>
    </tr>

        <tr>
            <td><b>Components:</b></td>
            <td>
                            None
                </td>
    </tr>
    

        <tr>
            <td><b>Affects versions:</b></td>
            <td>
                            None
                </td>
    </tr>
    

        <tr>
            <td><b>Fix versions:</b></td>
            <td>
                            None
                </td>
    </tr>
    
    
            <td width="20%"><b>Parent:</b></td>
        <td width="80%"><a href="https://exnodes.atlassian.net/browse/AL-16">Messages</a></td>
    </table>

<br />
<table class="grid" cellpadding="0" cellspacing="0" border="0" width="100%">
    <tr>
        <td bgcolor="#f0f0f0" valign="top" width="20%">
            <b>Type:</b>
        </td>
        <td bgcolor="#ffffff" valign="top"  width="30%" >
            Story
        </td>

                    <td bgcolor="#f0f0f0">
                <b>Priority:</b>
            </td>
            <td bgcolor="#ffffff" valign="top" nowrap>
                Medium
            </td>
            </tr>
    <tr>
                        <td bgcolor="#f0f0f0" valign="top" width="20%">
                <b>Reporter:</b>
            </td>
            <td bgcolor="#ffffff" valign="top"  width="30%" >
                                        
                
        
            <a class="user-hover" rel="712020:59b66c6e-1068-441c-9436-130289e16a26"
                id="word_reporter_712020:59b66c6e-1068-441c-9436-130289e16a26"
                href="https://exnodes.atlassian.net/secure/ViewProfile.jspa?accountId=712020%3A59b66c6e-1068-441c-9436-130289e16a26">
                    Duyen Nguyen
                </a>

                            </td>
        
                    <td bgcolor="#f0f0f0" width="20%">
                <b>Assignee:</b>
            </td>
            <td bgcolor="#ffffff" valign="top" nowrap  width="30%" >
                                    Unassigned
                            </td>
            </tr>
    	<tr>
		<td bgcolor="#f0f0f0" width="20%">
			<b>Resolution:</b>
		</td>
		<td bgcolor="#ffffff" valign="top" width="30%" nowrap>
            				Unresolved
                    </td>
                    <td bgcolor="#f0f0f0" width="20%">
                <b>Votes:</b>
            </td>
            <td bgcolor="#ffffff" valign="top" width="30%" nowrap>
                0
            </td>
        
    </tr>
    
        <tr>
        <td bgcolor="#f0f0f0" width="20%">
            <b>Labels:</b>
        </td>
        <td id="labels-37930-value" class="value" bgcolor="#ffffff" valign="top" colspan="3" nowrap>
                            None
                    </td>
    </tr>
    
    	<tr>
        		<td bgcolor="#f0f0f0" width="20%"><b>Remaining Estimate:</b></td>
        <td bgcolor="#ffffff" valign="top" nowrap width="80%" colspan="3">
                            Not Specified
            		</td>
    </tr>
    <tr>
                <td bgcolor="#f0f0f0" width="20%"><b>Time Spent:</b></td>
		<td bgcolor="#ffffff" valign="top" nowrap width="80%" colspan="3">
                            Not Specified
            		</td>
	</tr>
    <tr>
                <td bgcolor="#f0f0f0" width="20%"><b>Original estimate:</b></td>
		<td bgcolor="#ffffff" valign="top" nowrap width="80%" colspan="3">
                            Not Specified
            		</td>
    </tr>
    
    </table>



    <br />

    	<table class="grid" cellpadding="0" cellspacing="0" border="0" width="100%">
            

        
    


    
                        <tr>
                <td bgcolor="#f0f0f0" width="20%" valign="top"><b>Sprint:</b></td>
                <td id="customfield_10020-37930-value" class="value" bgcolor="#ffffff" width="80%"></td>
            </tr>
                                <tr>
                <td bgcolor="#f0f0f0" width="20%" valign="top"><b>Platform:</b></td>
                <td id="customfield_10687-37930-value" class="value" bgcolor="#ffffff" width="80%">    MOBILE</td>
            </tr>
            </table>

    <br/>

    <table cellpadding="2" cellspacing="0" border="0" width="100%" align="center">
    <tr>
        <td bgcolor="#bbbbbb" width="1%" nowrap align="center">
            &nbsp;<font color="#ffffff"><b>Description</b></font>&nbsp;
        </td>
        <td>&nbsp;</td>
    </tr>
    </table>

    <table cellpadding="0" cellspacing="0" border="0" width="100%">
    <tr>
        <td id="descriptionArea">
            <h3><a name="1.UseCaseDescription"></a>1. Use Case Description</h3>

<p>As a group chat participant, I want to leave the chat group so that I no longer receive messages or remain part of the conversation, and if I’m the admin, I want to transfer my admin rights before exiting.</p>

<hr />

<h3><a name="2.UserWorkflow%28StepbyStep%29"></a>2. User Workflow (Step-by-Step)</h3>

<ol>
	<li>User opens the group chat thread.</li>
	<li>Taps the menu (options) icon.</li>
	<li>Selects "Leave Group" from the options.</li>
	<li>System checks if the user is the group admin:
	<ul>
		<li><b>If not an admin</b> → Proceed to Step 6.</li>
		<li><b>If admin</b> → Show a list of members to choose a new admin.</li>
	</ul>
	</li>
	<li>User selects a new admin → Confirm.</li>
	<li>System shows confirmation modal:<br/>
“Are you sure you want to leave this group chat? You won’t receive any more messages unless re-added.”</li>
	<li>User confirms → system removes user and redirects to Message home.</li>
	<li>Other members continue conversation as normal.</li>
</ol>


<hr />

<h3><a name="3.FieldDefinitionsTable"></a>3. Field Definitions Table</h3>

<div class='table-wrap'>
<table class='confluenceTable'><tbody>
<tr>
<th class='confluenceTh'>Field Name</th>
<th class='confluenceTh'>Field Type</th>
<th class='confluenceTh'>Validation Rule</th>
<th class='confluenceTh'>Mandatory</th>
<th class='confluenceTh'>Description</th>
</tr>
<tr>
<td class='confluenceTd'>Leave Group Button</td>
<td class='confluenceTd'>CTA Button</td>
<td class='confluenceTd'>Visible to all group members</td>
<td class='confluenceTd'>Yes</td>
<td class='confluenceTd'>Triggers exit from group chat</td>
</tr>
<tr>
<td class='confluenceTd'>Transfer Admin Dropdown</td>
<td class='confluenceTd'>Selection</td>
<td class='confluenceTd'>Visible if current user is admin</td>
<td class='confluenceTd'>Yes (if admin)</td>
<td class='confluenceTd'>Must select one other member to become admin</td>
</tr>
</tbody></table>
</div>


<hr />

<h3><a name="4.AcceptanceCriteria"></a>4. Acceptance Criteria</h3>

<p><b>AC1.</b> Any user can leave the group chat, including admin.<br/>
<b>AC2.</b> A confirmation modal must appear before exiting.<br/>
<b>AC3.</b> After leaving, user is removed from the group and redirected to Message home.<br/>
<b>AC4.</b> Conversation continues for remaining members.<br/>
<b>AC5.</b> Group chat persists unless explicitly deleted by a user from their own message module.<br/>
<b>AC6.</b> A toast message appears post-leave: “You’ve left the group chat.”<br/>
<b>AC7.</b> If the leaving user is the <b>admin</b>, they must assign another member as admin before exit.<br/>
<b>AC8.</b> The system will not allow admin to leave until transfer is confirmed.<br/>
<b>AC9.</b> Admin transfer selection must show members of the group excluding self.</p>

<hr />

<h3><a name="5.SystemRules"></a>5. System Rules</h3>

<ul>
	<li>Group persists with or without admin.</li>
	<li>Leaving member is removed from all participant lists.</li>
	<li>Messages sent prior to leaving remain visible to others.</li>
	<li>Admin rights must always belong to one member; cannot be empty.</li>
</ul>


<hr />

<h3><a name="6.UXOptimizations"></a>6. UX Optimizations</h3>

<ul>
	<li>“Leave Group” button appears at the bottom of chat settings.</li>
	<li>If admin: transfer dropdown appears as step before confirmation modal.</li>
	<li>Admin dropdown excludes the current user.</li>
	<li>Toast appears after successful admin transfer: “Admin rights transferred. You have left the group.”</li>
</ul>

            <br/>
        </td>
    </tr>
    </table>

Generated at Mon Jul 07 08:27:18 UTC 2025 by Kid Nguyễn using Jira 1001.0.0-SNAPSHOT#100286-rev:7362dfe166e568bdd8002f7ad5d37bb053226fd0.

</body>
</html>