<!DOCTYPE html>
<html>
<head>
    <title>[#AL-84] [WEB - Marketplace] Product List</title>
    <meta http-equiv="Content-Type" Content="application/vnd.ms-word; charset=UTF-8">
        <style type="text/css">

.tableBorder, .grid
{
    background-color: #fff;
    width: 100%;
    border-collapse: collapse;
}

.tableBorder td, .grid td
{
    vertical-align: top;
    padding: 2px;
    border: 1px solid #ccc;
}

.noPadding
{
    padding: 0 !important;
}

h3 .subText
{
    font-size: 60%;
    font-weight: normal;
}

.tabLabel
{
    font-weight: bold;
    border: 1px solid #ccc;
    border-bottom:none;
    padding: 2px;
    display: inline;
}

td.blank
{
    padding: 0;
    margin: 0;
}

.blank td
{
    border: none;
}

#descriptionArea
{
    margin: 0;
    padding: 2px;
    border: 1px solid #ccc;
}

hr
{
    border-top:1px solid #aaa;
}

hr.fullcontent
{
  height: 15px;
  padding: 10px 0;
  background: #fff url('https://exnodes.atlassian.net/images/icons/hr.gif') no-repeat scroll center;
}

</style>

</head>
<body>

<table class="tableBorder" cellpadding="0" cellspacing="0" border="0" width="100%">
    <tr>
        <td bgcolor="#f0f0f0" width="100%" colspan="2" valign="top">
                            <b><a id="parent_issue_summary" href="https://exnodes.atlassian.net/browse/AL-20">Marketplace</a></b>
                <span style="font-size: 9px">(<a id="parent_issue_key" href="https://exnodes.atlassian.net/browse/AL-20">AL-20</a>)</span>
                <h3 class="formtitle"><img src="https://exnodes.atlassian.net/images/icons/link_out_bot.gif" width="16" height="16" />
                        [AL-84]&nbsp;<a href="https://exnodes.atlassian.net/browse/AL-84">[WEB - Marketplace] Product List</a>
            <span class="subText">
               Created: 10/Jun/25                   &nbsp;Updated: 25/Jun/25

                                            </span>
            </h3>
        </td>
    </tr>
    <tr>
        <td width="20%"><b>Status:</b></td>
        <td width="80%">In Progress</td>
    </tr>
    <tr>
        <td width="20%"><b>Project:</b></td>
        <td width="80%"><a href="https://exnodes.atlassian.net/secure/BrowseProject.jspa?id=10842">AIOS Link</a></td>
    </tr>

        <tr>
            <td><b>Components:</b></td>
            <td>
                            None
                </td>
    </tr>
    

        <tr>
            <td><b>Affects versions:</b></td>
            <td>
                            None
                </td>
    </tr>
    

        <tr>
            <td><b>Fix versions:</b></td>
            <td>
                            None
                </td>
    </tr>
    
    
            <td width="20%"><b>Parent:</b></td>
        <td width="80%"><a href="https://exnodes.atlassian.net/browse/AL-20">Marketplace</a></td>
    </table>

<br />
<table class="grid" cellpadding="0" cellspacing="0" border="0" width="100%">
    <tr>
        <td bgcolor="#f0f0f0" valign="top" width="20%">
            <b>Type:</b>
        </td>
        <td bgcolor="#ffffff" valign="top"  width="30%" >
            Story
        </td>

                    <td bgcolor="#f0f0f0">
                <b>Priority:</b>
            </td>
            <td bgcolor="#ffffff" valign="top" nowrap>
                Medium
            </td>
            </tr>
    <tr>
                        <td bgcolor="#f0f0f0" valign="top" width="20%">
                <b>Reporter:</b>
            </td>
            <td bgcolor="#ffffff" valign="top"  width="30%" >
                                        
                
        
            <a class="user-hover" rel="712020:59b66c6e-1068-441c-9436-130289e16a26"
                id="word_reporter_712020:59b66c6e-1068-441c-9436-130289e16a26"
                href="https://exnodes.atlassian.net/secure/ViewProfile.jspa?accountId=712020%3A59b66c6e-1068-441c-9436-130289e16a26">
                    Duyen Nguyen
                </a>

                            </td>
        
                    <td bgcolor="#f0f0f0" width="20%">
                <b>Assignee:</b>
            </td>
            <td bgcolor="#ffffff" valign="top" nowrap  width="30%" >
                                    Unassigned
                            </td>
            </tr>
    	<tr>
		<td bgcolor="#f0f0f0" width="20%">
			<b>Resolution:</b>
		</td>
		<td bgcolor="#ffffff" valign="top" width="30%" nowrap>
            				Unresolved
                    </td>
                    <td bgcolor="#f0f0f0" width="20%">
                <b>Votes:</b>
            </td>
            <td bgcolor="#ffffff" valign="top" width="30%" nowrap>
                0
            </td>
        
    </tr>
    
        <tr>
        <td bgcolor="#f0f0f0" width="20%">
            <b>Labels:</b>
        </td>
        <td id="labels-37945-value" class="value" bgcolor="#ffffff" valign="top" colspan="3" nowrap>
                            None
                    </td>
    </tr>
    
    	<tr>
        		<td bgcolor="#f0f0f0" width="20%"><b>Remaining Estimate:</b></td>
        <td bgcolor="#ffffff" valign="top" nowrap width="80%" colspan="3">
                            Not Specified
            		</td>
    </tr>
    <tr>
                <td bgcolor="#f0f0f0" width="20%"><b>Time Spent:</b></td>
		<td bgcolor="#ffffff" valign="top" nowrap width="80%" colspan="3">
                            Not Specified
            		</td>
	</tr>
    <tr>
                <td bgcolor="#f0f0f0" width="20%"><b>Original estimate:</b></td>
		<td bgcolor="#ffffff" valign="top" nowrap width="80%" colspan="3">
                            Not Specified
            		</td>
    </tr>
    
    </table>



    <br />

    	<table class="grid" cellpadding="0" cellspacing="0" border="0" width="100%">
            

        
    


    
                        <tr>
                <td bgcolor="#f0f0f0" width="20%" valign="top"><b>Sprint:</b></td>
                <td id="customfield_10020-37945-value" class="value" bgcolor="#ffffff" width="80%"></td>
            </tr>
                                <tr>
                <td bgcolor="#f0f0f0" width="20%" valign="top"><b>Platform:</b></td>
                <td id="customfield_10687-37945-value" class="value" bgcolor="#ffffff" width="80%">    WEB</td>
            </tr>
            </table>

    <br/>

    <table cellpadding="2" cellspacing="0" border="0" width="100%" align="center">
    <tr>
        <td bgcolor="#bbbbbb" width="1%" nowrap align="center">
            &nbsp;<font color="#ffffff"><b>Description</b></font>&nbsp;
        </td>
        <td>&nbsp;</td>
    </tr>
    </table>

    <table cellpadding="0" cellspacing="0" border="0" width="100%">
    <tr>
        <td id="descriptionArea">
            <h1><a name="MarketplaceProductListRequirementSpecification"></a>Marketplace Product List Requirement Specification</h1>

<p><b>Menu Path:</b> Marketplace &gt; Product List</p>

<h3><a name="1.UseCaseDescription"></a>1. Use Case Description</h3>

<p>As an admin on the web platform, I want to view all products currently listed in the marketplace so that I can manage prices and remove listings when needed.</p>

<h3><a name="2.UserWorkflow"></a>2. User Workflow</h3>

<ul>
	<li>Step 1: Admin opens <b>Marketplace &gt; Product List</b> from the Web Admin Module dashboard.</li>
	<li>Step 2: System displays a searchable table listing marketplace products with pagination.</li>
	<li>Step 3: Admin uses the search box or filters to locate specific products.</li>
	<li>Step 4: Admin clicks a row (excluding links) to open the <b>Product Details</b> page.</li>
	<li>Step 5: Admin clicks the Owner name link within the row to open the <b>Business Profile</b> page.</li>
	<li>Step 6: Admin clicks the <b>Actions</b> gear icon in the row and chooses <b>Update</b> or <b>Remove from Marketplace</b> from the popover.</li>
	<li>Step 7: System confirms the action and updates the listing accordingly.</li>
</ul>


<h3><a name="3.SearchandFilterRules"></a>3. Search and Filter Rules</h3>

<h4><a name="SearchRules"></a>Search Rules</h4>

<ul>
	<li>Partial, case-insensitive match on <b>Product Name</b>, <b>Category</b>, <b>Manufacturer</b>, and <b>Business Name</b>.</li>
</ul>


<h4><a name="FilterRules"></a>Filter Rules</h4>

<ul>
	<li><b>Category</b>: Multi-select dropdown of marketplace product categories.</li>
	<li><b>Manufacturer</b>: Dropdown listing all manufacturers.</li>
	<li><b>Business</b>: Dropdown of business names.</li>
	<li><b>Price Range</b>: Min/Max numeric fields to filter by listing price.</li>
	<li><b>Listing Date Range</b>: Date range picker to filter by listing date.</li>
</ul>


<h3><a name="4.FieldDefinitions"></a>4. Field Definitions</h3>

<h4><a name="InteractionElements"></a>Interaction Elements</h4>

<div class='table-wrap'>
<table class='confluenceTable'><tbody>
<tr>
<th class='confluenceTh'>Element Name</th>
<th class='confluenceTh'>Type</th>
<th class='confluenceTh'>Trigger Condition</th>
<th class='confluenceTh'>Description</th>
</tr>
<tr>
<td class='confluenceTd'>Row Link</td>
<td class='confluenceTd'>Row Click</td>
<td class='confluenceTd'>Always enabled</td>
<td class='confluenceTd'>Navigates to Product Details page</td>
</tr>
<tr>
<td class='confluenceTd'>Business Link</td>
<td class='confluenceTd'>Text Link</td>
<td class='confluenceTd'>Always visible</td>
<td class='confluenceTd'>Opens Business Profile page</td>
</tr>
<tr>
<td class='confluenceTd'>Actions Gear Icon</td>
<td class='confluenceTd'>Icon Button</td>
<td class='confluenceTd'>Always visible</td>
<td class='confluenceTd'>Opens popover with Update and Remove options</td>
</tr>
</tbody></table>
</div>


<h3><a name="5.DataDisplay%28Summary%29"></a>5. Data Display (Summary)</h3>

<div class='table-wrap'>
<table class='confluenceTable'><tbody>
<tr>
<th class='confluenceTh'>Data Name</th>
<th class='confluenceTh'>Data Type</th>
<th class='confluenceTh'>Display When Empty</th>
<th class='confluenceTh'>Format</th>
<th class='confluenceTh'>Description</th>
</tr>
<tr>
<td class='confluenceTd'>Product Name</td>
<td class='confluenceTd'>Text</td>
<td class='confluenceTd'>"--"</td>
<td class='confluenceTd'>Plaintext</td>
<td class='confluenceTd'>Name of the product</td>
</tr>
<tr>
<td class='confluenceTd'>Owner</td>
<td class='confluenceTd'>Text Link</td>
<td class='confluenceTd'>"--"</td>
<td class='confluenceTd'>Plaintext</td>
<td class='confluenceTd'>Business name of owner</td>
</tr>
<tr>
<td class='confluenceTd'>Price</td>
<td class='confluenceTd'>Amount</td>
<td class='confluenceTd'>"--"</td>
<td class='confluenceTd'>USD</td>
<td class='confluenceTd'>Listing price</td>
</tr>
<tr>
<td class='confluenceTd'>Category</td>
<td class='confluenceTd'>Text</td>
<td class='confluenceTd'>"--"</td>
<td class='confluenceTd'>Plaintext</td>
<td class='confluenceTd'>Product category</td>
</tr>
<tr>
<td class='confluenceTd'>Manufacturer</td>
<td class='confluenceTd'>Text</td>
<td class='confluenceTd'>"--"</td>
<td class='confluenceTd'>Plaintext</td>
<td class='confluenceTd'>Manufacturer brand</td>
</tr>
<tr>
<td class='confluenceTd'>Listing Date</td>
<td class='confluenceTd'>Date</td>
<td class='confluenceTd'>"--"</td>
<td class='confluenceTd'>YYYY-MM-DD</td>
<td class='confluenceTd'>Date product was listed</td>
</tr>
<tr>
<td class='confluenceTd'>Actions</td>
<td class='confluenceTd'>Gear Icon</td>
<td class='confluenceTd'>Hidden</td>
<td class='confluenceTd'>N/A</td>
<td class='confluenceTd'>Popover with Update or Remove options</td>
</tr>
</tbody></table>
</div>


<h3><a name="6.AcceptanceCriteria"></a>6. Acceptance Criteria</h3>

<ul>
	<li>AC1: Table must display columns for Product Name, Owner, Price, Category, Manufacturer, Listing Date, and Actions.</li>
	<li>AC2: Clicking a row opens Product Details; clicking the Owner link opens the Business Profile page.</li>
	<li>AC3: Search returns matching results on Product Name, Category, Manufacturer, or Business Name.</li>
	<li>AC4: Filters narrow results by Category, Manufacturer, Business, Price Range, and Listing Date Range.</li>
	<li>AC5: Update and Remove actions trigger confirmation and reflect changes immediately in the table.</li>
	<li>AC6: Table loads with pagination of 25 marketplace products per page by default.</li>
</ul>


<h3><a name="7.SystemRules"></a>7. System Rules</h3>

<ul>
	<li>Remove action requires confirmation and is only allowed when the product has no active orders.</li>
	<li>Table data is sorted by Listing Date (newest first) by default.</li>
	<li>Update action must check for concurrent changes to avoid conflicts.</li>
</ul>


<h3><a name="8.UXOptimizations"></a>8. UX Optimizations</h3>

<ul>
	<li>Keep search box focused after returning from details pages.</li>
	<li>Highlight rows on hover to indicate clickability.</li>
	<li>Disable the gear icon menu while processing and show a spinner.</li>
	<li>Remember last applied filters when admin returns to the screen.</li>
	<li>Provide a tooltip for the gear icon to clarify its purpose.</li>
</ul>

            <br/>
        </td>
    </tr>
    </table>

Generated at Fri Jun 27 02:51:09 UTC 2025 by Kid Nguyễn using Jira 1001.0.0-SNAPSHOT#100285-rev:3c7b32e5e5b342f76f058c25245725788fa6e907.

</body>
</html>