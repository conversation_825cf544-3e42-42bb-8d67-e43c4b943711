# Use an official Python runtime as a parent image
FROM public.ecr.aws/docker/library/python:3.9

ARG AWS_ACCESS_KEY_ID
ARG AWS_SECRET_ACCESS_KEY
ARG AWS_DEFAULT_REGION

# Install aws cli
RUN apt-get update  &&  apt-get install -y cmake libgl1-mesa-glx libglib2.0-0
RUN apt-get install -y \
        python3 \
        python3-pip \
        python3-setuptools \
        groff \
        less \
    && pip3 install --upgrade pip \
    && apt-get clean

RUN pip3 --no-cache-dir install --upgrade awscli

# Set the working directory in the container
WORKDIR /src

# COPY internal-exrunner.pem ./
# RUN chmod 400 internal-exrunner.pem

# Install any needed packages specified in requirements.txt
RUN pip install --upgrade pip

COPY requirements.txt ./
RUN pip install -r requirements.txt

# Copy the entrypoint script into the container
COPY entrypoint.sh ./
RUN sed -i 's/\r$//g' entrypoint.sh
RUN chmod +x entrypoint.sh
# COPY ./.env /

# Copy the current directory contents into the container at /src
COPY . .

# Make port 8000 available to the world outside this container
EXPOSE 80

# Copy the .env file into the container (assuming it's in the current directory)
# COPY .env /src/.env
# Run main.py when the container launches
# ENTRYPOINT ["/entrypoint.sh"]
ENTRYPOINT ["sh", "/src/entrypoint.sh"]
