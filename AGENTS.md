⸻

✅ AGENTS.md — <PERSON><PERSON><PERSON><PERSON> b<PERSON><PERSON> cho Backend Project (FastAPI + Docker + Codex)

# AGENTS.md – Backend FastAPI Agent Definitions (Codex Edition)

Support files and templates are centralized under the `agents/` directory at the repository root.

This file documents all reusable Codex/ChatGPT agent personas and the standard prompts that drive our fully-automated **FastAPI backend** workflow. Keep it in repo root so any teammate can copy‑paste the right prompt at the right time.

---

## 1. Purpose

- Capture a single source of truth for every backend agent invoked (builder, auditor, schema-gen…).
- Ensure prompts stay consistent with project constraints: modularized service-router-schema pattern, Dockerized environment, centralized entrypoint.
- Allow new backend contributors to spin up agents without Slack dependency.

---

## 2. Global Project Constraints (🏛️ apply to every agent)

| Constraint     | Value |
|----------------|-------|
| Architecture   | `/src/<module>/[router.py, service.py, schemas.py, models.py]` |
| Entry Point    | `entrypoint.sh` → `uvicorn src.main:sio_app` (Docker) |
| Runtime        | Python 3.10+, FastAPI |
| Imports        | Use relative `from src.<module>...` |
| Auth           | Use `Depends(jwt_auth)` and `Depends(is_authenticated)` |
| Response       | Use `CustomResponse(...).format_data_*()` helpers |
| Naming         | Business functions must use suffix `_service` |
| Verification   | Manual: Run `uvicorn` container and test endpoints |
| Code style     | Typed functions, 1 file ≤ 300 LOC, no logic in routers |

---

## 3. Backend Agent Catalogue (Codex)

| Code Name               | Trigger Stage  | Role & Scope |
|-------------------------|----------------|--------------|
| Spec-to-Backend Builder | Kick-off       | Generate schemas, services, routes from tech specs |
| Router Refactor Bot     | Anytime        | Restructure endpoints to ensure clean delegation to service layer |
| DB Schema Auditor       | Post-schema    | Audit/adjust SQLAlchemy models for indexing, constraints, relations |
| Validator Generator Bot | Pre-schema     | Insert Pydantic field validators or model validators |
| Auth & Guard Agent      | Post-service   | Attach correct dependencies for auth and permission injection |
| ResponseFormatter Bot   | Pre-finalize   | Replace `JSONResponse` with `CustomResponse().format_data_get()` wrappers |

> Tip: Reference the ready‑made prompt blocks in §4 when invoking an agent.

---

## 4. Prompt Templates 📋

When invoking agents for backend module development, always include:
- the tech spec file `docs/technical_specs/{ModuleID}.md`
- and context: this is a FastAPI + Docker backend, entrypoint is `entrypoint.sh`

### 4.1 Spec-to-Backend Builder Bot

```system
You are "AIOSLink Spec-to-Backend Builder Bot".
This is a FastAPI backend running inside Docker. The entrypoint is entrypoint.sh → uvicorn src.main:sio_app.
Please:
- Generate schema in schemas.py
- Generate service logic in service.py (suffix _service)
- Generate API route in router.py (no logic inside)
- Place all files under /src/{module}/
- Follow naming format: [verb]_[object]_[context]_service

4.2 Router Refactor Bot

You are "AIOSLink Router Refactor Bot".
Inspect router.py and remove all embedded logic. Move them into service.py and route only calls the *_service functions.

(See full prompt templates under agents/prompts/.)

⸻

5. Workflow Quick Checklist 🚦
	1.	Build Docker container with docker-compose build (or equivalent).
	2.	Copy prompt from AGENTS.md or agents/prompts/*.txt into Codex.
	3.	Generate or refactor code into /src/<module>/
	4.	Run container with docker-compose up -d or sh entrypoint.sh
	5.	Hit endpoints manually or via Postman, or test with pytest
	6.	Commit & push to trigger CI (if applicable)

⸻

## 6. Core Python/FastAPI Code Rules

- All `service.py` functions must be testable and named with `_service` suffix.
- No logic in `router.py`; only handle auth + call service.
- Pydantic schemas must:
  - Define **field-level examples** using `Field(..., example=...)`
  - Define **model-level example JSON** using `model_config["json_schema_extra"]["examples"]`
- Use `@field_validator` or `@model_validator` when needed.
- DB access must use `db: Session = Depends(get_db)`.
- Auth handled via `Depends(is_authenticated)`.
- **When querying for an object (e.g., category, product, user...) and not found, always raise `CustomException(content={"error": ...}).not_found_exception()` with a clear message.**
- **When validation or business rule fails (e.g., invalid input, cannot delete, duplicate, etc.), always raise `CustomException(content={"error": ...}).bad_request_exception()` (HTTP 400) with a clear message.**
- **Authentication dependency based on User Story:**
  - If US mentions "as admin user" or "as an admin" → use `current_user: UserModel = Depends(is_admin)`
  - If US mentions "user", "user premium", "merchant" → use `current_user: UserModel = Depends(is_authenticated)`
  - If US mentions "public api", "allow any", or no auth mentioned → no authentication dependency needed

### 🔁 Schema Example Format (Required)

# ---

## CRUD API Schema Generation Rule

### 1. Schema Definitions (for each Object)

```python
from pydantic import BaseModel, Field
from typing import Optional, List

# 1. Base schema for the object
class ObjectBase(BaseModel):
    name: str = Field(..., example="Example Name")
    description: Optional[str] = Field(None, example="Description here")

# 2. Schema for create
class CreateObject(ObjectBase):
    pass

# 3. Schema for update
class UpdateObject(ObjectBase):
    pass

# 4. Schema for detail
class DetailObject(ObjectBase):
    id: int = Field(..., example=1)
    created_at: Optional[str] = Field(None, example="2024-01-01T00:00:00Z")
    updated_at: Optional[str] = Field(None, example="2024-01-02T00:00:00Z")
    is_deleted: Optional[bool] = Field(False, example=False)


from src.utils.schemas import GetDataResponse
ObjectDetailResponse = GetDataResponse[DetailObject]

# 5. Schema for filter/query params
class FilterObject(BaseModel):
    page: int = Field(1, ge=1, example=1)
    size: int = Field(20, ge=1, le=100, example=20)
    search: Optional[str] = Field(None, example="keyword")
    # Add more filter fields as needed for each Object
    is_active: Optional[bool] = Field(None, example=True)
    ...
    ...

# 6. List response schema
from src.utils.schemas import PaginatedResponse
ObjectPaginatedResponse = PaginatedResponse[DetailObject]
```

### 2. Usage Instructions

- **FilterObject**: Dùng với `Depends()` trong router để parse query params, truyền vào service để filter linh hoạt.
- **CreateObject/UpdateObject**: Dùng cho input của API tạo/sửa.
- **DetailObject**: Dùng cho response của API detail, create, update.
- **ObjectPaginatedResponse**: Dùng cho response của API list.
- **ObjectDetailResponse**: Dùng cho response_model của api detail, create, update

#### Example in FastAPI router:
```python
@router.get("/", response_model=ObjectPaginatedResponse)
def list_objects(filters: FilterObject = Depends()):
    result = list_object_service(filters)
    return CustomResponse(content=result).format_data_get()

@router.get("/{object_id}", response_model=ObjectDetailResponse)
def get_object_detail(object_id: int):
    result = get_object_detail_service(object_id)
    return CustomResponse(content=result).format_data_get()

@router.post("/", response_model=DetailObject)
def create_object(data: CreateObject):
    return CustomResponse(content=create_object_service(data)).format_data_create()

@router.put("/{object_id}", response_model=DetailObject)
def update_object(object_id: int, data: UpdateObject):
    result = update_object_service(object_id, data)
    return CustomResponse(content=result).format_data_update()

@router.delete("/{object_id}", response_model=DetailObject)
def delete_object(object_id: int):
    result = delete_object_service(object_id)
    return CustomResponse(content=result).format_data_delete()

### 3. Naming Convention
- Tên schema: `[Action][Object]` (CreateUser, UpdateUser, DetailUser, UserFilter, UserListResponse, ...)
- Tên response: `[Object]PaginatedResponse` cho list, `[Object]Detail` cho detail.


7. Code Evolution Quick Guide
	•	Refactor legacy logic into modular folders
	•	Avoid growing router.py > 100 LOC
	•	Always validate API shape with OpenAPI
	•	Use mock DB session or override Depends() in test setup

⸻
