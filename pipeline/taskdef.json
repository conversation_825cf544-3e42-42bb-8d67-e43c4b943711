{"executionRoleArn": "__EXECUTION_ROLE_ARN__", "containerDefinitions": [{"name": "__TASK_DEFINITION_FAMILY__", "image": "__AWS_ACCOUNT_ID__.dkr.ecr.__AWS_REGION__.amazonaws.com/__REPOSITORY_NAME__:__REPOSITORY_VERSION__", "essential": true, "portMappings": [{"hostPort": 80, "protocol": "tcp", "containerPort": 80}], "logConfiguration": {"logDriver": "awslogs", "options": {"awslogs-group": "__LOGS_GROUP__", "awslogs-region": "__AWS_REGION__", "awslogs-stream-prefix": "ecs"}}, "command": ["u<PERSON><PERSON>", "src.main:app", "--host", "-b", ":80"], "environmentFiles": [{"type": "s3", "value": "__ENV_ARN__"}]}], "requiresCompatibilities": ["FARGATE"], "networkMode": "awsvpc", "cpu": "512", "memory": "1024", "family": "__TASK_DEFINITION_FAMILY__"}