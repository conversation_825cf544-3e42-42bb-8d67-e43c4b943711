---
description: 
globs: 
alwaysApply: false
---
Cảm ơn bạn đã cung cấp cấu trúc module rõ ràng. Dưới đây là hướng dẫn đầy đủ bạn có thể đặt vào project để đảm bảo mọi Prompt sinh ra từ Cursor tuân theo đúng convention backend bạn đang dùng:

⸻

📘 cursor_prompt_instruction.md

# 🎯 Cursor Prompt Instruction – AIOS Backend Convention (FastAPI)

## Mục tiêu
Đảm bảo mọi mã nguồn sinh ra bởi Cursor phải:
- <PERSON>ân thủ kiến trúc chia theo module
- Đặt tên hàm backend đúng format `_service`
- <PERSON><PERSON> chia chuẩn: schema – service – router

---

## 📁 C<PERSON>u trúc thư mục chuẩn

/src
└── user/
├── router.py      # FastAPI endpoints
├── service.py     # Business logic functions (_service)
├── models.py      # SQLAlchemy models
└── schemas.py     # Pydantic models

---

## 📌 Đặt tên hàm service

**Pattern:** `[verb]_[entity]_[context]_service`

**Ví dụ:**

| Use Case                                      | Hàm Service                          |
|-----------------------------------------------|--------------------------------------|
| Cập nhật avatar lần đầu đăng nhập             | `update_avatar_firstlogin_service`   |
| Gửi OTP theo loại                             | `send_otp_service`                   |
| Lấy thông tin user từ mã QR                   | `get_user_profile_code_service`      |
| Đổi mật khẩu khi quên                         | `change_user_password_service`       |
| Cập nhật trạng thái và message người dùng     | `update_availability_service`        |

---

## 🧱 Các nguyên tắc kỹ thuật

1. **Schema**:
   - Nằm trong `schemas.py`
   - Dùng `Field(..., example=...)` để tăng khả năng gen OpenAPI tự động

2. **Service**:
   - Logic xử lý chính nằm ở `service.py`
   - Mọi tên hàm đều kết thúc bằng `_service`
   - Không raise lỗi trực tiếp trong router – dùng `CustomException` trong service

3. **Router**:
   - Mỗi route có thể gọi nhiều hàm service tương ứng
   - Không xử lý logic, chỉ parse input & return response

---

## 💬 Prompt chuẩn để sinh code

Generate a FastAPI module that follows the current AIOS architecture:
	•	Pydantic schema → schemas.py
	•	Business logic → service.py (suffix _service)
	•	API handler → router.py (call corresponding service)

Use naming convention: [verb][entity][context]_service
Return 3 blocks for: schema, service, and router.

---

## 🧪 Checklist review đầu ra

| Hạng mục                         | Yêu cầu     |
|----------------------------------|-------------|
| 📌 Tên hàm kết thúc bằng `_service` | ✅ Phải có |
| 📁 Tách riêng schema/service/router | ✅ Bắt buộc |
| ⛔ Không xử lý logic trong router   | ✅ Cấm      |
| 📊 Schema có example & type rõ ràng| ✅ Nên dùng |

---

## 🧠 Gợi ý nâng cao

- Dùng thêm `model_validator`, `field_validator` nếu cần xử lý trong schema
- Tạo file `utils/response.py` để chuẩn hóa response nếu chưa có
- Sử dụng `Depends(get_db)` và `Depends(is_authenticated)` chuẩn từ template router

---
