from pydantic import Field, BaseModel
from typing import Union, Optional, List
from datetime import datetime
from src.utils.schemas import PaginatedResponse

class FCMTokenCreate(BaseModel):
    fcm_token: str
    device_id: str

class TopicBase(BaseModel):
    name: str = Field(
        title="This is a topic of notifications"
    )
    description: Union[str, None] = Field(
        default=None,
        title="This is a topic of notifications"
    )

class Topic(TopicBase):
    class Config:
        from_attributes = True

class TopicCreateUpdate(TopicBase):
    class Config:
        from_attributes = True

class NotificationCreate(BaseModel):
    title: str
    content: str

class NotificationBase(BaseModel):
    title: str = Field(
        title="This is a title of notifications"
    )
    content: str = Field(
        title="This is a content of notifications"
    )
    is_read: bool = Field(
        default=False,
        title="This is read status of notification"
    )

class NotificationActionBase(BaseModel):
    module: str = Field(..., example="messages")
    trigger_event: str = Field(..., example="new_chat_thread")
    is_merchant_only: bool = Field(False, example=False)
    is_system_forced: bool = Field(False, example=False)
    description: Optional[str] = Field(None, example="Receive notification when a new chat thread is opened")
    default_push: bool = Field(True, example=True)
    default_email: bool = Field(True, example=True)

    model_config = {
        "json_schema_extra": {
            "examples": [
                {
                    "module": "messages",
                    "trigger_event": "new_chat_thread",
                    "is_merchant_only": False,
                    "is_system_forced": False,
                    "description": "Receive notification when a new chat thread is opened",
                    "default_push": True,
                    "default_email": True
                }
            ]
        }
    }

class NotificationActionCreate(NotificationActionBase):
    pass

class NotificationActionUpdate(NotificationActionBase):
    pass

class NotificationAction(NotificationActionBase):
    id: int
    class Config:
        orm_mode = True


NotificationActionPaginatedResponse = PaginatedResponse[NotificationAction]

class NotificationConfigBase(BaseModel):
    push_enabled: bool = Field(True, example=True)
    email_enabled: bool = Field(True, example=True)

class NotificationConfigCreate(NotificationConfigBase):
    pass

class NotificationConfigUpdate(NotificationConfigBase):
    pass

class NotificationConfig(NotificationConfigBase):
    id: int
    action: NotificationAction
    class Config:
        from_attributes=True

NotificationConfigPaginatedResponse = PaginatedResponse[NotificationConfig]

class NotificationItem(BaseModel):
    id: int = Field(..., example=1)
    title: dict = Field(..., example={"en": "Order updated", "vi": "Đơn hàng đã được cập nhật"})
    content: dict = Field(..., example={"en": "Your order #123 has been shipped.", "vi": "Đơn hàng #123 của bạn đã được giao."})
    is_read: bool = Field(..., example=False)
    class Config:
        orm_mode = True

NotificationPaginatedResponse = PaginatedResponse[NotificationItem]
class NotificationDetailResponse(NotificationItem):
    pass

class NotificationCountResponse(BaseModel):
    count: int = Field(..., example=3)

class NotificationListQueryParams(BaseModel):
    page: int = Field(1, ge=1, example=1)
    size: int = Field(20, ge=1, le=100, example=20)

class NotificationConfigListQueryParams(BaseModel):
    page: int = Field(1, ge=1, example=1)
    size: int = Field(20, ge=1, le=100, example=20)
    # Filters for NotificationAction fields
    search: Optional[str] = Field(None, example="order")
    module: Optional[str] = Field(None, example="orders")
    trigger_event: Optional[str] = Field(None, example="order_status_updated_buyer")
    is_merchant_only: Optional[bool] = Field(None, example=False)
    is_system_forced: Optional[bool] = Field(None, example=False)
    default_push: Optional[bool] = Field(None, example=True)
    default_email: Optional[bool] = Field(None, example=True)

class NotificationCountQueryParams(BaseModel):
    is_read: Optional[bool] = Field(None, example=False)