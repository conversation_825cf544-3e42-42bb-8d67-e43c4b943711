from operator import or_
from firebase_admin import messaging
from typing import List
from src.utils.exceptions import CustomException
from src.notification_history.models import Topic, NotificationAction, NotificationConfig
from src.notification_history.schemas import NotificationActionPaginatedResponse, NotificationConfigListQueryParams, NotificationCountQueryParams, NotificationDetailResponse, NotificationListQueryParams, Topic, TopicCreateUpdate, FCMTokenCreate, NotificationCreate
from src.utils.common import query_list
from sqlalchemy.orm import Session
from src.notification_history.models import Notification, FCMToken, UserTopic
from src.utils.enum import UserRoleEnum
from sqlalchemy.exc import IntegrityError
from src.notification_history.enum import NotificationActionSeed
from src.notification_history.schemas import NotificationConfigPaginatedResponse, NotificationConfig as NotificationConfigSchemas

def send_notifications(employee_tokens: List[str], title: str, body: str):
    """
    <PERSON><PERSON><PERSON> thông b<PERSON>o qua Firebase Cloud Messaging.
    """
    if not employee_tokens:
        print("No tokens available for sending notifications.")
        return None

    message = messaging.MulticastMessage(
        notification=messaging.Notification(
            title=title,
            body=body,
        ),
        tokens=employee_tokens,
    )

    response = messaging.send_multicast(message)
    print(
        f'Successfully sent message: {response.success_count} messages were sent successfully')
    return response

def get_list_topics(db: Session):
    query = db.query(Topic).filter(Topic.is_deleted == False)
    return query.all()


def get_topic_by_id(db: Session, topic_id: int, return_schema: bool = False):
    topic = db.query(Topic).filter(Topic.id == topic_id).first()
    if not topic:
        raise CustomException().not_found_exception()
    return Topic.model_validate(topic) if return_schema else topic


def create_topic(db: Session, data: TopicCreateUpdate):
    topic = Topic(**data.model_dump())
    db.add(topic)
    db.commit()
    db.refresh(topic)
    return Topic.model_validate(topic)


def update_topic(db: Session, data: TopicCreateUpdate, topic: Topic):
    for key, value in data.model_dump().items():
        setattr(topic, key, value)
    db.add(topic)
    db.commit()
    db.refresh(topic)
    return Topic.model_validate(topic)

def mark_notification_as_read(db: Session, notification: Notification):
    setattr(notification, 'is_read', True)
    db.add(notification)
    db.commit()
    db.refresh(notification)
    return NotificationDetailResponse.model_validate(notification, from_attributes=True)


def mark_all_notifications_as_read(db: Session, notifications: List[Notification]):
    for notification in notifications:
        setattr(notification, 'is_read', True)
        db.add(notification)
    db.commit()
    db.refresh()
    return None

def subscribe_to_user_topic(db: Session, user_id: int, fcm_token: str):
    topic_name = "User"

    # Kiểm tra và tạo topic nếu chưa tồn tại
    topic = db.query(Topic).filter_by(name=topic_name).first()
    if not topic:
        topic = Topic(name=topic_name,
                      description=f"Notifications for all employees")
        db.add(topic)
        db.commit()
        db.refresh(topic)

    # Thêm vào bảng EmployeeTopic
    subscription = UserTopic(topic_id=topic.id, user_id=user_id)
    db.add(subscription)
    db.commit()

    # Đăng ký FCM token vào topic
    messaging.subscribe_to_topic([fcm_token], topic_name)
    print(f"Subscribed {fcm_token} to topic {topic_name}")


def create_fcm_token(db: Session, create_data: FCMTokenCreate, user_id: int):

    existing_fcm_token = db.query(FCMToken).filter_by(
        user_id=user_id, fcm_token=create_data.fcm_token).first()

    if existing_fcm_token:
        return existing_fcm_token

    new_fcm_token = FCMToken(
        user_id=user_id,
        fcm_token=create_data.fcm_token,
        device_id=create_data.device_id,
    )
    db.add(new_fcm_token)
    db.commit()
    db.refresh(new_fcm_token)

    try:
        subscribe_to_user_topic(db, user_id, create_data.fcm_token)
    except Exception as e:
        print(f"Error subscribing to topic: {e}")

    return new_fcm_token



# --- NotificationAction CRUD (admin) ---
def create_notification_action_service(db: Session, data):
    action = NotificationAction(**data.model_dump())
    db.add(action)
    db.commit()
    db.refresh(action)
    return action

def get_notification_action_service(db: Session, action_id: int):
    action = db.query(NotificationAction).filter(NotificationAction.id == action_id).first()
    if not action:
        raise CustomException(content={"error": "NotificationAction not found"}).not_found_exception()
    return action

def list_notification_action_service(db: Session, filters: NotificationConfigListQueryParams):
    query = db.query(NotificationAction)

    # Search filter
    if filters.search:
        search = f"%{filters.search}%"
        query = query.filter(
            or_(
                NotificationAction.module.ilike(search),
                NotificationAction.trigger_event.ilike(search),
                NotificationAction.description.ilike(search)
            )
        )

    # Field filters
    if filters.module:
        query = query.filter(NotificationAction.module == filters.module)
    if filters.trigger_event:
        query = query.filter(NotificationAction.trigger_event == filters.trigger_event)
    if filters.is_merchant_only is not None:
        query = query.filter(NotificationAction.is_merchant_only == filters.is_merchant_only)
    if filters.is_system_forced is not None:
        query = query.filter(NotificationAction.is_system_forced == filters.is_system_forced)
    if filters.default_push is not None:
        query = query.filter(NotificationAction.default_push == filters.default_push)
    if filters.default_email is not None:
        query = query.filter(NotificationAction.default_email == filters.default_email)

    # Pagination
    total = query.count()
    page = filters.page or 1
    size = filters.size or 20
    items = query.offset((page - 1) * size).limit(size).all()
    pages = (total + size - 1) // size if size else 1

    from src.notification_history.schemas import NotificationAction as NotificationActionSchema
    return NotificationActionPaginatedResponse(
        items=[NotificationActionSchema.model_validate(item, from_attributes=True) for item in items],
        total=total,
        page=page,
        size=size,
        total_pages=pages
    )

def update_notification_action_service(db: Session, action_id: int, data):
    action = get_notification_action_service(db, action_id)
    for key, value in data.model_dump().items():
        setattr(action, key, value)
    db.commit()
    db.refresh(action)
    return action

def delete_notification_action_service(db: Session, action_id: int):
    action = get_notification_action_service(db, action_id)
    db.delete(action)
    db.commit()
    return True

# --- NotificationConfig CRUD (user) ---
def get_user_notification_config_service(db: Session, user, filters: NotificationConfigListQueryParams):
    """
    Returns NotificationConfigPaginatedResponse for the current user,
    auto-creating missing configs for all available actions.
    """

    # Determine user type
    is_merchant = (user.user_type == UserRoleEnum.MERCHANT)

    # Build base query for actions with filters
    actions_query = db.query(NotificationAction)
    
    # Apply user type filter
    if not is_merchant:
        actions_query = actions_query.filter(NotificationAction.is_merchant_only == False)
    
    # Apply search filter
    if filters.search:
        search = f"%{filters.search}%"
        actions_query = actions_query.filter(
            or_(
                NotificationAction.module.ilike(search),
                NotificationAction.trigger_event.ilike(search),
                NotificationAction.description.ilike(search)
            )
        )
    
    # Apply specific filters
    if filters.module:
        actions_query = actions_query.filter(NotificationAction.module == filters.module)
    if filters.trigger_event:
        actions_query = actions_query.filter(NotificationAction.trigger_event == filters.trigger_event)
    if filters.is_merchant_only is not None:
        actions_query = actions_query.filter(NotificationAction.is_merchant_only == filters.is_merchant_only)
    if filters.is_system_forced is not None:
        actions_query = actions_query.filter(NotificationAction.is_system_forced == filters.is_system_forced)
    if filters.default_push is not None:
        actions_query = actions_query.filter(NotificationAction.default_push == filters.default_push)
    if filters.default_email is not None:
        actions_query = actions_query.filter(NotificationAction.default_email == filters.default_email)
    
    actions = actions_query.all()
    action_map = {a.id: a for a in actions}
    action_ids = set(action_map.keys())

    # Get all configs for user
    configs_query = db.query(NotificationConfig).filter(NotificationConfig.user_id == user.id)
    configs = configs_query.all()
    config_action_ids = {c.action_id for c in configs}

    # Auto-create missing configs in bulk
    missing_action_ids = action_ids - config_action_ids
    if missing_action_ids:
        new_configs = [
            NotificationConfig(
                user_id=user.id,
                action_id=aid,
                push_enabled=action_map[aid].default_push,
                email_enabled=action_map[aid].default_email
            )
            for aid in missing_action_ids
        ]
        db.bulk_save_objects(new_configs)
        try:
            db.commit()
        except IntegrityError:
            db.rollback()
        # Only reload if we actually inserted new configs
        configs = configs_query.all()

    # Apply pagination
    total = len(configs)
    total_pages = (total + filters.size - 1) // filters.size
    skip = (filters.page - 1) * filters.size
    
    # Apply pagination to configs
    paginated_configs = configs[skip:skip + filters.size]

    # Convert to schema
    items = [NotificationConfigSchemas.model_validate(config, from_attributes=True) for config in paginated_configs]

    return NotificationConfigPaginatedResponse(
        items=items, 
        total=total, 
        page=filters.page, 
        size=filters.size, 
        total_pages=total_pages
    )

def update_user_notification_config_service(db: Session, user, config_id: int, data):
    config = db.query(NotificationConfig).filter(NotificationConfig.id == config_id, NotificationConfig.user_id == user.id).first()
    if not config:
        raise CustomException(content={"error": "NotificationConfig not found"}).not_found_exception()
    # Không cho phép tắt system-forced
    if config.action.is_system_forced:
        raise CustomException(content={"error": "This notification cannot be disabled."}).bad_request_exception()
    for key, value in data.model_dump().items():
        setattr(config, key, value)
    db.commit()
    db.refresh(config)
    return NotificationConfigSchemas.model_validate(config, from_attributes=True)

def get_notification_config_by_id_service(db: Session, user, config_id: int):
    config = db.query(NotificationConfig).filter(NotificationConfig.id == config_id, NotificationConfig.user_id == user.id).first()
    if not config:
        raise CustomException(content={"error": "NotificationConfig not found"}).not_found_exception()
    return NotificationConfigSchemas.model_validate(config, from_attributes=True)

def get_list_notifications(db: Session, user_id: int, filters: NotificationListQueryParams):
    """
    Retrieve a paginated list of notifications for a user, returning schema objects.

    Args:
        db (Session): SQLAlchemy database session.
        user_id (int): ID of the user.
        filters (NotificationListQueryParams): Pagination and filter parameters.

    Returns:
        NotificationPaginatedResponse: Pydantic schema with items and total count.
    """
    from src.notification_history.schemas import NotificationItem, NotificationPaginatedResponse

    page = filters.page if hasattr(filters, "page") else 1
    size = filters.size if hasattr(filters, "size") else 20
    skip = (page - 1) * size

    query = db.query(Notification).filter(Notification.user_id == user_id)
    total = query.count()
    notifications = (
        query.order_by(Notification.id.desc())
        .offset(skip)
        .limit(size)
        .all()
    )
    items = [NotificationItem.model_validate(notification, from_attributes=True) for notification in notifications]
    total_pages = (total + size - 1) // size if size else 1
    return NotificationPaginatedResponse(
        items=items,
        total=total,
        page=page,
        size=size,
        total_pages=total_pages
    )

def get_notification_by_id(db: Session, notification_id: int, user_id: int, return_schema: bool = False):
    notification = db.query(Notification).filter(Notification.id == notification_id, Notification.user_id == user_id).first()
    if not notification:
        raise CustomException(content={"error": "Notification not found"}).not_found_exception()
    if return_schema:
        from src.notification_history.schemas import NotificationDetailResponse
        return NotificationDetailResponse.model_validate(notification, from_attributes=True)
    return notification

def get_count_notifications(db: Session, user_id: int, filters: NotificationCountQueryParams):
    """
    Returns the count of notifications for a user, optionally filtered by read status.

    Args:
        db (Session): SQLAlchemy database session.
        user_id (int): ID of the user.
        filters (NotificationCountQueryParams): Filter parameters (e.g., is_read).

    Returns:
        int: Number of notifications matching the criteria.
    """
    query = db.query(Notification).filter(Notification.user_id == user_id)
    if hasattr(filters, "is_read") and filters.is_read is not None:
        query = query.filter(Notification.is_read == filters.is_read)
    return query.count()

def seed_notification_actions_service(db: Session):
    """
    Seed NotificationAction table with predefined actions if not already present.
    """
    created, skipped = 0, 0
    for item in NotificationActionSeed.data:
        exists = db.query(NotificationAction).filter_by(
            module=item["module"],
            trigger_event=item["trigger_event"]
        ).first()
        if not exists:
            action = NotificationAction(**item)
            db.add(action)
            created += 1
        else:
            skipped += 1
    db.commit()
    return {"created": created, "skipped": skipped}