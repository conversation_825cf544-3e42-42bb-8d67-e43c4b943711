from sqlalchemy import Column, Date, DateTime, Enum, Float, Foreign<PERSON>ey, Integer, String, Text, Boolean, JSON, UniqueConstraint
from src.utils.base_model import BaseModel, Base
from sqlalchemy.orm import relationship
from datetime import datetime

class SMSHistory(Base, BaseModel):
    __tablename__ = "sms_history"
    payload_data = Column(JSON, nullable=True, default=None)
    phone_number = Column(String(256), nullable = True)
    message = Column(Text, nullable = True)


class FCMToken(BaseModel, Base):
    __tablename__ = "fcm_tokens"

    device_id = Column(String(255), index=True)
    fcm_token = Column(String(255), index=True)
    user_id = Column(Integer, ForeignKey("user.id"))

    user = relationship("User", back_populates="fcm_tokens")


class Notification(BaseModel, Base):
    __tablename__ = "notifications"

    title = Column(JSON, nullable=True, default=None)
    content = Column(JSON, nullable=True, default=None)
    user_id = Column(Integer, ForeignKey("user.id"))
    is_read = Column(Boolean, default=False)

    user = relationship("User", back_populates="notifications")

class Topic(BaseModel, Base):
    __tablename__ = "topic"

    name = Column(String(255), index=True)
    description = Column(String(255), nullable=True)
    # message_id = Column(Integer, ForeignKey("message.id"))

    user_topics = relationship("UserTopic", back_populates="topic")

class UserTopic(BaseModel, Base):
    __tablename__ = "user_topic"

    id = Column(Integer, primary_key=True, index=True)
    topic_id = Column(Integer, ForeignKey('topic.id'), nullable=False)
    user_id = Column(Integer, ForeignKey('user.id'), nullable=False)

    # Liên kết với bảng Topic và Employee
    topic = relationship("Topic", back_populates="user_topics")
    user = relationship("User", back_populates="user_topics")

class NotificationAction(BaseModel, Base):
    __tablename__ = "notification_action"

    module = Column(String(32), nullable=False)  # e.g. "messages"
    trigger_event = Column(String(64), nullable=False)  # e.g. "new_chat_thread"
    is_merchant_only = Column(Boolean, default=False)
    is_system_forced = Column(Boolean, default=False)# Use for System-level alerts (security/account login) are always active.
    description = Column(Text, nullable=True)  # For UI subtext
    default_push = Column(Boolean, default=True)
    default_email = Column(Boolean, default=True)

class NotificationConfig(BaseModel, Base):
    __tablename__ = "notification_config"

    user_id = Column(Integer, ForeignKey("user.id"), nullable=False)
    action_id = Column(Integer, ForeignKey("notification_action.id"), nullable=False)
    push_enabled = Column(Boolean, nullable=False, default=True)
    email_enabled = Column(Boolean, nullable=False, default=True)

    action = relationship("NotificationAction")