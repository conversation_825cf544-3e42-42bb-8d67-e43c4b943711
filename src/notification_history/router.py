from fastapi import BackgroundTasks, File, Form, Request, UploadFile, APIRouter, Depends, HTTPException, Response, Security
from src.utils.response import CustomResponse
from src.utils.response import *
from src.notification_history import schemas
from src.notification_history import service
from src.utils.database import get_db
from sqlalchemy.orm import Session
from src.user.models import User as UserModel
from src.utils.permission import is_authenticated, is_admin
from src.utils.token_docs import jwt_auth
from src.utils.common import get_user_token
from src.notification_history.schemas import (
    NotificationActionCreate, NotificationActionPaginatedResponse, NotificationActionUpdate, NotificationAction,
    NotificationConfig, NotificationConfigListQueryParams, NotificationConfigUpdate, NotificationConfigPaginatedResponse, NotificationCountQueryParams, NotificationListQueryParams, NotificationPaginatedResponse
)
from src.utils.schemas import GetDataResponse

notification_router = APIRouter()
topic_router = APIRouter()

fcm_token_tags = ['FCM Token']
notification_tags = ['Notification']
topic_tags = ['Topic']

@notification_router.post("/fcmtoken/", status_code=201, tags=fcm_token_tags)
async def register_fcm_token(
    request: Request,
    payload: schemas.FCMTokenCreate,
    db: Session = Depends(get_db)
):

    token_db = get_user_token(request, db)
    user_id = token_db.user_id

    return CustomResponse(content=service.create_fcm_token(db, payload, user_id)).format_data_create()


@notification_router.get("", tags=notification_tags, response_model=NotificationPaginatedResponse, dependencies=[Depends(jwt_auth)])
async def get_list(
    current_user: UserModel = Depends(is_authenticated),
    db: Session = Depends(get_db),
    filters: NotificationListQueryParams = Depends()
):
    list_notifications = service.get_list_notifications(db=db, user_id=current_user.id, filters=filters)
    return CustomResponse(content=list_notifications).format_data_get()


@notification_router.get("/{id}", tags=notification_tags, **get_data_response, dependencies=[Depends(jwt_auth)])
async def get_detail_by_id(
    id: int,
    current_user: UserModel = Depends(is_authenticated),
    db: Session = Depends(get_db)
):
    return CustomResponse(content=service.get_notification_by_id(db, id, current_user.id, True)).format_data_get()


@notification_router.put("/notifications/{id}", tags=notification_tags, **get_data_response, dependencies=[Depends(jwt_auth)])
async def update(
    id: int,
    current_user: UserModel = Depends(is_authenticated),
    db: Session = Depends(get_db),
):
    notification = service.get_notification_by_id(db, id, current_user.id)
    return CustomResponse(content=service.mark_notification_as_read(db, notification)).format_data_update()


@notification_router.get("/count/", tags=notification_tags, response_model=GetDataResponse, dependencies=[Depends(jwt_auth)])
async def get_notifications_count(
    current_user: UserModel = Depends(is_authenticated),
    db: Session = Depends(get_db),
    filters: NotificationCountQueryParams = Depends()
):
    return CustomResponse(content={'count': service.get_count_notifications(db, current_user.id, filters)}).format_data_get()


@topic_router.get("/topics", tags=topic_tags,  **get_data_response, dependencies=[Depends(jwt_auth)])
async def get_list(
    current_user: UserModel = Depends(is_authenticated),
    db: Session = Depends(get_db),
):
    list_topics = service.get_list_topics(
        db=db)
    return CustomResponse(content=list_topics).format_data_get()


@topic_router.post("/topics", tags=topic_tags, **get_data_response, dependencies=[Depends(jwt_auth)])
async def create(
    data: schemas.TopicCreateUpdate,
    current_user: UserModel = Depends(is_authenticated),
    db: Session = Depends(get_db),
):
    return CustomResponse(content=service.create_topic(db, data)).format_data_create()


@topic_router.get("/topics/{id}", tags=topic_tags, **get_data_response, dependencies=[Depends(jwt_auth)])
async def get_detail_by_id(
    id: int,
    current_user: UserModel = Depends(is_authenticated),
    db: Session = Depends(get_db)
):
    return CustomResponse(content=service.get_topic_by_id(db, id, True)).format_data_get()


@topic_router.put("/topics/{id}", tags=topic_tags, **get_data_response, dependencies=[Depends(jwt_auth)])
async def update(
    id: int,
    data: schemas.TopicCreateUpdate,
    current_user: UserModel = Depends(is_authenticated),
    db: Session = Depends(get_db),
):
    topic = service.get_topic_by_id(db, id)
    return CustomResponse(content=service.update_topic(db, data, topic)).format_data_update()


@topic_router.patch("/topics/{id}", tags=topic_tags, **get_data_response, dependencies=[Depends(jwt_auth)])
async def restore(
    id: int,
    current_user: UserModel = Depends(is_authenticated),
    db: Session = Depends(get_db),
):
    topic = service.get_topic_by_id(db, id)
    return CustomResponse(content=service.delete_or_restore_topic(db, topic, False)).format_data_update()


@topic_router.delete("/topics/{id}", tags=topic_tags, **get_data_response, dependencies=[Depends(jwt_auth)])
async def delete(
    id: int,
    current_user: UserModel = Depends(is_authenticated),
    db: Session = Depends(get_db),
):
    topic = service.get_topic_by_id(db, id)
    return CustomResponse(content=service.delete_or_restore_topic(db, topic)).format_data_delete()


# --- NotificationAction CRUD (admin) ---
@notification_router.post("/admin/notification-actions/", response_model=NotificationAction, tags=["NotificationAction"], dependencies=[Depends(jwt_auth)])
async def create_notification_action(
    data: NotificationActionCreate,
    current_user: UserModel = Depends(is_admin),
    db: Session = Depends(get_db),
):
    action = service.create_notification_action_service(db, data)
    return CustomResponse(content=action).format_data_create()

@notification_router.get("/admin/notification-actions/", response_model=NotificationActionPaginatedResponse, tags=["NotificationAction"], dependencies=[Depends(jwt_auth)])
async def list_notification_action(
    current_user: UserModel = Depends(is_admin),
    db: Session = Depends(get_db),
    filters: NotificationConfigListQueryParams = Depends()
):
    actions = service.list_notification_action_service(db, filters)
    return CustomResponse(content=actions).format_data_get()

@notification_router.get("/admin/notification-actions/{action_id}", response_model=NotificationAction, tags=["NotificationAction"], dependencies=[Depends(jwt_auth)])
async def get_notification_action(
    action_id: int,
    current_user: UserModel = Depends(is_admin),
    db: Session = Depends(get_db),
):
    action = service.get_notification_action_service(db, action_id)
    return CustomResponse(content=action).format_data_get()

@notification_router.put("/admin/notification-actions/{action_id}", response_model=NotificationAction, tags=["NotificationAction"], dependencies=[Depends(jwt_auth)])
async def update_notification_action(
    action_id: int,
    data: NotificationActionUpdate,
    current_user: UserModel = Depends(is_admin),
    db: Session = Depends(get_db),
):
    action = service.update_notification_action_service(db, action_id, data)
    return CustomResponse(content=action).format_data_update()

@notification_router.delete("/admin/notification-actions/{action_id}", tags=["NotificationAction"], dependencies=[Depends(jwt_auth)])
async def delete_notification_action(
    action_id: int,
    current_user: UserModel = Depends(is_admin),
    db: Session = Depends(get_db),
):
    service.delete_notification_action_service(db, action_id)
    return CustomResponse().format_data_delete()

@notification_router.post("/admin/notification-actions/seed", tags=["NotificationAction"], dependencies=[Depends(jwt_auth)])
async def seed_notification_actions(
    current_user: UserModel = Depends(is_admin),
    db: Session = Depends(get_db),
):
    result = service.seed_notification_actions_service(db)
    return CustomResponse(content=result).format_data_create()

# --- NotificationConfig CRUD (user) ---
@notification_router.get("/notification-configs/", response_model=NotificationConfigPaginatedResponse, tags=["NotificationConfig"], dependencies=[Depends(jwt_auth)])
async def list_notification_config(
    current_user: UserModel = Depends(is_authenticated),
    db: Session = Depends(get_db),
    filters: NotificationConfigListQueryParams = Depends()
):
    result = service.get_user_notification_config_service(db, current_user, filters)
    return CustomResponse(content=result).format_data_get()

@notification_router.get("/notification-configs/{config_id}", response_model=NotificationConfig, tags=["NotificationConfig"], dependencies=[Depends(jwt_auth)])
async def get_notification_config(
    config_id: int,
    current_user: UserModel = Depends(is_authenticated),
    db: Session = Depends(get_db),
):
    config = service.get_notification_config_by_id_service(db, current_user, config_id)
    return CustomResponse(content=config).format_data_get()

@notification_router.put("/notification-configs/{config_id}", response_model=NotificationConfig, tags=["NotificationConfig"], dependencies=[Depends(jwt_auth)])
async def update_notification_config(
    config_id: int,
    data: NotificationConfigUpdate,
    current_user: UserModel = Depends(is_authenticated),
    db: Session = Depends(get_db),
):
    config = service.update_user_notification_config_service(db, current_user, config_id, data)
    return CustomResponse(content=config).format_data_update()


