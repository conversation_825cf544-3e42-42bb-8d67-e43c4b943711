from typing import List, Dict

class NotificationActionSeed:
    """
    Static seeding data for NotificationAction. Each item is a dict matching NotificationAction fields.
    """
    data: List[Dict] = [
        # Shared: Users & Merchants
        {"module": "messages", "trigger_event": "new_chat_thread", "is_merchant_only": False, "is_system_forced": False, "description": "New chat thread opened", "default_push": True, "default_email": True},
        {"module": "gifts", "trigger_event": "gift_sent", "is_merchant_only": False, "is_system_forced": False, "description": "Gift sent", "default_push": True, "default_email": True},
        {"module": "gifts", "trigger_event": "gift_accepted_rejected", "is_merchant_only": False, "is_system_forced": False, "description": "Gift accepted/rejected", "default_push": True, "default_email": True},
        {"module": "gifts", "trigger_event": "gift_expired", "is_merchant_only": False, "is_system_forced": False, "description": "Gift expired (14 days unclaimed)", "default_push": True, "default_email": True},
        {"module": "orders", "trigger_event": "order_status_updated_buyer", "is_merchant_only": False, "is_system_forced": False, "description": "Order status updated (buyer's view)", "default_push": True, "default_email": True},
        {"module": "orders", "trigger_event": "order_canceled_buyer", "is_merchant_only": False, "is_system_forced": False, "description": "Order canceled (buyer's view)", "default_push": True, "default_email": True},
        {"module": "connections", "trigger_event": "new_connection_request", "is_merchant_only": False, "is_system_forced": False, "description": "New connection request received", "default_push": True, "default_email": True},
        {"module": "connections", "trigger_event": "connection_request_accepted", "is_merchant_only": False, "is_system_forced": False, "description": "Connection request accepted", "default_push": True, "default_email": True},
        {"module": "meetings", "trigger_event": "meeting_invite_received", "is_merchant_only": False, "is_system_forced": False, "description": "Meeting invite received", "default_push": True, "default_email": True},
        {"module": "meetings", "trigger_event": "invite_accepted_rejected", "is_merchant_only": False, "is_system_forced": False, "description": "Invite accepted or rejected", "default_push": True, "default_email": True},
        {"module": "meetings", "trigger_event": "meeting_updated_canceled", "is_merchant_only": False, "is_system_forced": False, "description": "Meeting updated or canceled", "default_push": True, "default_email": True},
        {"module": "system", "trigger_event": "feature_announcements", "is_merchant_only": False, "is_system_forced": False, "description": "Feature announcements", "default_push": True, "default_email": True},
        {"module": "system", "trigger_event": "account_security_alert", "is_merchant_only": False, "is_system_forced": True, "description": "Account or security alert (forced ON)", "default_push": True, "default_email": True},
        {"module": "account", "trigger_event": "login_new_device", "is_merchant_only": False, "is_system_forced": False, "description": "Login from new device/location", "default_push": True, "default_email": True},
        {"module": "subscription", "trigger_event": "renewal_success", "is_merchant_only": False, "is_system_forced": False, "description": "Renewal success", "default_push": True, "default_email": True},
        {"module": "subscription", "trigger_event": "subscription_canceled", "is_merchant_only": False, "is_system_forced": False, "description": "Subscription canceled", "default_push": True, "default_email": True},
        {"module": "subscription", "trigger_event": "renewal_reminder", "is_merchant_only": False, "is_system_forced": False, "description": "Renewal reminder (3 days before)", "default_push": True, "default_email": True},
        # Merchant-Specific Only
        {"module": "marketplace", "trigger_event": "product_purchased", "is_merchant_only": True, "is_system_forced": False, "description": "Product purchased", "default_push": True, "default_email": True},
        {"module": "marketplace", "trigger_event": "product_listing_removed", "is_merchant_only": True, "is_system_forced": False, "description": "Product listing removed by admin", "default_push": True, "default_email": True},
        {"module": "orders", "trigger_event": "new_order_received_seller", "is_merchant_only": True, "is_system_forced": False, "description": "New order received (seller view)", "default_push": True, "default_email": True},
        {"module": "orders", "trigger_event": "order_canceled_by_buyer", "is_merchant_only": True, "is_system_forced": False, "description": "Order canceled by buyer", "default_push": True, "default_email": True},
        {"module": "orders", "trigger_event": "order_status_changed_merchant", "is_merchant_only": True, "is_system_forced": False, "description": "Order status changed by merchant (confirmation log)", "default_push": True, "default_email": True},
        {"module": "storefront", "trigger_event": "buyer_left_review", "is_merchant_only": True, "is_system_forced": False, "description": "Buyer left a product review", "default_push": True, "default_email": True},
    ]

class English:
    """
    English translations for notification actions.
    """
    text = {
        "new_chat_thread": "New chat thread opened",
        "gift_sent": "Gift sent",
        "gift_accepted_rejected": "Gift accepted/rejected",
        "gift_expired": "Gift expired (14 days unclaimed)",
        "order_status_updated_buyer": "Order status updated (buyer's view)",
        "order_canceled_buyer": "Order canceled (buyer's view)",
        "new_connection_request": "New connection request received",
        "connection_request_accepted": "Connection request accepted",
        "meeting_invite_received": "Meeting invite received",
        "invite_accepted_rejected": "Invite accepted or rejected",
        "meeting_updated_canceled": "Meeting updated or canceled",
        "feature_announcements": "Feature announcements",
        "account_security_alert": "Account or security alert (forced ON)",
        "login_new_device": "Login from new device/location",
        "renewal_success": "Renewal success",
        "subscription_canceled": "Subscription canceled",
        "renewal_reminder": "Renewal reminder (3 days before)",
        "product_purchased": "Product purchased",
        "product_listing_removed": "Product listing removed by admin",
        "new_order_received_seller": "New order received (seller view)",
        "order_canceled_by_buyer": "Order canceled by buyer",
        "order_status_changed_merchant": "Order status changed by merchant (confirmation log)",
        "buyer_left_review": "Buyer left a product review",
    }

class VietNamese:
    """
    Vietnamese translations for notification actions.
    """
    text = {
        "new_chat_thread": "Đã mở đoạn chat mới",
        "gift_sent": "Đã gửi quà tặng",
        "gift_accepted_rejected": "Quà tặng đã được chấp nhận/từ chối",
        "gift_expired": "Quà tặng hết hạn (14 ngày không nhận)",
        "order_status_updated_buyer": "Cập nhật trạng thái đơn hàng (người mua)",
        "order_canceled_buyer": "Đơn hàng bị hủy (người mua)",
        "new_connection_request": "Nhận yêu cầu kết nối mới",
        "connection_request_accepted": "Yêu cầu kết nối đã được chấp nhận",
        "meeting_invite_received": "Nhận lời mời họp",
        "invite_accepted_rejected": "Lời mời đã được chấp nhận hoặc từ chối",
        "meeting_updated_canceled": "Cuộc họp đã được cập nhật hoặc hủy",
        "feature_announcements": "Thông báo tính năng mới",
        "account_security_alert": "Cảnh báo tài khoản hoặc bảo mật (bắt buộc)",
        "login_new_device": "Đăng nhập từ thiết bị/vị trí mới",
        "renewal_success": "Gia hạn thành công",
        "subscription_canceled": "Hủy đăng ký",
        "renewal_reminder": "Nhắc gia hạn (trước 3 ngày)",
        "product_purchased": "Sản phẩm đã được mua",
        "product_listing_removed": "Sản phẩm bị gỡ bởi admin",
        "new_order_received_seller": "Nhận đơn hàng mới (người bán)",
        "order_canceled_by_buyer": "Đơn hàng bị hủy bởi người mua",
        "order_status_changed_merchant": "Trạng thái đơn hàng thay đổi bởi người bán (log xác nhận)",
        "buyer_left_review": "Người mua đã để lại đánh giá sản phẩm",
    }
