from fastapi import APIRouter, Depends, Request, Security
from sqlalchemy.orm import Session
from src.utils.permission import check_security_scope, is_authenticated
from ..utils.response import *
from src.utils.token_docs import jwt_auth
from src.news.schemas import NewsBaseSchemas, NewsDetailSchemas
from src.utils.database import get_db
from src.user.models import User as UserModel
from src.news.service import create_news_service, get_list_news_service, get_detail_news_service
from src.utils.pagination import PaginationData, get_parameters_of_list

router = APIRouter()
tags = ['News']

@router.post("", tags = tags, **create_data_response, dependencies=[Depends(jwt_auth)])
def create_news(
    data: NewsBaseSchemas,
    db: Session = Depends(get_db),
    current_user: UserModel = Depends(is_authenticated)
):
    create_news_service(db, data)
    return CustomResponse().format_data_create()
    
@router.get("/list", tags = tags, response_model=PaginationData, **get_data_response, dependencies=[Depends(jwt_auth)])
def get_news(
    db: Session = Depends(get_db),
    current_user: UserModel = Depends(is_authenticated),
    params: dict = Depends(get_parameters_of_list)
):
    list_news, total_news = get_list_news_service(db, params, search_fields=['title'])
    return PaginationData.create_pagination(
        data=list_news,
        params=params,
        total_items=total_news,
    )

@router.get("/{id}", tags = tags, **get_data_response, dependencies=[Depends(jwt_auth)])
def get_details_news(
    id: int,
    db: Session = Depends(get_db),
    current_user: UserModel = Depends(is_authenticated),
):
    news = get_detail_news_service(db, id)
    data_res = NewsDetailSchemas.model_validate(news)
    return CustomResponse(content = data_res).format_data_get()
