import re
from typing import  List, Union
from pydantic import Field, EmailStr, field_validator, BaseModel
from datetime import datetime, timezone, date
from src.utils.exceptions import CustomException
from src.event.models import Event as EventModels
from sqlalchemy.orm import Session

class NewsBaseSchemas(BaseModel):
    title: str = Field(
        min_length = 5,
        max_length = 256,
        title = "Title of news"
    )
    short_description: Union[str, None] = Field(
        title = "Short description of News"
    )
    content: str = Field(
        title = "Content of news"
    )
    banner: str = Field(
        title = "Banner of news"
    )
    images: Union[List[str], None] = Field(
        default=None,
        title="The list images urls of news",
    )
    event_id: int = Field(
        title ="The event of news"
    )

    def model_validator(self, db: Session):
        errors = {}

        # Kiểm tra sự tồn tại của position_id
        if self.event_id:
            event_exists = db.query(EventModels).filter(
                (EventModels.id == self.event_id) & (EventModels.is_deleted == False)).first()
            if not event_exists:
                errors['event_id'] = ['Invalid Event ID']
        if errors.keys():
            raise CustomException(content=errors).bad_request_exception()
        
        return self

class ListNewsSchemas(NewsBaseSchemas):
    id: int = Field(
        title = "Id of news"
    )
    class Config:
        from_attributes = True

class NewsDetailSchemas(ListNewsSchemas):
    pass