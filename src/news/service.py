from typing import List
from sqlalchemy.orm import Session, joinedload
from sqlalchemy import desc
from src.utils.exceptions import CustomException
from src.news.schemas import NewsBaseSchemas, ListNewsSchemas, NewsDetailSchemas
from src.news.models import News as NewsModel
from datetime import datetime
from src.utils.common import query_list


def create_news_service(db: Session, data: NewsBaseSchemas):
    now = datetime.now()
    new_service = NewsModel(
        title = data.title,
        short_description = data.short_description,
        content = data.content,
        banner = data.banner,
        images = data.images,
        posted_date = now,
        event_id = data.event_id
    )
    db.add(new_service)
    try:
        db.commit()
    except Exception as e:
        db.rollback()
    finally:
        db.close()

def get_list_news_service(db: Session, params: dict, search_fields: List[str] = None):
    query = db.query(NewsModel).filter(NewsModel.is_deleted == False).order_by(desc(NewsModel.created_at))
    return query_list(query, NewsModel, ListNewsSchemas,params, search_fields)

def get_detail_news_service(db: Session, id: int):
    query = db.query(NewsModel).filter(NewsModel.id == id, NewsModel.is_deleted == False).first()
    if not query:
        raise CustomException().not_found_exception()
    return query