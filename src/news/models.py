from sqlalchemy import Column, Date, DateTime, Enum, Float, ForeignKey, Integer, String, Text, Boolean, JSON
from src.utils.base_model import BaseModel, Base
from sqlalchemy.orm import relationship

class News(Base, BaseModel):
    __tablename__ = "news"
    title = Column(String(256), nullable = False, index=True)
    short_description = Column(Text, nullable = True)
    content = Column(Text, nullable = True)
    banner = Column(Text, nullable = True)
    images = Column(JSON, nullable=True, default=None)
    posted_date = Column(DateTime, nullable = False, index = True)
    
