from sqlalchemy.orm import Session
from sqlalchemy import select
from src.utils.exceptions import CustomException
from src.product.models import Product
from .models import Cart, CartItem
from .schemas import CartItemCreate, PaginatedCartItemResponse, CheckoutRequest
from src.order.models import Order, OrderItem


def add_item_to_cart_service(db: Session, user_id: int, data: CartItemCreate) -> CartItem:
    try:
        cart = db.scalar(select(Cart).where(Cart.user_id == user_id))
        if not cart:
            cart = Cart(user_id=user_id)
            db.add(cart)
            db.flush()
        product = db.get(Product, data.product_id)
        if not product:
            raise CustomException().not_found_exception(message="Product not found")
        item = db.scalar(
            select(CartItem).where(
                CartItem.cart_id == cart.id,
                CartItem.product_id == data.product_id,
            )
        )
        price = (
            product.price * (1 + product.tax_rate / 100)
            if product.enable_tax and product.tax_rate is not None
            else product.price
        )
        if item:
            item.quantity += data.quantity
            item.price = price
        else:
            item = CartItem(
                cart_id=cart.id,
                product_id=data.product_id,
                quantity=data.quantity,
                price=price,
            )
            db.add(item)
        db.commit()
        db.refresh(item)
        return item
    except Exception:
        db.rollback()
        raise CustomException().bad_request_exception()


def update_cart_item_service(db: Session, cart_item_id: int, quantity: int) -> CartItem:
    try:
        item = db.get(CartItem, cart_item_id)
        if not item:
            raise CustomException().not_found_exception(message="Cart item not found")
        item.quantity = quantity
        product = item.product
        item.price = (
            product.price * (1 + product.tax_rate / 100)
            if product.enable_tax and product.tax_rate is not None
            else product.price
        )
        db.commit()
        db.refresh(item)
        return item
    except Exception:
        db.rollback()
        raise CustomException().bad_request_exception()


def remove_cart_item_service(db: Session, cart_item_id: int) -> bool:
    try:
        item = db.get(CartItem, cart_item_id)
        if not item:
            raise CustomException().not_found_exception(message="Cart item not found")
        db.delete(item)
        db.commit()
        return True
    except Exception:
        db.rollback()
        raise CustomException().bad_request_exception()


def list_cart_items_service(db: Session, user_id: int, page: int, size: int) -> PaginatedCartItemResponse:
    cart = db.scalar(select(Cart).where(Cart.user_id == user_id))
    if not cart:
        return PaginatedCartItemResponse(items=[], total=0, page=page, size=size, total_pages=0)
    query = db.query(CartItem).filter(CartItem.cart_id == cart.id)
    total = query.count()
    items = query.offset((page - 1) * size).limit(size).all()
    total_pages = (total + size - 1) // size
    return PaginatedCartItemResponse(items=items, total=total, page=page, size=size, total_pages=total_pages)


def checkout_service(db: Session, user_id: int, data: CheckoutRequest) -> list[Order]:
    try:
        cart = db.scalar(select(Cart).where(Cart.user_id == user_id))
        if not cart:
            raise CustomException().not_found_exception(message="Cart not found")
        items = db.execute(
            select(CartItem).where(
                CartItem.id.in_(data.cart_item_ids),
                CartItem.cart_id == cart.id,
            )
        ).scalars().all()
        if not items:
            raise CustomException().not_found_exception(message="Cart items not found")

        grouped: dict[int, list[CartItem]] = {}
        for item in items:
            org_id = item.product.organization_id
            grouped.setdefault(org_id, []).append(item)

        orders: list[Order] = []
        for org_id, org_items in grouped.items():
            subtotal = sum(
                i.quantity
                * (
                    i.product.price * (1 + i.product.tax_rate / 100)
                    if i.product.enable_tax and i.product.tax_rate is not None
                    else i.product.price
                )
                for i in org_items
            )
            total_price = subtotal + data.shipping_fee
            order = Order(
                user_id=user_id,
                organization_id=org_id,
                total_price=total_price,
                shipping_full_name=data.full_name,
                shipping_phone=data.phone_number,
                shipping_address=data.address,
                shipping_fee=data.shipping_fee,
                shipping_method=data.shipping_method,
            )
            db.add(order)
            db.flush()
            for item in org_items:
                db.add(
                    OrderItem(
                        order_id=order.id,
                        product_id=item.product_id,
                        quantity=item.quantity,
                        price=
                            item.quantity
                            * (
                                item.product.price * (1 + item.product.tax_rate / 100)
                                if item.product.enable_tax and item.product.tax_rate is not None
                                else item.product.price
                            ),
                    )
                )
                db.delete(item)
            orders.append(order)
        db.commit()
        for order in orders:
            db.refresh(order)
        return orders
    except Exception:
        db.rollback()
        raise CustomException().bad_request_exception()
