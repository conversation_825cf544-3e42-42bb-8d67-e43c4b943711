from datetime import datetime
from typing import Union
from pydantic import Field
from src.utils.auto_schema import AutoSchema
from src.utils.schemas import PaginatedResponse
from src.product.schemas import ProductRead
from src.utils.enum import ShippingMethodEnum

class CartItemBase(AutoSchema):
    product_id: int = Field(..., example=1)
    quantity: int = Field(..., example=1)

class CartItemCreate(CartItemBase):
    pass

class CartItemUpdate(AutoSchema):
    quantity: int = Field(..., example=1)

class CartItemRead(CartItemBase):
    id: int
    cart_id: Union[int, None]
    created_at: datetime
    updated_at: datetime
    product: ProductRead

    class Config:
        from_attributes = True

PaginatedCartItemResponse = PaginatedResponse[CartItemRead]


class CheckoutRequest(AutoSchema):
    cart_item_ids: list[int] = Field(..., example=[1, 2])
    full_name: str = Field(..., example="<PERSON> Do<PERSON>")
    phone_number: str = Field(..., example="123456789")
    address: str = Field(..., example="123 Main St")
    shipping_fee: float = Field(0, example=5.0)
    shipping_method: ShippingMethodEnum = Field(default=ShippingMethodEnum.STANDARD, example=ShippingMethodEnum.STANDARD)
