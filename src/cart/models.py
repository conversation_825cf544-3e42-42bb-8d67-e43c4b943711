from sqlalchemy import <PERSON>umn, <PERSON>teger, ForeignKey, Float
from sqlalchemy.orm import relationship
from src.utils.base_model import BaseModel, Base

class Cart(BaseModel, Base):
    __tablename__ = "cart"

    user_id = Column(Integer, ForeignKey("user.id"), unique=True, nullable=False)

    user = relationship("User", back_populates="cart", uselist=False)
    items = relationship("CartItem", back_populates="cart", cascade="all, delete-orphan")

class CartItem(BaseModel, Base):
    __tablename__ = "cart_item"

    cart_id = Column(Integer, ForeignKey("cart.id"), nullable=True)
    product_id = Column(Integer, ForeignKey("product.id"), nullable=False)
    quantity = Column(Integer, nullable=False, default=1)
    price = Column(Float, nullable=False, default=0)
    cart = relationship("Cart", back_populates="items")
    product = relationship("Product")
