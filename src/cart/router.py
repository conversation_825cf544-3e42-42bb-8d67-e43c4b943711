from fastapi import APIRouter, Depends, Query, Path
from sqlalchemy.orm import Session
from typing import List
from src.utils.database import get_db
from src.utils.permission import is_authenticated
from src.utils.token_docs import jwt_auth
from src.utils.response import CustomResponse
from .schemas import (
    CartItemCreate,
    CartItemRead,
    PaginatedCartItemResponse,
    CartItemUpdate,
    CheckoutRequest,
)
from .service import (
    add_item_to_cart_service,
    update_cart_item_service,
    remove_cart_item_service,
    list_cart_items_service,
    checkout_service,
)
from src.user.models import User
from src.order.schemas import OrderRead

router = APIRouter(prefix="", tags=["Cart"])


@router.post("/items", response_model=CartItemRead, dependencies=[Depends(jwt_auth)])
def add_cart_item(
    data: CartItemCreate,
    db: Session = Depends(get_db),
    current_user: User = Depends(is_authenticated),
):
    result = add_item_to_cart_service(db, current_user.id, data)
    return CustomResponse(content=result).format_data_create()


@router.put("/items/{cart_item_id}", response_model=CartItemRead, dependencies=[Depends(jwt_auth)])
def update_cart_item(
    cart_item_id: int = Path(..., ge=1),
    data: CartItemUpdate = None,
    db: Session = Depends(get_db),
    current_user: User = Depends(is_authenticated),
):
    result = update_cart_item_service(db, cart_item_id, data.quantity)
    return CustomResponse(content=result).format_data_update()


@router.delete("/items/{cart_item_id}", dependencies=[Depends(jwt_auth)])
def remove_cart_item(
    cart_item_id: int = Path(..., ge=1),
    db: Session = Depends(get_db),
    current_user: User = Depends(is_authenticated),
):
    remove_cart_item_service(db, cart_item_id)
    return CustomResponse().format_data_delete()


@router.get("/items", response_model=PaginatedCartItemResponse, dependencies=[Depends(jwt_auth)])
def list_cart_items(
    page: int = Query(1, ge=1),
    size: int = Query(10, ge=1, le=100),
    db: Session = Depends(get_db),
    current_user: User = Depends(is_authenticated),
):
    result = list_cart_items_service(db, current_user.id, page, size)
    return CustomResponse(content=result).format_data_get()


@router.post("/checkout", response_model=List[OrderRead], dependencies=[Depends(jwt_auth)])
def checkout_cart(
    data: CheckoutRequest,
    db: Session = Depends(get_db),
    current_user: User = Depends(is_authenticated),
):
    result = checkout_service(db, current_user.id, data)
    return CustomResponse(content=result).format_data_create()
