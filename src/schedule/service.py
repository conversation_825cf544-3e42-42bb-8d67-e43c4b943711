from datetime import datetime, timezone
from typing import List

from sqlalchemy import or_
from sqlalchemy.orm import Session

from .models import ScheduleItemTypeEnum
from .schemas import ScheduleItem, PaginatedScheduleResponse
from src.meeting.models import Meeting, MeetingParticipant, MeetingStatusEnum
from src.event.models import Event, EventInvitation
from src.utils.enum import EventStatusEnum, RSVPStatusEnum
from src.contact.models import Contact


def _get_meetings(db: Session, user_id: int, include_past: bool) -> List[ScheduleItem]:
    now = datetime.now(timezone.utc)
    query = db.query(Meeting).join(MeetingParticipant).filter(
        or_(Meeting.inviter_id == user_id, MeetingParticipant.user_id == user_id),
        Meeting.status != MeetingStatusEnum.CANCELLED,
    )
    if not include_past:
        query = query.filter(Meeting.end_time >= now)
    meetings = query.all()

    items: List[ScheduleItem] = []
    for meeting in meetings:
        contact_name = None
        if meeting.inviter_id == user_id:
            for p in meeting.participants:
                if p.user_id != user_id:
                    contact_name = (
                        p.user.profile.full_name if p.user.profile else p.user.user_name
                    )
                    break
        else:
            contact_name = (
                meeting.inviter.profile.full_name if meeting.inviter.profile else meeting.inviter.user_name
            )
        items.append(
            ScheduleItem(
                id=meeting.id,
                schedule_type=ScheduleItemTypeEnum.MEETING,
                title=meeting.title,
                start_time=meeting.start_time,
                end_time=meeting.end_time,
                related_contact=contact_name,
                location=meeting.location,
                meeting_link=meeting.meeting_link,
                status=meeting.status.value,
            )
        )
    return items


def _get_events(db: Session, user_id: int, include_past: bool) -> List[ScheduleItem]:
    now = datetime.now(timezone.utc)
    query = (
        db.query(Event)
        .outerjoin(EventInvitation, Event.id == EventInvitation.event_id)
        .outerjoin(Contact, EventInvitation.contact_id == Contact.id)
        .filter(
            or_(Event.created_by == user_id, Contact.user_contact_id == user_id),
            Event.status != EventStatusEnum.CANCELLED,
        )
    )
    if not include_past:
        query = query.filter(Event.end_time >= now)
    events = query.all()

    items: List[ScheduleItem] = []
    for event in events:
        items.append(
            ScheduleItem(
                id=event.id,
                schedule_type=ScheduleItemTypeEnum.EVENT,
                title=event.title,
                start_time=event.start_time,
                end_time=event.end_time,
                related_contact=None,
                location=event.location,
                meeting_link=event.meeting_link,
                status=event.status.value,
            )
        )
    return items


def get_schedule_service(
    db: Session, user_id: int, page: int = 1, size: int = 10, include_past: bool = False
) -> PaginatedScheduleResponse:
    meetings = _get_meetings(db, user_id, include_past)
    events = _get_events(db, user_id, include_past)

    all_items = meetings + events
    all_items.sort(key=lambda x: x.start_time)

    total = len(all_items)
    total_pages = (total + size - 1) // size
    start = (page - 1) * size
    end = start + size
    paginated_items = all_items[start:end]

    return PaginatedScheduleResponse(
        items=paginated_items,
        total=total,
        page=page,
        size=size,
        total_pages=total_pages,
    )

