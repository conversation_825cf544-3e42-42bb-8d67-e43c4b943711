from fastapi import APIRouter, Depends, Query
from sqlalchemy.orm import Session

from src.utils.database import get_db
from src.user.models import User
from src.utils.permission import is_authenticated
from src.utils.token_docs import jwt_auth
from src.utils.response import CustomResponse

from .schemas import PaginatedScheduleResponse
from .service import get_schedule_service

router = APIRouter(tags=["Schedule"])


@router.get("", response_model=PaginatedScheduleResponse, dependencies=[Depends(jwt_auth)])
def get_schedule(
    page: int = Query(1, ge=1),
    size: int = Query(10, ge=1, le=100),
    include_past: bool = Query(False, description="Include past items"),
    db: Session = Depends(get_db),
    current_user: User = Depends(is_authenticated),
):
    result = get_schedule_service(db, current_user.id, page, size, include_past)
    return CustomResponse(content=result).format_data_get()

