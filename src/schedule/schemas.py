from datetime import datetime
from typing import Optional, List
from pydantic import BaseModel, Field

from .models import ScheduleItemTypeEnum
from src.utils.schemas import PaginatedResponse


class ScheduleItem(BaseModel):
    id: int = Field(..., example=1)
    schedule_type: ScheduleItemTypeEnum = Field(..., example=ScheduleItemTypeEnum.MEETING)
    title: str = Field(..., example="Project Kickoff")
    start_time: datetime = Field(..., example="2025-06-20T09:00:00Z")
    end_time: datetime = Field(..., example="2025-06-20T10:00:00Z")
    related_contact: Optional[str] = Field(None, example="John Doe")
    location: Optional[str] = Field(None, example="Meeting Room A")
    meeting_link: Optional[str] = Field(None, example="https://meet.google.com/abc-defg-hij")
    status: str = Field(..., example="SCHEDULED")

    model_config = {
        "json_schema_extra": {
            "examples": [
                {
                    "id": 1,
                    "schedule_type": "MEETING",
                    "title": "Project Kickoff",
                    "start_time": "2025-06-20T09:00:00Z",
                    "end_time": "2025-06-20T10:00:00Z",
                    "related_contact": "<PERSON> Doe",
                    "location": "Meeting Room A",
                    "meeting_link": "https://meet.google.com/abc-defg-hij",
                    "status": "SCHEDULED"
                }
            ]
        }
    }


class PaginatedScheduleResponse(PaginatedResponse):
    items: List[ScheduleItem]

