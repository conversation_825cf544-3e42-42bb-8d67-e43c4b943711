from datetime import datetime, timedelta, timezone
from fastapi import Depends, FastAPI, HTTPException, Request, Security
from fastapi.responses import FileResponse, JSONResponse
from fastapi.routing import APIRoute
from jose import JWTError, jwt
from sqlalchemy.orm import Session
# from app.models.route import Route, Scope
# from app.models.user import User
# from src import celery
from fastapi.middleware.cors import CORSMiddleware
from src.utils.middleware import BearerTokenMiddleware
# import all you need from fastapi-pagination
# from fastapi_pagination import add_pagination
# from fastapi_pagination.utils import disable_installed_extensions_check
from src.utils.exceptions import CustomException
import os
from starlette.exceptions import HTTPException as StarletteHTTPException
from starlette.requests import Request as StarletteRequest
from starlette.responses import J<PERSON><PERSON><PERSON>ponse as StarletteJSONResponse
from src.utils.response import *
from src.user.router import router as user_router
from src.authentication.router import router as authentication_router
from src.event.router import router as event_router
from src.news.router import router as news_router
from src.admin.router import router as admin_router
from src.admin_user.router import router as admin_user_router
from src.admin_account.router import router as admin_account_router
from src.upload.router import router as upload_router
from src.contact.router import router as contact_router
from src.organization.router import router as organization_router
from src.product.router import router as product_router
from src.lead.router import router as lead_router
from src.ticket.router import router as ticket_router
from src.ticket_sale.router import router as ticket_sale_router
from src.payment.router import router as payment_router
from src.utils.response import CustomResponse
from src.chat.router import router as chat_router
from src.cart.router import router as cart_router
from src.order.router import router as order_router
from src.meeting.router import router as meeting_router
from src.schedule.router import router as schedule_router
from src.report_event.router import router as report_event_router
from src.notification_history.router import notification_router
from src.chat.socket import sio
import socketio
from fastapi.staticfiles import StaticFiles
from src.utils.database import engine, Base
# disable_installed_extensions_check()

from dotenv import load_dotenv
import logging
import sys
from src.gift.router import router as gift_router

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(sys.stdout)
    ]
)

# Get logger
logger = logging.getLogger(__name__)

# Load env files
load_dotenv(verbose=True)

# Create tables
# Base.metadata.create_all(bind=engine)

# Create FastAPI app
app = FastAPI(
    title="AIOS Link API",
    description="API for AIOS Link application",
    version="1.0.0"
)

# API prefix
prefix = "/api"

# Include routers
app.include_router(user_router, prefix="/api/user")
app.include_router(authentication_router, prefix="/api/authenticate")
app.include_router(event_router, prefix="/api/event")
app.include_router(news_router, prefix="/api/news")
app.include_router(contact_router, prefix="/api/contact")
app.include_router(organization_router, prefix="/api/organization")
app.include_router(product_router, prefix="/api/product")
app.include_router(lead_router, prefix="/api/lead")
app.include_router(gift_router, prefix="/api/gifts")
app.include_router(ticket_router, prefix="/api/ticket")
app.include_router(ticket_sale_router, prefix="/api/ticket-order")
app.include_router(payment_router, prefix="/api/payment")
app.include_router(cart_router, prefix="/api/cart")
app.include_router(order_router, prefix="/api/order")
app.include_router(meeting_router, prefix="/api/meeting")
app.include_router(schedule_router, prefix="/api/schedule")
app.include_router(upload_router, prefix="/api/upload")
app.include_router(report_event_router, prefix="/api/report-event")
app.include_router(admin_user_router, prefix="/api/admin/merchant")
# Admin user management
app.include_router(admin_account_router, prefix="/api/admin/user")
# app.include_router(admin_router, prefix = "/api/admin")
app.include_router(chat_router, prefix="/api/chat")
app.include_router(notification_router, prefix="/api/notifications")

# Add middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

app.add_middleware(
    BearerTokenMiddleware
)

# Mount static files

# Create Socket.IO ASGI app
sio_app = socketio.ASGIApp(
    sio,
    other_asgi_app=app,
)

# Mount Socket.IO at root
app.mount("/socket.io", sio_app) 

@app.exception_handler(StarletteHTTPException)
async def http_exception_handler(request, exc):
    return JSONResponse(status_code=exc.status_code, content=exc.detail)

# add_pagination(app)  # important! add pagination to your app

# Health check endpoint
@app.get("/", tags=["Health Check"])
def health_check():
    return CustomResponse(content={
        "message": "Welcome to AIOS Link API",
        "status": "healthy",
        "version": "1.0.0"
    }).format_data_get()



