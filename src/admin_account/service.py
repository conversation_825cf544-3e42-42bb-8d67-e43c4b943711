from typing import Union
from sqlalchemy.orm import Session, joinedload
from src.utils.enum import UserRoleEnum
from src.utils.exceptions import CustomException
from src.user.models import (
    User as UserModel,
    UserProfile as UserProfileModel,
    UserNotificationSettings,
    UserSubscriptionSettings,
    UserWebLinkSettings,
)
from src.organization.service import (
    create_organization_service,
    update_organization_service,
    get_organization_service,
)
from src.user.service import get_password_hash
from .schemas import (
    AdminUserCreate,
    AdminUserUpdate,
    AdminUserListResponse,
    AdminUserResponse,
    AdminUserDetailResponse,
)


def create_user_admin_service(db: Session, data: AdminUserCreate) -> UserModel:
    existing = db.query(UserModel).filter(UserModel.email == data.email, UserModel.is_deleted == False).first()
    if existing:
        raise CustomException(content={"error": ["Email already exists"]}).bad_request_exception()
    org_id = None
    if data.organization is not None:
        org = create_organization_service(db, data.organization)
        org_id = org.id
    hashed_pwd = get_password_hash(data.password)
    user = UserModel(
        email=data.email,
        password=hashed_pwd,
        user_type=data.role,
        is_active=False,
        organization_id=org_id,
    )
    db.add(user)
    db.flush()
    profile = UserProfileModel(full_name=data.full_name, user_id=user.id)
    db.add(profile)
    notification = UserNotificationSettings(user_id=user.id)
    subscription = UserSubscriptionSettings(user_id=user.id)
    web_link = UserWebLinkSettings(user_id=user.id, public_profile_link=f"profile/{user.id}")
    db.add_all([notification, subscription, web_link])
    db.commit()
    db.refresh(user)
    return user


def get_user_admin_service(db: Session, user_id: int) -> Union[UserModel, None]:
    return db.query(UserModel).options(joinedload(UserModel.profile), joinedload(UserModel.organization)).filter(
        UserModel.id == user_id,
        UserModel.is_deleted == False,
    ).first()


def get_user_detail_admin_service(db: Session, user_id: int) -> Union[AdminUserDetailResponse, None]:
    user = get_user_admin_service(db, user_id)
    if not user:
        return None
    
    return AdminUserDetailResponse.model_validate({
        "id": user.id,
        "email": user.email,
        "full_name": user.profile.full_name if user.profile else None,
        "is_active": user.is_active,
        "role": user.user_type,
        "organization": user.organization,
        "last_login": user.last_login,
        "phone_number": user.phone_number,
        "avatar": user.avatar,
        "qr_code": user.qr_code,
        "user_name": user.user_name,
        "created_at": user.created_at,
        "updated_at": user.updated_at,
        "profile": {
            "id": user.profile.id,
            "full_name": user.profile.full_name,
            "title": user.profile.title,
            "gender": user.profile.gender,
            "date_of_birth": user.profile.date_of_birth,
            "social_media_links": user.profile.social_media_links,
            "bio": user.profile.bio,
            "address": user.profile.address,
            "lattitude": user.profile.lattitude,
            "longitude": user.profile.longitude,
            "skill_interests": user.profile.skill_interests,
            "industry_tags": user.profile.industry_tags,
            "notes": user.profile.notes,
            "public_profile": user.profile.public_profile,
            "created_at": user.profile.created_at,
            "updated_at": user.profile.updated_at,
        } if user.profile else None,
    })


def list_users_admin_service(db: Session, page: int = 1, size: int = 10) -> AdminUserListResponse:
    if page < 1:
        page = 1
    if size < 1:
        size = 10
    if size > 100:
        size = 100
    skip = (page - 1) * size
    query = db.query(UserModel).filter(UserModel.is_deleted == False)
    total = query.count()
    items = query.options(joinedload(UserModel.profile), joinedload(UserModel.organization)).offset(skip).limit(size).all()
    pages = (total + size - 1) // size
    responses = []
    for u in items:
        responses.append(
            AdminUserResponse.model_validate(
                {
                    "id": u.id,
                    "email": u.email,
                    "full_name": u.profile.full_name if u.profile else None,
                    "is_active": u.is_active,
                    "role": u.user_type,
                    "organization": u.organization,
                    "last_login": u.last_login,
                }
            )
        )
    return AdminUserListResponse(items=responses, total=total, page=page, size=size, pages=pages)


def update_user_admin_service(db: Session, user_id: int, data: AdminUserUpdate) -> UserModel:
    user = get_user_admin_service(db, user_id)
    if not user:
        raise CustomException(content={"error": ["User not found"]}).bad_request_exception()
    if data.full_name is not None:
        if user.profile:
            user.profile.full_name = data.full_name
        else:
            profile = UserProfileModel(full_name=data.full_name, user_id=user.id)
            db.add(profile)
    if data.password is not None:
        user.password = get_password_hash(data.password)
    if data.role is not None:
        user.user_type = data.role
    if data.is_active is not None:
        user.is_active = data.is_active
    if data.organization is not None:
        if user.organization_id:
            update_organization_service(db, user.organization_id, data.organization)
        else:
            org = create_organization_service(db, data.organization)
            user.organization_id = org.id
    db.add(user)
    db.commit()
    db.refresh(user)
    return user


def delete_user_admin_service(db: Session, user_id: int) -> None:
    user = get_user_admin_service(db, user_id)
    if not user:
        raise CustomException(content={"error": ["User not found"]}).bad_request_exception()
    user.is_deleted = True
    db.add(user)
    db.commit()
