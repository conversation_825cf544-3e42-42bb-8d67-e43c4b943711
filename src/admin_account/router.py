from fastapi import APIRouter, Depends
from sqlalchemy.orm import Session
from src.utils.database import get_db
from src.utils.token_docs import jwt_auth
from src.utils.permission import is_admin
from src.utils.response import CustomResponse
from src.user.models import User as UserModel
from .schemas import AdminUserCreate, AdminUserUpdate
from .service import (
    create_user_admin_service,
    list_users_admin_service,
    get_user_admin_service,
    get_user_detail_admin_service,
    update_user_admin_service,
    delete_user_admin_service,
)

router = APIRouter()
tags = ["Admin User"]

@router.post("", dependencies=[Depends(jwt_auth)], tags=tags)
def create_user(
    data: AdminUserCreate,
    db: Session = Depends(get_db),
    current_user: UserModel = Depends(is_admin),
):
    user = create_user_admin_service(db, data)
    return CustomResponse(content=user).format_data_create()

@router.get("", dependencies=[Depends(jwt_auth)], tags=tags)
def list_users(
    page: int = 1,
    size: int = 10,
    db: Session = Depends(get_db),
    current_user: UserModel = Depends(is_admin),
):
    users = list_users_admin_service(db, page, size)
    return CustomResponse(content=users).format_data_get()

@router.get("/{user_id}", dependencies=[Depends(jwt_auth)], tags=tags)
def get_user(
    user_id: int,
    db: Session = Depends(get_db),
    current_user: UserModel = Depends(is_admin),
):
    user = get_user_detail_admin_service(db, user_id)
    if not user:
        return CustomResponse().format_data_not_found()
    return CustomResponse(content=user).format_data_get()

@router.put("/{user_id}", dependencies=[Depends(jwt_auth)], tags=tags)
def update_user(
    user_id: int,
    data: AdminUserUpdate,
    db: Session = Depends(get_db),
    current_user: UserModel = Depends(is_admin),
):
    user = update_user_admin_service(db, user_id, data)
    return CustomResponse(content=user).format_data_update()

@router.delete("/{user_id}", dependencies=[Depends(jwt_auth)], tags=tags)
def delete_user(
    user_id: int,
    db: Session = Depends(get_db),
    current_user: UserModel = Depends(is_admin),
):
    delete_user_admin_service(db, user_id)
    return CustomResponse().format_data_delete()

