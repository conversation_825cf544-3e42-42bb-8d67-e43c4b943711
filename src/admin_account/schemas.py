from typing import Optional, List
from datetime import datetime
from pydantic import BaseModel, Field, EmailStr
from src.organization.schemas import OrganizationCreate, OrganizationUpdate, OrganizationResponse
from src.utils.enum import UserRoleEnum

class AdminUserCreate(BaseModel):
    email: EmailStr = Field(..., example="<EMAIL>")
    password: str = Field(..., example="Password123!")
    full_name: str = Field(..., example="John Doe")
    role: UserRoleEnum = Field(..., example=UserRoleEnum.USER)
    organization: Optional[OrganizationCreate] = None

class AdminUserUpdate(BaseModel):
    full_name: Optional[str] = Field(None, example="John Doe")
    password: Optional[str] = Field(None, example="Password123!")
    role: Optional[UserRoleEnum] = None
    is_active: Optional[bool] = None
    organization: Optional[OrganizationUpdate] = None

class AdminUserResponse(BaseModel):
    id: int
    email: EmailStr
    full_name: Optional[str]
    is_active: bool
    role: UserRoleEnum
    organization: Optional[OrganizationResponse]
    last_login: Optional[datetime] = None

    class Config:
        from_attributes = True

class AdminUserListResponse(BaseModel):
    items: List[AdminUserResponse]
    total: int
    page: int
    size: int
    pages: int

    model_config = {
        "json_schema_extra": {
            "examples": [
                {
                    "items": [],
                    "total": 0,
                    "page": 1,
                    "size": 10,
                    "pages": 1,
                }
            ]
        }
    }

class AdminUserDetailResponse(BaseModel):
    id: int
    email: EmailStr
    full_name: Optional[str]
    is_active: bool
    role: UserRoleEnum
    organization: Optional[OrganizationResponse]
    last_login: Optional[datetime] = None
    phone_number: Optional[str] = None
    avatar: Optional[str] = None
    qr_code: Optional[str] = None
    user_name: Optional[str] = None
    created_at: Optional[datetime] = None
    updated_at: Optional[datetime] = None
    profile: Optional[dict] = None

    class Config:
        from_attributes = True
