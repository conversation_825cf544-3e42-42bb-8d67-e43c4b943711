from src.utils.exceptions import CustomException
from sqlalchemy.orm import Session
from .models import ChatRoomMember, Message
from src.utils.database import get_db
from datetime import datetime
from typing import Dict, Set, List
import json
import socketio
import logging
from .schemas import MessageCreate
from contextlib import asynccontextmanager
# Configure logging
logger = logging.getLogger(__name__)
@asynccontextmanager
async def get_db_context():
    db_gen = get_db()
    try:
        db = await anext(db_gen)
        yield db
    finally:
        await db_gen.aclose()
# Global tracking
socket_user_map: Dict[str, int] = {}  # sid -> user_id
user_socket_map: Dict[int, Set[str]] = {}  # user_id -> set of sids

# Initialize Socket.IO server
sio = socketio.AsyncServer(
    async_mode='asgi',
    cors_allowed_origins='*',
    logger=True,  # Bật logger để debug
    engineio_logger=True  # Bật engine.io logger
)

async def get_user_rooms(user_id: int, db: Session) -> List[int]:
    rooms = db.query(ChatRoomMember.room_id).filter(
        ChatRoomMember.user_id == user_id
    ).all()
    return [r[0] for r in rooms]

async def emit_user_status(user_id: int, is_online: bool):
    async with get_db_context() as db:
        rooms = await get_user_rooms(user_id, db)
        members = db.query(ChatRoomMember.user_id).filter(
            ChatRoomMember.room_id.in_(rooms)
        ).distinct().all()

        for member_id in [m[0] for m in members]:
            if member_id != user_id:
                await sio.emit(
                    'user_status',
                    {
                        'user_id': user_id,
                        'is_online': is_online,
                        'last_seen': datetime.now().isoformat() if not is_online else None
                    },
                    room=f"user:{member_id}"
                )

@sio.event
async def connect(sid, environ):
    try:
        print(f"[CONNECT] Raw SID={sid}, environ={environ}")
        user_id = None
        query_bytes = environ.get("asgi.scope", {}).get("query_string", b"")
        query_string = query_bytes.decode()
        print(f"[DEBUG] Raw query string: {query_string}")

        from urllib.parse import parse_qs
        parsed = parse_qs(query_string)
        print(f"[DEBUG] Parsed params: {parsed}")

        user_id_list = parsed.get("user_id")
        if user_id_list and user_id_list[0].isdigit():
            user_id = int(user_id_list[0])

        if not user_id:
            print("[ERROR] user_id is missing or invalid")
            raise CustomException().bad_request_exception()

        socket_user_map[sid] = user_id
        user_socket_map.setdefault(user_id, set()).add(sid)
        sio.enter_room(sid, f"user:{user_id}")
        emit_user_status(user_id, True)
        print(f"Client connected: {sid} (user: {user_id})")
    except Exception as e:
        print(f"Error in connect: {str(e)}")
        await sio.disconnect(sid)

@sio.event
async def disconnect(sid):
    try:
        user_id = socket_user_map.get(sid)
        if user_id:
            socket_user_map.pop(sid, None)
            user_socket_map[user_id].discard(sid)
            if not user_socket_map[user_id]:
                async with get_db_context() as db:
                    await emit_user_status(user_id, False)
                user_socket_map.pop(user_id, None)
        print(f"Client disconnected: {sid} (user: {user_id})")
    except Exception as e:
        print(f"Error in disconnect: {str(e)}")

@sio.on('join_room')
async def join_room(sid, data):
    """
    Handle client joining a room
    
    Sample data:
    {
        "room_id": 123,
        "user_id": 456
    }
    """
    print(f"[DEBUG] join_room called with sid={sid}, data={data}")
    print(f"[DEBUG] Current socket_user_map: {socket_user_map}")
    
    try:
        print(f"[JOIN_ROOM] Received event from {sid} with data: {data}")
        
        room_id = data.get('room_id')
        user_id = socket_user_map.get(sid)
        
        print(f"[JOIN_ROOM] User {user_id} attempting to join room {room_id}")
        print(f"[DEBUG] room_id type: {type(room_id)}, user_id type: {type(user_id)}")
        
        if not room_id or not user_id:
            print(f"[JOIN_ROOM] Error: Missing room_id or user_id. room_id={room_id}, user_id={user_id}")
            raise CustomException(content={"error": "Missing room_id or user_id"}).bad_request_exception()
        
        # Check if user is member of room
        async with get_db_context() as db:
            member = db.query(ChatRoomMember).filter(
                ChatRoomMember.room_id == room_id,
                ChatRoomMember.user_id == user_id
            ).first()
            
            print(f"[DEBUG] Member query result: {member}")
            
            if not member:
                print(f"[JOIN_ROOM] Error: User {user_id} is not a member of room {room_id}")
                raise CustomException().not_found_exception()
        
        # Join socket room
        sio.enter_room(sid, str(room_id))
        print(f"[JOIN_ROOM] Success: Client {sid} (user: {user_id}) joined room {room_id}")
        
    except Exception as e:
        print(f"[JOIN_ROOM] Error: {str(e)}")
        print(f"[DEBUG] Exception type: {type(e)}")
        import traceback
        print(f"[DEBUG] Traceback: {traceback.format_exc()}")
        sio.emit('error', {'message': str(e)}, room=sid)

@sio.event
async def leave_room(sid, data):
    """
    Handle client leaving a room
    
    Sample data:
    {
        "room_id": 123
    }
    """
    try:
        room_id = data.get('room_id')
        if not room_id:
            raise CustomException(content={"error": "Missing room_id"}).bad_request_exception()
        
        # Leave socket room
        await sio.leave_room(sid, str(room_id))
        print(f"Client {sid} left room {room_id}")
        
    except Exception as e:
        print(f"Error leaving room: {str(e)}")
        await sio.emit('error', {'message': str(e)}, room=sid)

@sio.event
async def typing(sid, data):
    """
    Handle typing indicator
    
    Sample data:
    {
        "room_id": 123,
        "is_typing": true
    }
    
    Emitted data:
    {
        "room_id": 123,
        "user_id": 456,
        "is_typing": true
    }
    """
    try:
        room_id = data.get('room_id')
        user_id = socket_user_map.get(sid)
        is_typing = data.get('is_typing', False)
        
        if not room_id or not user_id:
            raise CustomException(content={"error": "Missing room_id or user_id"}).bad_request_exception()
        
        # Emit to all room members except sender
        await sio.emit(
            'typing',
            {
                'room_id': room_id,
                'user_id': user_id,
                'is_typing': is_typing
            },
            room=str(room_id),
            skip_sid=sid
        )
        
    except Exception as e:
        print(f"Error in typing: {str(e)}")
        await sio.emit('error', {'message': str(e)}, room=sid)

@sio.event
async def message_seen(sid, data):
    """
    Handle message seen status
    
    Sample data:
    {
        "room_id": 123,
        "message_id": 789
    }
    
    Emitted data:
    {
        "room_id": 123,
        "message_id": 789,
        "seen_by": 456
    }
    """
    try:
        room_id = data.get('room_id')
        user_id = socket_user_map.get(sid)
        message_id = data.get('message_id')
        
        if not room_id or not user_id:
            raise CustomException(content={"error": "Missing room_id or user_id"}).bad_request_exception()
        
        # Get message sender
        async with get_db_context() as db:
            message = db.query(Message).filter(
                Message.id == message_id,
                Message.room_id == room_id
            ).first()
            
            if message and message.sender_id != user_id:
                # Emit to message sender's personal channel
                sio.emit(
                    'message_seen',
                    {
                        'room_id': room_id,
                        'message_id': message_id,
                        'seen_by': user_id
                    },
                    room=f"user:{message.sender_id}"
                )
        
    except Exception as e:
        print(f"Error in message_seen: {str(e)}")
        await sio.emit('error', {'message': str(e)}, room=sid)

@sio.on('send_message')
async def send_message(sid, data):
    """
    Handle sending message through socket
    
    Sample data:
    {
        "room_id": 123,
        "content": "Hello world!",
        "message_type": "TEXT"
    }
    
    Emitted data (new_message):
    {
        "message": {
            "id": 789,
            "room_id": 123,
            "sender_id": 456,
            "content": "Hello world!",
            "message_type": "TEXT",
            "created_at": "2024-03-21T10:30:00"
        }
    }
    
    Emitted data (room_updated):
    {
        "room_id": 123,
        "last_message": {
            "id": 789,
            "content": "Hello world!",
            "sender_id": 456,
            "created_at": "2024-03-21T10:30:00"
        },
        "unread_count": 1
    }
    
    Emitted data (message_sent):
    {
        "message_id": 789,
        "room_id": 123
    }
    """
    try:
        print(f"[SEND_MESSAGE] Received event from {sid} with data: {data}")
        
        user_id = socket_user_map.get(sid)
        if not user_id:
            print(f"[SEND_MESSAGE] Error: User not authenticated for socket {sid}")
            raise CustomException(content={"error": "User not authenticated"}).bad_request_exception()
        
        room_id = data.get('room_id')
        content = data.get('content')
        message_type = data.get('message_type', 'TEXT')
        
        print(f"[SEND_MESSAGE] User {user_id} attempting to send message to room {room_id}")
        
        if not room_id or not content:
            print(f"[SEND_MESSAGE] Error: Missing room_id or content. room_id={room_id}, content={content}")
            raise CustomException(content={"error": "Missing room_id or content"}).bad_request_exception()
        
        # Check if user is member of room
        async with get_db_context() as db:
            member = db.query(ChatRoomMember).filter(
                ChatRoomMember.room_id == room_id,
                ChatRoomMember.user_id == user_id
            ).first()
            
            if not member:
                print(f"[SEND_MESSAGE] Error: User {user_id} is not a member of room {room_id}")
                raise CustomException().not_found_exception(
                    message="Room not found or you don't have access"
                )
            
            # Create message
            message = Message(
                room_id=room_id,
                sender_id=user_id,
                content=content,
                message_type=message_type
            )
            db.add(message)
            db.commit()
            db.refresh(message)
            
            print(f"[SEND_MESSAGE] Message created: id={message.id}, room={room_id}, sender={user_id}")
            
            # Get room members
            members = db.query(ChatRoomMember.user_id).filter(
                ChatRoomMember.room_id == room_id
            ).all()
            
            # Emit message to room
            await sio.emit(
                'new_message',
                {
                    'message': {
                        'id': message.id,
                        'room_id': message.room_id,
                        'sender_id': message.sender_id,
                        'content': message.content,
                        'message_type': message.message_type,
                        'created_at': message.created_at.isoformat()
                    }
                },
                room=str(room_id)
            )
            
            print(f"[SEND_MESSAGE] Emitted new_message to room {room_id}")
            
        # Emit room update to each member's personal channel
        for member_id in [m[0] for m in members]:
            if member_id != user_id:  # Don't emit to sender
                sio.emit(
                    'room_updated',
                    {
                        'room_id': room_id,
                        'last_message': {
                            'id': message.id,
                            'content': message.content,
                            'sender_id': message.sender_id,
                            'created_at': message.created_at.isoformat()
                        },
                        'unread_count': 1
                    },
                    room=f"user:{member_id}"
                )
                print(f"[SEND_MESSAGE] Emitted room_updated to user {member_id}")
        
        # Acknowledge message sent
        sio.emit('message_sent', {
            'message_id': message.id,
            'room_id': room_id
        }, room=sid)
        
        print(f"[SEND_MESSAGE] Message sent successfully: id={message.id}")
        
    except Exception as e:
        print(f"[SEND_MESSAGE] Error: {str(e)}")
        sio.emit('error', {'message': str(e)}, room=sid) 