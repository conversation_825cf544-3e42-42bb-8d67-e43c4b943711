from sqlalchemy.orm import Session, joinedload, load_only
from sqlalchemy import desc
from typing import List, Optional
from datetime import datetime
from src.utils.exceptions import CustomException
from src.user.models import User, UserProfile, BusinessProfile
from .models import ChatRoom, ChatRoomMember, Message, MessageTypeEnum, MemberRoleEnum, MemberStatusEnum, GroupInviteActionEnum
from .schemas import (
    ChatRoomCreate, ChatRoomRead, ChatRoomDetail, MessageChatSchemas,
    AddMembersRequest, RemoveMemberRequest, UpdateMemberRoleRequest, UpdateMemberNicknameRequest, UpdateRoomThemeRequest, MemberInvitationResponse,
    PaginatedChatRoomResponse, PaginatedMessageResponse
)
from src.utils.schemas import PaginatedResponse

def create_chat_room_service(
    db: Session,
    user_id: int,
    room_data: ChatRoomCreate
) -> ChatRoom:
    try:
        # Validate all users exist
        users = db.query(User).filter(
            User.id.in_(room_data.member_ids),
            User.is_deleted == False
        ).all()
        
        if len(users) != len(room_data.member_ids):
            raise CustomException(content={"error": "One or more users not found"}).bad_request_exception()
        
        # Create room
        room = ChatRoom(
            name=room_data.name,
            is_group=room_data.is_group,
            created_by=user_id
        )
        db.add(room)
        db.flush()  # Get room ID
        
        # Add members - creator là ADMIN/ACCEPTED, các thành viên khác là MEMBER/PENDING
        members = []
        for user in users:
            if user.id == user_id:
                role = MemberRoleEnum.ADMIN
                status = MemberStatusEnum.ACCEPTED
                invited_by = None
                invited_at = None
            else:
                role = MemberRoleEnum.MEMBER
                status = MemberStatusEnum.PENDING
                invited_by = user_id
                invited_at = datetime.now()
            member = ChatRoomMember(
                room_id=room.id,
                user_id=user.id,
                role=role,
                status=status,
                invited_by=invited_by,
                invited_at=invited_at,
                accepted_at=datetime.now() if status == MemberStatusEnum.ACCEPTED else None
            )
            members.append(member)
        
        db.add_all(members)
        db.commit()
        db.refresh(room)
        
        return room
        
    except Exception as e:
        db.rollback()
        raise CustomException().bad_request_exception()

def get_user_rooms_service(
    db: Session,
    user_id: int,
    page: int = 1,
    size: int = 10,
    status: Optional[str] = None
) -> dict:
    # Get rooms where user is a member (filter theo status nếu có)
    query = db.query(ChatRoom).join(
        ChatRoomMember
    ).filter(
        ChatRoomMember.user_id == user_id
    )
    if status:
        query = query.filter(ChatRoomMember.status == status)
    query = query.options(
        # Load user info including profile
        joinedload(ChatRoom.members).joinedload(ChatRoomMember.user).load_only(
            User.id,
            User.user_name,
            User.avatar,
            User.email
        ),
        joinedload(ChatRoom.members).joinedload(ChatRoomMember.user).joinedload(User.profile).load_only(
            UserProfile.full_name
        ),
        joinedload(ChatRoom.members).joinedload(ChatRoomMember.user).joinedload(User.business_profile).load_only(
            BusinessProfile.business_name,
            BusinessProfile.business_address,
            BusinessProfile.industry_tags
        )
    ).order_by(
        desc(ChatRoom.created_at)
    )
    
    # Get total count
    total = query.count()
    
    # Calculate pagination
    total_pages = (total + size - 1) // size
    skip = (page - 1) * size
    
    # Execute query with pagination
    rooms = query.offset(skip).limit(size).all()
    
    # Process rooms and set name for 1-1 chats
    processed_rooms = []
    for room in rooms:
        # Nếu là 1-1 chat (not group), set lại name
        if not room.is_group:
            other_member = None
            for member in room.members:
                if member.user_id != user_id:
                    other_member = member
                    break
            # Ưu tiên nick_name, nếu không có thì lấy full_name
            if other_member and other_member.nick_name:
                room.name = other_member.nick_name
            elif other_member and other_member.user.profile and other_member.user.profile.full_name:
                room.name = other_member.user.profile.full_name
            elif other_member and other_member.user.user_name:
                room.name = other_member.user.user_name
            elif other_member:
                room.name = other_member.user.email
        # Gán status của user hiện tại vào room
        my_member = next((m for m in room.members if m.user_id == user_id), None)
        room.status = my_member.status if my_member else None
        processed_rooms.append(room)
    
    # Convert to response schema
    room_schemas = [ChatRoomRead.from_orm(room) for room in processed_rooms]
    
    return PaginatedChatRoomResponse(
        items=room_schemas,
        total=total,
        page=page,
        size=size,
        total_pages=total_pages
    )

def get_room_messages_service(
    db: Session,
    room_id: int,
    user_id: int,
    page: int = 1,
    size: int = 50
) -> dict:
    # Check if user is member of room
    member = db.query(ChatRoomMember).filter(
        ChatRoomMember.room_id == room_id,
        ChatRoomMember.user_id == user_id,
        ChatRoomMember.status == MemberStatusEnum.ACCEPTED
    ).first()
    
    if not member:
        raise CustomException(content={"error": "Room not found or you don't have access"}).bad_request_exception()
    
    # Get messages
    query = db.query(Message).filter(
        Message.room_id == room_id
    ).options(
        # Load sender info including profile
        joinedload(Message.sender).load_only(
            User.id,
            User.user_name,
            User.avatar,
            User.email
        ),
        joinedload(Message.sender).joinedload(User.profile).load_only(
            UserProfile.full_name
        ),
    ).order_by(
        desc(Message.created_at)
    )
    
    # Get total count
    total = query.count()
    
    # Calculate pagination
    total_pages = (total + size - 1) // size
    skip = (page - 1) * size
    
    # Execute query with pagination
    messages = query.offset(skip).limit(size).all()
    
    # Convert to response schema
    message_schemas = [MessageChatSchemas.from_orm(message) for message in messages]
    
    return PaginatedMessageResponse(
        items=message_schemas,
        total=total,
        page=page,
        size=size,
        total_pages=total_pages
    )

def add_members_to_group_service(
    db: Session,
    room_id: int,
    user_id: int,
    request: AddMembersRequest
) -> MemberInvitationResponse:
    """Add members to a group chat"""
    try:
        # Check if room exists and is a group
        room = db.query(ChatRoom).filter(
            ChatRoom.id == room_id,
            ChatRoom.is_group == True
        ).first()
        
        if not room:
            raise CustomException(content={"error": "Group chat not found"}).not_found_exception()
        
        # Check if user is member of the group
        current_member = db.query(ChatRoomMember).filter(
            ChatRoomMember.room_id == room_id,
            ChatRoomMember.user_id == user_id,
            ChatRoomMember.status == MemberStatusEnum.ACCEPTED
        ).first()
        
        if not current_member:
            raise CustomException(content={"error": "You are not a member of this group"}).bad_request_exception()
        
        # Validate all users exist and are not already members
        users = db.query(User).filter(
            User.id.in_(request.member_ids),
            User.is_deleted == False
        ).all()
        
        existing_members = db.query(ChatRoomMember).filter(
            ChatRoomMember.room_id == room_id,
            ChatRoomMember.user_id.in_(request.member_ids)
        ).all()
        
        existing_user_ids = [member.user_id for member in existing_members]
        new_user_ids = [user.id for user in users if user.id not in existing_user_ids]
        
        # Create new members
        new_members = []
        for user_id_to_add in new_user_ids:
            member = ChatRoomMember(
                room_id=room_id,
                user_id=user_id_to_add,
                role=MemberRoleEnum.MEMBER,
                status=MemberStatusEnum.PENDING,
                invited_by=user_id,
                invited_at=datetime.now()
            )
            new_members.append(member)
        
        db.add_all(new_members)
        db.commit()
        
        # Get user info for response
        added_users = db.query(User).filter(User.id.in_(new_user_ids)).all()
        added_user_info = []
        for user in added_users:
            user_info = {
                "id": user.id,
                "user_name": user.user_name,
                "avatar": user.avatar,
                "email": user.email,
                "full_name": user.profile.full_name if user.profile else None
            }
            added_user_info.append(user_info)
        
        failed_members = []
        for member_id in request.member_ids:
            if member_id in existing_user_ids:
                failed_members.append({
                    "user_id": member_id,
                    "reason": "User is already a member of this group"
                })
            elif member_id not in new_user_ids:
                failed_members.append({
                    "user_id": member_id,
                    "reason": "User not found"
                })
        
        return MemberInvitationResponse(
            success=True,
            message=f"Successfully invited {len(added_user_info)} members to the group",
            added_members=added_user_info,
            failed_members=failed_members
        )
        
    except Exception as e:
        db.rollback()
        raise CustomException().bad_request_exception()

def remove_member_from_group_service(
    db: Session,
    room_id: int,
    admin_id: int,
    request: RemoveMemberRequest
) -> dict:
    """Remove a member from group chat (admin only)"""
    try:
        # Check if room exists and is a group
        room = db.query(ChatRoom).filter(
            ChatRoom.id == room_id,
            ChatRoom.is_group == True
        ).first()
        
        if not room:
            raise CustomException(content={"error": "Group chat not found"}).bad_request_exception()
        
        # Check if admin is actually an admin
        admin_member = db.query(ChatRoomMember).filter(
            ChatRoomMember.room_id == room_id,
            ChatRoomMember.user_id == admin_id,
            ChatRoomMember.role == MemberRoleEnum.ADMIN,
            ChatRoomMember.status == MemberStatusEnum.ACCEPTED
        ).first()
        
        if not admin_member:
            raise CustomException(content={"error": "Only admins can remove members from the group"}).bad_request_exception()
        
        # Check if trying to remove themselves
        if admin_id == request.user_id:
            raise CustomException(content={"error": "Admin cannot remove themselves from the group"}).bad_request_exception()
        
        # Check if target user is a member
        target_member = db.query(ChatRoomMember).filter(
            ChatRoomMember.room_id == room_id,
            ChatRoomMember.user_id == request.user_id
        ).first()
        
        if not target_member:
            raise CustomException(content={"error": "User is not a member of this group"}).bad_request_exception()
        
        # Remove the member
        db.delete(target_member)
        db.commit()
        
        return {
            "message": f"User has been removed from the group"
        }
        
    except Exception as e:
        db.rollback()
        raise CustomException().bad_request_exception()

def update_member_role_service(
    db: Session,
    room_id: int,
    admin_id: int,
    request: UpdateMemberRoleRequest
) -> dict:
    """Update member role (admin only)"""
    try:
        # Check if room exists and is a group
        room = db.query(ChatRoom).filter(
            ChatRoom.id == room_id,
            ChatRoom.is_group == True
        ).first()
        
        if not room:
            raise CustomException(content={"error": "Group chat not found"}).bad_request_exception()
        
        # Check if admin is actually an admin
        admin_member = db.query(ChatRoomMember).filter(
            ChatRoomMember.room_id == room_id,
            ChatRoomMember.user_id == admin_id,
            ChatRoomMember.role == MemberRoleEnum.ADMIN,
            ChatRoomMember.status == MemberStatusEnum.ACCEPTED
        ).first()
        
        if not admin_member:
            raise CustomException(content={"error": "Only admins can update member roles"}).bad_request_exception()
        
        # Check if target user is a member
        target_member = db.query(ChatRoomMember).filter(
            ChatRoomMember.room_id == room_id,
            ChatRoomMember.user_id == request.user_id,
            ChatRoomMember.status == MemberStatusEnum.ACCEPTED
        ).first()
        
        if not target_member:
            raise CustomException(content={"error": "User is not a member of this group"}).bad_request_exception()
        
        # Update role
        target_member.role = request.role
        db.commit()
        
        return {
            "message": f"User {request.user_id} role updated to {request.role}"
        }
        
    except Exception as e:
        db.rollback()
        raise CustomException().bad_request_exception()

def get_group_members_service(
    db: Session,
    room_id: int,
    user_id: int,
    status: Optional[str] = None
) -> List[ChatRoomMember]:
    """Get all members of a group chat, optionally filter by status"""
    # Check if user là member của room (bất kỳ trạng thái nào)
    member = db.query(ChatRoomMember).filter(
        ChatRoomMember.room_id == room_id,
        ChatRoomMember.user_id == user_id
    ).first()
    
    if not member:
        raise CustomException(content={"error": "You are not a member of this group"}).bad_request_exception()
    
    query = db.query(ChatRoomMember).filter(ChatRoomMember.room_id == room_id)
    if status:
        query = query.filter(ChatRoomMember.status == status)
    members = query.options(
        joinedload(ChatRoomMember.user).load_only(
            User.id,
            User.user_name,
            User.avatar,
            User.email
        ),
        joinedload(ChatRoomMember.user).joinedload(User.profile).load_only(
            UserProfile.full_name
        ),
        joinedload(ChatRoomMember.user).joinedload(User.business_profile).load_only(
            BusinessProfile.business_name,
            BusinessProfile.business_address,
            BusinessProfile.industry_tags
        )
    ).all()
    return members

def update_member_nickname_service(
    db: Session,
    room_id: int,
    user_id: int,
    request: UpdateMemberNicknameRequest
) -> dict:
    # Check if user is member of the room
    user_member = db.query(ChatRoomMember).filter(
        ChatRoomMember.room_id == room_id,
        ChatRoomMember.user_id == user_id,
        ChatRoomMember.status == MemberStatusEnum.ACCEPTED
    ).first()
    
    if not user_member:
        raise CustomException(content={"error": "You are not a member of this room"}).bad_request_exception()
    
    # Find the member to update
    member = db.query(ChatRoomMember).filter(
        ChatRoomMember.room_id == room_id,
        ChatRoomMember.user_id == request.user_id,
        ChatRoomMember.status == MemberStatusEnum.ACCEPTED
    ).first()
    
    if not member:
        raise CustomException(content={"error": "Member not found in this room"}).bad_request_exception()
    
    # Update nickname
    member.nick_name = request.nick_name
    db.commit()
    db.refresh(member)
    
    return {
        "message": "Nickname updated successfully",
        "member": member
    }

def update_room_theme_service(
    db: Session,
    room_id: int,
    user_id: int,
    request: UpdateRoomThemeRequest
) -> dict:
    # Check if user is member of room
    member = db.query(ChatRoomMember).filter(
        ChatRoomMember.room_id == room_id,
        ChatRoomMember.user_id == user_id,
        ChatRoomMember.status == MemberStatusEnum.ACCEPTED
    ).first()
    
    if not member:
        raise CustomException(content={"error": "Room not found or you don't have access"}).bad_request_exception()
    
    # Get room
    room = db.query(ChatRoom).filter(ChatRoom.id == room_id).first()
    if not room:
        raise CustomException(content={"error": "Room not found"}).bad_request_exception()
    
    # Update theme
    room.theme = request.theme
    db.commit()
    db.refresh(room)
    
    return {
        "message": "Room theme updated successfully",
        "room": room
    }

def group_invite_action_service(
    db: Session,
    room_id: int,
    user_id: int,
    action: GroupInviteActionEnum
) -> dict:
    # Kiểm tra member có trong room không
    member = db.query(ChatRoomMember).filter(
        ChatRoomMember.room_id == room_id,
        ChatRoomMember.user_id == user_id
    ).first()
    if not member:
        raise CustomException(content={"error": "You are not a member of this group"}).bad_request_exception()

    # Nếu đã ACCEPTED hoặc BLOCKED thì không cho thao tác lại
    if member.status == MemberStatusEnum.ACCEPTED:
        raise CustomException(content={"error": "You have already joined this group."}).bad_request_exception()
    if member.status == MemberStatusEnum.BLOCKED:
        raise CustomException(content={"error": "You have already blocked this group."}).bad_request_exception()

    if action == GroupInviteActionEnum.ACCEPT:
        member.status = MemberStatusEnum.ACCEPTED
        db.commit()
        return {"message": "You have joined this group."}
    elif action == GroupInviteActionEnum.REJECT:
        member.status = MemberStatusEnum.REJECTED
        db.commit()
        return {"message": "You have rejected this group."}
    elif action == GroupInviteActionEnum.BLOCK:
        member.status = MemberStatusEnum.BLOCKED
        db.commit()
        return {"message": "You have blocked this group."}
    

def leave_group_service(
    db: Session,
    room_id: int,
    user_id: int,
    new_admin_id: Optional[int] = None
) -> dict:
    # Kiểm tra user có phải thành viên group không
    member = db.query(ChatRoomMember).filter(
        ChatRoomMember.room_id == room_id,
        ChatRoomMember.user_id == user_id,
        ChatRoomMember.status == MemberStatusEnum.ACCEPTED
    ).first()
    if not member:
        raise CustomException(content={"error": "You are not a member of this group."}).bad_request_exception()

    # Kiểm tra group có phải group chat không
    room = db.query(ChatRoom).filter(ChatRoom.id == room_id, ChatRoom.is_group == True).first()
    if not room:
        raise CustomException(content={"error": "Group chat not found."}).bad_request_exception()

    # Lấy danh sách thành viên còn lại (trừ user hiện tại)
    other_members = db.query(ChatRoomMember).filter(
        ChatRoomMember.room_id == room_id,
        ChatRoomMember.user_id != user_id,
        ChatRoomMember.status == MemberStatusEnum.ACCEPTED
    ).all()

    # Nếu user là admin
    if member.role == MemberRoleEnum.ADMIN:
        if len(other_members) == 0:
            # Chỉ còn 1 mình admin, cho phép rời group, group không còn admin
            db.delete(member)
            db.commit()
            return {"message": "You have left the group. The group now has no admin."}
        else:
            # Còn nhiều thành viên, bắt buộc phải chuyển quyền admin
            if not new_admin_id:
                raise CustomException(content={"error": "You must select a new admin before leaving the group."}).bad_request_exception()
            new_admin = db.query(ChatRoomMember).filter(
                ChatRoomMember.room_id == room_id,
                ChatRoomMember.user_id == new_admin_id,
                ChatRoomMember.status == MemberStatusEnum.ACCEPTED
            ).first()
            if not new_admin or new_admin.user_id == user_id:
                raise CustomException(content={"error": "Invalid new admin selection."}).bad_request_exception()
            # Chuyển quyền admin
            new_admin.role = MemberRoleEnum.ADMIN
            db.delete(member)
            db.commit()
            return {"message": "Admin rights transferred. You have left the group."}
    else:
        # Không phải admin, chỉ cần xóa khỏi group
        db.delete(member)
        db.commit()
        return {"message": "You have left the group chat."}

def update_group_info_service(
    db: Session,
    room_id: int,
    user_id: int,
    name: Optional[str] = None,
    room_avatar: Optional[str] = None
) -> dict:
    # Lấy group chat
    room = db.query(ChatRoom).filter(ChatRoom.id == room_id, ChatRoom.is_group == True).first()
    if not room:
        raise CustomException(content={"error": "Group chat not found."}).bad_request_exception()

    # Kiểm tra quyền admin/creator
    member = db.query(ChatRoomMember).filter(
        ChatRoomMember.room_id == room_id,
        ChatRoomMember.user_id == user_id,
        ChatRoomMember.status == MemberStatusEnum.ACCEPTED
    ).first()
    if not member or (member.role != MemberRoleEnum.ADMIN and room.created_by != user_id):
        raise CustomException(content={"error": "You do not have permission to edit this group."}).bad_request_exception()

    updated = False
    # Cập nhật tên nhóm
    if name is not None:
        name = name.strip()
        if not name:
            raise CustomException(content={"error": "Group name cannot be empty."}).bad_request_exception()
        if len(name) > 50:
            raise CustomException(content={"error": "Group name must be <= 50 characters."}).bad_request_exception()
        room.name = name
        updated = True
    # Cập nhật avatar nhóm
    if room_avatar is not None:
        # Ở đây giả sử client đã upload lên storage và truyền url, hoặc truyền base64 thì cần xử lý lưu file, resize...
        # Để đơn giản, chỉ nhận url hoặc base64, nếu cần xử lý ảnh thực tế sẽ bổ sung sau
        room.room_avatar = room_avatar
        updated = True
    if not updated:
        return {"success": False, "message": "No changes provided."}
    db.commit()
    # Gửi system message cho group (giả sử có hàm send_system_message)
    try:
        from .socket import send_system_message
        user = db.query(User).filter(User.id == user_id).first()
        display_name = user.profile.full_name if user and user.profile and user.profile.full_name else user.email
        msg = f"Group info updated by {display_name}."
        send_system_message(db, room_id, msg)
    except Exception:
        pass
    return {"success": True, "message": "Group info updated successfully.", "name": room.name, "room_avatar": room.room_avatar}

PaginatedMessageResponse = PaginatedResponse[MessageChatSchemas]
PaginatedChatRoomResponse = PaginatedResponse[ChatRoomRead]