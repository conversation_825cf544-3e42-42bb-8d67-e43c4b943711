from fastapi import APIRouter, Depends, Query
from sqlalchemy.orm import Session
from typing import Optional
from src.utils.database import get_db
from src.user.models import User
from . import service
from .schemas import (
    ChatRoomCreate, AddMembersRequest, RemoveMemberRequest, UpdateMemberRoleRequest, UpdateMemberNicknameRequest, UpdateRoomThemeRequest, GroupInviteActionRequest, LeaveGroupRequest, UpdateGroupInfoRequest
)
from src.utils.permission import is_authenticated
from src.user.models import User as UserModel
from src.utils.token_docs import jwt_auth
from ..utils.response import *

router = APIRouter()

tags = ['Chat']

@router.post("/rooms/", tags=tags, **create_data_response, dependencies=[Depends(jwt_auth)])
def create_chat_room(
    room_data: ChatRoomCreate,
    db: Session = Depends(get_db),
    current_user: UserModel = Depends(is_authenticated),
):
    """
    Create a new chat roomtr
    """
    result = service.create_chat_room_service(
        db=db,
        user_id=current_user.id,
        room_data=room_data
    )
    return CustomResponse(content=result).format_data_create()
 

@router.get("/rooms/", tags=tags, **get_data_response, dependencies=[Depends(jwt_auth)])
def get_user_rooms(
    page: int = Query(1, ge=1),
    size: int = Query(10, ge=1, le=100),
    status: Optional[str] = Query(None, description="Filter by member status (ACCEPTED, PENDING, BLOCKED, REJECTED)"),
    db: Session = Depends(get_db),
    current_user: UserModel = Depends(is_authenticated),
):
    """
    Get list of chat rooms for current user, optionally filter by member status
    """
    result = service.get_user_rooms_service(
        db=db,
        user_id=current_user.id,
        page=page,
        size=size,
        status=status
    )
    return CustomResponse(content=result).format_data_get()

@router.get("/rooms/{room_id}/messages/", tags=tags, **get_data_response, dependencies=[Depends(jwt_auth)])
def get_room_messages(
    room_id: int,
    page: int = Query(1, ge=1),
    size: int = Query(50, ge=1, le=100),
    db: Session = Depends(get_db),
    current_user: UserModel = Depends(is_authenticated),
):
    """
    Get messages in a chat room
    """
    result = service.get_room_messages_service(
        db=db,
        room_id=room_id,
        user_id=current_user.id,
        page=page,
        size=size
    ) 
    return CustomResponse(content=result).format_data_get()

@router.post("/rooms/{room_id}/members/", tags=tags, **create_data_response, dependencies=[Depends(jwt_auth)])
def add_members_to_group(
    room_id: int,
    request: AddMembersRequest,
    db: Session = Depends(get_db),
    current_user: UserModel = Depends(is_authenticated),
):
    """
    Add members to a group chat
    """
    result = service.add_members_to_group_service(
        db=db,
        room_id=room_id,
        user_id=current_user.id,
        request=request
    )
    return CustomResponse(content=result).format_data_create()

@router.delete("/rooms/{room_id}/members/", tags=tags, **delete_data_response, dependencies=[Depends(jwt_auth)])
def remove_member_from_group(
    room_id: int,
    request: RemoveMemberRequest,
    db: Session = Depends(get_db),
    current_user: UserModel = Depends(is_authenticated),
):
    """
    Remove a member from group chat (admin only)
    """
    result = service.remove_member_from_group_service(
        db=db,
        room_id=room_id,
        admin_id=current_user.id,
        request=request
    )
    return CustomResponse(content=result).format_data_delete()

@router.put("/rooms/{room_id}/members/role/", tags=tags, **update_data_response, dependencies=[Depends(jwt_auth)])
def update_member_role(
    room_id: int,
    request: UpdateMemberRoleRequest,
    db: Session = Depends(get_db),
    current_user: UserModel = Depends(is_authenticated),
):
    """
    Update member role in group chat (admin only)
    """
    result = service.update_member_role_service(
        db=db,
        room_id=room_id,
        admin_id=current_user.id,
        request=request
    )
    return CustomResponse(content=result).format_data_update()

@router.get("/rooms/{room_id}/members/", tags=tags, **get_data_response, dependencies=[Depends(jwt_auth)])
def get_group_members(
    room_id: int,
    status: Optional[str] = Query(None, description="Filter by member status (ACCEPTED, PENDING, REJECTED, BLOCKED)"),
    db: Session = Depends(get_db),
    current_user: UserModel = Depends(is_authenticated),
):
    """
    Get all members of a group chat, optionally filter by status
    """
    result = service.get_group_members_service(
        db=db,
        room_id=room_id,
        user_id=current_user.id,
        status=status
    )
    return CustomResponse(content=result).format_data_get()

@router.put("/rooms/{room_id}/members/nickname/", tags=tags, **update_data_response, dependencies=[Depends(jwt_auth)])
def update_member_nickname(
    room_id: int,
    request: UpdateMemberNicknameRequest,
    db: Session = Depends(get_db),
    current_user: UserModel = Depends(is_authenticated),
):
    """
    Update member nickname in chat room (any member can update any member's nickname)
    """
    result = service.update_member_nickname_service(
        db=db,
        room_id=room_id,
        user_id=current_user.id,
        request=request
    )
    return CustomResponse(content=result).format_data_update()

@router.put("/rooms/{room_id}/theme/", tags=tags, **update_data_response, dependencies=[Depends(jwt_auth)])
def update_room_theme(
    room_id: int,
    request: UpdateRoomThemeRequest,
    db: Session = Depends(get_db),
    current_user: UserModel = Depends(is_authenticated),
):
    """
    Update room theme (any member can update)
    """
    result = service.update_room_theme_service(
        db=db,
        room_id=room_id,
        user_id=current_user.id,
        request=request
    )
    return CustomResponse(content=result).format_data_update()

@router.put("/rooms/{room_id}/members/action/", tags=tags, **update_data_response, dependencies=[Depends(jwt_auth)])
def group_invite_action(
    room_id: int,
    request: GroupInviteActionRequest,
    db: Session = Depends(get_db),
    current_user: UserModel = Depends(is_authenticated),
):
    """
    Accept/Reject/Block group chat invitation (PENDING -> ACCEPTED/REJECTED/BLOCKED)
    """
    result = service.group_invite_action_service(
        db=db,
        room_id=room_id,
        user_id=current_user.id,
        action=request.action
    )
    return CustomResponse(content=result).format_data_update()

@router.put("/rooms/{room_id}/leave/", tags=tags, **update_data_response, dependencies=[Depends(jwt_auth)])
def leave_group(
    room_id: int,
    request: LeaveGroupRequest,
    db: Session = Depends(get_db),
    current_user: UserModel = Depends(is_authenticated),
):
    """
    Leave group chat. Nếu là admin và group còn nhiều người, phải chọn người chuyển quyền admin.
    """
    result = service.leave_group_service(
        db=db,
        room_id=room_id,
        user_id=current_user.id,
        new_admin_id=request.new_admin_id
    )
    return CustomResponse(content=result).format_data_update()

@router.put("/rooms/{room_id}/info/", tags=tags, **update_data_response, dependencies=[Depends(jwt_auth)])
def update_group_info(
    room_id: int,
    request: UpdateGroupInfoRequest,
    db: Session = Depends(get_db),
    current_user: UserModel = Depends(is_authenticated),
):
    """
    Update group name và avatar (chỉ admin hoặc creator được phép).
    """
    result = service.update_group_info_service(
        db=db,
        room_id=room_id,
        user_id=current_user.id,
        name=request.name,
        room_avatar=request.room_avatar
    )
    return CustomResponse(content=result).format_data_update()
