from pydantic import Field, BaseModel
from typing import List, Optional
from datetime import datetime
from src.utils.schemas import PaginatedResponse
from .models import MessageType<PERSON>num, MemberRoleEnum, MemberStatusEnum, GroupInviteActionEnum

class MessageBase(BaseModel):
    content: str
    message_type: MessageTypeEnum = MessageTypeEnum.TEXT

class MessageCreate(MessageBase):
    room_id: int

class UserProfileInfo(BaseModel):
    full_name: Optional[str] = None
    title: Optional[str] = None
    gender: Optional[str] = None
    date_of_birth: Optional[str] = None
    social_media_links: Optional[dict] = None
    bio: Optional[str] = None
    notes: Optional[str] = None
    public_profile: Optional[bool] = None
    address: Optional[str] = None
    lattitude: Optional[float] = None
    longitude: Optional[float] = None
    is_visibled_map: Optional[bool] = None
    skill_interests: Optional[dict] = None
    industry_tags: Optional[dict] = None
    enabled_availability: Optional[bool] = None
    availability: Optional[dict] = None

    class Config:
        from_attributes = True

class BusinessProfileInfo(BaseModel):
    business_name: Optional[str] = None
    business_address: Optional[str] = None
    business_description: Optional[str] = None

    class Config:
        from_attributes = True

class UserBasicInfo(BaseModel):
    id: int
    user_name: Optional[str] = None
    avatar: Optional[str] = None
    email: Optional[str] = None
    profile: Optional[UserProfileInfo] = None
    business_profile: Optional[BusinessProfileInfo] = None
    full_name: Optional[str] = None
    
    class Config:
        from_attributes = True

class MessageChatSchemas(MessageBase):
    id: int
    room_id: int
    sender: UserBasicInfo
    created_at: datetime

    class Config:
        from_attributes = True

class ChatRoomMemberRead(BaseModel):
    id: int
    room_id: int
    user_id: int
    user: UserBasicInfo
    role: MemberRoleEnum
    status: MemberStatusEnum
    joined_at: datetime
    invited_by: Optional[int] = None
    invited_at: Optional[datetime] = None
    accepted_at: Optional[datetime] = None
    nick_name: Optional[str] = None

    class Config:
        from_attributes = True

class ChatRoomBase(BaseModel):
    name: Optional[str] = None
    is_group: bool = False
    room_name: Optional[str] = None
    room_avatar: Optional[str] = None
    theme: Optional[str] = "default"

class ChatRoomCreate(ChatRoomBase):
    member_ids: List[int] = Field(..., description="List of user IDs to add to room")

class ChatRoomRead(ChatRoomBase):
    id: int
    created_by: int
    created_at: datetime
    members: List[ChatRoomMemberRead]
    status: Optional[str] = Field(
        None,
        description="Trạng thái của user hiện tại trong phòng (ACCEPTED, PENDING, BLOCKED, REJECTED)",
        example="ACCEPTED"
    )

    class Config:
        from_attributes = True

class ChatRoomDetail(ChatRoomRead):
    messages: List[MessageChatSchemas]

    class Config:
        from_attributes = True

# New schemas for add/remove members
class AddMembersRequest(BaseModel):
    member_ids: List[int] = Field(..., description="List of user IDs to add to group")

class RemoveMemberRequest(BaseModel):
    user_id: int = Field(..., description="User ID to remove from group")

class UpdateMemberRoleRequest(BaseModel):
    user_id: int = Field(..., description="User ID to update role")
    role: MemberRoleEnum = Field(..., description="New role for the user")

class UpdateMemberNicknameRequest(BaseModel):
    user_id: int = Field(..., description="User ID to update nickname")
    nick_name: Optional[str] = Field(None, description="New nickname for the user in this room")

class UpdateRoomThemeRequest(BaseModel):
    theme: str = Field(..., description="New theme for the room")

class MemberInvitationResponse(BaseModel):
    success: bool
    message: str
    added_members: List[UserBasicInfo]
    failed_members: List[dict]  # List of {user_id, reason}

class PaginatedMessageResponse(PaginatedResponse[MessageChatSchemas]):
    pass

class PaginatedChatRoomResponse(PaginatedResponse[ChatRoomRead]):
    pass

class TypingPayload(BaseModel):
    room_id: int
    user_id: int
    is_typing: bool

class SeenPayload(BaseModel):
    room_id: int
    user_id: int
    message_id: Optional[int] = None

class UserStatusPayload(BaseModel):
    user_id: int
    is_online: bool
    last_seen: Optional[datetime] = None

class RoomUpdatePayload(BaseModel):
    room_id: int
    last_message: Optional[MessageChatSchemas] = None
    unread_count: int = 0

class GroupInviteActionRequest(BaseModel):
    action: GroupInviteActionEnum

class GroupInviteActionResponse(BaseModel):
    success: bool
    message: str

class LeaveGroupRequest(BaseModel):
    new_admin_id: Optional[int] = Field(None, description="ID thành viên mới sẽ làm admin (bắt buộc nếu user hiện tại là admin và group còn nhiều người)")

class LeaveGroupResponse(BaseModel):
    success: bool
    message: str

class UpdateGroupInfoRequest(BaseModel):
    name: Optional[str] = Field(None, max_length=50, description="Tên nhóm (tối đa 50 ký tự)")
    room_avatar: Optional[str] = Field(None, description="URL hoặc base64 ảnh mới (JPG/PNG, tối đa 2MB, tỉ lệ vuông)")

class UpdateGroupInfoResponse(BaseModel):
    success: bool
    message: str
    name: Optional[str] = None
    room_avatar: Optional[str] = None 