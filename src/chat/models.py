from sqlalchemy import <PERSON>um<PERSON>, <PERSON><PERSON><PERSON>, Foreign<PERSON>ey, String, DateTime, Boolean, Enum, Text
from sqlalchemy.orm import relationship
from datetime import datetime
import enum
from src.utils.base_model import BaseModel, Base

class MessageTypeEnum(str, enum.Enum):
    TEXT = "TEXT"
    FILE = "FILE"

class MemberRoleEnum(str, enum.Enum):
    ADMIN = "ADMIN"
    MEMBER = "MEMBER"

class MemberStatusEnum(str, enum.Enum):
    PENDING = "PENDING"
    ACCEPTED = "ACCEPTED"
    REJECTED = "REJECTED"
    BLOCKED = "BLOCKED"

class GroupInviteActionEnum(str, enum.Enum):
    ACCEPT = "ACCEPT"
    REJECT = "REJECT"
    BLOCK = "BLOCK"

class ChatRoom(BaseModel, Base):
    __tablename__ = "chat_room"

    id = Column(Integer, primary_key=True)
    name = Column(String(255), nullable=True)
    is_group = Column(<PERSON><PERSON>an, default=False)
    created_by = Column(Integer, ForeignKey("user.id"), nullable=False)
    created_at = Column(DateTime, default=datetime.now)
    room_name = Column(String(255), nullable=True)
    room_avatar = Column(String(255), nullable=True)
    theme = Column(String(50), nullable=True, default="default")  # Theme for chat room
    # Relationships
    creator = relationship("User", foreign_keys=[created_by], back_populates="created_rooms", lazy="select")
    members = relationship("ChatRoomMember", back_populates="room", lazy="select")
    messages = relationship("Message", back_populates="room", lazy="select")

class ChatRoomMember(BaseModel, Base):
    __tablename__ = "chat_room_member"

    id = Column(Integer, primary_key=True)
    room_id = Column(Integer, ForeignKey("chat_room.id"), nullable=False)
    user_id = Column(Integer, ForeignKey("user.id"), nullable=False)
    role = Column(Enum(MemberRoleEnum), default=MemberRoleEnum.MEMBER)
    status = Column(Enum(MemberStatusEnum), default=MemberStatusEnum.ACCEPTED)
    joined_at = Column(DateTime, default=datetime.now)
    invited_by = Column(Integer, ForeignKey("user.id"), nullable=True)
    invited_at = Column(DateTime, nullable=True)
    accepted_at = Column(DateTime, nullable=True)
    nick_name = Column(String(100), nullable=True)  # Custom nickname for this member in this room
    created_at = Column(DateTime, default=datetime.now)

    # Relationships
    room = relationship("ChatRoom", back_populates="members", lazy="select")
    user = relationship("User", foreign_keys=[user_id], back_populates="chat_rooms", lazy="select")
    inviter = relationship("User", foreign_keys=[invited_by], back_populates="invited_to_rooms", lazy="select")

class Message(BaseModel, Base):
    __tablename__ = "message"

    id = Column(Integer, primary_key=True)
    room_id = Column(Integer, ForeignKey("chat_room.id"), nullable=False)
    sender_id = Column(Integer, ForeignKey("user.id"), nullable=False)
    content = Column(Text, nullable=False)
    message_type = Column(Enum(MessageTypeEnum), default=MessageTypeEnum.TEXT)
    created_at = Column(DateTime, default=datetime.now)

    # Relationships
    room = relationship("ChatRoom", back_populates="messages", lazy="select")
    sender = relationship("User", back_populates="messages", lazy="select") 