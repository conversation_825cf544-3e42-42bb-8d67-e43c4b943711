from sqlalchemy.orm import Session
from sqlalchemy import select
from src.utils.exceptions import CustomException
from src.ticket_sale.models import TicketOrder, PaymentStatusEnum
from .models import Transaction, Invoice, Receipt
from .schemas import TransactionCreate, InvoiceCreate, ReceiptCreate
import uuid

def create_transaction_service(db: Session, data: TransactionCreate) -> Transaction:
    try:
        transaction = Transaction(
            amount=data.amount,
            payment_method=data.payment_method,
        )
        db.add(transaction)
        db.flush()
        orders = db.execute(
            select(TicketOrder).where(TicketOrder.id.in_(data.ticket_order_ids))
        ).scalars().all()
        for order in orders:
            transaction.ticket_orders.append(order)
            order.payment_status = PaymentStatusEnum.PAID
        db.commit()
        db.refresh(transaction)
        return transaction
    except Exception:
        db.rollback()
        raise CustomException().bad_request_exception()


def create_invoice_service(db: Session, data: InvoiceCreate) -> Invoice:
    try:
        invoice = Invoice(
            user_id=data.user_id,
            total_amount=data.total_amount,
            uuid=str(uuid.uuid4()),
        )
        db.add(invoice)
        db.flush()
        orders = db.execute(
            select(TicketOrder).where(TicketOrder.id.in_(data.ticket_order_ids))
        ).scalars().all()
        for order in orders:
            invoice.ticket_orders.append(order)
        db.commit()
        db.refresh(invoice)
        return invoice
    except Exception:
        db.rollback()
        raise CustomException().bad_request_exception()


def create_receipt_service(db: Session, data: ReceiptCreate) -> Receipt:
    try:
        transaction = db.get(Transaction, data.transaction_id)
        if not transaction:
            raise CustomException().not_found_exception(message="Transaction not found")
        receipt = Receipt(
            transaction_id=data.transaction_id,
            amount=data.amount,
            uuid=str(uuid.uuid4()),
        )
        db.add(receipt)
        db.commit()
        db.refresh(receipt)
        return receipt
    except Exception:
        db.rollback()
        raise CustomException().bad_request_exception()
