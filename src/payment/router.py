from fastapi import APIRouter, Depends
from sqlalchemy.orm import Session
from src.utils.database import get_db
from src.utils.permission import is_authenticated
from src.utils.token_docs import jwt_auth
from src.utils.response import CustomResponse
from src.user.models import User
from .schemas import TransactionCreate, TransactionResponse, InvoiceCreate, InvoiceResponse, ReceiptCreate, ReceiptResponse
from .service import create_transaction_service, create_invoice_service, create_receipt_service

router = APIRouter(prefix="", tags=["Payment"])


@router.post("/transaction", response_model=TransactionResponse, dependencies=[Depends(jwt_auth)])
def create_transaction(
    data: TransactionCreate,
    db: Session = Depends(get_db),
    current_user: User = Depends(is_authenticated)
):
    result = create_transaction_service(db, data)
    return CustomResponse(content=result).format_data_create()


@router.post("/invoice", response_model=InvoiceResponse, dependencies=[Depends(jwt_auth)])
def create_invoice(
    data: InvoiceCreate,
    db: Session = Depends(get_db),
    current_user: User = Depends(is_authenticated)
):
    result = create_invoice_service(db, data)
    return CustomResponse(content=result).format_data_create()


@router.post("/receipt", response_model=ReceiptResponse, dependencies=[Depends(jwt_auth)])
def create_receipt(
    data: ReceiptCreate,
    db: Session = Depends(get_db),
    current_user: User = Depends(is_authenticated)
):
    result = create_receipt_service(db, data)
    return CustomResponse(content=result).format_data_create()
