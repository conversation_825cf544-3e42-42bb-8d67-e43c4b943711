from datetime import datetime
from sqlalchemy import Column, Integer, Float, String, DateTime, ForeignKey, Table, JSON
from sqlalchemy.orm import relationship
from src.utils.base_model import BaseModel, Base

# Association tables

ticket_order_transaction = Table(
    "ticket_order_transaction",
    Base.metadata,
    Column("ticket_order_id", ForeignKey("ticket_order.id"), primary_key=True),
    Column("transaction_id", ForeignKey("transaction.id"), primary_key=True),
)

invoice_ticket_order = Table(
    "invoice_ticket_order",
    Base.metadata,
    Column("invoice_id", ForeignKey("invoice.id"), primary_key=True),
    Column("ticket_order_id", ForeignKey("ticket_order.id"), primary_key=True),
)


class Transaction(BaseModel, Base):
    __tablename__ = "transaction"

    amount = Column(Float, nullable=False)
    payment_method = Column(String(50), nullable=False)
    transaction_date = Column(DateTime, default=datetime.utcnow)
    payload = Column(JSON, nullable=True)
    ticket_orders = relationship(
        "TicketOrder",
        secondary=ticket_order_transaction,
        back_populates="transactions",
    )
    receipts = relationship("Receipt", back_populates="transaction")


class Invoice(BaseModel, Base):
    __tablename__ = "invoice"

    user_id = Column(Integer, ForeignKey("user.id"), nullable=False)
    total_amount = Column(Float, nullable=False)
    issued_at = Column(DateTime, default=datetime.utcnow)
    payload = Column(JSON, nullable=True)
    uuid = Column(String(36), nullable=False, unique=True)
    ticket_orders = relationship(
        "TicketOrder",
        secondary=invoice_ticket_order,
        back_populates="invoices",
    )


class Receipt(BaseModel, Base):
    __tablename__ = "receipt"

    transaction_id = Column(Integer, ForeignKey("transaction.id"), nullable=False)
    amount = Column(Float, nullable=False)
    issued_at = Column(DateTime, default=datetime.utcnow)
    payload = Column(JSON, nullable=True)
    uuid = Column(String(36), nullable=False, unique=True)
    transaction = relationship("Transaction", back_populates="receipts")
