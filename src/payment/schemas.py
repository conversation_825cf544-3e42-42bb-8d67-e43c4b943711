from datetime import datetime
from typing import List
from pydantic import Field
from src.utils.auto_schema import AutoSchema


class TransactionCreate(AutoSchema):
    amount: float = Field(..., example=100)
    payment_method: str = Field(..., example="CARD")
    ticket_order_ids: List[int] = Field(..., example=[1])


class TransactionResponse(AutoSchema):
    id: int
    amount: float
    payment_method: str
    transaction_date: datetime

    class Config:
        from_attributes = True


class InvoiceCreate(AutoSchema):
    user_id: int = Field(..., example=1)
    ticket_order_ids: List[int] = Field(..., example=[1])
    total_amount: float = Field(..., example=100)


class InvoiceResponse(AutoSchema):
    id: int
    user_id: int
    total_amount: float
    issued_at: datetime

    class Config:
        from_attributes = True


class ReceiptCreate(AutoSchema):
    transaction_id: int = Field(..., example=1)
    amount: float = Field(..., example=100)


class ReceiptResponse(AutoSchema):
    id: int
    transaction_id: int
    amount: float
    issued_at: datetime

    class Config:
        from_attributes = True
