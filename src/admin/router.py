from src.utils.database import get_db
from src.utils.token_docs import jwt_auth
from src.user.models import User as UserModel
from fastapi import APIRouter, Depends, Request, Security
from sqlalchemy.orm import Session
from src.utils.permission import check_security_scope, is_authenticated, is_admin
from ..utils.response import *
from src.admin.schemas import MapBIBForSTRSchemas, WebhookForTestSchemas
from src.admin.service import mapping_user_with_bib_STR_service
router = APIRouter()
tags = ['Admin']

@router.post("/str/mapbib", tags=tags, **create_data_response, dependencies=[Depends(jwt_auth)])
def map_bib_str(
    data: MapBIBForSTRSchemas,
    db: Session = Depends(get_db),
    current_user: UserModel = Depends(is_admin)
):
    data.model_validator(db)
    mapping_user_with_bib_STR_service(db, data)
    return CustomResponse().format_data_create()

from fastapi import FastAPI, Body
from typing import Optional, Any

@router.post("/webhook")
def webhook_test(
    data: Optional[Any] = Body(None)

):
    print('data webhook', data)
    return CustomResponse().format_data_get()

@router.get("/webhook")
def webhook_test():
    print('health check webhook')
    return CustomResponse().format_data_get()