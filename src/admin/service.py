from sqlalchemy.orm import Session
from src.utils.exceptions import CustomException
from src.user.models import User as UserModel
from src.admin.schemas import MapBIBForSTRSchemas

def mapping_user_with_bib_STR_service(db: Session, data: MapBIBForSTRSchemas):
    pass
#     user = check_exist_user(db, data.phone_number)
#     if not user:
#         pwd = f"STR{data.phone_number}#"
#         pwd_hash = get_password_hash(pwd)
#         new_user = UserModel(
#             password = pwd_hash,
#             phone_number=data.phone_number,
#             is_active=True,
#             avatar = data.avatar
#         )
#         db.add(new_user)
#         db.flush()
        
#         new_profile = UserProfileModel(
#             user_id=new_user.id,
#         )
#         db.add(new_profile)
#         db.flush()
        
#         new_bib = BibModel(
#             bib_code = data.bib,
#             is_used = True,
#             event_id = data.event_id
#         )
#         db.add(new_bib)
#         db.flush()
#         new_runner_information = RunnerInformationModel(
#             user_id=new_user.id,
#             bib_id = new_bib.id
#         )
#         db.add(new_runner_information)
#         db.flush()
#     else:
#         new_bib = BibModel(
#             bib_code = data.bib,
#             is_used = True,
#             event_id = data.event_id
#         )
#         db.add(new_bib)
#         db.flush()

#         runner_information = db.query(RunnerInformationModel).\
#             join(UserModel, RunnerInformationModel.user_id == UserModel.id).\
#             join(TicketDetailModel, RunnerInformationModel.ticket_detail_id == TicketDetailModel.id).\
#             join(TicketModel, TicketDetailModel.ticket_id == TicketModel.id).\
#             join(DistanceRunningEventModel, TicketModel.distance_running_event_id == DistanceRunningEventModel.id).\
#             join(EventModel, DistanceRunningEventModel.event_id == EventModel.id).\
#             filter(EventModel.id == data.event_id, UserModel.phone_number == data.phone_number).first()
#         if not runner_information:
#             new_runner_information = RunnerInformationModel(
#                 user_id = user.id, 
#                 bib_id = new_bib.id
#             )
#             db.add(new_runner_information)
#         else:
#             runner_information.bib_id = new_bib.id
#         user.avatar = data.avatar
#         db.add(user)
#     try:
#         db.commit()
#     except Exception as e:
#         db.rollback()
#     finally:
#         db.close()

