from typing import  List, Union
from pydantic import Field, EmailStr, field_validator
from datetime import datetime, timezone, date
from src.user.models import User as UserModel
from sqlalchemy.orm import Session
from src.utils.exceptions import CustomException
from src.event.models import Event as EventModel
from pydantic import BaseModel

class MapBIBForSTRSchemas(BaseModel):
    bib: str = Field(
        title = "bib for mapping"
    )

    phone_number: str = Field(
        title = "phone number for mapping"
    )
    avatar: Union[str, None] = Field(
        title = "avatar of User"
    )
    event_id: int = Field(
        title = "id of event for mapping bib"
    )
    def model_validator(self, db: Session):
        errors = {}

        if self.event_id is not None:
            event_exist = db.query(EventModel).filter(
                (EventModel.id == self.event_id)).first()
            if not event_exist:
                errors['event'] = ['Invalid event'] 

        if errors.keys():
            raise CustomException(content=errors).bad_request_exception()

        return self

class WebhookForTestSchemas(BaseModel):
    pass