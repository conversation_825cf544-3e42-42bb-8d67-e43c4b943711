from fastapi import APIRouter, Depends, Request, Security
from sqlalchemy.orm import Session
from src.utils.common import format_response
from src.utils.database import get_db
from ..utils.response import *
from src.utils.response import CustomResponse
from src.user.schemas import (
    UserSettingsSchema,
    VerifyRegisterInfo, 
    RegisterUserBase, 
    ForgotPasswordBase, 
    ChangePasswordSchemas,
    NotificationSettingsBase,
    SubscriptionSettingsBase,
    WebLinkSettingsBase,
    UpdateAllSettingsBase,
    MyProfileUpdate,
    BusinessProfileUpdate,
    AvailabilityUpdateSchema,
    OTPCreateSchema,
    OTPVerifySchema,
    ChangeEmailRequestSchema,
    ChangeEmailVerifySchema,
    ChangePhoneRequestSchema,
    ChangePhoneVerifySchema,
)
from src.user.service import (
    get_user_profile_by_link_service,
    register_user_service, 
    change_user_password_service,
    pwd_context, 
    update_password_service,
    update_notification_settings_service,
    update_subscription_settings_service,
    update_user_settings_service,
    update_web_link_settings_service,
    generate_profile_link_service,  
    update_all_settings_service,
    get_user_settings_service,
    update_user_profile_service,
    get_availability_service,
    update_availability_service,
    send_otp_service,
    get_user_profile_service,
    get_user_profile_code_service,
    verify_otp_service,
    resend_otp_service,
    active_user_service,
    check_user_exists_service,
    send_forgot_password_otp_service,
    send_change_email_otp_service,
    verify_change_email_otp_service,
    send_change_phone_otp_service,
    verify_change_phone_otp_service,
    delete_user_account_service,
)
from src.user.models import User as UserModel
from src.utils.permission import is_authenticated 
from src.utils.token_docs import jwt_auth
from src.utils.exceptions import CustomException

router = APIRouter()
tags = ['User']

@router.post("/verify-email", tags=tags, **check_data_response)
def verify_email(
    data: VerifyRegisterInfo,
    db: Session = Depends(get_db),
):
    result = check_user_exists_service(db, data.email)
    return CustomResponse(content=result).format_check_data()

@router.post("/register", tags = tags, **create_data_response)
def register_user(
    data: RegisterUserBase,
    db: Session = Depends(get_db)
):
    # data.model_validator(db)
    result = register_user_service(db, data)
    return CustomResponse(content=result).format_data_create()


    
@router.put("/forgot-pwd", tags = tags, **update_data_response)
def forgot_pwd(
    data: ForgotPasswordBase,
    db: Session = Depends(get_db),
):
    # validate_otp(data.phone_number, data.otp)
    change_user_password_service(db, data)

    return CustomResponse().format_data_update()

# @router.put("/firstlogin/update-avatar", tags = tags, **update_data_response, dependencies=[Depends(jwt_auth)])
# def update_avatar_firstlogin(
#     data: UpdateAvatarBase,
#     db: Session = Depends(get_db),
#     current_user: UserModel = Depends(is_authenticated)
# ):
#     update_avatar_firstlogin_service(db, data, current_user)
#     return CustomResponse().format_data_update()

@router.put("/change-pwd", tags = tags, **update_data_response, dependencies=[Depends(jwt_auth)])
def change_pwd(
    data: ChangePasswordSchemas,
    db: Session = Depends(get_db),
    current_user: UserModel = Depends(is_authenticated)
):
    is_valid = pwd_context.verify(data.current_password, current_user.password)
    if not is_valid:
        raise CustomException(content={"current_password": [
                              "Invalid current password"]}).bad_request_exception()
    update_password_service(db, data.new_password, current_user)
    return CustomResponse().format_data_update()

# @router.post("/search-user-with-image", tags = tags, **get_data_response)
# def search_user_with_image(
#     data: UpdateAvatarBase,
#     db: Session = Depends(get_db)
# ):
#     list_user_ids = find_user_with_image_service(db, data.avatar)
#     return CustomResponse(content = list_user_ids).format_data_get()

@router.get("/settings", dependencies=[Depends(jwt_auth)], tags=tags)
async def get_user_settings(
    current_user: UserModel = Depends(is_authenticated),
    db: Session = Depends(get_db)
):
    """Get all settings for current user"""
    result = await get_user_settings_service(db, current_user.id)
    return CustomResponse(content=result).format_data_get()

@router.put("/settings", dependencies=[Depends(jwt_auth)], tags=tags)
async def update_notification_settings(
    settings: UserSettingsSchema,
    current_user: UserModel = Depends(is_authenticated),
    db: Session = Depends(get_db)
):
    """Update settings for current user"""
    result = await update_user_settings_service(db, current_user.id, settings)
    return CustomResponse(content=result).format_data_update()

@router.put("/settings/notifications", dependencies=[Depends(jwt_auth)], tags=tags)
async def update_notification_settings(
    settings: NotificationSettingsBase,
    current_user: UserModel = Depends(is_authenticated),
    db: Session = Depends(get_db)
):
    """Update notification settings for current user"""
    result = await update_notification_settings_service(db, current_user.id, settings)
    return CustomResponse(content=result).format_data_update()

@router.put("/settings/subscription", dependencies=[Depends(jwt_auth)], tags=tags)
async def update_subscription_settings(
    settings: SubscriptionSettingsBase,
    current_user: UserModel = Depends(is_authenticated),
    db: Session = Depends(get_db)
):
    """Update subscription settings for current user"""
    result = await update_subscription_settings_service(db, current_user.id, settings)
    return CustomResponse(content=result).format_data_update()

@router.put("/settings/web-links", dependencies=[Depends(jwt_auth)], tags=tags)
async def update_web_link_settings(
    settings: WebLinkSettingsBase,
    current_user: UserModel = Depends(is_authenticated),
    db: Session = Depends(get_db)
):
    """Update web link settings for current user"""
    result = await update_web_link_settings_service(db, current_user.id, settings)
    return CustomResponse(content=result).format_data_update()

@router.post("/settings/generate-profile-link", dependencies=[Depends(jwt_auth)], tags=tags)
async def generate_profile_link(
    current_user: UserModel = Depends(is_authenticated),
    db: Session = Depends(get_db)
):
    """Generate new public profile link for current user"""
    result = await generate_profile_link_service(db, current_user.id)
    return CustomResponse(content=result).format_data_create()


@router.put("/settings/all", dependencies=[Depends(jwt_auth)], tags=tags)
async def update_all_settings(
    settings: UpdateAllSettingsBase,
    current_user: UserModel = Depends(is_authenticated),
    db: Session = Depends(get_db)
):
    """Update all settings for current user"""
    result = await update_all_settings_service(db, current_user.id, settings)
    return CustomResponse(content=result).format_data_update()

@router.get("/profile", dependencies=[Depends(jwt_auth)], tags=tags, **get_data_response)
def get_user_profile(
    current_user: UserModel = Depends(is_authenticated),
    db: Session = Depends(get_db)
):
    """Get current user's profile"""
    result = get_user_profile_service(db, current_user.id)
    return CustomResponse(content=result).format_data_get()

@router.put("/profile", dependencies=[Depends(jwt_auth)], tags=tags, **update_data_response)
def update_user_profile(
    profile_data: MyProfileUpdate,
    current_user: UserModel = Depends(is_authenticated),
    db: Session = Depends(get_db)
):
    """Update user profile"""
    result = update_user_profile_service(db, current_user.id, profile_data)
    return CustomResponse(content=result).format_data_update()

@router.get("/profile/{code}", dependencies=[Depends(jwt_auth)], tags=tags)
def get_user_profile_code(
    code: str,
    current_user: UserModel = Depends(is_authenticated),
    db: Session = Depends(get_db)
):
    """Get user profile code"""
    result = get_user_profile_code_service(db, code, current_user.id)
    if not result:
        return CustomResponse().format_data_not_found()
    return CustomResponse(content=result).format_data_get()

@router.get("/profile/public-profile-link/", dependencies=[Depends(jwt_auth)], tags=tags)
def get_user_profile_by_link(
    link: str,
    current_user: UserModel = Depends(is_authenticated),
    db: Session = Depends(get_db)
):
    """Get user profile by link"""
    result = get_user_profile_by_link_service(db, link, current_user.id)
    if not result:
        return CustomResponse().format_data_not_found()
    return CustomResponse(content=result).format_data_get()

@router.get("/availability", dependencies=[Depends(jwt_auth)], tags=tags)
def get_availability(
    current_user: UserModel = Depends(is_authenticated),
    db: Session = Depends(get_db)
):
    """Get availability for current user"""
    result = get_availability_service(db, current_user.id)
    return CustomResponse(content=result).format_data_get()

@router.put("/availability", dependencies=[Depends(jwt_auth)], tags=tags)
def update_availability(
    availability: AvailabilityUpdateSchema,
    current_user: UserModel = Depends(is_authenticated),
    db: Session = Depends(get_db)
):
    """Update availability for current user"""
    result = update_availability_service(db, current_user.id, availability)
    return CustomResponse(content=result).format_data_update()

@router.post("/otp/send", tags=tags)
def send_otp(
    data: OTPCreateSchema,
    db: Session = Depends(get_db)
):
    """Send OTP to current user"""
    # Get user by email
    user = db.query(UserModel).filter(UserModel.email == data.email).first()
    if not user:
        raise CustomException(content={"error": "User not found"}).not_found_exception()
    result = send_otp_service(db, user, data.otp_type)
    return CustomResponse(content=result).format_data_create()

@router.post("/otp/verify/register", tags=tags)
def verify_otp(
    data: OTPVerifySchema,
    db: Session = Depends(get_db)
):
    """Verify OTP for user"""
    result = verify_otp_service(db, data.email, data.otp_code, data.otp_type)
    if result:
        active_user_service(db, data.email)
    return CustomResponse(content=result).format_data_create()

@router.post("/otp/resend", tags=tags)
def resend_otp(
    data: OTPCreateSchema,
    db: Session = Depends(get_db)
):
    """Resend OTP for user"""
    # Get user by email
    user = db.query(UserModel).filter(UserModel.email == data.email).first()
    if not user:
        raise CustomException(content={"error": "User not found"}).not_found_exception()
        
    result = resend_otp_service(db, user, data.otp_type)
    return CustomResponse(content=result).format_data_create()

@router.post("/forgot-password/send-otp", tags=tags, **create_data_response)
def send_forgot_password_otp(
    data: VerifyRegisterInfo,
    db: Session = Depends(get_db)
):
    """Send OTP for forgot password"""
    result = send_forgot_password_otp_service(db, data.email)
    return CustomResponse(content=result).format_data_create()


@router.post("/change-email/send-otp", dependencies=[Depends(jwt_auth)], tags=tags)
def send_change_email_otp(
    data: ChangeEmailRequestSchema,
    current_user: UserModel = Depends(is_authenticated),
    db: Session = Depends(get_db)
):
    result = send_change_email_otp_service(db, current_user.id, data.new_email)
    return CustomResponse(content={"expires_at": result.expires_at}).format_data_create()


@router.post("/change-email/verify", dependencies=[Depends(jwt_auth)], tags=tags)
def verify_change_email_otp(
    data: ChangeEmailVerifySchema,
    current_user: UserModel = Depends(is_authenticated),
    db: Session = Depends(get_db)
):
    result = verify_change_email_otp_service(db, current_user.id, data.new_email, data.otp_code)
    return CustomResponse(content=result).format_data_create()


@router.post("/change-phone/send-otp", dependencies=[Depends(jwt_auth)], tags=tags)
def send_change_phone_otp(
    data: ChangePhoneRequestSchema,
    current_user: UserModel = Depends(is_authenticated),
    db: Session = Depends(get_db),
):
    result = send_change_phone_otp_service(db, current_user.id, data.new_phone)
    return CustomResponse(content={"expires_at": result.expires_at}).format_data_create()


@router.post("/change-phone/verify", dependencies=[Depends(jwt_auth)], tags=tags)
def verify_change_phone_otp(
    data: ChangePhoneVerifySchema,
    current_user: UserModel = Depends(is_authenticated),
    db: Session = Depends(get_db),
):
    result = verify_change_phone_otp_service(db, current_user.id, data.new_phone, data.otp_code)
    return CustomResponse(content=result).format_data_create()

@router.delete("/delete-account", dependencies=[Depends(jwt_auth)], tags=tags)
def delete_account(
    current_user: UserModel = Depends(is_authenticated),
    db: Session = Depends(get_db)
):
    """Delete current user account and all related data"""
    result = delete_user_account_service(db, current_user.id)
    return CustomResponse(content=result).format_data_delete()

@router.delete("/public/delete-account", tags=tags)
def delete_account(
    data: VerifyRegisterInfo,
    db: Session = Depends(get_db)
):
    """Delete current user account and all related data"""
    user = db.query(UserModel).filter(UserModel.email == data.email).first()
    if user:
        result = delete_user_account_service(db, user.id)
        return CustomResponse(content=result).format_data_delete()
    else:
        return CustomException().not_found_exception()
