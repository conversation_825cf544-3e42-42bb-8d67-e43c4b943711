from sqlalchemy import Column, Date, DateTime, Enum, Float, <PERSON><PERSON><PERSON>, Integer, String, Text, Boolean, JSON, Time
from src.utils.base_model import BaseModel, Base
from sqlalchemy.orm import relationship
from src.utils.enum import OTPTypeEnum, UserRoleEnum
# from src.news.models import News
import sqlalchemy as sa


class User(BaseModel, Base):
    __tablename__ = "user"

    phone_number = Column(String(255), index=True, unique=False, nullable=True)
    user_name = Column(String(50), index=True, nullable = True)
    email = Column(String(50), index=True, unique=True)
    password = Column(Text)
    avatar = Column(Text, nullable=True, default=None)
    user_type = Column(Enum(UserRoleEnum), nullable=False)
    is_active = Column(Boolean, default=False)
    is_admin = Column(Boolean, default=False)
    last_login = Column(DateTime, nullable = True, default = None)
    qr_code = Column(Text, nullable=True, default=None)
    organization_id = Column(Integer, ForeignKey('organization.id'), nullable=True)
    enabled_touch_id = Column(Boolean, default=False, server_default=sa.text('0'))
    enabled_face_id = Column(Boolean, default=False, server_default=sa.text('0'))

    # Most commonly used relationships - keep these eager loaded
    organization = relationship('Organization', back_populates='users', lazy="select")
    profile = relationship(
        'UserProfile', 
        back_populates='user', 
        uselist=False,
        lazy="select",
        cascade="all, delete-orphan"
    )

    # Settings - lazy load by default
    notification_settings = relationship(
        "UserNotificationSettings", 
        back_populates="user", 
        uselist=False,
        lazy="select",
        cascade="all, delete-orphan"
    )
    subscription_settings = relationship(
        "UserSubscriptionSettings", 
        back_populates="user", 
        uselist=False,
        lazy="select",
        cascade="all, delete-orphan"
    )
    web_link_settings = relationship(
        "UserWebLinkSettings", 
        back_populates="user", 
        uselist=False,
        lazy="select",
        cascade="all, delete-orphan"
    )

    # Integrations & Tokens - lazy load
    integrations = relationship(
        "UserIntegration", 
        back_populates="user",
        lazy="select",
        cascade="all, delete-orphan"
    )
    tokens = relationship(
        "Token", 
        back_populates="user",
        lazy="select",
        cascade="all, delete-orphan"
    )

    # Events - lazy load
    created_events = relationship(
        "Event", 
        back_populates="creator", 
        foreign_keys="Event.created_by",
        lazy="select"
    )

    # Leads & Contacts - lazy load
    leads = relationship(
        "Lead",
        back_populates="user",
        foreign_keys="Lead.user_id",
        lazy="select"
    )
    contacts = relationship(
        "Contact",
        foreign_keys="Contact.user_id",
        back_populates="user",
        lazy="select"
    )
    contact_of = relationship(
        "Contact",
        foreign_keys="Contact.user_contact_id",
        back_populates="user_contact",
        lazy="select"
    )
    business_profile = relationship(
        "BusinessProfile",
        back_populates="user",
        lazy="select"
    )

    # Chat related - lazy load
    created_rooms = relationship(
        "ChatRoom",
        back_populates="creator",
        lazy="select"
    )
    chat_rooms = relationship(
        "ChatRoomMember",
        back_populates="user",
        foreign_keys="ChatRoomMember.user_id",
        lazy="select"
    )
    invited_to_rooms = relationship(
        "ChatRoomMember",
        foreign_keys="ChatRoomMember.invited_by",
        lazy="select"
    )
    messages = relationship(
        "Message",
        back_populates="sender",
        lazy="select"
    )

    # Contact requests - lazy load
    sent_contact_requests = relationship(
        "ContactRequest",
        back_populates="from_user",
        foreign_keys="ContactRequest.from_user_id",
        lazy="select"
    )
    received_contact_requests = relationship(
        "ContactRequest",
        back_populates="to_user",
        foreign_keys="ContactRequest.to_user_id",
        lazy="select"
    )

    # Gifts - lazy load
    sent_gifts = relationship(
        "UserGift",
        back_populates="sender",
        foreign_keys="UserGift.sender_id",
        lazy="select"
    )
    received_gifts = relationship(
        "UserGift",
        back_populates="receiver",
        foreign_keys="UserGift.receiver_id",
        lazy="select"
    )

    # Notifications - lazy load
    fcm_tokens = relationship(
        "FCMToken",
        back_populates="user",
        lazy="select"
    )
    notifications = relationship(
        "Notification",
        back_populates="user",
        lazy="select"
    )
    user_topics = relationship(
        "UserTopic",
        back_populates="user",
        lazy="select"
    )

    # Cart & Orders - lazy load
    cart = relationship(
        "Cart",
        back_populates="user",
        uselist=False,
        lazy="select"
    )
    orders = relationship(
        "Order",
        back_populates="user",
        lazy="select"
    )

    # Meetings - lazy load
    created_meetings = relationship(
        "Meeting",
        back_populates="inviter",
        foreign_keys="Meeting.inviter_id",
        lazy="select"
    )
    otps = relationship("UserOTP", back_populates="user", cascade="all, delete-orphan", lazy="select")
    meeting_participants = relationship(
        "MeetingParticipant",
        back_populates="user",
        lazy="select"
    )

class UserProfile(BaseModel, Base):
    __tablename__ = "userprofile"

    full_name = Column(String(255), nullable=True, default=None)
    title = Column(String(255), nullable=True, default=None)
    gender = Column(String(255), nullable=True, default=None)
    date_of_birth = Column(Date, nullable=True, default=None)
    social_media_links = Column(JSON, nullable=True, default=None)   # e.g. {"linkedin": "...", "telegram": "...", "whatsapp": "..."}
    bio = Column(Text, nullable=True, default=None)
    notes = Column(Text, nullable=True, default=None)
    public_profile = Column(Boolean, default=True)
    #location 
    address = Column(String(255), nullable=True, default=None)
    lattitude = Column(Float, nullable=True, default=None)
    longitude = Column(Float, nullable=True, default=None)
    is_visibled_map = Column(Boolean, default=False)

    skill_interests = Column(JSON, nullable=True, default=None)
    
    industry_tags = Column(JSON, nullable=True, default=None)

    enabled_availability = Column(Boolean, default=False)
    availability = Column(JSON, nullable=True, default=None)

    user_id = Column(Integer, ForeignKey('user.id'),
                         unique=True, nullable=False)
    user = relationship('User', back_populates='profile')
class BusinessProfile(BaseModel, Base):
    __tablename__ = "businessprofile"

    user_id = Column(Integer, ForeignKey('user.id'),
                         unique=True, nullable=False)
    user = relationship('User', back_populates='business_profile')
    business_name = Column(String(255), nullable=True)
    business_ein = Column(String(255), nullable=True)
    business_address = Column(String(255), nullable=True)
    business_description = Column(Text, nullable=True)
    opening_hours = Column(JSON, nullable=True)
    industry_tags = Column(JSON, nullable=True)
    about = Column(Text, nullable=True)
    services = Column(JSON, nullable=True)

class Token(BaseModel, Base):
    __tablename__ = 'token'

    access_token = Column(Text, nullable=False)
    refresh_token = Column(Text, nullable=False)
    is_expired = Column(Boolean, default=False)
    expire_at = Column(DateTime)
    ip_address = Column(Text, nullable=True, default=None)
    user_agent = Column(Text, nullable=True, default=None)
    device_id = Column(Text, nullable=True, default=None)
    user_id = Column(Integer, ForeignKey('user.id'))
    user = relationship("User", back_populates="tokens")

class UserIntegration(BaseModel, Base):
    __tablename__ = 'user_integration'

    user_id = Column(Integer, ForeignKey('user.id'))
    user = relationship("User", back_populates="integrations")
    
    google_calendar = Column(Boolean, default=False)
    google_access_token = Column(Text, nullable=True, default=None)
    google_refresh_token = Column(Text, nullable=True, default=None)
    apple_access_token = Column(Text, nullable=True, default=None)
    apple_refresh_token = Column(Text, nullable=True, default=None)
    linkedin_connection = Column(Boolean, default=False)
    linkedin_access_token = Column(Text, nullable=True, default=None)
    linkedin_refresh_token = Column(Text, nullable=True, default=None)

class UserNotificationSettings(BaseModel, Base):
    __tablename__ = "user_notification_settings"

    user_id = Column(Integer, ForeignKey('user.id'), unique=True)
    user = relationship("User", back_populates="notification_settings")
    
    rsvp_reminders = Column(Boolean, default=True)
    contact_nearby_alerts = Column(Boolean, default=True)
    follow_up_nudges = Column(Boolean, default=True)
    gift_delivery_confirmations = Column(Boolean, default=True)

class UserSubscriptionSettings(BaseModel, Base):
    __tablename__ = "user_subscription_settings"

    user_id = Column(Integer, ForeignKey('user.id'), unique=True)
    user = relationship("User", back_populates="subscription_settings")
    
    current_plan = Column(String(50), default="free")  # free, basic, business
    billing_cycle = Column(String(50), nullable=True)  # monthly, yearly
    next_billing_date = Column(DateTime, nullable=True)
    team_members_limit = Column(Integer, default=1)

class UserWebLinkSettings(BaseModel, Base):
    __tablename__ = "user_web_link_settings"

    user_id = Column(Integer, ForeignKey('user.id'), unique=True)
    user = relationship("User", back_populates="web_link_settings")
    
    public_profile_link = Column(String(255), unique=True, nullable=True)
    active_invite_pages = Column(Text, nullable=True)  # JSON string of active invite page IDs
    gift_claim_link = Column(String(255), unique=True, nullable=True)


class UserOTP(Base, BaseModel):
    __tablename__ = "user_otps"

    id = Column(Integer, primary_key=True, index=True)
    user_id = Column(Integer, ForeignKey("user.id"), nullable=False)
    otp_code = Column(String(6), nullable=False)
    otp_type = Column(Enum(OTPTypeEnum), nullable=False)
    is_used = Column(Boolean, default=False)
    expires_at = Column(DateTime, nullable=False)
    resend_count = Column(Integer, default=0)  # Track number of times OTP has been resent
    new_email = Column(String(50), nullable=True)
    new_phone = Column(String(20), nullable=True)
    user = relationship("User", back_populates="otps")