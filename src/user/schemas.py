from typing import Optional, Type, Union, List, Dict, Literal, Annotated
from pydantic import ConfigDict, Field, ValidationInfo, constr, EmailStr, field_validator, model_validator, validator, BaseModel, root_validator
from datetime import date, datetime
from sqlalchemy.orm import Session
import re
from src.utils.enum import AvailabilityDayStatusEnum, AvailabilitySmartTagsEnum, Gender, UserRoleEnum, AvailabilityStatusEnum, AvailabilityTagsEnum, OTPTypeEnum
from src.utils.exceptions import CustomException
from src.user.models import User as UserModel
from src.user.models import UserProfile as UserProfileModel
from src.user.models import BusinessProfile as BusinessProfileModel
from src.user.models import UserOTP as UserOTPModel
from datetime import time
from src.utils.enum import DayInWeekEnum
from src.organization.schemas import OrganizationInDB, OrganizationUserCreate

class VerifyRegisterInfo(BaseModel):
    email: EmailStr = Field(
        default = None,
        title = "Email of user"
    )


class UserProfileBase(BaseModel):
    full_name: Optional[str] = Field(None, example="John Doe")
    gender: Optional[Gender] = Field(None, example=Gender.MALE)
    address: Optional[str] = Field(None, example="123 Nguyen Hue Street, District 1, Ho Chi Minh City")
    date_of_birth: Optional[date] = Field(None, example="1990-01-01")
    title: Optional[str] = Field(None, example="Software Engineer")
    bio: Optional[str] = Field(None, example="Experienced software engineer with 5 years of experience")
    notes: Optional[str] = Field(None, example="Prefers morning meetings")
    visibility: Optional[str] = Field(None, example="PUBLIC")
    latitude: Optional[float] = Field(None, example=10.776901)
    longitude: Optional[float] = Field(None, example=106.700902)
    public_profile: Optional[bool] = Field(None, example=True)
    social_media_links: Optional[dict] = Field(None, example={"linkedin": "https://linkedin.com/in/nguyenvana"})
    industry: Optional[dict] = Field(None, example={"primary": "Technology", "secondary": ["Software", "AI"]})

class BusinessProfileBase(BaseModel):
    title: Optional[str] = Field(None, example="CEO")
    company: Optional[str] = Field(None, example="Tech Company Ltd.")
    company_address: Optional[str] = Field(None, example="456 Le Loi Street, District 1, Ho Chi Minh City")
    company_description: Optional[str] = Field(None, example="Leading technology solutions provider")
    industry_tags: Optional[dict] = Field(None, example={"primary": "Technology", "secondary": ["Software", "AI"]})
    business_name: Optional[str] = Field(None, min_length=1, max_length=100, example="Sunrise Coffee Ltd.")
    business_ein: Optional[str] = Field(None, example="92-8471923")
    business_address: Optional[str] = Field(None, max_length=200, example="123 Business St")
    established_at: Optional[date] = Field(None, example="2024-01-01")

    @field_validator('business_ein')
    def validate_business_ein(cls, v: str) -> str:
        if v is not None and not re.match(r'^\d{2}-\d{7}$', v):
            raise ValueError('EIN must be in the format XX-XXXXXXX')
        return v

    @field_validator('established_at')
    def validate_established_at(cls, v: date) -> date:
        if v and v > date.today():
            raise ValueError('Date of Establishment must be in the past or today')
        return v

class RegisterUserBase(VerifyRegisterInfo):
    email: EmailStr = Field(..., example="<EMAIL>")
    password: str = Field(..., example="password123")
    confirm_password: str = Field(..., example="password123")
    full_name: str = Field(min_length=3, max_length=50, example="John Doe")
    user_type: str = Field(..., example="USER/MERCHANT")
    date_of_birth: Optional[date] = Field(None, example="1990-01-01")
    business: Optional[OrganizationUserCreate] = Field(None, example={
        "name": "ACME Corp",
        "ein": "12-3456789",
    })

    @field_validator('user_type')
    def validate_user_type(cls, v):
        valid_types = [e.value for e in UserRoleEnum]
        if v not in valid_types:
            raise ValueError(f'User type must be one of: {", ".join(valid_types)}')
        return v
    
    @field_validator('password')
    def validate_password_complexity(cls, v: str) -> str:
        """Validate password complexity based on security rules."""
        if len(v) < 8:
            raise ValueError('Password must be at least 8 characters')
        if not re.search("[a-z]", v):
            raise ValueError('Password must contain at least one lowercase letter')
        if not re.search("[A-Z]", v):
            raise ValueError('Password must contain at least one uppercase letter')
        if not re.search("[0-9]", v):
            raise ValueError('Password must contain at least one number')
        if not re.search(r'[!@#$%^&*(),.?":{}|<>]', v):
            raise ValueError('Password must contain at least one special character')
        return v

    @model_validator(mode='after')
    def check_passwords_match(self) -> 'RegisterUserBase':
        """Verify that password and confirm_password are the same."""
        if self.password != self.confirm_password:
            raise ValueError('Passwords do not match')
        return self
    
    @field_validator('business')
    def validate_business_required_for_merchant(cls, v, info):
        user_type = info.data.get('user_type')
        if user_type == UserRoleEnum.MERCHANT.value and v is None:
            raise ValueError("Business information is required when user_type is MERCHANT")
        return v

class UserBase(BaseModel):
    email: EmailStr = Field(..., example="<EMAIL>")
    full_name: str = Field(..., example="John Doe")
    phone: Optional[str] = Field(None, example="0123456789")
    user_type: str = Field(..., example="USER/MERCHANT")

class UserResponse(UserBase):
    id: int
    created_at: datetime
    updated_at: datetime
    is_active: bool
    is_admin: bool
    profile: Optional[UserProfileBase] = None
    business_profile: Optional[BusinessProfileBase] = None

    class Config:
        from_attributes = True

class UserListResponse(BaseModel):
    items: List[UserResponse]
    total: int
    page: int
    size: int
    pages: int

    model_config = {
        "json_schema_extra": {
            "examples": [
                {
                    "items": [
                        {
                            "id": 1,
                            "email": "<EMAIL>",
                            "full_name": "John Doe",
                            "phone": "0123456789",
                            "user_type": "USER",
                            "created_at": "2024-03-20T10:00:00Z",
                            "updated_at": "2024-03-20T10:00:00Z",
                            "is_active": True,
                            "is_admin": False,
                            "profile": {
                                "full_name": "John Doe",
                                "gender": "MALE",
                                "date_of_birth": "1990-01-01"
                            }
                        }
                    ],
                    "total": 1,
                    "page": 1,
                    "size": 10,
                    "pages": 1
                }
            ]
        }
    }

class UserProfileCreate(UserProfileBase):
    pass

class UserProfileUpdate(UserProfileBase):
    pass

class UserProfileInDB(UserProfileBase):
    id: int
    user_id: int
    created_at: datetime
    updated_at: datetime
    is_deleted: bool

    class Config:
        from_attributes = True

class UserProfileResponse(UserProfileInDB):
    pass


class MyProfileUpdate(BaseModel):
    full_name: Optional[str] = Field(None, example="John Doe")
    avatar: Optional[str] = Field(None, example="https://example.com/avatar.png")
    title: Optional[str] = Field(None, example="Product Manager")
    gender: Optional[Gender] = Field(None, example=Gender.MALE)
    date_of_birth: Optional[date] = Field(None, example="1990-01-01")
    address: Optional[str] = Field(None, example="123 Main St")
    bio: Optional[str] = Field(None, example="Experienced professional")
    skill_interests: Optional[dict] = Field(None, example={"tags": ["AI", "ML"]})
    industry_tags: Optional[dict] = Field(None, example={"primary": "Tech"})
    social_media_links: Optional[dict] = Field(
        None,
        example={"linkedin": "https://linkedin.com/in/john"},
    )


class MyProfileResponse(MyProfileUpdate):
    id: int = Field(..., example=1)
    email: Optional[EmailStr] = Field(None, example="<EMAIL>")
    phone_number: Optional[str] = Field(None, example="0123456789")
    model_config = {
        "json_schema_extra": {
            "examples": [
                {
                    "id": 1,
                    "full_name": "John Doe",
                    "email": "<EMAIL>",
                    "phone_number": "0123456789",
                    "avatar": "https://example.com/avatar.png",
                    "title": "Product Manager",
                    "gender": "MALE",
                    "date_of_birth": "1990-01-01",
                    "address": "123 Main St",
                    "bio": "Experienced professional",
                    "skill_interests": {"tags": ["AI", "ML"]},
                    "industry_tags": {"primary": "Tech"},
                    "social_media_links": {
                        "linkedin": "https://linkedin.com/in/john"
                    },
                }
            ]
        }
    }

class BusinessProfileCreate(BusinessProfileBase):
    pass

class BusinessProfileUpdate(BusinessProfileBase):
    pass

class BusinessProfileInDB(BusinessProfileBase):
    id: int
    user_id: int
    created_at: datetime
    updated_at: datetime
    is_deleted: bool

    class Config:
        from_attributes = True

class BusinessProfileResponse(BusinessProfileInDB):
        pass

class SentOTPBase(BaseModel):
    phone_number: str = Field(
        max_length = 11,
        min_length = 9,
        title="The phone number of the user",
    )

    # @field_validator('phone_number')
    # def validate_phone_number(cls, value):
    #     if value and not re.search(r"^\d{10}$", value):
    #         raise CustomException(content={"phone_number":['Invalid phone number format']}).bad_request_exception()
    #     return value
    
class UserInfoBase(BaseModel):
    id: Union[int, None] = Field(
        title ="Id of the user"
    )
    phone_number: Union[str, None] = Field(
        max_length = 10,
        min_length = 10,
        title="The phone number of the user",
    )
    email: Union[EmailStr, None] = Field(
        title = "The email of the user",
        default=None
    )
    full_name: Union[str, None] = Field(
        title="The full name of the user",
    )
    gender: Union[Gender, None] = Field(
        default=Gender.OTHER,
        title="The sender of the user",
    )

    date_of_birth: Union[date, None] = Field(
        default=None,
        title="Birthday of user"
    )
    address: Union[str, None] = Field(
        default=None,
        title="Address of user"
    )
    avatar: Union[str, None] = Field(
        default=None,
        title="Avatar of user"
    )
    last_login: Union[datetime, None] = Field(
        default=None,
        title="Lastlogin of user"
    )
    is_admin: bool = Field(
        title = "Is admin of user"
    )
    user_type: Union[UserRoleEnum, None] = Field(
        title="The role of the user",
    )
    organization: Union[OrganizationInDB, None] = Field(
        title="The organization of the user",
    )

class UpdateUserInfo(BaseModel):
    full_name: Optional[str] = None
    gender: Optional[str] = None
    date_of_birth: Optional[date] = None
    address: Optional[str] = None
    avatar: Optional[str] = None

class CheckOTPForgonPWDBase(BaseModel):
    otp: str = Field(
        min_length = 6,
        max_length = 6,
        title="OTP for verify"
    )
    phone_number: str = Field(
        max_length = 10,
        min_length = 10,
        title="The phone number of the user",
    )

    @field_validator('phone_number')
    def validate_phone_number(cls, value):
        if value and not re.search(r"^\d{10}$", value):
            raise ValueError("Invalid phone number format")
        return value

class ForgotPasswordBase(BaseModel):
    password: str = Field(
        title="The password of the employee",
        min_length=8,
    )
    otp: str = Field(
        min_length = 6,
        max_length = 6,
        title="OTP for verify"
    )
    email: EmailStr = Field(
        title="The email of the user",
    )

  
    
    @field_validator('password', mode="before")
    def validate_password(cls, value):
        if not re.search(r'[A-Z]', value) or not re.search(r'[a-z]', value) or not re.search(r'\d', value) or not re.search(r'[@$!%*?&]', value):
            raise ValueError("Password must contain at least one uppercase letter, least one lowercase letter, least one digit and least one special character")
        return value
    

class UpdateAvatarBase(BaseModel):
    avatar: str = Field(
        title = "Avatar of the employee"
    )

class ChangePasswordSchemas(BaseModel):
    current_password: str = Field(
        title="The password of the user",
        min_length=8,
    )
    new_password: str = Field(
        title="The confirm password of the user",
        min_length=8,
    )

    @field_validator('current_password')
    def validate_current_password(cls, value):
        if not re.search(r'[A-Z]', value) or not re.search(r'[a-z]', value) or not re.search(r'\d', value) or not re.search(r'[@$!%*?&]', value):
            raise CustomException(content={"current_password": [
                                  'Password must contain at least one uppercase letter, least one lowercase letter, least one digit and least one special character']}).bad_request_exception()
        return value

    @field_validator('new_password')
    def validate_new_password(cls, value):
        if not re.search(r'[A-Z]', value) or not re.search(r'[a-z]', value) or not re.search(r'\d', value) or not re.search(r'[@$!%*?&]', value):
            raise CustomException(content={"new_password": [
                                  'Password must contain at least one uppercase letter, least one lowercase letter, least one digit and least one special character']}).bad_request_exception()
        return value


class NotificationSettingsBase(BaseModel):
    rsvp_reminders: bool = True
    contact_nearby_alerts: bool = True
    follow_up_nudges: bool = True
    gift_delivery_confirmations: bool = True

class NotificationSettingsResponse(NotificationSettingsBase):
    id: int
    user_id: int
    created_at: datetime
    updated_at: datetime

    class Config:
        from_attributes = True

class SubscriptionSettingsBase(BaseModel):
    current_plan: str
    billing_cycle: Optional[str] = None
    next_billing_date: Optional[datetime] = None
    team_members_limit: int = 1

class SubscriptionSettingsResponse(SubscriptionSettingsBase):
    id: int
    user_id: int
    created_at: datetime
    updated_at: datetime

    class Config:
        from_attributes = True

class WebLinkSettingsBase(BaseModel):
    public_profile_link: Optional[str] = None
    active_invite_pages: Optional[str] = None  # JSON string
    gift_claim_link: Optional[str] = None

class WebLinkSettingsResponse(WebLinkSettingsBase):
    id: int
    user_id: int
    created_at: datetime
    updated_at: datetime

    class Config:
        from_attributes = True

class UserSettingsResponse(BaseModel):
    notification_settings: NotificationSettingsResponse
    subscription_settings: SubscriptionSettingsResponse
    web_link_settings: WebLinkSettingsResponse

    class Config:
        from_attributes = True

class UpdateAllSettingsBase(BaseModel):
    notification_settings: Optional[NotificationSettingsBase] = None
    subscription_settings: Optional[SubscriptionSettingsBase] = None
    web_link_settings: Optional[WebLinkSettingsBase] = None

class UpdateAllSettingsResponse(UserSettingsResponse):
    pass

class UserCreate(RegisterUserBase):
    pass

class UserUpdate(BaseModel):
    email: Optional[str] = Field(None, example="<EMAIL>")
    full_name: Optional[str] = Field(None, example="John Doe")
    phone: Optional[str] = Field(None, example="0123456789")
    

class UserResponse(UserBase):
    id: int
    created_at: datetime
    updated_at: datetime

    class Config:
        from_attributes = True

class UserListResponse(BaseModel):
    items: List[UserResponse]
    total: int
    page: int
    size: int
    pages: int

    model_config = {
        "json_schema_extra": {
            "examples": [
                {
                    "items": [
                        {
                            "id": 1,
                            "email": "<EMAIL>",
                            "full_name": "John Doe",
                            "phone": "0123456789",
                            "user_type": "USER",
                            "created_at": "2024-03-20T10:00:00Z",
                            "updated_at": "2024-03-20T10:00:00Z",
                            "is_active": True,
                            "is_admin": False,
                            "profile": {
                                "full_name": "John Doe",
                                "gender": "MALE",
                                "date_of_birth": "1990-01-01"
                            }
                        }
                    ],
                    "total": 1,
                    "page": 1,
                    "size": 10,
                    "pages": 1
                }
            ]
        }
    }



class RegisterUserCreate(RegisterUserBase):
    pass

class RegisterUserUpdate(BaseModel):
    email: Optional[EmailStr] = Field(None, example="<EMAIL>")
    password: Optional[str] = Field(None, min_length=8, example="newpassword123")
    full_name: Optional[str] = Field(None, example="New Full Name")
    phone: Optional[str] = Field(None, example="+84987654321")
    address: Optional[str] = Field(None, example="456 New Street, New City, New Country")
    date_of_birth: Optional[datetime] = Field(None, example="1995-01-01T00:00:00")
    gender: Optional[str] = Field(None, example="female")
    avatar_url: Optional[str] = Field(None, example="https://example.com/new-avatar.jpg")

class RegisterUserResponse(RegisterUserBase):
    id: int = Field(..., example=1)
    created_at: datetime = Field(..., example="2024-03-20T10:00:00")
    updated_at: datetime = Field(..., example="2024-03-20T10:00:00")
    is_active: bool = Field(..., example=True)
    is_verified: bool = Field(..., example=False)
    is_superuser: bool = Field(..., example=False)

    class Config:
        from_attributes = True

class RegisterUserListResponse(BaseModel):
    items: list[RegisterUserResponse]
    total: int = Field(..., example=100)
    page: int = Field(..., example=1)
    size: int = Field(..., example=10)
    pages: int = Field(..., example=10)

class UserProfileCreate(UserProfileBase):
    pass

class UserProfileUpdate(UserProfileBase):
    pass

class UserProfileInDB(UserProfileBase):
    id: int
    user_id: int
    created_at: datetime
    updated_at: datetime
    is_deleted: bool

    class Config:
        from_attributes = True

class UserProfileResponse(UserProfileInDB):
    pass

class BusinessInformationSchema(BaseModel):
    business_name: Optional[str] = Field(None, example="Tech Solutions Inc.")
    business_address: Optional[str] = Field(None, example="123 Business Street, District 1, Ho Chi Minh City")
    business_description: Optional[str] = Field(None, example="Leading technology solutions provider in Vietnam")
    industry_tags: Optional[dict] = Field(None, example={
        "primary": "Technology",
        "secondary": ["Software Development", "AI", "Cloud Computing"]
    })
    about: Optional[str] = Field(None, example="We are a technology solutions provider that provides software development, AI, and cloud computing services.")
    services: Optional[dict] = Field(None, example={
        "primary": "Software Development",
        "secondary": ["AI", "Cloud Computing"]
    })
    opening_hours: Optional[dict] = Field(None, example={
        "Monday": "09:00-17:00",
        "Tuesday": "09:00-17:00",
        "Wednesday": "09:00-17:00",
        "Thursday": "09:00-17:00",
        "Friday": "09:00-17:00",
        "Saturday": "09:00-17:00",
        "Sunday": "09:00-17:00"
    })

    model_config = {
        "json_schema_extra": {
            "examples": [
                {
                    "business_name": "Tech Solutions Inc.",
                    "business_address": "123 Business Street, District 1, Ho Chi Minh City",
                    "business_description": "Leading technology solutions provider in Vietnam",
                    "industry_tags": {
                        "primary": "Technology",
                        "secondary": ["Software Development", "AI", "Cloud Computing"]
                    },
                    "about": "We are a technology solutions provider that provides software development, AI, and cloud computing services.",
                    "services": {
                        "primary": "Software Development",
                        "secondary": ["AI", "Cloud Computing"]
                    },
                    "opening_hours": {
                        "Monday": "09:00-17:00",
                        "Tuesday": "09:00-17:00",
                        "Wednesday": "09:00-17:00",
                        "Thursday": "09:00-17:00",
                        "Friday": "09:00-17:00",
                        "Saturday": "09:00-17:00",
                        "Sunday": "09:00-17:00"
                    }
                }
            ]
        }
    }

class UserInformationGetByCodeSchema(BaseModel):
    id: int = Field(..., example=1)
    email: EmailStr = Field(..., example="<EMAIL>")
    full_name: Optional[str] = Field(None, example="John Doe")
    gender: Optional[str] = Field(None, example="MALE")
    date_of_birth: Optional[date] = Field(None, example="1990-01-01")
    title: Optional[str] = Field(None, example="Software Engineer")
    bio: Optional[str] = Field(None, example="Experienced software engineer with 5 years of experience in web development")
    address: Optional[str] = Field(None, example="456 Residential Street, District 2, Ho Chi Minh City")
    social_media_links: Optional[dict] = Field(None, example={
        "linkedin": "https://linkedin.com/in/johndoe",
        "github": "https://github.com/johndoe",
        "twitter": "https://twitter.com/johndoe"
    })
    industry: Optional[dict] = Field(None, example={
        "primary": "Technology",
        "secondary": ["Software Development", "Web Development"]
    })
    is_contact: bool = Field(False, example=True)
    business_info: Optional[BusinessInformationSchema] = None

    model_config = {
        "json_schema_extra": {
            "examples": [
                {
                    "id": 1,
                    "email": "<EMAIL>",
                    "full_name": "John Doe",
                    "gender": "MALE",
                    "date_of_birth": "1990-01-01",
                    "title": "Software Engineer",
                    "bio": "Experienced software engineer with 5 years of experience in web development",
                    "address": "456 Residential Street, District 2, Ho Chi Minh City",
                    "social_media_links": {
                        "linkedin": "https://linkedin.com/in/johndoe",
                        "github": "https://github.com/johndoe",
                        "twitter": "https://twitter.com/johndoe"
                    },
                    "industry": {
                        "primary": "Technology",
                        "secondary": ["Software Development", "Web Development"]
                    },
                    "is_contact": True,
                    "business_info": {
                        "business_name": "Tech Solutions Inc.",
                        "business_address": "123 Business Street, District 1, Ho Chi Minh City",
                        "business_description": "Leading technology solutions provider in Vietnam",
                        "industry_tags": {
                            "primary": "Technology",
                            "secondary": ["Software Development", "AI", "Cloud Computing"]
                        },
                        "about": "We are a technology solutions provider that provides software development, AI, and cloud computing services.",
                        "services": {
                            "primary": "Software Development",
                            "secondary": ["AI", "Cloud Computing"]
                        },
                        "opening_hours": {
                            "Monday": "09:00-17:00",
                            "Tuesday": "09:00-17:00",
                            "Wednesday": "09:00-17:00",
                            "Thursday": "09:00-17:00",
                            "Friday": "09:00-17:00",
                            "Saturday": "09:00-17:00",
                            "Sunday": "09:00-17:00"
                        }
                    }
                }
            ]
        }
    }

class DailyAvailabilitySchema(BaseModel):
    status: AvailabilityDayStatusEnum = Field(AvailabilityDayStatusEnum.BUSY, example=AvailabilityDayStatusEnum.ACTIVE)
    start_time: Optional[str] = Field(
        None,
        example="09:00",
        description="Start time in HH:MM 24-hour format"
    )
    end_time: Optional[str] = Field(
        None,
        example="17:00", 
        description="End time in HH:MM 24-hour format"
    )
    smart_tags: Optional[List[AvailabilitySmartTagsEnum]] = Field([], example=[AvailabilitySmartTagsEnum.BRUNCH, AvailabilitySmartTagsEnum.COFFEE])
    smart_tags_expired_at: Optional[str] = Field(None, example="2025-07-01T00:00:00Z")
    message: Optional[str] = Field(None, example="Available for meetings in the morning.")

    @field_validator('start_time', 'end_time')
    def validate_time(cls, v, info: ValidationInfo):
        status = info.data.get('status')
        if status == AvailabilityDayStatusEnum.ACTIVE and v is None:
            raise ValueError("start_time and end_time are required when status is ACTIVE")
        return v

    @field_validator('end_time')
    def validate_end_time(cls, v, info: ValidationInfo):
        start_time = info.data.get('start_time')
        if start_time and v:
            # Convert times to minutes for comparison
            start_minutes = int(start_time.split(':')[0]) * 60 + int(start_time.split(':')[1])
            end_minutes = int(v.split(':')[0]) * 60 + int(v.split(':')[1])
            if end_minutes <= start_minutes:
                raise ValueError("end_time must be greater than start_time")
        return v

    @field_validator('smart_tags')
    def validate_smart_tags(cls, v):
        if v and len(v) > 5:
            raise ValueError("smart_tags cannot have more than 5 items")
        
        # Check for duplicate tags
        if v and len(v) != len(set(v)):
            raise ValueError("smart_tags cannot contain duplicate items")
            
        return v

class AvailabilityUpdateSchema(BaseModel):
    MON: List[DailyAvailabilitySchema] = Field(default_factory=list)
    TUE: List[DailyAvailabilitySchema] = Field(default_factory=list)
    WED: List[DailyAvailabilitySchema] = Field(default_factory=list)
    THU: List[DailyAvailabilitySchema] = Field(default_factory=list)
    FRI: List[DailyAvailabilitySchema] = Field(default_factory=list)
    SAT: List[DailyAvailabilitySchema] = Field(default_factory=list)
    SUN: List[DailyAvailabilitySchema] = Field(default_factory=list)

class OTPBase(BaseModel):
    otp_type: OTPTypeEnum = Field(
        ...,
        description="Type of OTP",
        example=OTPTypeEnum.USER_VERIFICATION.value,
        title="OTP Type"
    )

    @field_validator('otp_type')
    def validate_otp_type(cls, v):
        if not v:
            raise ValueError("OTP type is required")
        if v not in [e.value for e in OTPTypeEnum]:
            raise ValueError(f"OTP type must be one of: {', '.join([e.value for e in OTPTypeEnum])}")
        return v

    model_config = {
        "json_schema_extra": {
            "examples": [
                {
                    "otp_type": "EMAIL_VERIFICATION"
                },
                {
                    "otp_type": "FORGOT_PASSWORD"
                }
            ]
        }
    }

class OTPCreateSchema(OTPBase):
    email: EmailStr = Field(
        ...,
        title="Email of user"
    )
    model_config = {
        "json_schema_extra": {
            "examples": [
                {
                    "otp_type": "USER_VERIFICATION/FORGOT_PASSWORD",
                    "email": "<EMAIL>"
                }
                
            ]
        }
    }

class OTPVerifySchema(OTPBase):
    email: EmailStr = Field(
        ...,
        title="Email of user"
    )
    otp_code: str = Field(
        min_length = 6,
        max_length = 6,
        title="OTP for verify"
    )
    model_config = {
        "json_schema_extra": {
            "examples": [
                {
                    "otp_type": "USER_VERIFICATION",
                    "email": "<EMAIL>",
                    "otp_code": "123456"    
                }
            ]
        }
    }

class OTPResponseSchema(OTPBase):
    id: int
    user_id: int
    otp_code: str
    is_used: bool
    expires_at: datetime
    created_at: datetime
    updated_at: datetime

    class Config:
        from_attributes = True


class ChangeEmailRequestSchema(BaseModel):
    new_email: EmailStr = Field(..., example="<EMAIL>", title="New Email")


class ChangeEmailVerifySchema(BaseModel):
    new_email: EmailStr = Field(..., example="<EMAIL>", title="New Email")
    otp_code: Annotated[str, Field(min_length=6, max_length=6)] = Field(..., example="123456", title="OTP for verify")


class ChangePhoneRequestSchema(BaseModel):
    new_phone: Annotated[str, Field(min_length=9, max_length=11)] = Field(..., example="0912345678", title="New Phone Number")


class ChangePhoneVerifySchema(BaseModel):
    new_phone: Annotated[str, Field(min_length=9, max_length=11)] = Field(..., example="0912345678", title="New Phone Number")
    otp_code: Annotated[str, Field(min_length=6, max_length=6)] = Field(..., example="123456", title="OTP for verify")

class UserSettingsSchema(BaseModel):
    enabled_touch_id: bool = Field(..., example=False)
    enabled_face_id: bool = Field(..., example=False)
    is_visibled_map: bool = Field(..., example=False)
    enabled_availability: bool = Field(..., example=False)
