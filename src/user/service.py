# from typing import List, Union
# from fastapi import HTTPException
# from sqlalchemy import or_
from sqlalchemy.orm import Session
import datetime as datetime_obj
from datetime import datetime, timedelta, timezone, time
import os
from jose import jwt
from passlib.context import CryptContext
from src.user.schemas import (
    RegisterUserBase, 
    SentOTPBase, 
    NotificationSettingsBase,
    SubscriptionSettingsBase,
    UserSettingsSchema,
    WebLinkSettingsBase,
    UpdateAllSettingsBase,
    UserInformationGetByCodeSchema,
    AvailabilityUpdateSchema
)
from src.utils.redis_cache import RedisUtils
# from src.position.models import Position
# from src.utils.common import query_list
# from src.utils.enum import Portal
from src.utils.exceptions import CustomException
from src.user.models import (
    User as UserModel,
    UserProfile as UserProfileModel,
    Token as TokenModel,
    UserNotificationSettings,
    UserSubscriptionSettings,
    UserWebLinkSettings,
    BusinessProfile,
    UserOTP as UserOTPModel
)
# from src.employee.schemas import Employee as EmployeeSchema
from sqlalchemy.orm import joinedload
from src.utils.utils import generate_code, send_email_background
from src.authentication.schemas import TokenCreate
from src.user.schemas import (
    UserInfoBase,
    UpdateUserInfo,
    VerifyRegisterInfo,
    ForgotPasswordBase,
    UpdateAvatarBase,
    UserProfileCreate,
    MyProfileUpdate,
    BusinessProfileUpdate,
    ChangeEmailRequestSchema,
    ChangeEmailVerifySchema,
)
from src.utils.enum import Portal, OTPTypeEnum, UserRoleEnum, AvailabilityStatusEnum
from src.utils.twilio import send_sms_sendgrid
import requests
import numpy as np
import uuid
from typing import Optional
from src.contact.models import Contact as ContactModel
import random
from src.utils.email_template import template_otp_email, template_forgot_password_otp_email
from sqlalchemy.sql import func
from src.organization.service import create_organization_service
from src.organization.schemas import OrganizationCreate
from src.organization.models import Organization

pwd_context = CryptContext(schemes=["bcrypt"], deprecated="auto")
redis_utils = RedisUtils()
def get_password_hash(password):
    return pwd_context.hash(password)

def register_user_service(db: Session, data: RegisterUserBase):
    # Generate and send OTP for email verification
    try:
        # Start transaction
        db.begin()

        # Check if email exists
        existing_user = db.query(UserModel).filter(UserModel.email == data.email).first()
        if existing_user:
            raise CustomException(content={"error": "Email already exists"}).bad_request_exception()
        
        hashed_password = get_password_hash(data.password)
        
        user = UserModel(
            is_active = False,
            password = hashed_password,
            email = data.email,
            qr_code = uuid.uuid4().hex[:8],
            user_type = data.user_type
        )
        db.add(user)
        db.flush()

        # Create user profile
        user_profile = UserProfileModel(
            full_name = data.full_name,
            date_of_birth = data.date_of_birth,
            user_id = user.id
        )
        db.add(user_profile)

        # Create business profile if user_type is BUSINESS
        if data.user_type == UserRoleEnum.MERCHANT:
            business_data = data.business
            business_data.owner_id = user.id
            create_organization_service(db, business_data)

        # Create default notification settings
        notification_settings = UserNotificationSettings(
            user_id = user.id,
            rsvp_reminders = True,
            contact_nearby_alerts = True,
            follow_up_nudges = True,
            gift_delivery_confirmations = True
        )
        db.add(notification_settings)

        # Create default subscription settings
        subscription_settings = UserSubscriptionSettings(
            user_id = user.id,
            current_plan = "free",
            billing_cycle = None,
            next_billing_date = None,
            team_members_limit = 1
        )
        db.add(subscription_settings)

        # Create default web link settings
        web_link_settings = UserWebLinkSettings(
            user_id = user.id,
            public_profile_link = f"profile/{user.id}",
            gift_claim_link = f"gift/claim/{user.id}"
        )
        db.add(web_link_settings)

        # Commit transaction
        db.commit()

        # Generate and send OTP for email verification
        try:
            otp = generate_otp_service(db, user.id, OTPTypeEnum.USER_VERIFICATION)
            send_email_background(
                to_email=user.email,
                subject="Verify Your Account",
                body=template_otp_email(user.email, otp.otp_code)
            )
        except Exception as e:
            raise CustomException(content={"error": "Failed to send verification OTP"}).api_exception()

        return {
            "user": {
                "id": user.id,
                "phone_number": user.phone_number,
                "email": user.email,
                "full_name": user_profile.full_name,
                "gender": user_profile.gender,
                "date_of_birth": user_profile.date_of_birth,
                "address": user_profile.address,
                "is_active": user.is_active,
                "is_admin": user.is_admin,
                "user_type": user.user_type
            }
        }

    except Exception as e:
        # Rollback transaction on error
        db.rollback()
        raise e

def check_spam(now, time_sending):
    if (now - time_sending) > 120:
        return True
    else:
        return False
def check_change_otp(now, first_time_sending):
    if (now - first_time_sending) > 900: #15p
        return True
    else:
        return False
def send_and_save_otp(self,db, phone_number):
    phone_number = phone_number
    key = phone_number
    num = redis_utils.check_key(key)
    if num == 0:
        dict = {}
        otp = generate_code(6)
        first_time_sending = datetime.timestamp(datetime.now())
        time_sending = datetime.timestamp(datetime.now())
        expired_time = datetime.now() + datetime_obj.timedelta(minutes = 15)
        expired_timestamp = datetime.timestamp(expired_time)
        count = 0
        dict['otp'] = otp
        dict['first_time_sending'] = first_time_sending
        dict['expired_time'] = expired_timestamp
        dict['time_sending'] = time_sending
        dict['count'] = count
        redis_utils.set_value(key,dict,86400)
        message = f"Your otp code is {otp}"
        # send_sms_sendgrid(db,phone_number, message)
        #implement logic sent otp here
    else:
        data = redis_utils.hgetall(key, False)
        otp = data.get(b'otp').decode()
        now_timestamp = datetime.timestamp(datetime.now())
        expired_time = data.get(b'expired_time').decode()
        spam = int(data.get(b'count').decode())
        timesending = float(data.get(b'time_sending').decode())
        send = check_spam(float(now_timestamp), timesending)
        if not send:
            raise CustomException(content={"error": "You have received code please try again after 2 minutes"}).bad_request_exception()
        if spam > 3:
            raise CustomException(content={"error": "Your phone number have been spam"}).bad_request_exception()
        first_time_sending = float(data.get(b'first_time_sending').decode())
        change_otp = check_change_otp(float(now_timestamp), first_time_sending)
        if not change_otp:
            otp = generate_code(6)
        spam += 1
        new_expired_time = datetime.timestamp(datetime.now() + datetime_obj.timedelta(minutes = 15))
        dict = {}
        dict['time_sending'] = now_timestamp
        dict['expired_time'] = new_expired_time
        dict['count'] = spam
        dict['otp'] = otp
        redis_utils.set_value(key,dict)
        message = f"Your otp code is {otp}"
        # send_sms_sendgrid(db,phone_number, message)

    return  {
        "otp": otp
    }

def verify_password(plain_password, hashed_password):
    valid = pwd_context.verify(plain_password, hashed_password)
    if not valid:
        raise CustomException().credentials_exception()
    return

# def get_password_hash(password):
#     return pwd_context.hash(password)

def get_user_by_user_name(db: Session, data: str):
    return db.query(UserModel).options(joinedload(UserModel.profile), joinedload(UserModel.organization)).filter(
        UserModel.email == data
    ).first()

def authenticate_user(db: Session, data: str, password: str):
    user = get_user_by_user_name(db, data)
    if not user:
        raise CustomException().credentials_exception()
    if not user.is_active:
        raise CustomException().deactivated_account_exception()
    if user.last_login:
        now = datetime.now(timezone.utc)
        user.last_login = now
        db.add(user)
        db.commit()
    verify_password(password, user.password)
    return user

def create_access_token(data: dict, expires_delta: timedelta):
    to_encode = data.copy()
    expire = datetime.now(timezone.utc) + expires_delta
    to_encode.update({"exp": expire})
    encoded_jwt = jwt.encode(to_encode, os.environ.get('SECRET_KEY','478fe93a5eb60c9a98ed8c09fd51b749b54ef5f2718982ed531c6818d707f96a'), algorithm=os.environ.get('ALGORITHM','HS256'))
    return encoded_jwt, expire

def create_token_of_user(db: Session, create_data: TokenCreate, user_id:int):
    data = create_data.model_dump()
    data['user_id'] = user_id
    token = TokenModel(**data)
    db.add(token)
    db.commit()
    db.refresh(token)
    return token

def get_token_in_db(db: Session, access_token: str):
    return db.query(TokenModel).filter((TokenModel.access_token == access_token)&(TokenModel.is_expired == False)).first()

def get_user_profile_service(db: Session, user_id: int) -> dict:
    """Return consolidated profile information for current user"""
    user = (
        db.query(UserModel)
        .options(joinedload(UserModel.profile), joinedload(UserModel.organization))
        .filter(UserModel.id == user_id)
        .first()
    )
    if not user or not user.profile:
        raise CustomException().not_found_exception()

    profile = user.profile
    return {
        "id": user.id,
        "email": user.email,
        "phone_number": user.phone_number,
        "avatar": user.avatar,
        "full_name": profile.full_name,
        "title": profile.title,
        "gender": profile.gender,
        "date_of_birth": profile.date_of_birth,
        "address": profile.address,
        "bio": profile.bio,
        "skill_interests": profile.skill_interests,
        "industry_tags": profile.industry_tags,
        "social_media_links": profile.social_media_links,
        "organization": user.organization
    }

def update_user_profile_service(db: Session, user_id: int, profile_data: MyProfileUpdate) -> dict:
    """Update profile and return the updated information"""
    user = (
        db.query(UserModel)
        .options(joinedload(UserModel.profile))
        .filter(UserModel.id == user_id)
        .first()
    )
    if not user or not user.profile:
        raise CustomException().not_found_exception()

    profile = user.profile

    update_dict = profile_data.dict(exclude_unset=True)

    # Update user table fields
    if "email" in update_dict:
        user.email = update_dict.pop("email")
    if "phone_number" in update_dict:
        user.phone_number = update_dict.pop("phone_number")
    if "avatar" in update_dict:
        user.avatar = update_dict.pop("avatar")

    # Update profile fields
    for field, value in update_dict.items():
        setattr(profile, field, value)

    db.commit()
    db.refresh(user)
    db.refresh(profile)

    return get_user_profile_service(db, user_id)

def change_user_password_service(db: Session, data: ForgotPasswordBase):
    # Kiểm tra user có tồn tại không
    user_query = db.query(UserModel).filter(UserModel.email == data.email).first()
    if not user_query:
        raise CustomException().not_found_exception()
    
    # Kiểm tra OTP
    otp = db.query(UserOTPModel).filter(
        UserOTPModel.user_id == user_query.id,
        UserOTPModel.otp_type == OTPTypeEnum.FORGOT_PASSWORD,
        UserOTPModel.is_used == False,
        UserOTPModel.expires_at > datetime.utcnow()
    ).order_by(UserOTPModel.created_at.desc()).first()
    
    if not otp:
        raise CustomException(content={
            "error": "No valid OTP found"
        }).bad_request_exception()
        
    if otp.otp_code != data.otp:
        raise CustomException(content={
            "error": "Invalid OTP code"
        }).bad_request_exception()
    
    # Kiểm tra mật khẩu mới có trùng với mật khẩu cũ không
    if pwd_context.verify(data.password, user_query.password):
        raise CustomException(content={
            "error": "New password must be different from current password"
        }).bad_request_exception()
    
    # Cập nhật mật khẩu mới
    hashed_password = get_password_hash(data.password)
    user_query.password = hashed_password
    
    # Đánh dấu OTP đã sử dụng
    otp.is_used = True
    
    db.add(user_query)
    db.add(otp)
    db.commit()
    

def update_avatar_firstlogin_service(db: Session, data: UpdateAvatarBase, user: UserModel):
    user.avatar = data.avatar
    response = requests.get(data.avatar, stream=True)

    # Kiểm tra nếu yêu cầu thành công (HTTP 200)
    if response.status_code == 200:
        # Chuyển đổi dữ liệu hình ảnh sang định dạng NumPy array
        image_arr = np.asarray(bytearray(response.content), dtype=np.uint8)
        user.avatar_binary = image_arr
    now = datetime.now(timezone.utc)
    user.last_login = now
    db.add(user)
    db.commit() 

def reresh_token_service(db: Session, refresh_token:str):
    payload = jwt.decode(refresh_token, os.environ.get('SECRET_KEY'), algorithms=[os.environ.get('ALGORITHM')])

    # Check token expire time
    if not payload.get('exp') or payload.get('exp') and (payload.get('exp') < datetime.now(timezone.utc).timestamp()):
        raise CustomException().expired_token_exception()

    token = db.query(TokenModel).join(UserModel).filter((TokenModel.is_expired == False) & (TokenModel.refresh_token == refresh_token) & (TokenModel.user_id == payload.get('id')) & (UserModel.phone_number == payload.get('user_name'))).first()

    if not token:
        raise CustomException().invalid_token_exception()
    
    access_token, expired_access_token = create_access_token(data=payload, expires_delta=timedelta(days=int(os.environ.get('ACCESS_TOKEN_EXPIRE_DAYS'))))
    setattr(token, 'access_token', access_token)
    setattr(token, 'expire_at', expired_access_token)
    db.add(token)
    db.commit()
    db.refresh(token)
    return token

def update_password_service(db: Session, new_password: str, user: UserModel):
    setattr(user, 'password', get_password_hash(new_password))
    db.add(user)
    db.commit()
    db.refresh(user)
    return user


# def find_user_with_image_service(db: Session, image_path: str):
#     users = db.query(UserModel).filter(UserModel.avatar.isnot(None), UserModel.is_deleted == False).all()
#     identified_faces = find_user_with_image(users, image_path)
#     return identified_faces

def conver_imageurl_to_binary(db: Session):
    try:
        # Truy vấn tất cả người dùng có avatar không rỗng và chưa bị xóa
        users = db.query(UserModel).filter(UserModel.avatar.isnot(None), UserModel.is_deleted == False).all()
        
        updated_users = []

        for item in users:
            try:
                # Gửi yêu cầu HTTP để tải hình ảnh từ URL
                response = requests.get(item.avatar, stream=True)
                
                # Kiểm tra nếu yêu cầu thành công (HTTP 200)
                if response.status_code == 200:
                    # Chuyển đổi dữ liệu hình ảnh sang định dạng NumPy array
                    image_arr = np.asarray(bytearray(response.content), dtype=np.uint8)
                    
                    # Nén dữ liệu hình ảnh
                    
                    # Cập nhật dữ liệu nhị phân vào đối tượng User
                    item.avatar_binary = image_arr
                    updated_users.append(item)
                else:
                    print(f"Failed to download image for user {item.id}, status code: {response.status_code}")
            except Exception as e:
                print(f"An error occurred for user {item.id}: {str(e)}")

        # Sử dụng bulk_save_objects để lưu trữ hàng loạt
        if updated_users:
            db.bulk_save_objects(updated_users)
            db.commit()

    
    except Exception as e:
        print(f"An error occurred: {str(e)}")
    finally:
        db.close()
            

    
async def get_user_settings_service(db: Session, user_id: int):
    """Get all settings for a user"""
    user = db.query(UserModel).join(UserModel.profile).filter(
        UserModel.id == user_id
    ).first()
    user_settings = {}
    user_settings['enabled_touch_id'] = user.enabled_touch_id
    user_settings['enabled_face_id'] = user.enabled_face_id
    user_settings['is_visibled_map'] = user.profile.is_visibled_map
    user_settings['enabled_availability'] = user.profile.enabled_availability

    notification_settings = db.query(UserNotificationSettings).filter(
        UserNotificationSettings.user_id == user_id
    ).first()
    
    if not notification_settings:
        notification_settings = UserNotificationSettings(user_id=user_id)
        db.add(notification_settings)
        db.commit()
        db.refresh(notification_settings)

    subscription_settings = db.query(UserSubscriptionSettings).filter(
        UserSubscriptionSettings.user_id == user_id
    ).first()
    
    if not subscription_settings:
        subscription_settings = UserSubscriptionSettings(user_id=user_id)
        db.add(subscription_settings)
        db.commit()
        db.refresh(subscription_settings)

    web_link_settings = db.query(UserWebLinkSettings).filter(
        UserWebLinkSettings.user_id == user_id
    ).first()
    
    if not web_link_settings:
        web_link_settings = UserWebLinkSettings(user_id=user_id)
        db.add(web_link_settings)
        db.commit()
        db.refresh(web_link_settings)

    return {
        "user_settings": UserSettingsSchema(**user_settings),
        "notification_settings": notification_settings,
        "subscription_settings": subscription_settings,
        "web_link_settings": web_link_settings
    }

async def update_user_settings_service(db: Session, user_id:int, settings: UserSettingsSchema):
    user = db.query(UserModel).join(UserModel.profile).filter(
        UserModel.id == user_id
    ).first()
    user_profile = user.profile 
    user.enabled_touch_id = settings.enabled_touch_id
    user.enabled_face_id = settings.enabled_face_id
    user_profile.is_visibled_map = settings.is_visibled_map
    user_profile.enabled_availability = settings.enabled_availability
    db.commit()

async def update_notification_settings_service(db: Session, user_id: int, settings: NotificationSettingsBase):
    """Update notification settings for a user"""
    notification_settings = db.query(UserNotificationSettings).filter(
        UserNotificationSettings.user_id == user_id
    ).first()
    
    if not notification_settings:
        notification_settings = UserNotificationSettings(user_id=user_id)
        db.add(notification_settings)

    for key, value in settings.dict().items():
        setattr(notification_settings, key, value)

    db.commit()
    db.refresh(notification_settings)
    return notification_settings

async def update_subscription_settings_service(db: Session, user_id: int, settings: SubscriptionSettingsBase):
    """Update subscription settings for a user"""
    subscription_settings = db.query(UserSubscriptionSettings).filter(
        UserSubscriptionSettings.user_id == user_id
    ).first()
    
    if not subscription_settings:
        subscription_settings = UserSubscriptionSettings(user_id=user_id)
        db.add(subscription_settings)

    for key, value in settings.dict().items():
        setattr(subscription_settings, key, value)

    db.commit()
    db.refresh(subscription_settings)
    return subscription_settings

async def update_web_link_settings_service(db: Session, user_id: int, settings: WebLinkSettingsBase):
    """Update web link settings for a user"""
    web_link_settings = db.query(UserWebLinkSettings).filter(
        UserWebLinkSettings.user_id == user_id
    ).first()
    
    if not web_link_settings:
        web_link_settings = UserWebLinkSettings(user_id=user_id)
        db.add(web_link_settings)

    for key, value in settings.dict().items():
        setattr(web_link_settings, key, value)

    db.commit()
    db.refresh(web_link_settings)
    return web_link_settings

async def generate_profile_link_service(db: Session, user_id: int):
    """Generate new public profile link for a user"""
    web_link_settings = db.query(UserWebLinkSettings).filter(
        UserWebLinkSettings.user_id == user_id
    ).first()
    
    if not web_link_settings:
        web_link_settings = UserWebLinkSettings(user_id=user_id)
        db.add(web_link_settings)

    # Generate unique profile link
    profile_link = f"profile/{user_id}/{uuid.uuid4().hex[:8]}"
    web_link_settings.public_profile_link = profile_link

    db.commit()
    db.refresh(web_link_settings)
    return web_link_settings

async def generate_gift_claim_link_service(db: Session, user_id: int):
    """Generate new gift claim link for a user"""
    web_link_settings = db.query(UserWebLinkSettings).filter(
        UserWebLinkSettings.user_id == user_id
    ).first()
    
    if not web_link_settings:
        web_link_settings = UserWebLinkSettings(user_id=user_id)
        db.add(web_link_settings)

    # Generate unique gift claim link
    gift_claim_link = f"gift/{user_id}/{uuid.uuid4().hex[:8]}"
    web_link_settings.gift_claim_link = gift_claim_link

    db.commit()
    db.refresh(web_link_settings)
    return web_link_settings
        

async def update_all_settings_service(db: Session, user_id: int, settings: UpdateAllSettingsBase):
    """Update all settings for a user"""
    result = {}
    
    if settings.notification_settings:
        notification_settings = await update_notification_settings_service(db, user_id, settings.notification_settings)
        result["notification_settings"] = notification_settings
        
    if settings.subscription_settings:
        subscription_settings = await update_subscription_settings_service(db, user_id, settings.subscription_settings)
        result["subscription_settings"] = subscription_settings
        
    if settings.web_link_settings:
        web_link_settings = await update_web_link_settings_service(db, user_id, settings.web_link_settings)
        result["web_link_settings"] = web_link_settings
        
    # If any settings were not updated, get their current values
    if not result.get("notification_settings"):
        notification_settings = db.query(UserNotificationSettings).filter(
            UserNotificationSettings.user_id == user_id
        ).first()
        if notification_settings:
            result["notification_settings"] = notification_settings
            
    if not result.get("subscription_settings"):
        subscription_settings = db.query(UserSubscriptionSettings).filter(
            UserSubscriptionSettings.user_id == user_id
        ).first()
        if subscription_settings:
            result["subscription_settings"] = subscription_settings
            
    if not result.get("web_link_settings"):
        web_link_settings = db.query(UserWebLinkSettings).filter(
            UserWebLinkSettings.user_id == user_id
        ).first()
        if web_link_settings:
            result["web_link_settings"] = web_link_settings
            
    return result
        

def get_user_profile_code_service(db: Session, code: str, current_user_id: int) -> Optional[UserInformationGetByCodeSchema]:
    # Tìm user theo code
    user = db.query(UserModel).join(
        UserModel.profile
    ).options(
        joinedload(UserModel.profile),
        joinedload(UserModel.business_profile)
    ).filter(
        UserModel.qr_code == code,
        UserModel.is_deleted == False,
        UserModel.profile.has(public_profile=True)
    ).first()
    
    if not user:
        return None
        
    # Kiểm tra xem user đã là contact của current_user chưa
    is_contact = db.query(ContactModel).filter(
        ContactModel.user_id == current_user_id,
        ContactModel.user_contact_id == user.id,
        ContactModel.is_deleted == False
    ).first() is not None
    
    # Tạo response data
    response_data = {
        "id": user.id,
        "email": user.email,
        "is_contact": is_contact
    }
    
    # Thêm thông tin từ profile nếu có
    if user.profile:
        profile_data = {
            "full_name": user.profile.full_name,
            "gender": user.profile.gender,
            "date_of_birth": user.profile.date_of_birth,
            "bio": user.profile.bio,
            "address": user.profile.address,
            "social_media_links": user.profile.social_media_links,
            "industry_tags": user.profile.industry_tags,
            "skill_interests": user.profile.skill_interests
        }
        response_data.update(profile_data)
    
    # Thêm thông tin từ business profile nếu có
    if user.business_profile:
        business_info = {
            "business_name": user.business_profile.business_name,
            "business_ein": user.business_profile.business_ein,
            "business_address": user.business_profile.business_address,
            "business_description": user.business_profile.business_description,
            "opening_hours": user.business_profile.opening_hours,
            "industry_tags": user.business_profile.industry_tags,
            "about": user.business_profile.about,
            "services": user.business_profile.services
        }
        response_data["business_info"] = business_info
    
    return UserInformationGetByCodeSchema(**response_data)

def update_availability_service(db, user_id: int, data):
    user_profile = db.query(UserProfileModel).filter(UserProfileModel.user_id == user_id).first()
    if not user_profile:
        raise CustomException().not_found_exception()

    # Validate input (Pydantic đã validate ở schema)
    user_profile.enabled_availability = True
    user_profile.availability = data.dict()
    db.add(user_profile)
    db.commit()
    db.refresh(user_profile)
    return {
        "enabled_availability": user_profile.enabled_availability,
        "availability": user_profile.availability
    }

def get_user_profile_by_link_service(db: Session, link: str, current_user_id: int) -> Optional[UserInformationGetByCodeSchema]:
    # Tìm user theo code
    user = db.query(UserModel).join(
        UserModel.profile
    ).join(
        UserModel.web_link_settings
    ).options(
        joinedload(UserModel.profile),
        joinedload(UserModel.business_profile),
        joinedload(UserModel.web_link_settings)
    ).filter(
        UserModel.web_link_settings.has(public_profile_link=link),
        UserModel.is_deleted == False,
        UserModel.profile.has(public_profile=True)
    ).first()
    
    if not user:
        return None
        
    # Kiểm tra xem user đã là contact của current_user chưa
    is_contact = db.query(ContactModel).filter(
        ContactModel.user_id == current_user_id,
        ContactModel.user_contact_id == user.id,
        ContactModel.is_deleted == False
    ).first() is not None
    
    # Tạo response data
    response_data = {
        "id": user.id,
        "email": user.email,
        "is_contact": is_contact
    }
    
    # Thêm thông tin từ profile nếu có
    if user.profile:
        profile_data = {
            "full_name": user.profile.full_name,
            "gender": user.profile.gender,
            "date_of_birth": user.profile.date_of_birth,
            "bio": user.profile.bio,
            "address": user.profile.address,
            "social_media_links": user.profile.social_media_links,
            "industry_tags": user.profile.industry_tags,
            "skill_interests": user.profile.skill_interests
        }
        response_data.update(profile_data)
    
    # Thêm thông tin từ business profile nếu có
    if user.business_profile:
        business_info = {
            "business_name": user.business_profile.business_name,
            "business_ein": user.business_profile.business_ein,
            "business_address": user.business_profile.business_address,
            "business_description": user.business_profile.business_description,
            "opening_hours": user.business_profile.opening_hours,
            "industry_tags": user.business_profile.industry_tags,
            "about": user.business_profile.about,
            "services": user.business_profile.services
        }
        response_data["business_info"] = business_info
    
    return UserInformationGetByCodeSchema(**response_data)

def update_availability_service(db, user_id: int, data):
    user_profile = db.query(UserProfileModel).filter(UserProfileModel.user_id == user_id).first()
    if not user_profile:
        raise CustomException().not_found_exception()

    # Validate input (Pydantic đã validate ở schema)
    user_profile.enabled_availability = True
    user_profile.availability = data.dict()
    db.add(user_profile)
    db.commit()
    db.refresh(user_profile)
    return {
        "enabled_availability": user_profile.enabled_availability,
        "availability": user_profile.availability
    }

def get_availability_service(db, user_id: int):
    user_profile = db.query(UserProfileModel).filter(UserProfileModel.user_id == user_id).first()
    if not user_profile:
        raise CustomException().not_found_exception()
    return {
        "enabled_availability": user_profile.enabled_availability,
        "availability": user_profile.availability
    }

def generate_otp_service(
    db: Session,
    user_id: int,
    otp_type: OTPTypeEnum,
    new_email: Optional[str] = None,
    new_phone: Optional[str] = None,
) -> UserOTPModel:
    """Generate new OTP for user"""
    # Check daily limit for each OTP type
    today_start = datetime.utcnow().replace(hour=0, minute=0, second=0, microsecond=0)
    
    # Count total attempts today for this specific OTP type (new OTPs + resends)
    today_attempts = db.query(
        func.count(UserOTPModel.id) + func.sum(UserOTPModel.resend_count)
    ).filter(
        UserOTPModel.user_id == user_id,
        UserOTPModel.otp_type == otp_type,  # Filter by OTP type
        UserOTPModel.created_at >= today_start
    ).scalar() or 0
    
    # Limit: 5 total attempts per day per OTP type
    if today_attempts >= 5:
        raise CustomException(content={
            "error": f"Daily OTP limit reached for {otp_type.value}",
            "details": {
                "otp_type": otp_type.value,
                "total_attempts_today": today_attempts,
                "limit": 5
            }
        }).bad_request_exception()

    # Generate 6 digit OTP
    otp_code = ''.join([str(random.randint(0, 9)) for _ in range(6)])
    
    # Set expiration time (30 minutes from now)
    expires_at = datetime.utcnow() + timedelta(minutes=30)
    
    # Create new OTP record
    otp = UserOTPModel(
        user_id=user_id,
        otp_code=otp_code,
        otp_type=otp_type,
        expires_at=expires_at,
        resend_count=0,
        new_email=new_email,
        new_phone=new_phone,
    )
    
    db.add(otp)
    db.commit()
    db.refresh(otp)
    
    return otp

def send_otp_service(db: Session, user: UserModel, otp_type: OTPTypeEnum) -> UserOTPModel:
    user_id = user.id
    # Check if there's a valid OTP that can be reused
    existing_otp = db.query(UserOTPModel).filter(
        UserOTPModel.user_id == user_id,
        UserOTPModel.otp_type == otp_type,
        UserOTPModel.is_used == False,
        UserOTPModel.expires_at > datetime.utcnow()
    ).order_by(UserOTPModel.created_at.desc()).first()

    if existing_otp:
        # Check resend limit (max 5 times)
        if existing_otp.resend_count >= 5:
            raise CustomException(content={"error": "Maximum resend limit reached. Please request a new OTP."}).bad_request_exception()
            
        # If existing OTP has more than 2 minutes left, reuse it
        time_left = existing_otp.expires_at - datetime.utcnow()
        if time_left.total_seconds() > 120:  # 2 minutes
            # Increment resend count
            existing_otp.resend_count += 1
            db.commit()
            otp = existing_otp
        else:
            # Generate new OTP if existing one is about to expire
            otp = generate_otp_service(db, user_id, otp_type)
    else:
        # Generate new OTP if no valid OTP exists
        otp = generate_otp_service(db, user_id, otp_type)
    
    # Send OTP via email
    if otp_type in [OTPTypeEnum.USER_VERIFICATION, OTPTypeEnum.FORGOT_PASSWORD]:
        send_email_background(
            to_email=user.email,
            subject="Your OTP Code",
            body=template_otp_email(user.email, otp.otp_code)
        )
    # TODO: Add SMS sending for phone verification
    
    return otp

def verify_otp_service(db: Session, email: str, otp_code: str, otp_type: OTPTypeEnum) -> bool:
    """Verify OTP code"""
    # Get user by email
    user = db.query(UserModel).filter(UserModel.email == email).first()
    if not user:
        raise CustomException(content={"error": "User not found"}).not_found_exception()

    # Get latest unused OTP for user
    otp = db.query(UserOTPModel).filter(
        UserOTPModel.user_id == user.id,
        UserOTPModel.otp_type == otp_type,
        UserOTPModel.is_used == False,
        UserOTPModel.expires_at > datetime.utcnow()
    ).order_by(UserOTPModel.created_at.desc()).first()
    
    if not otp:
        raise CustomException(content={"error": "No valid OTP found"}).bad_request_exception()
        
    if otp.otp_code != otp_code:
        raise CustomException(content={"error": "Invalid OTP code"}).bad_request_exception()
        
    # Mark OTP as used
    otp.is_used = True
    db.commit()
    return True
def active_user_service(db: Session, email: str) -> bool:
    """Verify OTP code"""
    user = db.query(UserModel).filter(UserModel.email == email).first()
    if not user:
        raise CustomException(content={"error": "User not found"}).not_found_exception()
    
    user.is_active = True
    db.commit()
    return True

def resend_otp_service(db: Session, user: UserModel, otp_type: OTPTypeEnum) -> UserOTPModel:
    user_id = user.id
    # Check daily limit
    today_start = datetime.utcnow().replace(hour=0, minute=0, second=0, microsecond=0)
    today_otps = db.query(UserOTPModel).filter(
        UserOTPModel.user_id == user_id,
        UserOTPModel.otp_type == otp_type,
        UserOTPModel.created_at >= today_start
    ).count()
    
    if today_otps >= 5:
        raise CustomException(content={"error": "Daily OTP limit reached"}).bad_request_exception()

    # Check if there's a valid OTP that can be reused
    existing_otp = db.query(UserOTPModel).filter(
        UserOTPModel.user_id == user_id,
        UserOTPModel.otp_type == otp_type,
        UserOTPModel.is_used == False,
        UserOTPModel.expires_at > datetime.utcnow()
    ).order_by(UserOTPModel.created_at.desc()).first()

    if existing_otp:
        # Check resend limit (max 5 times)
        if existing_otp.resend_count >= 5:
            raise CustomException(content={"error": "Maximum resend limit reached. Please request a new OTP."}).bad_request_exception()
            
        # If existing OTP has more than 2 minutes left, reuse it
        time_left = existing_otp.expires_at - datetime.utcnow()
        if time_left.total_seconds() > 120:  # 2 minutes
            # Increment resend count
            db.commit()
            return send_otp_service(db, user, otp_type)
    
    # Invalidate all existing unused OTPs
    db.query(UserOTPModel).filter(
        UserOTPModel.user_id == user_id,
        UserOTPModel.otp_type == otp_type,
        UserOTPModel.is_used == False
    ).update({"is_used": True})
    db.commit()
    
    # Generate and send new OTP
    return send_otp_service(db, user, otp_type)

def check_user_exists_service(db: Session, email: str) -> dict:
    """
    Kiểm tra user có tồn tại hay không
    Returns:
        dict: {
            "exists": bool,
            "is_verified": bool,
            "message": str
        }
    """
    user = db.query(UserModel).filter(UserModel.email == email).first()
    
    if not user:
        return {
            "exists": False,
            "is_verified": False,
            "message": "User not found"
        }
    
    if not user.is_active:
        return {
            "exists": True,
            "is_verified": False,
            "message": "User exists but not verified"
        }
    
    return {
        "exists": True,
        "is_verified": True,
        "message": "User exists and verified"
    }

def send_forgot_password_otp_service(db: Session, email: str) -> dict:
    # Kiểm tra user có tồn tại không
    user = db.query(UserModel).filter(UserModel.email == email).first()
    if not user:
        raise CustomException(content={
            "error": "User not found"
        }).not_found_exception()
    
    # Gửi OTP qua email
    try:
        otp = generate_otp_service(db, user.id, OTPTypeEnum.FORGOT_PASSWORD)
        send_email_background(
            to_email=user.email,
            subject="Reset Your Password",
            body=template_forgot_password_otp_email(user.email, otp.otp_code)
        )
        
    except Exception as e:
        raise CustomException(content={
            "error": f"Failed to send OTP: {str(e)}"
        }).bad_request_exception()


def send_change_email_otp_service(db: Session, user_id: int, new_email: str) -> UserOTPModel:
    """Send OTP to new email for email change"""
    if db.query(UserModel).filter(UserModel.email == new_email).first():
        raise CustomException(content={"error": "Please enter a valid, unused email address."}).bad_request_exception()

    user = db.query(UserModel).filter(UserModel.id == user_id).first()
    if not user:
        raise CustomException(content={"error": "User not found"}).not_found_exception()

    otp = send_change_email_otp_generate(db, user_id, new_email)
    return otp


def send_change_phone_otp_service(db: Session, user_id: int, new_phone: str) -> UserOTPModel:
    """Send OTP to new phone number for phone change"""
    if db.query(UserModel).filter(UserModel.phone_number == new_phone).first():
        raise CustomException(content={"error": "Please enter a valid, unused phone number."}).bad_request_exception()

    user = db.query(UserModel).filter(UserModel.id == user_id).first()
    if not user:
        raise CustomException(content={"error": "User not found"}).not_found_exception()

    otp = send_change_phone_otp_generate(db, user_id, new_phone)
    return otp


def send_change_email_otp_generate(db: Session, user_id: int, new_email: str) -> UserOTPModel:
    """Generate and send OTP for email change"""
    existing_otp = db.query(UserOTPModel).filter(
        UserOTPModel.user_id == user_id,
        UserOTPModel.otp_type == OTPTypeEnum.CHANGE_EMAIL,
        UserOTPModel.new_email == new_email,
        UserOTPModel.is_used == False,
        UserOTPModel.expires_at > datetime.utcnow()
    ).order_by(UserOTPModel.created_at.desc()).first()

    if existing_otp:
        if existing_otp.resend_count >= 5:
            raise CustomException(content={"error": "Maximum resend limit reached. Please request a new OTP."}).bad_request_exception()
        time_left = existing_otp.expires_at - datetime.utcnow()
        if time_left.total_seconds() > 120:
            existing_otp.resend_count += 1
            db.commit()
            otp = existing_otp
        else:
            otp = generate_otp_service(db, user_id, OTPTypeEnum.CHANGE_EMAIL, new_email)
    else:
        otp = generate_otp_service(db, user_id, OTPTypeEnum.CHANGE_EMAIL, new_email)

    send_email_background(
        to_email=new_email,
        subject="Verify Your New Email",
        body=template_otp_email(new_email, otp.otp_code)
    )
    return otp


def send_change_phone_otp_generate(db: Session, user_id: int, new_phone: str) -> UserOTPModel:
    """Generate and send OTP for phone number change"""
    existing_otp = db.query(UserOTPModel).filter(
        UserOTPModel.user_id == user_id,
        UserOTPModel.otp_type == OTPTypeEnum.CHANGE_PHONE,
        UserOTPModel.new_phone == new_phone,
        UserOTPModel.is_used == False,
        UserOTPModel.expires_at > datetime.utcnow(),
    ).order_by(UserOTPModel.created_at.desc()).first()

    if existing_otp:
        if existing_otp.resend_count >= 5:
            raise CustomException(content={"error": "Maximum resend limit reached. Please request a new OTP."}).bad_request_exception()
        time_left = existing_otp.expires_at - datetime.utcnow()
        if time_left.total_seconds() > 120:
            existing_otp.resend_count += 1
            db.commit()
            otp = existing_otp
        else:
            otp = generate_otp_service(db, user_id, OTPTypeEnum.CHANGE_PHONE, new_phone=new_phone)
    else:
        otp = generate_otp_service(db, user_id, OTPTypeEnum.CHANGE_PHONE, new_phone=new_phone)

    try:
        send_sms_sendgrid(db, new_phone, f"Your OTP code is {otp.otp_code}")
    except Exception as e:
        print(f"Failed to send phone OTP: {str(e)}")
    return otp


def verify_change_phone_otp_service(db: Session, user_id: int, new_phone: str, otp_code: str) -> bool:
    """Verify OTP and update user's phone number"""
    user = db.query(UserModel).filter(UserModel.id == user_id).first()
    if not user:
        raise CustomException(content={"error": "User not found"}).not_found_exception()

    otp = db.query(UserOTPModel).filter(
        UserOTPModel.user_id == user_id,
        UserOTPModel.new_phone == new_phone,
        UserOTPModel.otp_type == OTPTypeEnum.CHANGE_PHONE,
        UserOTPModel.is_used == False,
        UserOTPModel.expires_at > datetime.utcnow(),
    ).order_by(UserOTPModel.created_at.desc()).first()

    if not otp or otp.otp_code != otp_code:
        raise CustomException(content={"error": "Invalid or expired verification code."}).bad_request_exception()

    otp.is_used = True
    user.phone_number = new_phone
    db.add_all([otp, user])
    db.commit()
    return True


def verify_change_email_otp_service(db: Session, user_id: int, new_email: str, otp_code: str) -> bool:
    """Verify OTP and update user's email"""
    user = db.query(UserModel).filter(UserModel.id == user_id).first()
    if not user:
        raise CustomException(content={"error": "User not found"}).not_found_exception()

    otp = db.query(UserOTPModel).filter(
        UserOTPModel.user_id == user_id,
        UserOTPModel.new_email == new_email,
        UserOTPModel.otp_type == OTPTypeEnum.CHANGE_EMAIL,
        UserOTPModel.is_used == False,
        UserOTPModel.expires_at > datetime.utcnow()
    ).order_by(UserOTPModel.created_at.desc()).first()

    if not otp or otp.otp_code != otp_code:
        raise CustomException(content={"error": "Invalid or expired verification code."}).bad_request_exception()

    otp.is_used = True
    user.email = new_email
    db.add_all([otp, user])
    db.commit()
    return True

def delete_user_account_service(db: Session, user_id: int) -> dict:
    """
    Đánh dấu is_deleted cho toàn bộ dữ liệu liên quan đến user và user,
    đồng thời đổi email user thành email+_deleted.
    """
    from src.user.models import (
        User as UserModel,
        UserProfile as UserProfileModel,
        Token as TokenModel,
        UserNotificationSettings,
        UserSubscriptionSettings,
        UserWebLinkSettings,
        BusinessProfile,
        UserOTP as UserOTPModel,
    )
    from src.contact.models import Contact, ContactRequest, ContactDismissSuggestion
    from src.event.models import Event, EventInvitation, EventInteraction
    from src.lead.models import Lead
    from src.order.models import Order

    try:
        # Update is_deleted cho các bảng liên quan (nếu có trường is_deleted)
        models_with_is_deleted = [
            (Contact, ((Contact.user_id == user_id) | (Contact.user_contact_id == user_id))),
            (ContactRequest, ((ContactRequest.from_user_id == user_id) | (ContactRequest.to_user_id == user_id))),
            (ContactDismissSuggestion, (ContactDismissSuggestion.user_id == user_id)),
            (EventInvitation, (EventInvitation.contact_id.in_(db.query(Contact.id).filter(Contact.user_id == user_id)))),
            (EventInteraction, (EventInteraction.user_id == user_id)),
            (Event, (Event.created_by == user_id)),
            (Lead, (Lead.user_id == user_id)),
            (Order, (Order.user_id == user_id)),
            (TokenModel, (TokenModel.user_id == user_id)),
            (UserProfileModel, (UserProfileModel.user_id == user_id)),
            (BusinessProfile, (BusinessProfile.user_id == user_id)),
            (UserNotificationSettings, (UserNotificationSettings.user_id == user_id)),
            (UserSubscriptionSettings, (UserSubscriptionSettings.user_id == user_id)),
            (UserWebLinkSettings, (UserWebLinkSettings.user_id == user_id)),
            (UserOTPModel, (UserOTPModel.user_id == user_id)),
        ]

        for model, filter_cond in models_with_is_deleted:
            # Kiểm tra model có trường is_deleted không
            if hasattr(model, "is_deleted"):
                db.query(model).filter(filter_cond).update({"is_deleted": True}, synchronize_session=False)

        # Xử lý riêng cho User
        user = db.query(UserModel).filter(UserModel.id == user_id).first()
        if not user:
            db.rollback()
            raise CustomException(content={"error": "User not found"}).not_found_exception()
        # Đổi email thành email+_deleted (nếu chưa có _deleted)
        if user.email and not user.email.endswith("_deleted"):
            from datetime import datetime
            timestamp = datetime.utcnow().strftime("%Y%m%d%H%M%S")
            user.email = f"{user.email}_{timestamp}_deleted"
        # Đánh dấu is_deleted cho user nếu có
        if hasattr(user, "is_deleted"):
            user.is_deleted = True
        db.add(user)

        db.commit()
        return {"message": "Account and all related data marked as deleted successfully."}
    except Exception as e:
        db.rollback()
        raise CustomException(content={"error": str(e)}).api_exception()

