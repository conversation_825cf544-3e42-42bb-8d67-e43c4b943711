from fastapi import APIRouter, Depends, Query
from sqlalchemy.orm import Session
from src.utils.database import get_db
from src.user.models import User
from src.utils.permission import is_authenticated
from src.utils.token_docs import jwt_auth
from src.utils.response import CustomResponse
from src.ticket_sale.schemas import (
    TicketOrderCreate,
    TicketOrderResponse,
    PaginatedTicketOrderResponse
)
from src.ticket_sale.service import (
    purchase_ticket_service,
    get_order_detail_service,
    list_orders_service
)

router = APIRouter(prefix="", tags=["TicketOrder"])


@router.post("", response_model=TicketOrderResponse, dependencies=[Depends(jwt_auth)])
def purchase_ticket(
    order_data: TicketOrderCreate,
    db: Session = Depends(get_db),
    current_user: User = Depends(is_authenticated)
):
    result = purchase_ticket_service(db, current_user.id, order_data)
    return CustomResponse(content=result).format_data_create()


@router.get("/{order_id}", response_model=TicketOrderResponse, dependencies=[Depends(jwt_auth)])
def get_order_detail(
    order_id: int,
    db: Session = Depends(get_db),
    current_user: User = Depends(is_authenticated)
):
    result = get_order_detail_service(db, order_id, current_user.id)
    return CustomResponse(content=result).format_data_get()


@router.get("", response_model=PaginatedTicketOrderResponse, dependencies=[Depends(jwt_auth)])
def list_my_orders(
    page: int = Query(1, ge=1),
    size: int = Query(10, ge=1, le=100),
    db: Session = Depends(get_db),
    current_user: User = Depends(is_authenticated)
):
    result = list_orders_service(db, current_user.id, page, size)
    return CustomResponse(content=result).format_data_get()
