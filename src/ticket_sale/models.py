from datetime import datetime
import enum
from sqlalchemy import Column, Integer, ForeignKey, Float, DateTime, Enum
from sqlalchemy.orm import relationship
from src.utils.base_model import BaseModel, Base


class PaymentStatusEnum(str, enum.Enum):
    PENDING = "PENDING"
    PAID = "PAID"
    FAILED = "FAILED"

class TicketOrder(BaseModel, Base):
    __tablename__ = "ticket_order"

    ticket_id = Column(Integer, ForeignKey("ticket.id"), nullable=False)
    user_id = Column(Integer, ForeignKey("user.id"), nullable=False)
    quantity = Column(Integer, nullable=False)
    total_price = Column(Float, nullable=False)
    purchased_at = Column(DateTime, default=datetime.utcnow)
    payment_status = Column(Enum(PaymentStatusEnum), default=PaymentStatusEnum.PENDING)

    ticket = relationship("Ticket")
    user = relationship("User")
    transactions = relationship(
        "Transaction",
        secondary="ticket_order_transaction",
        back_populates="ticket_orders",
    )
    invoices = relationship(
        "Invoice",
        secondary="invoice_ticket_order",
        back_populates="ticket_orders",
    )
