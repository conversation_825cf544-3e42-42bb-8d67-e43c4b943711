from sqlalchemy.orm import Session
from sqlalchemy import desc
from src.utils.exceptions import CustomException
from src.ticket.models import Ticket
from src.ticket_sale.models import TicketOrder, PaymentStatusEnum
from src.ticket_sale.schemas import TicketOrderCreate, PaginatedTicketOrderResponse


def purchase_ticket_service(db: Session, user_id: int, order_data: TicketOrderCreate) -> TicketOrder:
    try:
        ticket = db.query(Ticket).filter(
            Ticket.id == order_data.ticket_id,
            Ticket.is_deleted == False
        ).first()
        if not ticket:
            raise CustomException(content={"error": "Ticket not found"}).not_found_exception()
        if ticket.quantity < order_data.quantity:
            raise CustomException(content={"error": "Not enough tickets available"}).bad_request_exception()

        ticket.quantity -= order_data.quantity
        total_price = ticket.price * order_data.quantity
        order = TicketOrder(
            ticket_id=ticket.id,
            user_id=user_id,
            quantity=order_data.quantity,
            total_price=total_price,
            payment_status=PaymentStatusEnum.PENDING
        )
        db.add(order)
        db.commit()
        db.refresh(order)
        return order
    except Exception:
        db.rollback()
        raise CustomException().bad_request_exception()


def get_order_detail_service(db: Session, order_id: int, user_id: int) -> TicketOrder:
    order = db.query(TicketOrder).filter(
        TicketOrder.id == order_id,
        TicketOrder.user_id == user_id,
        TicketOrder.is_deleted == False
    ).first()
    if not order:
        raise CustomException().not_found_exception()
    return order


def list_orders_service(db: Session, user_id: int, page: int, size: int) -> PaginatedTicketOrderResponse:
    query = db.query(TicketOrder).filter(
        TicketOrder.user_id == user_id,
        TicketOrder.is_deleted == False
    ).order_by(desc(TicketOrder.created_at))
    total = query.count()
    total_pages = (total + size - 1) // size
    orders = query.offset((page - 1) * size).limit(size).all()
    return PaginatedTicketOrderResponse(
        items=orders,
        total=total,
        page=page,
        size=size,
        total_pages=total_pages
    )
