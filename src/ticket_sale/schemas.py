from datetime import datetime
from pydantic import Field
from .models import PaymentStatusEnum
from src.utils.schemas import PaginatedResponse
from src.utils.auto_schema import AutoSchema

class TicketOrderCreate(AutoSchema):
    ticket_id: int = Field(..., example=1)
    quantity: int = Field(..., example=1)

class TicketOrderResponse(AutoSchema):
    id: int
    ticket_id: int
    user_id: int
    quantity: int
    total_price: float
    purchased_at: datetime
    payment_status: PaymentStatusEnum
    created_at: datetime
    updated_at: datetime

    class Config:
        from_attributes = True

PaginatedTicketOrderResponse = PaginatedResponse[TicketOrderResponse]
