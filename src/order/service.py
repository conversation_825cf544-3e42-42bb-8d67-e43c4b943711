from sqlalchemy.orm import Session
from sqlalchemy import select, desc
from src.utils.exceptions import CustomException
from .models import Order
from .schemas import PaginatedOrderResponse


def list_orders_service(db: Session, user_id: int, page: int, size: int) -> PaginatedOrderResponse:
    query = db.query(Order).filter(Order.user_id == user_id).order_by(desc(Order.created_at))
    total = query.count()
    items = query.offset((page - 1) * size).limit(size).all()
    total_pages = (total + size - 1) // size
    return PaginatedOrderResponse(items=items, total=total, page=page, size=size, total_pages=total_pages)


def list_orders_by_organization_service(db: Session, organization_id: int, page: int, size: int) -> PaginatedOrderResponse:
    query = db.query(Order).filter(Order.organization_id == organization_id).order_by(desc(Order.created_at))
    total = query.count()
    items = query.offset((page - 1) * size).limit(size).all()
    total_pages = (total + size - 1) // size
    return PaginatedOrderResponse(items=items, total=total, page=page, size=size, total_pages=total_pages)


def get_order_detail_service(db: Session, order_id: int, user_id: int) -> Order:
    order = db.scalar(select(Order).where(Order.id == order_id, Order.user_id == user_id))
    if not order:
        raise CustomException().not_found_exception(message="Order not found")
    return order
