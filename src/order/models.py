from sqlalchemy import Column, Integer, ForeignKey, Float, String, Enum, Text
from sqlalchemy.orm import relationship
from src.utils.base_model import BaseModel, Base
from src.utils.enum import ShippingMethodEnum, OrderStatusEnum

class Order(BaseModel, Base):
    __tablename__ = "order"

    user_id = Column(Integer, ForeignKey("user.id"), nullable=False)
    organization_id = Column(Integer, ForeignKey("organization.id"), nullable=False)
    total_price = Column(Float, nullable=False, default=0)
    shipping_full_name = Column(String(255), nullable=False)
    shipping_phone = Column(String(50), nullable=False)
    shipping_address = Column(Text, nullable=False)
    shipping_fee = Column(Float, nullable=False, default=0)
    shipping_method = Column(Enum(ShippingMethodEnum), default=ShippingMethodEnum.STANDARD)
    status = Column(Enum(OrderStatusEnum), default=OrderStatusEnum.PENDING)

    user = relationship("User")
    organization = relationship("Organization")
    items = relationship("OrderItem", back_populates="order", cascade="all, delete-orphan")


class OrderItem(BaseModel, Base):
    __tablename__ = "order_item"

    order_id = Column(Integer, ForeignKey("order.id"), nullable=False)
    product_id = Column(Integer, ForeignKey("product.id"), nullable=False)
    quantity = Column(Integer, nullable=False, default=1)
    price = Column(Float, nullable=False, default=0)

    order = relationship("Order", back_populates="items")
    product = relationship("Product")
