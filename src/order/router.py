from fastapi import APIRouter, Depends, Query, Path
from sqlalchemy.orm import Session
from src.utils.database import get_db
from src.utils.permission import is_authenticated
from src.utils.token_docs import jwt_auth
from src.utils.response import CustomResponse
from .schemas import PaginatedOrderResponse, OrderRead
from .service import list_orders_service, list_orders_by_organization_service, get_order_detail_service
from src.user.models import User

router = APIRouter(prefix="", tags=["Order"])


@router.get("", response_model=PaginatedOrderResponse, dependencies=[Depends(jwt_auth)])
def list_orders(
    page: int = Query(1, ge=1),
    size: int = Query(10, ge=1, le=100),
    db: Session = Depends(get_db),
    current_user: User = Depends(is_authenticated),
):
    result = list_orders_service(db, current_user.id, page, size)
    return CustomResponse(content=result).format_data_get()


@router.get("/organization/{organization_id}", response_model=PaginatedOrderResponse, dependencies=[Depends(jwt_auth)])
def list_orders_by_organization(
    organization_id: int = Path(..., ge=1),
    page: int = Query(1, ge=1),
    size: int = Query(10, ge=1, le=100),
    db: Session = Depends(get_db),
    current_user: User = Depends(is_authenticated),
):
    result = list_orders_by_organization_service(db, organization_id, page, size)
    return CustomResponse(content=result).format_data_get()


@router.get("/{order_id}", response_model=OrderRead, dependencies=[Depends(jwt_auth)])
def get_order_detail(
    order_id: int = Path(..., ge=1),
    db: Session = Depends(get_db),
    current_user: User = Depends(is_authenticated),
):
    result = get_order_detail_service(db, order_id, current_user.id)
    return CustomResponse(content=result).format_data_get()
