from datetime import datetime
from typing import List
from pydantic import Field
from src.utils.auto_schema import AutoSchema
from src.utils.schemas import PaginatedResponse
from src.product.schemas import ProductRead
from src.utils.enum import ShippingMethodEnum, OrderStatusEnum


class OrderItemRead(AutoSchema):
    id: int
    order_id: int
    product_id: int
    quantity: int = Field(..., example=1)
    price: float = Field(..., example=10.0)
    created_at: datetime
    updated_at: datetime
    product: ProductRead

    class Config:
        from_attributes = True

class OrderRead(AutoSchema):
    id: int
    user_id: int
    organization_id: int
    total_price: float = Field(..., example=100.0)
    shipping_full_name: str = Field(..., example="John Doe")
    shipping_phone: str = Field(..., example="123456789")
    shipping_address: str = Field(..., example="123 Main St")
    shipping_fee: float = Field(..., example=5.0)
    shipping_method: ShippingMethodEnum = Field(default=ShippingMethodEnum.STANDARD, example=ShippingMethodEnum.STANDARD)
    status: OrderStatusEnum = Field(default=OrderStatusEnum.PENDING, example=OrderStatusEnum.PENDING)
    created_at: datetime
    updated_at: datetime
    items: List[OrderItemRead]

    class Config:
        from_attributes = True

PaginatedOrderResponse = PaginatedResponse[OrderRead]
