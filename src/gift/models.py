import datetime
from sqlalchemy import Column, Integer, String, Text, ForeignKey, DateTime, Enum, JSON
from sqlalchemy.orm import relationship
from src.utils.base_model import Base, BaseModel
from src.utils.enum import GiftTypeEnum, GiftStatusEnum

class GiftCategory(Base, BaseModel):
    __tablename__ = "gift_category"

    id = Column(Integer, primary_key=True, index=True)
    name = Column(String(255), nullable=False)
    description = Column(Text, nullable=True)
    

    # Relationship
    gifts = relationship("Gift", back_populates="category")

class Gift(Base, BaseModel):
    __tablename__ = "gift"

    id = Column(Integer, primary_key=True, index=True)
    name = Column(String(255), nullable=False)
    image = Column(String(255), nullable=False)
    description = Column(Text, nullable=True)
    category_id = Column(Integer, ForeignKey("gift_category.id"), nullable=False)
    

    # Relationships
    category = relationship("GiftCategory", back_populates="gifts")
    user_gifts = relationship("UserGift", back_populates="gift")

class UserGift(Base, BaseModel):
    __tablename__ = "user_gift"

    id = Column(Integer, primary_key=True, index=True)
    sender_id = Column(Integer, ForeignKey("user.id"), nullable=False)
    receiver_id = Column(Integer, ForeignKey("user.id"), nullable=False)
    gift_id = Column(Integer, ForeignKey("gift.id"), nullable=False)
    gift_type = Column(Enum(GiftTypeEnum), nullable=False, default=GiftTypeEnum.PRODUCT)
    ticket_order_id = Column(Integer, ForeignKey("ticket_order.id"), nullable=True)
    status = Column(Enum(GiftStatusEnum), default=GiftStatusEnum.SENT)
    message = Column(Text, nullable=True)
    sent_at = Column(DateTime, default=datetime.datetime.now(datetime.timezone.utc))
    order_id = Column(Integer, ForeignKey("order.id"), nullable=True)
    details = Column(JSON, nullable=True, default=None)

    # Relationships
    gift = relationship("Gift", back_populates="user_gifts")
    sender = relationship("User", foreign_keys=[sender_id], back_populates="sent_gifts")
    receiver = relationship("User", foreign_keys=[receiver_id], back_populates="received_gifts")
    ticket_order = relationship("TicketOrder")
    order = relationship("Order")