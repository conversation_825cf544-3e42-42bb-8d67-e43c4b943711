from datetime import datetime
from typing import Optional, List
from pydantic import Field, BaseModel
from src.utils.schemas import PaginatedResponse
from src.utils.enum import GiftTypeEnum, GiftStatusEnum, ShippingMethodEnum
import enum

# Gift Category Schemas
class GiftCategoryBaseSchemas(BaseModel):
    name: str = Field(..., description="Name of the gift category")
    description: Optional[str] = Field(None, description="Description of the gift category")

    model_config = {
        "json_schema_extra": {
            "examples": [
                {
                    "name": "Birthday Gifts",
                    "description": "Gifts suitable for birthday celebrations"
                }
            ]
        }
    }

class GiftCategoryCreateSchemas(GiftCategoryBaseSchemas):
    pass

class GiftCategoryUpdateSchemas(GiftCategoryBaseSchemas):
    pass

class GiftCategorySchemas(GiftCategoryBaseSchemas):
    id: int
    created_at: datetime
    updated_at: datetime

    class Config:
        from_attributes = True

# Gift Schemas
class GiftBaseSchemas(BaseModel):
    name: str = <PERSON>(..., description="Name of the gift")
    image: str = Field(..., description="URL or filename of the gift image")
    description: Optional[str] = Field(None, description="Description of the gift")
    category_id: int = Field(..., description="ID of the gift category")

    model_config = {
        "json_schema_extra": {
            "examples": [
                {
                    "name": "Birthday Cake",
                    "image": "https://example.com/birthday-cake.jpg",
                    "description": "A delicious birthday cake",
                    "category_id": 1
                }
            ]
        }
    }

class GiftCreateSchemas(GiftBaseSchemas):
    pass

class GiftUpdateSchemas(GiftBaseSchemas):
    pass

class GiftSchemas(GiftBaseSchemas):
    id: int
    created_at: datetime
    updated_at: datetime
    category: GiftCategorySchemas

    class Config:
        from_attributes = True

# User Gift Schemas
class UserGiftBaseSchemas(BaseModel):
    receiver_id: int = Field(..., description="ID of the user receiving the gift")
    gift_id: int = Field(..., description="ID of the gift being sent")
    gift_type: GiftTypeEnum = Field(..., description="Type of gift")
    ticket_order_id: Optional[int] = Field(None, description="Ticket order ID if gifting a ticket")
    order_item_id: Optional[int] = Field(None, description="Order item ID if gifting a product")
    message: Optional[str] = Field(None, description="Optional message with the gift")

    model_config = {
        "json_schema_extra": {
            "examples": [
                {
                    "receiver_id": 123,
                    "gift_id": 1,
                    "gift_type": "PRODUCT",
                    "order_item_id": 10,
                    "ticket_order_id": None,
                    "message": "Happy Birthday!"
                }
            ]
        }
    }

class GiftItemCreateSchema(BaseModel):
    product_id: int
    quantity: int = Field(..., gt=0)

class GiftDetailCreateSchema(BaseModel):
    organization_id: int
    shipping_fee: float
    shipping_method: Optional[ShippingMethodEnum] = None
    shipping_full_name: Optional[str] = None
    shipping_phone_number: Optional[str] = None
    shipping_address: Optional[str] = None
    product_id: int
    quantity: int = Field(..., gt=0)
    total_price: float

class TicketGiftDetailCreateSchema(BaseModel):
    ticket_id: int = Field(..., description="ID of the ticket to gift")
    quantity: int = Field(..., gt=0, description="Quantity of tickets to gift")

class UserGiftCreateSchemas(BaseModel):
    gift_id: Optional[int] = None  # template
    message: Optional[str] = None
    receiver_id: int
    gift_type: GiftTypeEnum = Field(..., description="Type of gift: PRODUCT or TICKET")
    product_details: Optional[List[GiftDetailCreateSchema]] = Field(None, description="Product gift details")
    ticket_details: Optional[List[TicketGiftDetailCreateSchema]] = Field(None, description="Ticket gift details")

class UserGiftSchemas(UserGiftBaseSchemas):
    id: int
    sender_id: int
    receiver_id: int
    gift_id: Optional[int] = None  # Có thể nullable cho ticket
    gift_type: GiftTypeEnum
    ticket_order_id: Optional[int] = None
    order_id: Optional[int] = None
    message: Optional[str] = None
    status: GiftStatusEnum
    sent_at: datetime
    gift: Optional[GiftSchemas] = None  # Có thể nullable cho ticket

    class Config:
        from_attributes = True

# Response Schemas
class GiftCategoryListSchemas(BaseModel):
    id: int
    name: str
    description: Optional[str] = None
    created_at: datetime
    updated_at: datetime

    model_config = {
        "json_schema_extra": {
            "examples": [
                {
                    "id": 1,
                    "name": "Birthday Gifts",
                    "description": "Gifts suitable for birthday celebrations",
                    "created_at": "2024-03-20T10:00:00",
                    "updated_at": "2024-03-20T10:00:00"
                }
            ]
        }
    }

class GiftListSchemas(BaseModel):
    id: int
    name: str
    image: str
    description: Optional[str] = None
    category_id: int
    created_at: datetime
    updated_at: datetime
    category: GiftCategorySchemas

    model_config = {
        "json_schema_extra": {
            "examples": [
                {
                    "id": 1,
                    "name": "Birthday Cake",
                    "image": "https://example.com/birthday-cake.jpg",
                    "description": "A delicious birthday cake",
                    "category_id": 1,
                    "created_at": "2024-03-20T10:00:00",
                    "updated_at": "2024-03-20T10:00:00",
                    "category": {
                        "id": 1,
                        "name": "Birthday Gifts",
                        "description": "Gifts suitable for birthday celebrations",
                        "created_at": "2024-03-20T10:00:00",
                        "updated_at": "2024-03-20T10:00:00"
                    }
                }
            ]
        }
    }

class UserGiftListSchemas(BaseModel):
    id: int
    sender_id: int
    receiver_id: int
    gift_id: int
    gift_type: GiftTypeEnum
    ticket_order_id: Optional[int] = None
    order_item_id: Optional[int] = None
    message: Optional[str] = None
    status: GiftStatusEnum
    sent_at: datetime
    gift: GiftSchemas

    model_config = {
        "json_schema_extra": {
            "examples": [
                {
                    "id": 1,
                    "sender_id": 456,
                    "receiver_id": 123,
                    "gift_id": 1,
                    "gift_type": "PRODUCT",
                    "ticket_order_id": None,
                    "order_item_id": 10,
                    "message": "Happy Birthday!",
                    "status": "SENT",
                    "sent_at": "2024-03-20T10:00:00",
                    "gift": {
                        "id": 1,
                        "name": "Birthday Cake",
                        "image": "https://example.com/birthday-cake.jpg",
                        "description": "A delicious birthday cake",
                        "category_id": 1,
                        "created_at": "2024-03-20T10:00:00",
                        "updated_at": "2024-03-20T10:00:00",
                        "category": {
                            "id": 1,
                            "name": "Birthday Gifts",
                            "description": "Gifts suitable for birthday celebrations",
                            "created_at": "2024-03-20T10:00:00",
                            "updated_at": "2024-03-20T10:00:00"
                        }
                    }
                }
            ]
        }
    }

# Use generic pagination response
PaginatedGiftCategoryResponse = PaginatedResponse[GiftCategoryListSchemas]
PaginatedGiftResponse = PaginatedResponse[GiftListSchemas]
PaginatedUserGiftResponse = PaginatedResponse[UserGiftListSchemas]

# Add example for paginated response
PaginatedGiftCategoryResponse.model_config = {
    "json_schema_extra": {
        "examples": [
            {
                "items": [
                    {
                        "id": 1,
                        "name": "Birthday Gifts",
                        "description": "Gifts suitable for birthday celebrations",
                        "created_at": "2024-03-20T10:00:00",
                        "updated_at": "2024-03-20T10:00:00"
                    },
                    {
                        "id": 2,
                        "name": "Anniversary Gifts",
                        "description": "Gifts for wedding anniversaries",
                        "created_at": "2024-03-20T10:00:00",
                        "updated_at": "2024-03-20T10:00:00"
                    }
                ],
                "total": 2,
                "page": 1,
                "size": 10,
                "total_pages": 1
            }
        ]
    }
}

class GiftActionEnum(str, enum.Enum):
    ACCEPT = "ACCEPT"
    REJECT = "REJECT"

class GiftActionRequest(BaseModel):
    action: GiftActionEnum

class GiftActionResponse(BaseModel):
    success: bool
    message: str
    status: Optional[str] = None 