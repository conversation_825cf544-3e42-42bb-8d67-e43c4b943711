from typing import List
from fastapi import APIRouter, Depends, HTTPException
from sqlalchemy.orm import Session
from src.gift import models, schemas, services
from src.utils.database import get_db
from src.user.models import User as UserModel
from src.utils.token_docs import jwt_auth
from src.utils.permission import is_authenticated, is_merchant
from src.utils.response import CustomResponse
from .schemas import GiftActionRequest, GiftActionResponse

router = APIRouter()
tags = ["Gift"]
# Gift Category Endpoints
@router.get("/categories", response_model=schemas.PaginatedGiftCategoryResponse, tags=tags, dependencies=[Depends(jwt_auth)])
def list_gift_categories(
    page: int = 1,
    size: int = 10,
    db: Session = Depends(get_db)
):
    categories = services.get_gift_categories_service(db, page=page, size=size)
    return CustomResponse(content=categories).format_data_get()

@router.post("/categories", response_model=schemas.GiftCategorySchemas, tags=tags, dependencies=[Depends(jwt_auth)])
def create_gift_category(
    category: schemas.GiftCategoryCreateSchemas,
    db: Session = Depends(get_db),
    current_user: UserModel = Depends(is_authenticated)
):
    result = services.create_gift_category_service(db=db, category=category)
    return CustomResponse(content=result).format_data_create()

@router.put("/categories/{category_id}", response_model=schemas.GiftCategorySchemas, tags=tags, dependencies=[Depends(jwt_auth)])
def update_gift_category(
    category_id: int,
    category: schemas.GiftCategoryUpdateSchemas,
    db: Session = Depends(get_db),
    current_user: UserModel = Depends(is_authenticated)
):
    result = services.update_gift_category_service(db=db, category_id=category_id, category=category)
    return CustomResponse(content=result).format_data_update()

@router.delete("/categories/{category_id}", tags=tags, dependencies=[Depends(jwt_auth)])
def delete_gift_category(
    category_id: int,
    db: Session = Depends(get_db),
    current_user: UserModel = Depends(is_authenticated)
):
    services.delete_gift_category_service(db=db, category_id=category_id)
    return CustomResponse(content={"message": "Gift category deleted successfully"}).format_data_delete()

# Gift Endpoints
@router.get("", response_model=schemas.PaginatedGiftResponse, tags=tags, dependencies=[Depends(jwt_auth)])
def list_gifts(
    page: int = 1,
    size: int = 10,
    db: Session = Depends(get_db)
):
    gifts = services.get_gifts_service(db, page=page, size=size)
    return CustomResponse(content=gifts).format_data_get()

@router.post("", response_model=schemas.GiftSchemas, tags=tags, dependencies=[Depends(jwt_auth)])
def create_gift(
    gift: schemas.GiftCreateSchemas,
    db: Session = Depends(get_db),
    current_user: UserModel = Depends(is_authenticated)
):
    result = services.create_gift_service(db=db, gift=gift)
    return CustomResponse(content=result).format_data_create()

@router.put("/{gift_id}", response_model=schemas.GiftSchemas, tags=tags, dependencies=[Depends(jwt_auth)])
def update_gift(
    gift_id: int,
    gift: schemas.GiftUpdateSchemas,
    db: Session = Depends(get_db),
    current_user: UserModel = Depends(is_authenticated)
):
    result = services.update_gift_service(db=db, gift_id=gift_id, gift=gift)
    return CustomResponse(content=result).format_data_update()

@router.delete("/{gift_id}", tags=tags, dependencies=[Depends(jwt_auth)])
def delete_gift(
    gift_id: int,
    db: Session = Depends(get_db),
    current_user: UserModel = Depends(is_authenticated)
):
    services.delete_gift_service(db=db, gift_id=gift_id)
    return CustomResponse(content={"message": "Gift deleted successfully"}).format_data_delete()

# User Gift Endpoints
@router.post("/send-with-sale", response_model=List[schemas.UserGiftSchemas], tags=tags, dependencies=[Depends(jwt_auth)])
def send_gift(
    gift_data: schemas.UserGiftCreateSchemas,
    db: Session = Depends(get_db),
    current_user: UserModel = Depends(is_authenticated)
):
    result = services.send_gift_service(db=db, sender_id=current_user.id, gift_data=gift_data)
    return CustomResponse(content=result).format_data_create()

@router.post("/send-direct", response_model=List[schemas.UserGiftSchemas], tags=tags, dependencies=[Depends(jwt_auth)])
def send_gift_direct(
    gift_data: schemas.UserGiftCreateSchemas,
    db: Session = Depends(get_db),
    current_user: UserModel = Depends(is_merchant)
):
    """
    API cho MERCHANT gửi quà trực tiếp không cần mua trước
    """
    result = services.send_gift_direct_service(db=db, sender_id=current_user.id, gift_data=gift_data)
    return CustomResponse(content=result).format_data_create()

@router.get("/sent", response_model=schemas.PaginatedUserGiftResponse, tags=tags, dependencies=[Depends(jwt_auth)])
def get_sent_gifts(
    page: int = 1,
    size: int = 10,
    db: Session = Depends(get_db),
    current_user: UserModel = Depends(is_authenticated)
):
    gifts = services.get_sent_gifts_service(db=db, user_id=current_user.id, page=page, size=size)
    return CustomResponse(content=gifts).format_data_get()

@router.get("/received", response_model=schemas.PaginatedUserGiftResponse, tags=tags, dependencies=[Depends(jwt_auth)])
def get_received_gifts(
    page: int = 1,
    size: int = 10,
    db: Session = Depends(get_db),
    current_user: UserModel = Depends(is_authenticated)
):
    gifts = services.get_received_gifts_service(db=db, user_id=current_user.id, page=page, size=size)
    return CustomResponse(content=gifts).format_data_get()

@router.put("/gifts/{id}/action/", response_model=GiftActionResponse, tags=tags, dependencies=[Depends(jwt_auth)])
def accept_reject_gift(
    id: int,
    request: GiftActionRequest,
    db: Session = Depends(get_db),
    current_user: UserModel = Depends(is_authenticated),
):
    """
    Accept hoặc Reject quà tặng (chỉ người nhận mới thao tác được, trong 14 ngày).
    """
    result = services.accept_reject_gift_service(
        db=db,
        id=id,
        user_id=current_user.id,
        action=request.action.value
    )
    return CustomResponse(content=result).format_data_update()
