from typing import List, Optional
from datetime import datetime, timezone
from sqlalchemy.orm import Session
from src.gift import models, schemas
from src.user.models import User
from src.order.models import OrderItem, Order
from src.ticket_sale.models import TicketOrder
from src.utils.exceptions import CustomException
from src.utils.schemas import PaginatedResponse
from src.utils.enum import GiftTypeEnum, GiftStatusEnum, OrderStatusEnum, ShippingMethodEnum
from src.ticket_sale.models import PaymentStatusEnum
from src.contact.models import Contact
from src.product.models import Product
from sqlalchemy import func
from src.gift.schemas import GiftActionEnum

# Gift Category Services
def get_gift_categories_service(db: Session, page: int = 1, size: int = 10) -> PaginatedResponse[schemas.GiftCategoryListSchemas]:
    # Calculate skip and limit
    skip = (page - 1) * size
    limit = size

    # Get total count
    total = db.query(models.GiftCategory).count()
    
    # Calculate total pages
    total_pages = (total + size - 1) // size

    # Get items
    items = db.query(models.GiftCategory).offset(skip).limit(limit).all()

    # <PERSON><PERSON><PERSON><PERSON> đ<PERSON><PERSON> sang schema Pydantic (dùng dict, lo<PERSON><PERSON> bỏ _sa_instance_state)
    items_schema = [
        schemas.GiftCategoryListSchemas.model_validate(
            {k: v for k, v in vars(item).items() if k != '_sa_instance_state'}
        )
        for item in items
    ]

    return PaginatedResponse(
        items=items_schema,
        total=total,
        page=page,
        size=size,
        total_pages=total_pages
    )

def create_gift_category_service(db: Session, category: schemas.GiftCategoryCreateSchemas) -> models.GiftCategory:
    try:
        db_category = models.GiftCategory(**category.model_dump())
        db.add(db_category)
        db.commit()
        db.refresh(db_category)
        return db_category
    except Exception as e:
        db.rollback()
        raise CustomException().bad_request_exception()

def update_gift_category_service(db: Session, category_id: int, category: schemas.GiftCategoryUpdateSchemas) -> models.GiftCategory:
    try:
        db_category = db.query(models.GiftCategory).filter(models.GiftCategory.id == category_id).first()
        if not db_category:
            raise CustomException(content={"message": "Gift category not found"}).not_found_exception()
        
        for key, value in category.model_dump().items():
            setattr(db_category, key, value)
        
        db.commit()
        db.refresh(db_category)
        return db_category
    except Exception as e:
        db.rollback()
        raise CustomException().bad_request_exception()

def delete_gift_category_service(db: Session, category_id: int) -> bool:
    try:
        db_category = db.query(models.GiftCategory).filter(models.GiftCategory.id == category_id).first()
        if not db_category:
            raise CustomException(content={"message": "Gift category not found"}).not_found_exception()
        
        db.delete(db_category)
        db.commit()
        return True
    except Exception as e:
        db.rollback()
        raise CustomException().bad_request_exception()

# Gift Services
def get_gifts_service(db: Session, page: int = 1, size: int = 10) -> PaginatedResponse[schemas.GiftListSchemas]:
    skip = (page - 1) * size
    limit = size

    total = db.query(models.Gift).count()
    total_pages = (total + size - 1) // size

    items = db.query(models.Gift).offset(skip).limit(limit).all()

    items_schema = []
    for item in items:
        item_dict = {k: v for k, v in vars(item).items() if k != '_sa_instance_state'}
        # Chuyển category sang schema
        if hasattr(item, "category") and item.category:
            item_dict["category"] = schemas.GiftCategorySchemas.model_validate(
                {k: v for k, v in vars(item.category).items() if k != '_sa_instance_state'}
            )
        items_schema.append(schemas.GiftListSchemas.model_validate(item_dict))

    return PaginatedResponse(
        items=items_schema,
        total=total,
        page=page,
        size=size,
        total_pages=total_pages
    )

def create_gift_service(db: Session, gift: schemas.GiftCreateSchemas) -> models.Gift:
    try:
        # Verify category exists
        category = db.query(models.GiftCategory).filter(models.GiftCategory.id == gift.category_id).first()
        if not category:
            raise CustomException(content={"message": "Gift category not found"}).not_found_exception()

        db_gift = models.Gift(**gift.model_dump())
        db.add(db_gift)
        db.commit()
        db.refresh(db_gift)
        return db_gift
    except Exception as e:
        db.rollback()
        raise CustomException().bad_request_exception()

def update_gift_service(db: Session, gift_id: int, gift: schemas.GiftUpdateSchemas) -> models.Gift:
    try:
        db_gift = db.query(models.Gift).filter(models.Gift.id == gift_id).first()
        if not db_gift:
            raise CustomException(content={"message": "Gift not found"}).not_found_exception()
        
        # Verify category exists if being updated
        if gift.category_id:
            category = db.query(models.GiftCategory).filter(models.GiftCategory.id == gift.category_id).first()
            if not category:
                raise CustomException(content={"message": "Gift category not found"}).not_found_exception()
        
        for key, value in gift.model_dump().items():
            setattr(db_gift, key, value)
        
        db.commit()
        db.refresh(db_gift)
        return db_gift
    except Exception as e:
        db.rollback()
        raise CustomException().bad_request_exception()

def delete_gift_service(db: Session, gift_id: int) -> bool:
    try:
        db_gift = db.query(models.Gift).filter(models.Gift.id == gift_id).first()
        if not db_gift:
            raise CustomException(content={"message": "Gift not found"}).not_found_exception()
        
        db.delete(db_gift)
        db.commit()
        return True
    except Exception as e:
        db.rollback()
        raise CustomException().bad_request_exception()

# User Gift Services
def send_gift_service(db: Session, sender_id: int, gift_data: schemas.UserGiftCreateSchemas) -> List[schemas.UserGiftSchemas]:
    # Kiểm tra receiver có tồn tại không
    receiver = db.query(User).filter(User.id == gift_data.receiver_id).first()
    if not receiver:
        raise CustomException(content={"error": "Receiver not found"}).not_found_exception()

    # Kiểm tra receiver có phải là contact của sender không
    contact = db.query(Contact).filter(
        Contact.user_id == sender_id,
        Contact.user_contact_id == gift_data.receiver_id,
        Contact.is_deleted == False
    ).first()
    if not contact:
        raise CustomException(content={"error": "Receiver is not your contact"}).bad_request_exception()

    user_gifts = []
    
    # Xử lý theo gift_type
    if gift_data.gift_type == GiftTypeEnum.PRODUCT:
        if not gift_data.product_details:
            raise CustomException(content={"error": "Product details are required for PRODUCT gift type"}).bad_request_exception()
        
        # Xử lý từng detail (mỗi detail là một organization)
        for detail in gift_data.product_details:
            # Kiểm tra product có tồn tại không
            product = db.query(Product).filter(Product.id == detail.product_id).first()
            if not product:
                raise CustomException(content={"error": f"Product {detail.product_id} not found"}).not_found_exception()
            
            # Kiểm tra organization_id có khớp với product không
            if product.organization_id != detail.organization_id:
                raise CustomException(content={"error": f"Product {detail.product_id} does not belong to organization {detail.organization_id}"}).bad_request_exception()

            # Lấy thông tin shipping: ưu tiên từ detail, nếu không có thì lấy từ receiver
            shipping_full_name = detail.shipping_full_name or (receiver.profile.full_name if receiver.profile and receiver.profile.full_name else "Unknown")
            shipping_phone = detail.shipping_phone_number or (receiver.phone_number if receiver.phone_number else "Unknown")
            shipping_address = detail.shipping_address or (receiver.profile.address if receiver.profile and receiver.profile.address else "Unknown")
            shipping_method = detail.shipping_method or ShippingMethodEnum.STANDARD

            # Tạo order cho organization này
            order = Order(
                user_id=sender_id,
                organization_id=detail.organization_id,
                status=OrderStatusEnum.PENDING,
                shipping_full_name=shipping_full_name,
                shipping_phone=shipping_phone,
                shipping_address=shipping_address,
                shipping_method=shipping_method,
                shipping_fee=detail.shipping_fee,
                total_price=detail.total_price,
            )
            db.add(order)
            db.flush()  # Để lấy order.id

            # Tạo order_item
            order_item = OrderItem(
                order_id=order.id,
                product_id=detail.product_id,
                quantity=detail.quantity,
                price=product.price
            )
            db.add(order_item)
            db.flush()

            # Tạo UserGift cho order này
            db_user_gift = models.UserGift(
                sender_id=sender_id,
                receiver_id=gift_data.receiver_id,
                gift_id=gift_data.gift_id,  # template, có thể nullable
                gift_type=GiftTypeEnum.PRODUCT,
                order_id=order.id,
                message=gift_data.message,
                status=GiftStatusEnum.PENDING,
                details=detail.model_dump()
            )
            db.add(db_user_gift)
            user_gifts.append(db_user_gift)
    
    elif gift_data.gift_type == GiftTypeEnum.TICKET:
        if not gift_data.ticket_details:
            raise CustomException(content={"error": "Ticket details are required for TICKET gift type"}).bad_request_exception()
        
        # Import Ticket model
        from src.ticket.models import Ticket
        
        # Xử lý từng ticket detail
        for ticket_detail in gift_data.ticket_details:
            # Kiểm tra ticket có tồn tại không
            ticket = db.query(Ticket).filter(Ticket.id == ticket_detail.ticket_id).first()
            if not ticket:
                raise CustomException(content={"error": f"Ticket {ticket_detail.ticket_id} not found"}).not_found_exception()
            
            # Kiểm tra thời gian bán vé
            now = datetime.now(timezone.utc)
            
            # Đảm bảo ticket.start_sale có timezone nếu có
            if ticket.start_sale:
                if ticket.start_sale.tzinfo is None:
                    # Nếu start_sale không có timezone, giả sử là UTC
                    start_sale_utc = ticket.start_sale.replace(tzinfo=timezone.utc)
                else:
                    start_sale_utc = ticket.start_sale
                
                if now < start_sale_utc:
                    raise CustomException(content={"error": f"Ticket is not available for sale yet"}).bad_request_exception()
            
            # Đảm bảo ticket.end_sale có timezone nếu có
            if ticket.end_sale:
                if ticket.end_sale.tzinfo is None:
                    # Nếu end_sale không có timezone, giả sử là UTC
                    end_sale_utc = ticket.end_sale.replace(tzinfo=timezone.utc)
                else:
                    end_sale_utc = ticket.end_sale
                
                if now > end_sale_utc:
                    raise CustomException(content={"error": f"Ticket sale has ended"}).bad_request_exception()
            
            # Kiểm tra số lượng ticket có đủ không
            if ticket.quantity < ticket_detail.quantity:
                raise CustomException(content={"error": f"Not enough tickets available for ticket"}).bad_request_exception()
            
            # Tính total_price
            total_price = ticket.price * ticket_detail.quantity
            
            # Tạo ticket_order
            ticket_order = TicketOrder(
                ticket_id=ticket_detail.ticket_id,
                user_id=sender_id,
                quantity=ticket_detail.quantity,
                total_price=total_price,
                payment_status=PaymentStatusEnum.PENDING
            )
            db.add(ticket_order)
            db.flush()  # Để lấy ticket_order.id
            
            # Cập nhật số lượng ticket còn lại
            ticket.quantity -= ticket_detail.quantity
            
            # Tạo UserGift cho ticket này
            db_user_gift = models.UserGift(
                sender_id=sender_id,
                receiver_id=gift_data.receiver_id,
                gift_id=gift_data.gift_id,  # template, có thể nullable
                gift_type=GiftTypeEnum.TICKET,
                ticket_order_id=ticket_order.id,
                message=gift_data.message,
                status=GiftStatusEnum.PENDING,
                details = ticket_detail.model_dump()
            )
            db.add(db_user_gift)
            user_gifts.append(db_user_gift)
    
    else:
        raise CustomException(content={"error": f"Invalid gift type: {gift_data.gift_type}"}).bad_request_exception()

    db.commit()
    # Refresh tất cả user_gifts
    for user_gift in user_gifts:
        db.refresh(user_gift)
    
    # Chuyển đổi sang schema Pydantic
    result_schemas = []
    for user_gift in user_gifts:
        user_gift_dict = {k: v for k, v in vars(user_gift).items() if k != '_sa_instance_state'}
        # Đảm bảo status không bị None
        if user_gift_dict.get("status") is None:
            user_gift_dict["status"] = GiftStatusEnum.PENDING
        # Chuyển gift sang schema nếu có và gift_id không None
        if hasattr(user_gift, "gift") and user_gift.gift and user_gift.gift_id:
            try:
                gift_dict = {k: v for k, v in vars(user_gift.gift).items() if k != '_sa_instance_state'}
                # Chuyển category sang schema nếu có
                if hasattr(user_gift.gift, "category") and user_gift.gift.category:
                    gift_dict["category"] = schemas.GiftCategorySchemas.model_validate(
                        {k: v for k, v in vars(user_gift.gift.category).items() if k != '_sa_instance_state'}
                    )
                user_gift_dict["gift"] = schemas.GiftSchemas.model_validate(gift_dict)
            except Exception as e:
                # Nếu có lỗi khi chuyển đổi gift, set gift = None
                user_gift_dict["gift"] = None
        else:
            # Nếu không có gift hoặc gift_id = None, set gift = None
            user_gift_dict["gift"] = None
        
        result_schemas.append(schemas.UserGiftSchemas.model_validate(user_gift_dict))
    
    return result_schemas

def get_sent_gifts_service(db: Session, user_id: int, page: int = 1, size: int = 10) -> PaginatedResponse[schemas.UserGiftListSchemas]:
    # Calculate skip and limit
    skip = (page - 1) * size
    limit = size

    # Get total count
    total = db.query(models.UserGift).filter(models.UserGift.sender_id == user_id).count()
    
    # Calculate total pages
    total_pages = (total + size - 1) // size

    # Get items with relationships
    items = db.query(models.UserGift)\
        .filter(models.UserGift.sender_id == user_id)\
        .offset(skip)\
        .limit(limit)\
        .all()

    # Chuyển đổi sang schema Pydantic
    items_schema = []
    for item in items:
        item_dict = {k: v for k, v in vars(item).items() if k != '_sa_instance_state'}
        # Đảm bảo status không bị None
        if item_dict.get("status") is None:
            item_dict["status"] = GiftStatusEnum.PENDING
        # Chuyển gift sang schema nếu có và gift_id không None
        if hasattr(item, "gift") and item.gift and item.gift_id:
            try:
                gift_dict = {k: v for k, v in vars(item.gift).items() if k != '_sa_instance_state'}
                # Chuyển category sang schema nếu có
                if hasattr(item.gift, "category") and item.gift.category:
                    gift_dict["category"] = schemas.GiftCategorySchemas.model_validate(
                        {k: v for k, v in vars(item.gift.category).items() if k != '_sa_instance_state'}
                    )
                item_dict["gift"] = schemas.GiftSchemas.model_validate(gift_dict)
            except Exception as e:
                # Nếu có lỗi khi chuyển đổi gift, set gift = None
                item_dict["gift"] = None
        else:
            # Nếu không có gift hoặc gift_id = None, set gift = None
            item_dict["gift"] = None
        
        items_schema.append(schemas.UserGiftListSchemas.model_validate(item_dict))

    return PaginatedResponse(
        items=items_schema,
        total=total,
        page=page,
        size=size,
        total_pages=total_pages
    )

def get_received_gifts_service(db: Session, user_id: int, page: int = 1, size: int = 10) -> PaginatedResponse[schemas.UserGiftListSchemas]:
    # Calculate skip and limit
    skip = (page - 1) * size
    limit = size

    # Get total count
    total = db.query(models.UserGift).filter(models.UserGift.receiver_id == user_id).count()
    
    # Calculate total pages
    total_pages = (total + size - 1) // size

    # Get items with relationships
    items = db.query(models.UserGift)\
        .filter(models.UserGift.receiver_id == user_id)\
        .offset(skip)\
        .limit(limit)\
        .all()

    # Chuyển đổi sang schema Pydantic
    items_schema = []
    for item in items:
        item_dict = {k: v for k, v in vars(item).items() if k != '_sa_instance_state'}
        # Đảm bảo status không bị None
        if item_dict.get("status") is None:
            item_dict["status"] = GiftStatusEnum.PENDING
        # Chuyển gift sang schema nếu có và gift_id không None
        if hasattr(item, "gift") and item.gift and item.gift_id:
            try:
                gift_dict = {k: v for k, v in vars(item.gift).items() if k != '_sa_instance_state'}
                # Chuyển category sang schema nếu có
                if hasattr(item.gift, "category") and item.gift.category:
                    gift_dict["category"] = schemas.GiftCategorySchemas.model_validate(
                        {k: v for k, v in vars(item.gift.category).items() if k != '_sa_instance_state'}
                    )
                item_dict["gift"] = schemas.GiftSchemas.model_validate(gift_dict)
            except Exception as e:
                # Nếu có lỗi khi chuyển đổi gift, set gift = None
                item_dict["gift"] = None
        else:
            # Nếu không có gift hoặc gift_id = None, set gift = None
            item_dict["gift"] = None
        
        items_schema.append(schemas.UserGiftListSchemas.model_validate(item_dict))

    return PaginatedResponse(
        items=items_schema,
        total=total,
        page=page,
        size=size,
        total_pages=total_pages
    )

def send_gift_direct_service(db: Session, sender_id: int, gift_data: schemas.UserGiftCreateSchemas) -> List[schemas.UserGiftSchemas]:
    """
    Service cho MERCHANT gửi quà trực tiếp không cần mua trước
    """
    # Kiểm tra receiver có tồn tại không
    receiver = db.query(User).filter(User.id == gift_data.receiver_id).first()
    if not receiver:
        raise CustomException(content={"error": "Receiver not found"}).not_found_exception()

    # Kiểm tra receiver có phải là contact của sender không
    contact = db.query(Contact).filter(
        Contact.user_id == sender_id,
        Contact.user_contact_id == gift_data.receiver_id,
        Contact.is_deleted == False
    ).first()
    if not contact:
        raise CustomException(content={"error": "Receiver is not your contact"}).bad_request_exception()

    user_gifts = []
    
    # Xử lý theo gift_type
    if gift_data.gift_type == GiftTypeEnum.PRODUCT:
        if not gift_data.product_details:
            raise CustomException(content={"error": "Product details are required for PRODUCT gift type"}).bad_request_exception()
        
        # Xử lý từng detail (mỗi detail là một organization)
        for detail in gift_data.product_details:
            # Kiểm tra product có tồn tại không
            product = db.query(Product).filter(Product.id == detail.product_id).first()
            if not product:
                raise CustomException(content={"error": f"Product {detail.product_id} not found"}).not_found_exception()
            
            # Kiểm tra organization_id có khớp với product không
            if product.organization_id != detail.organization_id:
                raise CustomException(content={"error": f"Product {detail.product_id} does not belong to organization {detail.organization_id}"}).bad_request_exception()

            # Tạo UserGift trực tiếp cho product này (không tạo order)
            db_user_gift = models.UserGift(
                sender_id=sender_id,
                receiver_id=gift_data.receiver_id,
                gift_id=gift_data.gift_id,  # template, có thể nullable
                gift_type=GiftTypeEnum.PRODUCT,
                order_id=None,  # Không có order vì gửi trực tiếp
                message=gift_data.message,
                status=GiftStatusEnum.PENDING,  # Trực tiếp gửi, không cần pending
                details = detail.model_dump()
            )
            db.add(db_user_gift)
            user_gifts.append(db_user_gift)
    
    elif gift_data.gift_type == GiftTypeEnum.TICKET:
        if not gift_data.ticket_details:
            raise CustomException(content={"error": "Ticket details are required for TICKET gift type"}).bad_request_exception()
        
        # Import Ticket model
        from src.ticket.models import Ticket
        
        # Xử lý từng ticket detail
        for ticket_detail in gift_data.ticket_details:
            # Kiểm tra ticket có tồn tại không
            ticket = db.query(Ticket).filter(Ticket.id == ticket_detail.ticket_id).first()
            if not ticket:
                raise CustomException(content={"error": f"Ticket {ticket_detail.ticket_id} not found"}).not_found_exception()
            
            # Kiểm tra thời gian bán vé
            now = datetime.now(timezone.utc)
            
            # Đảm bảo ticket.start_sale có timezone nếu có
            if ticket.start_sale:
                if ticket.start_sale.tzinfo is None:
                    # Nếu start_sale không có timezone, giả sử là UTC
                    start_sale_utc = ticket.start_sale.replace(tzinfo=timezone.utc)
                else:
                    start_sale_utc = ticket.start_sale
                
                if now < start_sale_utc:
                    raise CustomException(content={"error": f"Ticket {ticket_detail.ticket_id} is not available for sale yet"}).bad_request_exception()
            
            # Đảm bảo ticket.end_sale có timezone nếu có
            if ticket.end_sale:
                if ticket.end_sale.tzinfo is None:
                    # Nếu end_sale không có timezone, giả sử là UTC
                    end_sale_utc = ticket.end_sale.replace(tzinfo=timezone.utc)
                else:
                    end_sale_utc = ticket.end_sale
                
                if now > end_sale_utc:
                    raise CustomException(content={"error": f"Ticket {ticket_detail.ticket_id} sale has ended"}).bad_request_exception()
            
            # Kiểm tra số lượng ticket có đủ không
            if ticket.quantity < ticket_detail.quantity:
                raise CustomException(content={"error": f"Not enough tickets available for ticket {ticket_detail.ticket_id}"}).bad_request_exception()
            
            # Cập nhật số lượng ticket còn lại
            ticket.quantity -= ticket_detail.quantity
            
            # Tạo UserGift trực tiếp cho ticket này (không tạo ticket_order)
            db_user_gift = models.UserGift(
                sender_id=sender_id,
                receiver_id=gift_data.receiver_id,
                gift_id=gift_data.gift_id,  # template, có thể nullable
                gift_type=GiftTypeEnum.TICKET,
                ticket_order_id=None,  # Không có ticket_order vì gửi trực tiếp
                message=gift_data.message,
                status=GiftStatusEnum.SENT,  # Trực tiếp gửi, không cần pending
                details = ticket_detail.model_dump()
            )
            db.add(db_user_gift)
            user_gifts.append(db_user_gift)
    else:
        raise CustomException(content={"error": f"Invalid gift type: {gift_data.gift_type}"}).bad_request_exception()

    db.commit()
    # Refresh tất cả user_gifts
    for user_gift in user_gifts:
        db.refresh(user_gift)
    
    # Chuyển đổi sang schema Pydantic
    result_schemas = []
    for user_gift in user_gifts:
        user_gift_dict = {k: v for k, v in vars(user_gift).items() if k != '_sa_instance_state'}
        # Đảm bảo status không bị None
        if user_gift_dict.get("status") is None:
            user_gift_dict["status"] = GiftStatusEnum.SENT
        # Chuyển gift sang schema nếu có và gift_id không None
        if hasattr(user_gift, "gift") and user_gift.gift and user_gift.gift_id:
            try:
                gift_dict = {k: v for k, v in vars(user_gift.gift).items() if k != '_sa_instance_state'}
                # Chuyển category sang schema nếu có
                if hasattr(user_gift.gift, "category") and user_gift.gift.category:
                    gift_dict["category"] = schemas.GiftCategorySchemas.model_validate(
                        {k: v for k, v in vars(user_gift.gift.category).items() if k != '_sa_instance_state'}
                    )
                user_gift_dict["gift"] = schemas.GiftSchemas.model_validate(gift_dict)
            except Exception as e:
                # Nếu có lỗi khi chuyển đổi gift, set gift = None
                user_gift_dict["gift"] = None
        else:
            # Nếu không có gift hoặc gift_id = None, set gift = None
            user_gift_dict["gift"] = None
        
        result_schemas.append(schemas.UserGiftSchemas.model_validate(user_gift_dict))
    
    return result_schemas 

def accept_reject_gift_service(
    db: Session,
    id: int,
    user_id: int,
    action: str
) -> dict:
    from src.gift.models import UserGift, GiftStatusEnum
    from datetime import datetime, timedelta, timezone
    # Lấy gift
    user_gift = db.query(UserGift).filter(UserGift.id == id, UserGift.receiver_id == user_id).first()
    if not user_gift:
        raise CustomException(content={"error": "Gift not found or you are not the receiver."}).bad_request_exception()
    # Kiểm tra trạng thái
    if user_gift.status not in [GiftStatusEnum.PENDING, GiftStatusEnum.EXPIRED]:
        raise CustomException(content={"message": "Gift is not pending or has already been processed.", "status": user_gift.status}).bad_request_exception()
    # Kiểm tra hết hạn
    now = datetime.now(timezone.utc)
    sent_at = user_gift.sent_at if user_gift.sent_at else user_gift.created_at
    if sent_at and sent_at.tzinfo is None:
        sent_at = sent_at.replace(tzinfo=timezone.utc)
    if sent_at and (now - sent_at).days >= 14:
        user_gift.status = GiftStatusEnum.EXPIRED
        db.commit()
        raise CustomException(content={"message": "Gift has expired and returned to sender.", "status": user_gift.status}).bad_request_exception()
    if action == GiftActionEnum.ACCEPT:
        user_gift.status = GiftStatusEnum.ACCEPTED
        db.commit()
        return {"message": "Gift accepted and added to your inventory.", "status": user_gift.status}
    elif action == GiftActionEnum.REJECT:
        user_gift.status = GiftStatusEnum.REJECTED
        db.commit()
        return {"message": "Gift rejected and returned to sender.", "status": user_gift.status}
    else:
        raise CustomException(content={"error": "Invalid action."}).bad_request_exception() 
