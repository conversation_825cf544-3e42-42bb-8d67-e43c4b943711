from pydantic import BaseModel, Field
from typing import List, Optional
from datetime import datetime
from src.utils.schemas import PaginatedResponse
from .models import ProductTypeEnum
from src.utils.enum import ProductStatusEnum
from src.organization.schemas import OrganizationResponse

class CategoryBase(BaseModel):
    name: str = Field(..., example="Electronics")
    description: Optional[str] = Field(None, example="Electronic devices")
    parent_id: Optional[int] = Field(None, example=1)

    model_config = {
        "json_schema_extra": {
            "examples": [
                {
                    "name": "Electronics",
                    "description": "Electronic devices",
                    "parent_id": 1,
                }
            ]
        }
    }

class CategoryCreate(CategoryBase):
    pass

class CategoryUpdate(CategoryBase):
    name: Optional[str] = None

class CategoryRead(CategoryBase):
    id: int
    created_at: datetime
    updated_at: datetime
    children: List['CategoryRead'] = []

    class Config:
        from_attributes = True

class ProductBase(BaseModel):
    name: str = Field(..., min_length=1, max_length=100, example="Wireless Mouse")
    description: Optional[str] = Field(None, max_length=500, example="Bluetooth 5.0 mouse")
    price: float = Field(..., gt=0, example=15.99)
    quantity: int = Field(..., ge=0, example=10)
    thumbnail: Optional[str] = Field(None, example="https://example.com/photo.jpg")
    type: ProductTypeEnum = Field(default=ProductTypeEnum.PRODUCT, example=ProductTypeEnum.PRODUCT)
    status: ProductStatusEnum = Field(default=ProductStatusEnum.ACTIVE, example=ProductStatusEnum.ACTIVE)
    category_id: int = Field(..., example=1)
    enable_tax: bool = Field(default=False, example=False)
    tax_rate: Optional[float] = Field(None, example=10.0)
    on_marketplace: bool = Field(default=False, example=False)
    price_with_tax: Optional[float] = Field(None, example=16.5)

    model_config = {
        "json_schema_extra": {
            "examples": [
                {
                    "name": "Wireless Mouse",
                    "description": "Bluetooth 5.0 mouse",
                    "price": 15.99,
                    "quantity": 10,
                    "thumbnail": "https://example.com/photo.jpg",
                    "type": "PRODUCT",
                    "status": "ACTIVE",
                    "category_id": 1,
                    "enable_tax": False,
                    "tax_rate": 10.0,
                    "on_marketplace": False,
                    "price_with_tax": 16.5,
                }
            ]
        }
    }

class ProductCreate(ProductBase):
    pass

class ProductUpdate(BaseModel):
    name: Optional[str] = Field(None, min_length=1, max_length=100, example="Wireless Mouse")
    description: Optional[str] = Field(None, max_length=500, example="Bluetooth 5.0 mouse")
    price: Optional[float] = Field(None, gt=0, example=15.99)
    quantity: Optional[int] = Field(None, ge=0, example=10)
    thumbnail: Optional[str] = Field(None, example="https://example.com/photo.jpg")
    type: Optional[ProductTypeEnum] = Field(None, example=ProductTypeEnum.PRODUCT)
    category_id: Optional[int] = Field(None, example=1)
    status: Optional[ProductStatusEnum] = Field(None, example=ProductStatusEnum.ACTIVE)
    enable_tax: Optional[bool] = Field(None, example=False)
    tax_rate: Optional[float] = Field(None, example=10.0)
    on_marketplace: Optional[bool] = Field(None, example=False)
    price_with_tax: Optional[float] = Field(None, example=16.5)

class ProductPublish(BaseModel):
    price: float = Field(..., gt=0, example=15.99)
    enable_tax: bool = Field(default=False, example=False)
    tax_rate: Optional[float] = Field(None, ge=0, le=100, example=10.0)

    model_config = {
        "json_schema_extra": {
            "examples": [
                {
                    "price": 15.99,
                    "enable_tax": False,
                    "tax_rate": 10.0
                }
            ]
        }
    }

class ProductRead(ProductBase):
    id: int
    created_at: datetime
    updated_at: datetime
    listed_at: Optional[datetime] = None
    category: CategoryRead
    organization: OrganizationResponse

    model_config = {
        "from_attributes": True,
        "json_schema_extra": {
            "examples": [
                {
                    "id": 1,
                    "name": "Wireless Mouse",
                    "description": "Bluetooth 5.0 mouse",
                    "price": 15.99,
                    "quantity": 10,
                    "thumbnail": "https://example.com/photo.jpg",
                    "type": "PRODUCT",
                    "status": "ACTIVE",
                    "category_id": 1,
                    "enable_tax": False,
                    "tax_rate": 10.0,
                    "on_marketplace": False,
                    "price_with_tax": 16.5,
                    "created_at": "2024-01-01T00:00:00",
                    "updated_at": "2024-01-01T00:00:00",
                    "listed_at": "2024-01-01T00:00:00",
                    "category": {
                        "id": 1,
                        "name": "Electronics",
                        "description": "Electronic devices",
                        "parent_id": 0,
                        "created_at": "2024-01-01T00:00:00",
                        "updated_at": "2024-01-01T00:00:00",
                        "children": []
                    },
                    "organization": {
                        "id": 1,
                        "name": "ACME Corp",
                        "ein": "12-3456789",
                        "address": "123 Main St"
                    }
                }
            ]
        }
    }

PaginatedProductResponse = PaginatedResponse[ProductRead]
PaginatedCategoryResponse = PaginatedResponse[CategoryRead]

class ProductFilter(BaseModel):
    page: int = Field(1, ge=1, description="Current page number")
    size: int = Field(10, ge=1, le=100, description="Number of items per page")
    category_id: Optional[int] = Field(None, description="Filter by category id")
    type: Optional[ProductTypeEnum] = Field(None, description="Filter by product type")
    organization_id: Optional[int] = Field(None, description="Filter by organization id")
    price_min: Optional[float] = Field(None, ge=0, description="Minimum price")
    price_max: Optional[float] = Field(None, ge=0, description="Maximum price")
    status: Optional[ProductStatusEnum] = Field(None, description="Filter by product status")
    on_marketplace: Optional[bool] = Field(None, description="Filter by marketplace flag")
 