from sqlalchemy import Column, Integer, ForeignKey, String, DateTime, Enum, Text, Float, UniqueConstraint, Boolean
from sqlalchemy.orm import relationship
from datetime import datetime
import enum
from src.utils.base_model import BaseModel, Base
from src.utils.enum import ProductTypeEnum, ProductStatusEnum


class Category(BaseModel, Base):
    __tablename__ = "category"

    id = Column(Integer, primary_key=True)
    name = Column(String(255), nullable=False)
    description = Column(Text, nullable=True)
    parent_id = Column(Integer, ForeignKey("category.id"), nullable=True)
    created_at = Column(DateTime, default=datetime.now)
    updated_at = Column(DateTime, default=datetime.now, onupdate=datetime.now)

    # Relationships
    parent = relationship("Category", remote_side=[id], backref="children")
    products = relationship("Product", back_populates="category")

class Product(BaseModel, Base):
    __tablename__ = "product"
    __table_args__ = (
        UniqueConstraint("organization_id", "name", name="uq_organization_product_name"),
    )

    id = Column(Integer, primary_key=True)
    name = Column(String(255), nullable=False)
    description = Column(Text, nullable=True)
    price = Column(Float, nullable=False)
    quantity = Column(Integer, nullable=False, default=0)
    thumbnail = Column(String(255), nullable=True)
    type = Column(Enum(ProductTypeEnum), default=ProductTypeEnum.PRODUCT)
    status = Column(Enum(ProductStatusEnum), default=ProductStatusEnum.ACTIVE)
    enable_tax = Column(Boolean, default=False)
    tax_rate = Column(Float, nullable=True)
    on_marketplace = Column(Boolean, default=False)
    listed_at = Column(DateTime, nullable=True)
    price_with_tax = Column(Float, nullable=True)
    category_id = Column(Integer, ForeignKey("category.id"), nullable=False)
    organization_id = Column(Integer, ForeignKey("organization.id"), nullable=False)
    created_at = Column(DateTime, default=datetime.now)
    updated_at = Column(DateTime, default=datetime.now, onupdate=datetime.now)

    # Relationships
    category = relationship("Category", back_populates="products")
    organization = relationship("Organization", back_populates="products") 
    orders = relationship("OrderItem", back_populates="product", lazy="select")
    cart_items = relationship("CartItem", back_populates="product", lazy="select")