from sqlalchemy.orm import Session, joinedload
from sqlalchemy import desc
from typing import Optional
from datetime import datetime, timezone
from src.utils.exceptions import CustomException
from .models import Category, Product, ProductTypeEnum
from .schemas import (
    CategoryCreate,
    CategoryUpdate,
    ProductCreate,
    ProductFilter,
    ProductRead,
    ProductUpdate,
    ProductPublish,
    PaginatedCategoryResponse,
    PaginatedProductResponse,
)
from src.user.models import User

def create_category_service(
    db: Session,
    category_data: CategoryCreate
) -> Category:
    try:
        # Check if parent exists
        if category_data.parent_id:
            parent = db.query(Category).filter(
                Category.id == category_data.parent_id
            ).first()
            if not parent:
                raise CustomException().not_found_exception(
                    message="Parent category not found"
                )
        
        # Create category
        category = Category(
            name=category_data.name,
            description=category_data.description,
            parent_id=category_data.parent_id if category_data.parent_id else None
        )
        db.add(category)
        db.commit()
        db.refresh(category)
        
        return category
        
    except Exception as e:
        print(e)
        db.rollback()
        raise CustomException().bad_request_exception()

def get_category_detail_service(
    db: Session,
    category_id: int
) -> Category:
    category = db.query(Category).options(
        joinedload(Category.children)
    ).filter(
        Category.id == category_id
    ).first()
    
    if not category:
        raise CustomException().not_found_exception(
            message="Category not found"
        )
    
    return category

def update_category_service(
    db: Session,
    category_id: int,
    category_data: CategoryUpdate
) -> Category:
    try:
        # Get category
        category = db.query(Category).filter(
            Category.id == category_id
        ).first()
        
        if not category:
            raise CustomException().not_found_exception(
                message="Category not found"
            )
        
        # Check if parent exists
        if category_data.parent_id:
            parent = db.query(Category).filter(
                Category.id == category_data.parent_id
            ).first()
            if not parent:
                raise CustomException().not_found_exception(
                    message="Parent category not found"
                )
        
        # Update category
        update_data = category_data.model_dump(exclude_unset=True)
        for key, value in update_data.items():
            setattr(category, key, value)
        
        db.commit()
        db.refresh(category)
        
        return category
        
    except Exception:
        db.rollback()
        raise CustomException().bad_request_exception()

def delete_category_service(
    db: Session,
    category_id: int
) -> bool:
    # Get category
    category = db.query(Category).filter(
        Category.id == category_id
    ).first()
    
    if not category:
        raise CustomException(content={'error': 'Category not found'}).not_found_exception()
    
    # Check if category has products
    if category.products:
        raise CustomException(content={'error': 'Cannot delete category with products'}).bad_request_exception()
        
    # Delete category
    db.delete(category)
    db.commit()
    
    return True

def get_categories_service(
    db: Session,
    page: int = 1,
    size: int = 10,
    parent_id: Optional[int] = None
) -> dict:
    # Build query
    query = db.query(Category)
    
    # Filter by parent
    if parent_id is not None:
        query = query.filter(Category.parent_id == parent_id)
    else:
        query = query.filter(Category.parent_id.is_(None))
    
    # Load relationships
    query = query.options(
        joinedload(Category.children)
    ).order_by(
        desc(Category.created_at)
    )
    
    # Get total count
    total = query.count()
    
    # Calculate pagination
    total_pages = (total + size - 1) // size
    skip = (page - 1) * size
    
    # Execute query with pagination
    categories = query.offset(skip).limit(size).all()
    
    return PaginatedCategoryResponse(
        items=categories,
        total=total,
        page=page,
        size=size,
        total_pages=total_pages
    )

def create_product_service(
    db: Session,
    product_data: ProductCreate,
    current_user: User
) -> Product:
    # Check if category exists
    category = db.query(Category).filter(
        Category.id == product_data.category_id
    ).first()
    if not category:
        raise CustomException(content={"error": "Category not found"}).not_found_exception()
    if not current_user.organization_id:
        raise CustomException(content={"error": "User has no organization"}).bad_request_exception()
    
    # Check name uniqueness within organization's inventory
    existing = db.query(Product).filter(
        Product.organization_id == current_user.organization_id,
        Product.name == product_data.name,
    ).first()
    if existing:
        raise CustomException(content={"error": "Product name already exists"}).bad_request_exception()

    # Create product with organization_id
    product_dict = product_data.model_dump()
    product_dict["organization_id"] = current_user.organization_id
    if product_dict.get("enable_tax") and product_dict.get("tax_rate") is not None:
        product_dict["price_with_tax"] = product_dict["price"] * (
            1 + product_dict["tax_rate"] / 100
        )
    else:
        product_dict["price_with_tax"] = product_dict["price"]
    product = Product(**product_dict)
    db.add(product)
    db.commit()
    db.refresh(product)
    
    return product

def get_product_detail_service(
    db: Session,
    product_id: int,
    user: User
) -> Product:
    query = db.query(Product).options(
        joinedload(Product.category),
        joinedload(Product.organization),
    ).filter(
        Product.id == product_id
    )

    if user.organization_id:
        query = query.filter(Product.organization_id == user.organization_id)

    product = query.first()
    
    if not product:
        raise CustomException(content={"error": "Product not found"}).not_found_exception()
    
    return product

def update_product_service(
    db: Session,
    product_id: int,
    product_data: ProductUpdate,
    current_user: User
) -> Product:
        # Get product
        product = db.query(Product).filter(
            Product.id == product_id
        ).first()
        
        if not product:
            raise CustomException(content={"error": "Product not found"}).not_found_exception()
        
        # Check if organization owns the product
        if product.organization_id != current_user.organization_id:
            raise CustomException(content={"error": "You don't have permission to update this product"}).forbidden_exception()
        
        # Check if category exists
        if product_data.category_id:
            category = db.query(Category).filter(
                Category.id == product_data.category_id
            ).first()
            if not category:
                raise CustomException(content={"error": "Category not found"}).not_found_exception()

        if product_data.name:
            existing = db.query(Product).filter(
                Product.organization_id == current_user.organization_id,
                Product.name == product_data.name,
                Product.id != product.id,
            ).first()
            if existing:
                raise CustomException(content={"error": "Product name already exists"}).bad_request_exception()
        
        # Update product
        update_data = product_data.model_dump(exclude_unset=True)
        for key, value in update_data.items():
            setattr(product, key, value)
        if (
            "price" in update_data
            or "tax_rate" in update_data
            or "enable_tax" in update_data
        ):
            if product.enable_tax and product.tax_rate is not None:
                product.price_with_tax = product.price * (1 + product.tax_rate / 100)
            else:
                product.price_with_tax = product.price
        
        db.commit()
        db.refresh(product)
        
        return product

def delete_product_service(
    db: Session,
    product_id: int,
    current_user: User
) -> bool:
        # Get product
        product = db.query(Product).filter(
            Product.id == product_id
        ).first()
        
        if not product:
            raise CustomException(content={"error": "Product not found"}).not_found_exception()
        
        # Check if organization owns the product
        if product.organization_id != current_user.organization_id:
            raise CustomException().forbidden_exception(
                data = {"error": ["You don't have permission to delete this product"]}
            )
        
        # Delete product
        db.delete(product)
        db.commit()

        return True

def publish_product_service(
    db: Session,
    product_id: int,
    publish_data: ProductPublish,
    current_user: User
) -> Product:
    product = db.query(Product).filter(Product.id == product_id).first()

    if not product:
        raise CustomException(content={"error": "Product not found"}).not_found_exception()

    if product.organization_id != current_user.organization_id:
        raise CustomException(content={"error": "You don't have permission to publish this product"}).permission_denied_exception()

    if product.on_marketplace:
        raise CustomException(content={"error": "Product already published"}).bad_request_exception()

    product.price = publish_data.price
    product.enable_tax = publish_data.enable_tax
    product.tax_rate = publish_data.tax_rate
    if product.enable_tax and product.tax_rate is not None:
        product.price_with_tax = product.price * (1 + product.tax_rate / 100)
    else:
        product.price_with_tax = product.price

    product.on_marketplace = True
    product.listed_at = datetime.now(timezone.utc)

    db.commit()
    db.refresh(product)

    return product

def get_products_service(
    db: Session,
    user: User,
    filters: ProductFilter
) -> dict:
    query = db.query(Product)

    # Apply filters
    if filters.category_id:
        query = query.filter(Product.category_id == filters.category_id)
    if filters.type:
        query = query.filter(Product.type == filters.type)
    if filters.organization_id:
        query = query.filter(Product.organization_id == filters.organization_id)
    if filters.price_min is not None:
        query = query.filter(Product.price >= filters.price_min)
    if filters.price_max is not None:
        query = query.filter(Product.price <= filters.price_max)
    if filters.status:
        query = query.filter(Product.status == filters.status)
    if filters.on_marketplace is not None:
        query = query.filter(Product.on_marketplace == filters.on_marketplace)
    if user.organization_id:
        query = query.filter(Product.organization_id == user.organization_id)

    # Load relationships
    query = query.options(
        joinedload(Product.category),
        joinedload(Product.organization),
    ).order_by(
        desc(Product.created_at)
    )

    # Get total count
    total = query.count()

    # Calculate pagination
    total_pages = (total + filters.size - 1) // filters.size
    skip = (filters.page - 1) * filters.size

    # Execute query with pagination
    products = query.offset(skip).limit(filters.size).all()

    return PaginatedProductResponse(
        items=[ProductRead.model_validate(product, from_attributes=True) for product in products],
        total=total,
        page=filters.page,
        size=filters.size,
        total_pages=total_pages
    ) 