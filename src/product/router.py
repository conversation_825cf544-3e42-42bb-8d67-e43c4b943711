from fastapi import APIRouter, Depends, Query, Path
from sqlalchemy.orm import Session
from typing import Optional
from src.utils.database import get_db
from src.user.models import User
from src.product import service
from src.product.schemas import (
    CategoryCreate,
    CategoryUpdate,
    CategoryRead,
    ProductCreate,
    ProductFilter,
    ProductUpdate,
    ProductPublish,
    ProductRead,
    PaginatedCategoryResponse,
    PaginatedProductResponse
)
from src.product.models import ProductTypeEnum
from src.utils.permission import is_authenticated
from src.utils.token_docs import jwt_auth
from src.utils.response import CustomResponse
router = APIRouter(prefix="", tags=["Product"])

@router.post("/categories", response_model=CategoryRead, dependencies=[Depends(jwt_auth)])
def create_category(
    category_data: CategoryCreate,
    db: Session = Depends(get_db),
    current_user: User = Depends(is_authenticated)
):
    result = service.create_category_service(db, category_data)
    return CustomResponse().format_data_create()

@router.get("/categories", response_model=PaginatedCategoryResponse, dependencies=[Depends(jwt_auth)])
def get_categories(
    page: int = Query(1, ge=1),
    size: int = Query(10, ge=1, le=100),
    parent_id: Optional[int] = None,
    db: Session = Depends(get_db),
    current_user: User = Depends(is_authenticated)
):
    result = service.get_categories_service(db, page, size, parent_id)
    return CustomResponse(content=result).format_data_get()

@router.get("/categories/{category_id}", response_model=CategoryRead, dependencies=[Depends(jwt_auth)])
def get_category_detail(
    category_id: int = Path(..., ge=1),
    db: Session = Depends(get_db),
    current_user: User = Depends(is_authenticated)
):
    result = service.get_category_detail_service(db, category_id)
    return CustomResponse(content=result).format_data_get()

@router.put("/categories/{category_id}", response_model=CategoryRead, dependencies=[Depends(jwt_auth)])
def update_category(
    category_id: int = Path(..., ge=1),
    category_data: CategoryUpdate = None,
    db: Session = Depends(get_db),
    current_user: User = Depends(is_authenticated)
):
    result = service.update_category_service(db, category_id, category_data)
    return CustomResponse(content=result).format_data_update()

@router.delete("/categories/{category_id}", dependencies=[Depends(jwt_auth)])
def delete_category(
    category_id: int = Path(..., ge=1),
    db: Session = Depends(get_db),
    current_user: User = Depends(is_authenticated)
):
    result = service.delete_category_service(db, category_id)
    return CustomResponse(content=result).format_data_delete()

@router.post("", response_model=ProductRead, dependencies=[Depends(jwt_auth)])
def create_product(
    product_data: ProductCreate,
    db: Session = Depends(get_db),
    current_user: User = Depends(is_authenticated)
):
    result = service.create_product_service(db, product_data, current_user)
    return CustomResponse(content=result).format_data_create()

@router.get("", response_model=PaginatedProductResponse, dependencies=[Depends(jwt_auth)])
def get_products(
    filters: ProductFilter = Depends(),
    db: Session = Depends(get_db),
    current_user: User = Depends(is_authenticated),
):
    result = service.get_products_service(db, current_user, filters)
    return CustomResponse(content=result).format_data_get()

@router.get("/{product_id}", response_model=ProductRead, dependencies=[Depends(jwt_auth)])
def get_product_detail(
    product_id: int = Path(..., ge=1),
    db: Session = Depends(get_db),
    current_user: User = Depends(is_authenticated)
):
    result = service.get_product_detail_service(db, product_id, current_user)
    return CustomResponse(content=result).format_data_get()

@router.put("/{product_id}", response_model=ProductRead, dependencies=[Depends(jwt_auth)])
def update_product(
    product_id: int = Path(..., ge=1),
    product_data: ProductUpdate = None,
    db: Session = Depends(get_db),
    current_user: User = Depends(is_authenticated)
):
    result = service.update_product_service(db, product_id, product_data, current_user)
    return CustomResponse(content=result).format_data_update()

@router.post("/{product_id}/market", response_model=ProductRead, dependencies=[Depends(jwt_auth)])
def publish_product(
    product_id: int = Path(..., ge=1),
    publish_data: ProductPublish = None,
    db: Session = Depends(get_db),
    current_user: User = Depends(is_authenticated)
):
    result = service.publish_product_service(db, product_id, publish_data, current_user)
    return CustomResponse(content=result).format_data_update()

@router.delete("/{product_id}", dependencies=[Depends(jwt_auth)])
def delete_product(
    product_id: int = Path(..., ge=1),
    db: Session = Depends(get_db),
    current_user: User = Depends(is_authenticated)
):
    result = service.delete_product_service(db, product_id, current_user)
    return CustomResponse(content=result).format_data_delete()
