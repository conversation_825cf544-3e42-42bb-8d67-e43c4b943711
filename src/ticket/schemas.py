from datetime import datetime
from typing import Optional
from pydantic import Field
from src.utils.schemas import PaginatedResponse
from src.utils.auto_schema import AutoSchema

class TicketBase(AutoSchema):
    name: str = Field(..., example="VIP")
    description: Optional[str] = Field(None, example="VIP ticket")
    price: float = Field(..., example=100.0)
    quantity: int = Field(..., example=50)
    start_sale: Optional[datetime] = Field(None, example="2024-05-01T00:00:00Z")
    end_sale: Optional[datetime] = Field(None, example="2024-05-10T00:00:00Z")

class TicketCreate(TicketBase):
    event_id: int = Field(..., example=1)

class TicketUpdate(AutoSchema):
    name: Optional[str] = Field(None, example="VIP")
    description: Optional[str] = Field(None, example="VIP ticket")
    price: Optional[float] = Field(None, example=100.0)
    quantity: Optional[int] = Field(None, example=50)
    start_sale: Optional[datetime] = Field(None, example="2024-05-01T00:00:00Z")
    end_sale: Optional[datetime] = Field(None, example="2024-05-10T00:00:00Z")

class TicketResponse(TicketBase):
    id: int
    event_id: int
    created_at: datetime
    updated_at: datetime

    class Config:
        from_attributes = True

PaginatedTicketResponse = PaginatedResponse[TicketResponse]
