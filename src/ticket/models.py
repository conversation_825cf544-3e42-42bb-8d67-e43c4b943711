from sqlalchemy import Column, Integer, ForeignKey, String, Float, Text, DateTime
from sqlalchemy.orm import relationship
from datetime import datetime
from src.utils.base_model import BaseModel, Base

class Ticket(BaseModel, Base):
    __tablename__ = "ticket"

    event_id = Column(Integer, ForeignKey("event.id"), nullable=False)
    name = Column(String(255), nullable=False)
    description = Column(Text, nullable=True)
    price = Column(Float, nullable=False)
    quantity = Column(Integer, nullable=False)
    start_sale = Column(DateTime, nullable=True)
    end_sale = Column(DateTime, nullable=True)

    event = relationship("Event", back_populates="tickets")
