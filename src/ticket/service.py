from sqlalchemy.orm import Session
from sqlalchemy import desc
from src.utils.exceptions import CustomException
from src.ticket.models import Ticket
from src.ticket.schemas import TicketCreate, TicketUpdate, PaginatedTicketResponse
from src.event.models import Event


def create_ticket_service(db: Session, ticket_data: TicketCreate, user_id: int) -> Ticket:
    event = db.query(Event).filter(
        Event.id == ticket_data.event_id,
        Event.created_by == user_id,
        Event.is_deleted == False
    ).first()
    if not event:
        raise CustomException(content={"error": "Event not found"}).not_found_exception()

    ticket = Ticket(**ticket_data.model_dump())
    db.add(ticket)
    db.commit()
    db.refresh(ticket)
    return ticket


def get_ticket_detail_service(db: Session, ticket_id: int, user_id: int) -> Ticket:
    ticket = db.query(Ticket).join(Event).filter(
        Ticket.id == ticket_id,
        Ticket.is_deleted == False,
        Event.created_by == user_id
    ).first()
    if not ticket:
        raise CustomException().not_found_exception()
    return ticket


def update_ticket_service(db: Session, ticket_id: int, ticket_data: TicketUpdate, user_id: int) -> Ticket:
    ticket = get_ticket_detail_service(db, ticket_id, user_id)
    for key, value in ticket_data.model_dump(exclude_unset=True).items():
        setattr(ticket, key, value)
    db.commit()
    db.refresh(ticket)
    return ticket


def delete_ticket_service(db: Session, ticket_id: int, user_id: int) -> bool:
    ticket = get_ticket_detail_service(db, ticket_id, user_id)
    db.delete(ticket)
    db.commit()
    return True


def get_tickets_by_event_service(db: Session, event_id: int, page: int, size: int, user_id: int) -> PaginatedTicketResponse:
    query = db.query(Ticket).join(Event).filter(
        Ticket.event_id == event_id,
        Ticket.is_deleted == False,
        Event.created_by == user_id
    ).order_by(desc(Ticket.created_at))

    total = query.count()
    total_pages = (total + size - 1) // size
    tickets = query.offset((page - 1) * size).limit(size).all()

    return PaginatedTicketResponse(
        items=tickets,
        total=total,
        page=page,
        size=size,
        total_pages=total_pages
    )
