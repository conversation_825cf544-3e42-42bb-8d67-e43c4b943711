from fastapi import APIRouter, Depends, Query
from sqlalchemy.orm import Session
from src.utils.database import get_db
from src.user.models import User
from src.utils.permission import is_authenticated
from src.utils.token_docs import jwt_auth
from src.utils.response import CustomResponse
from src.ticket.schemas import (
    TicketCreate,
    TicketUpdate,
    TicketResponse,
    PaginatedTicketResponse
)
from src.ticket.service import (
    create_ticket_service,
    get_ticket_detail_service,
    update_ticket_service,
    delete_ticket_service,
    get_tickets_by_event_service
)

router = APIRouter(prefix="", tags=["Ticket"])


@router.post("/create", response_model=TicketResponse, dependencies=[Depends(jwt_auth)])
def create_ticket(
    ticket_data: TicketCreate,
    db: Session = Depends(get_db),
    current_user: User = Depends(is_authenticated)
):
    result = create_ticket_service(db, ticket_data, current_user.id)
    return CustomResponse(content=result).format_data_create()


@router.get("/detail/{ticket_id}", response_model=TicketResponse, dependencies=[Depends(jwt_auth)])
def get_ticket_detail(
    ticket_id: int,
    db: Session = Depends(get_db),
    current_user: User = Depends(is_authenticated)
):
    result = get_ticket_detail_service(db, ticket_id, current_user.id)
    return CustomResponse(content=result).format_data_get()


@router.put("/update/{ticket_id}", response_model=TicketResponse, dependencies=[Depends(jwt_auth)])
def update_ticket(
    ticket_id: int,
    ticket_data: TicketUpdate,
    db: Session = Depends(get_db),
    current_user: User = Depends(is_authenticated)
):
    result = update_ticket_service(db, ticket_id, ticket_data, current_user.id)
    return CustomResponse(content=result).format_data_update()


@router.delete("/delete/{ticket_id}", dependencies=[Depends(jwt_auth)])
def delete_ticket(
    ticket_id: int,
    db: Session = Depends(get_db),
    current_user: User = Depends(is_authenticated)
):
    delete_ticket_service(db, ticket_id, current_user.id)
    return CustomResponse().format_data_delete()


@router.get("/event/{event_id}", response_model=PaginatedTicketResponse, dependencies=[Depends(jwt_auth)])
def get_tickets_by_event(
    event_id: int,
    page: int = Query(1, ge=1),
    size: int = Query(10, ge=1, le=100),
    db: Session = Depends(get_db),
    current_user: User = Depends(is_authenticated)
):
    result = get_tickets_by_event_service(db, event_id, page, size, current_user.id)
    return CustomResponse(content=result).format_data_get()
