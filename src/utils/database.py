from fastapi import Request
from sqlalchemy import create_engine
from src.utils.base_model import Base
from sqlalchemy.orm import sessionmaker
import os
from jose import JWTError
from sshtunnel import SSHTunnelForwarder
from dotenv import load_dotenv

# Load env files
load_dotenv(verbose=True)
# ssh_tunnel = SSHTunnelForwarder(
#     ssh_address_or_host='*************',
#     ssh_username='ubuntu',
#     ssh_pkey='/src/internal-exrunner.pem',
#     remote_bind_address=('exrunner-sgp-db.cbidniyzzfaj.ap-southeast-1.rds.amazonaws.com',3306)
# )
# ssh_tunnel.start()

# SQLALCHEMY_DATABASE_URL = f"mysql://{os.environ.get('DATABASE_USER')}:{os.environ.get('DATABASE_PASSWORD')}@{os.environ.get('DATABASE_HOST')}:{ssh_tunnel.local_bind_port}/{os.environ.get('DATABASE_NAME')}?charset=utf8mb4"

SQLALCHEMY_DATABASE_URL = (
    f"mysql+pymysql://{os.environ.get('DB_USER')}:{os.environ.get('DB_PASSWORD')}@"
    f"{os.environ.get('DB_HOST')}:{os.environ.get('DB_PORT')}/{os.environ.get('DB_NAME')}"
)
# SQLALCHEMY_DATABASE_URL = "***********************************************/aioslink"

# Enable connection health checks and recycling to avoid stale connections
engine = create_engine(
    SQLALCHEMY_DATABASE_URL,
    pool_pre_ping=True,
    pool_recycle=3600,
)
SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)

# Create tables
# Base.metadata.create_all(bind=engine)

# Dependency
# def get_db(request: Request):
#     return request.state.db

async def get_db():
    db = SessionLocal()
    try:
        yield db
    finally:
        db.close()
def get_token(request: Request):
    authorization: str = request.headers.get("Authorization")
    if authorization:
        scheme, _, token = authorization.partition(" ")
        return token
    return None
