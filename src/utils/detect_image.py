# import cv2
# import face_recognition
# import os
# import numpy as np
# import requests

# def store_face(image_binary):
#     # Sử dụng OpenCV để đọc hình <PERSON>nh từ mảng byte
    
#     image_arr = np.frombuffer(image_binary, dtype=np.uint8)

#     # Sử dụng OpenCV để giải mã mảng NumPy thành hình ảnh
#     image = cv2.imdecode(image_arr, cv2.IMREAD_COLOR)

#     if image is None:
#         print("Error: Could not read image.")
#         return None
#     else:
#         # Chuyển đổi hình ảnh sang không gian màu RGB (dành cho face_recognition)
#         rgb_image = cv2.cvtColor(image, cv2.COLOR_BGR2RGB)
        
#         # Sử dụng face_recognition để lấy các face encodings
#         face_encodings = face_recognition.face_encodings(rgb_image)
        
#         return face_encodings

# def load_all_faces_in_db(users):
#     face_encodings = []
#     face_names = []

#     for user in users:
#         if user.avatar_binary:
#             image_arr = np.frombuffer(user.avatar_binary, dtype=np.uint8)
#             image = cv2.imdecode(image_arr, cv2.IMREAD_COLOR)
#             if image is not None:
#                 rgb_image = cv2.cvtColor(image, cv2.COLOR_BGR2RGB)
#                 encodings = face_recognition.face_encodings(rgb_image)
#                 if encodings:
#                     face_encodings.append(encodings[0])
#                     face_names.append(user.id)  # Store user ID or name
#     return face_encodings, face_names

# def search_faces_in_image(image_arr, face_encodings, face_names):
#     image = cv2.imdecode(image_arr, cv2.IMREAD_COLOR)
#     if image is None:
#         return []
#     rgb_image = cv2.cvtColor(image, cv2.COLOR_BGR2RGB)
#     unknown_face_encodings = face_recognition.face_encodings(rgb_image)

#     identified_faces = []
#     for unknown_encoding in unknown_face_encodings:
#         matches = face_recognition.compare_faces(face_encodings, unknown_encoding)
#         if True in matches:
#             first_match_index = matches.index(True)
#             identified_faces.append(face_names[first_match_index])
#     return identified_faces

# def find_user_with_image(list_users, image_path: str):
#     # Tải tất cả khuôn mặt từ cơ sở dữ liệu
#     face_encodings, face_names = load_all_faces_in_db(list_users)
    
#     # Tải hình ảnh từ URL và chuyển đổi sang numpy array
#     response = requests.get(image_path, stream=True)
#     if response.status_code == 200:
#         image_arr = np.asarray(bytearray(response.content), dtype=np.uint8)
#         identified_faces = search_faces_in_image(image_arr, face_encodings, face_names)
#         return identified_faces
#     else:
#         return []
