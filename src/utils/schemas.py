from typing import <PERSON>V<PERSON>, Generic, List
from src.utils.auto_schema import AutoSchema as BaseModel
from pydantic import Field

T = TypeVar('T')

class PaginatedResponse(BaseModel, Generic[T]):
    items: List[T]
    total: int
    page: int = Field(..., description="Current page number", ge=1)
    size: int = Field(..., description="Number of items per page", ge=1, le=100)
    total_pages: int

    model_config = {
        "json_schema_extra": {
            "examples": [
                {
                    "success": True,
                    "status": 200,
                    "message": "GET_DATA_SUCCESS",
                    "data": {
                        "items": [],
                        "total": 100,
                        "page": 1,
                        "size": 10,
                        "total_pages": 10
                    }
                }
            ]
        }
    } 

class GetDataResponse(BaseModel, Generic[T]):
    success: bool = True
    status: int = 200
    messsage: int = Field("Get data successfully", description="Get data successfully")
    data: T

    model_config = {
        "json_schema_extra": {
            "examples": [
                {
                    "success": True,
                    "status": 200,
                    "message": "Get data successfully",
                    "data": {}
                }
            ]
        }
    } 