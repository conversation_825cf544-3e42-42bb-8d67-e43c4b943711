from fastapi.security import HTT<PERSON>uthorizationCredentials, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>
from typing import Op<PERSON>
from fastapi.exceptions import HTTPException
from fastapi.security.utils import get_authorization_scheme_param
from starlette.requests import Request
from starlette.status import HTTP_401_UNAUTHORIZED
from src.utils.exceptions import CustomException

class HTTPBearerCustom(HTTPBearer):
    async def __call__(
        self, request: Request
    ) -> Optional[HTTPAuthorizationCredentials]:
        authorization = request.headers.get("Authorization")
        scheme, credentials = get_authorization_scheme_param(authorization)
        if not (authorization and scheme and credentials):
            if self.auto_error:
                raise CustomException().invalid_token_exception()
            else:
                return None
        if scheme.lower() != "bearer":
            if self.auto_error:
                raise CustomException().invalid_token_exception()
            else:
                return None
        return HTTPAuthorizationCredentials(scheme=scheme, credentials=credentials)

jwt_auth = HTTPBearerCustom(scheme_name='Authorization')