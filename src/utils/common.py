import asyncio
from datetime import date, datetime, timedelta, timezone
from email.mime.multipart import MIMEMultipart
from email.mime.text import MIMEText
import json
import os
import secrets
import smtplib
import string
from typing import List, Type, TypeVar, Union
from uuid import uuid4
from fastapi import Depends, FastAPI, HTTPException, Request, UploadFile
from fastapi.routing import APIRoute
from pymysql import NULL
import pytz
from sqlalchemy import func, or_, table
from sqlalchemy.orm import Session, Query, joinedload
# from src.translation.models import Translation
from src.utils.base_model import Base
import re
from src.user import models
from sqlalchemy.inspection import inspect

from .exceptions import CustomException 
from src.utils.enum import ExpirationUnit, FormatDateTime
# from google.oauth2 import service_account
# from googleapiclient.discovery import build
from openpyxl.utils import get_column_letter
from sqlalchemy.orm import class_mapper
# import firebase_admin
# from firebase_admin import credentials, auth
import boto3
from alembic import op
import sqlalchemy as sa

# Define a TypeVar to use for generic typing
T = TypeVar("T", bound=Base)

firebase_app = None


def get_firebase():
    global firebase_app
    if not firebase_admin._apps:
        cred = credentials.Certificate(
            "exnodes-8257f-firebase-adminsdk-w274t-0c8b8e433b.json")
        firebase_app = firebase_admin.initialize_app(cred)
    return firebase_app


def convert_model_to_schema(instance, schema):
    return schema(**instance.__dict__)


def query_list_translation(db: Session, query: Query[Base], model: Type[Base], schema, params: dict, search_fields: List[str], language: str, return_list=False):
    # Áp dụng tìm kiếm theo các trường search_fields
    if params.get('search'):
        search_conditions = []
        for field in search_fields:
            if '__' in field:
                # Xử lý việc lọc theo các trường có liên quan
                related_model_name, related_field = field.split('__')
                related_model = None

                for rel in inspect(model).relationships:
                    if rel.key == related_model_name:
                        related_model = rel.mapper.class_
                        break

                if related_model:
                    query = query.join(getattr(model, related_model_name))
                    search_conditions.append(
                        getattr(related_model, related_field, None).ilike(f"%{params.get('search')}%"))
                else:
                    continue
            elif hasattr(model, field):
                search_conditions.append(
                    getattr(model, field).ilike(f"%{params.get('search')}%"))
        if search_conditions:
            query = query.filter(or_(*search_conditions))

    # Áp dụng sắp xếp
    if params.get('order_by'):
        list_order_by_conditions = [i.strip() for i in str(
            params.get('order_by')).split(',')]
        for con in list_order_by_conditions:
            if con[0] == '-':
                query = query.order_by(getattr(model, con[1:]).desc())
            else:
                query = query.order_by(getattr(model, con).asc())

    # Áp dụng lọc theo danh mục
    if params.get('filter_fields'):
        for filter_field, multiple_values in params.get('filter_fields').items():
            if '__' in filter_field:
                # Xử lý việc lọc theo các trường có liên quan
                related_model_name, related_field = filter_field.split('__')
                related_model = None

                for rel in inspect(model).relationships:
                    if rel.key == related_model_name:
                        related_model = rel.mapper.class_
                        break

                if related_model:
                    query = query.join(getattr(model, related_model_name))
                    column = getattr(related_model, related_field, None)
                else:
                    continue
            else:
                column = getattr(model, filter_field, None)

            if column:
                list_values = [int(item.strip()) if str(filter_field).__contains__('_id') else item.strip() for item in
                               str(multiple_values).split(',')]
                for value in list_values:
                    query = query.filter(column == value)

    # Tải trước các bản dịch và lọc theo language_code
    query = query.options(joinedload(
        model.translations).load_only('field_name', 'text'))

    # Lọc các bản dịch theo language_code bằng cách sử dụng subquery
    subquery = db.query(Translation.entity_id).filter(
        Translation.entity_id == model.id,
        Translation.entity_type == 'position',
        Translation.language_code == language
    ).subquery()

    query = query.filter(model.id.in_(subquery))

    # Phân trang dữ liệu
    items = query.offset(params['skip']).limit(params['limit'])

    return [schema.model_validate(item) for item in items] if not return_list else items.all(), query.count()


def query_list(query: Query[Base], model: Type[Base], schema, params: dict, search_fields=List[str], return_list=False):  # type: ignore

    # Apply search by search_fields
    if params.get('search'):
        search_conditions = []
        for field in search_fields:
            if '__' in field:
                # Handling related field filtering
                related_model_name, related_field = field.split('__')
                related_model = None

                for rel in inspect(model).relationships:
                    if rel.key == related_model_name:
                        related_model = rel.mapper.class_
                        break

                if related_model:
                    query = query.join(getattr(model, related_model_name))
                    search_conditions.append(
                        getattr(related_model, related_field, None).ilike(f"%{params.get('search')}%"))
                else:
                    continue
            elif hasattr(model, field):
                search_conditions.append(
                    getattr(model, field).ilike(f"%{params.get('search')}%"))
        if search_conditions:
            query = query.filter(or_(*search_conditions))

    # Apply order by
    if params.get('order_by'):
        list_order_by_conditions = [i.strip() for i in str(
            params.get('order_by')).split(',')]
        for con in list_order_by_conditions:
            if con[0] == '-':
                query = query.order_by(getattr(model, con[1:]).desc())
            else:
                query = query.order_by(getattr(model, con).asc())

    # Apply filter by category
    if params.get('filter_fields'):
        for filter_field, multiple_values in params.get('filter_fields').items():
            if '__' in filter_field and '__gt' not in filter_field and '__gte' not in filter_field and '__lt' not in filter_field and '__lte' not in filter_field:
                # Handling related field filtering
                related_model_name, related_field = filter_field.split('__')
                related_model = None

                for rel in inspect(model).relationships:
                    if rel.key == related_model_name:
                        related_model = rel.mapper.class_
                        break

                if related_model:
                    query = query.join(getattr(model, related_model_name))
                    column = getattr(related_model, related_field, None)
                else:
                    continue
            elif '__gt' in filter_field or '__gte' in filter_field or '__lt' in filter_field or '__lte' in filter_field:
                # Handling related field filtering
                field, operator = filter_field.split('__')
                column = getattr(model, field, None)

                if operator == 'gt':
                    query = query.filter(column > multiple_values)
                elif operator == '__gte':
                    query = query.filter(column >= multiple_values)
                elif operator == '__lt':
                    query = query.filter(column < multiple_values)
                elif operator == '__lte':
                    query = query.filter(column <= multiple_values)
            else:
                column = getattr(model, filter_field, None)

                if column:
                    list_values = [int(item.strip()) if str(filter_field).__contains__(
                        '_id') else item.strip() for item in str(multiple_values).split(',')]
                    for value in list_values:
                        query = query.filter(column == value)

    # Paginate data
    items = query.offset(params['skip']).limit(params['limit'])

    return [schema.model_validate(item) for item in items] if not return_list else items.all(), query.count()


# type: ignore
def query_list_translation(query: Query, model: Type[Base], schema, params: dict, search_fields: List[str], return_list=False):
    # Apply search by search_fields
    if params.get('search'):
        search_conditions = []
        for field in search_fields:
            if '__' in field:
                related_model_name, related_field = field.split('__')
                related_model = None
                for rel in inspect(model).relationships:
                    if rel.key == related_model_name:
                        related_model = rel.mapper.class_
                        break
                if related_model:
                    query = query.join(getattr(model, related_model_name))
                    search_conditions.append(
                        getattr(related_model, related_field, None).ilike(f"%{params.get('search')}%"))
                else:
                    continue
            elif hasattr(model, field):
                search_conditions.append(
                    getattr(model, field).ilike(f"%{params.get('search')}%"))
        if search_conditions:
            query = query.filter(or_(*search_conditions))

    # Apply order by
    if params.get('order_by'):
        list_order_by_conditions = [i.strip() for i in str(
            params.get('order_by')).split(',')]
        for con in list_order_by_conditions:
            if con[0] == '-':
                query = query.order_by(getattr(model, con[1:]).desc())
            else:
                query = query.order_by(getattr(model, con).asc())

    # Apply filter by category
    if params.get('filter_fields'):
        for filter_field, multiple_values in params.get('filter_fields').items():
            if '__' in filter_field:
                related_model_name, related_field = filter_field.split('__')
                related_model = None
                for rel in inspect(model).relationships:
                    if rel.key == related_model_name:
                        related_model = rel.mapper.class_
                        break
                if related_model:
                    query = query.join(getattr(model, related_model_name))
                    column = getattr(related_model, related_field, None)
                else:
                    continue
            else:
                column = getattr(model, filter_field, None)
                if column:
                    list_values = [int(item.strip()) if str(filter_field).__contains__(
                        '_id') else item.strip() for item in str(multiple_values).split(',')]
                    for value in list_values:
                        query = query.filter(column == value)

    # Paginate data
    items = query.offset(params['skip']).limit(params['limit']).all()

    # Kết hợp các đối tượng Translation thành các nhóm theo entity_id, entity_type và language_code
    grouped_items = {}
    for item in items:
        key = (item.entity_id, item.entity_type, item.language_code)
        if key not in grouped_items:
            grouped_items[key] = []
        grouped_items[key].append(item)

    # Chuyển đổi từng nhóm thành đối tượng Pydantic với trường `data` hợp lệ
    result = [schema.from_orm_with_data(group)
              for group in grouped_items.values()]

    return result if not return_list else items, query.count()


def replace_integers_with_id(input_str):
    # Define a function to use as the replacement in re.sub
    def replacer(match):
        # match.group() returns the matched string, which is an integer here
        return '{id}'

    # Use re.sub to replace all integers in the string with ':id'
    result = re.sub(r'\b\d+\b', replacer, input_str)
    return result

# Function to get all route information


def get_routes_info(app: FastAPI):
    routes_info = []
    for route in app.routes:
        if isinstance(route, APIRoute):
            methods = route.methods
            iterator = iter(methods)
            scopes = []

            for dependency in route.dependencies:
                if hasattr(dependency, 'dependency'):
                    dep = dependency.dependency
                    # Check if the dependency is the has_scope function
                    if hasattr(dep, '__name__') and dep.__name__ == 'scope_dependency':
                        # Extract the required scope from the closure
                        scopes_in_route = dep.__closure__[0].cell_contents
                        scopes.extend(scopes_in_route)

            route_info = {
                "url": route.path,
                "name": route.name,
                "method": next(iterator, None),
                "end_point": route.endpoint.__name__,
                "scopes": scopes if scopes else [],
            }
            routes_info.append(route_info)
    return routes_info


def format_response(success, status, message, data):
    return {
        "success": success,
        "status": status,
        "message": message,
        "data": data
    }


async def send_email(subject: str, to: List[str], html_body: str):

    try:
        msg = MIMEMultipart()
        email_from = os.environ.get('EMAIL_FROM', '<EMAIL>')
        msg['From'] = email_from
        msg['To'] = ', '.join(to)
        msg['Subject'] = subject
        msg.attach(MIMEText(html_body, 'html'))

        with smtplib.SMTP_SSL(os.environ.get('EMAIL_HOST', 'smtp.gmail.com'), int(os.environ.get('EMAIL_PORT', 465))) as server:
            server.login(os.environ.get('EMAIL_USER', '<EMAIL>'),
                         os.environ.get('EMAIL_PASSWORD', 'ruoq fgzn iwxj mwwg'))
            server.sendmail(email_from, to, msg.as_string())
            server.quit()
            print('Sent email')
    except Exception as e:
        print("Error sending email:", e)


def generate_otp(length: int = 6):
    """Generate a random OTP of the specified length."""
    alphabet = string.digits  # Use digits (0-9) for OTP
    return ''.join(secrets.choice(alphabet) for _ in range(length))


def calculate_expired_time(expire_unit: ExpirationUnit = ExpirationUnit.DAYS, value: int = 5):
    if expire_unit == ExpirationUnit.DAYS:
        expires_delta = timedelta(days=value)
    elif expire_unit == ExpirationUnit.HOURS:
        expires_delta = timedelta(hours=value)
    elif expire_unit == ExpirationUnit.MINUTES:
        expires_delta = timedelta(minutes=value)
    return datetime.now(timezone.utc) + expires_delta


def get_user_token(request: Request, db: Session):
    auth_header = request.headers.get("Authorization")
    if auth_header:
        token = auth_header[len("Bearer "):]
        token_db = db.query(models.Token).filter(
            models.Token.access_token == token
        ).first()
        return token_db
    else:
        raise CustomException().authorization_exception()


# Múi giờ Việt Nam
VIETNAM_TIMEZONE = pytz.timezone('Asia/Ho_Chi_Minh')


def get_current_vietnam_time():
    return datetime.now(VIETNAM_TIMEZONE)


def get_current_vietnam_date():
    return get_current_vietnam_time().date()


def get_current_vietnam_time_only():
    return get_current_vietnam_time().time()


def format_date(check_date):
    return check_date.strftime('%d/%m/%Y')


def format_time(time):
    return time.strftime('%H:%M')


def get_service_gg_sheet():
    # Đường dẫn tới tệp JSON khóa dịch vụ
    credentials_file = os.environ.get(
        'GOOGLE_SHEET_CREDENTIALS_FILE', 'exnodes-8257f-522a6e96a351.json')

    # Phạm vi (scope) của dịch vụ Google Sheets API
    SCOPES = [os.environ.get('GOOGLE_SHEET_SCOPES',
                             'https://www.googleapis.com/auth/spreadsheets')]

    # Xác thực với dịch vụ Google Sheets API
    credentials = service_account.Credentials.from_service_account_file(
        credentials_file, scopes=SCOPES)
    service = build('sheets', 'v4', credentials=credentials)

    return service


def check_sheet_exist(service, spreadsheet_id, sheet_name):
    sheet_metadata = service.spreadsheets().get(
        spreadsheetId=spreadsheet_id).execute()
    sheets = sheet_metadata.get('sheets', [])
    for sheet in sheets:
        if sheet['properties']['title'] == sheet_name:
            return True
    return False


def create_new_sheet(service, spreadsheet_id, sheet_name):
    batch_update_spreadsheet_request_body = {
        'requests': [{
            'addSheet': {
                'properties': {
                    'title': sheet_name,
                }
            }
        }]
    }
    service.spreadsheets().batchUpdate(
        spreadsheetId=spreadsheet_id,
        body=batch_update_spreadsheet_request_body
    ).execute()


def get_sheet_id(service, spreadsheet_id, sheet_name):
    sheet_metadata = service.spreadsheets().get(
        spreadsheetId=spreadsheet_id).execute()
    sheets = sheet_metadata.get('sheets', [])
    for sheet in sheets:
        if sheet.get("properties", {}).get("title") == sheet_name:
            return sheet.get("properties", {}).get("sheetId")
    return None


def generate_sheet_scope(rows: int, cols: int, start_cell: str = 'A1'):
    end_cell = get_column_letter(cols) + str(rows)
    return f"{start_cell}:{end_cell}"


def upload_data_to_sheet(service, spreadsheet_id: str, range_: str, data: list):

    request = {
        'valueInputOption': 'RAW',
        'data': [
            {
                'range': range_,
                'values': data,
                'majorDimension': 'ROWS'
            }
        ]
    }

    # Ghi dữ liệu vào Google Sheets
    service.spreadsheets().values().batchUpdate(
        spreadsheetId=spreadsheet_id,
        body=request,
    ).execute()


def get_range_sheet(sheet_name: str, cols_count: int, rows_count: int):
    return f'{sheet_name}!{generate_sheet_scope(rows=rows_count+1,cols=cols_count)}'


def get_url_sheet(spreadsheet_id: str, service, sheet_name: str):
    return f"https://docs.google.com/spreadsheets/d/{spreadsheet_id}/edit#gid={get_sheet_id(service, spreadsheet_id, sheet_name)}"


def json_serializer(obj):
    if isinstance(obj, datetime):
        return obj.strftime(FormatDateTime.NORMAL)
    elif isinstance(obj, set):
        return list(obj)
    elif isinstance(obj, float):
        return str(obj)
    elif isinstance(obj, date):
        return obj.strftime('%d/%m/%Y')
    elif isinstance(obj, str) or isinstance(obj, type(None)):
        return obj
    raise TypeError(f"Object of type {type(obj)} is not JSON serializable")


# type: ignore
def convert_list_models_to_sheet_data(cols: List[str], data: List[Base], merged_header=False):
    """
        `cols` is a list field name of Model and Related Model. Related field name: {related_model_name}__{related_field}.

        `data` is a list Model.

        `merge_head` is True if you want to merge headers base on Model.
    """
    # Get model of list data
    model = type(data[0])
    sheet_data, merged_header = [], []
    related_models = {}

    # Get related_model_name and related_model and map to dictionary
    for rel in inspect(model).relationships:
        related_models[rel.key] = rel.mapper.class_

    # Merge head base on model
    if merged_header:
        for col in cols:
            if '__' not in col:
                merged_header.append(model.__tablename__.upper())
            else:
                related_model_name, related_field = col.split('__')
                merged_header.append(related_models.get(
                    related_model_name).__tablename__.upper())
        sheet_data.append(merged_header)

    # Insert heads
    sheet_data.append(convert_fields_to_headers(cols))

    # Loop list data to get data base on cols
    for item in data:
        row_data = []
        for col in cols:
            if '__' not in col:
                # value of field in model
                value = getattr(item, col, '')
            else:
                # Handling related field filtering
                related_model_name, related_field = col.split('__')

                # value of field in related_model
                if related_model_name in related_models.keys():
                    related_model = related_models.get(related_model_name)
                    mapper = class_mapper(related_model)
                    attributes = [
                        prop.key for prop in mapper.iterate_properties]
                    if related_field in attributes:
                        related_data = getattr(
                            item, related_model.__tablename__, '')
                        if related_data:
                            value = getattr(related_data, related_field, '')
                        else:
                            value = ''
                    else:
                        value = ''
                else:
                    value = ''
            # Parse data to available for JSON Serializer
            row_data.append(json_serializer(value))
        sheet_data.append(row_data)  # Insert data in row
    return sheet_data


def create_a_merge_request(sheet_id: int, range_rows: tuple, range_cols: tuple):
    # Define the merge request
    return {
        "mergeCells": {
            "range": {
                "sheetId": sheet_id,  # The ID of the sheet, you can find this in the URL of the sheet
                "startRowIndex": range_rows[0],
                "endRowIndex": range_rows[1],
                "startColumnIndex": range_cols[0],
                "endColumnIndex": range_cols[1],
            },
            "mergeType": "MERGE_ALL"
        }
    }


def convert_fields_to_headers(fields: List[str]):
    headers = []
    for field in fields:
        if '__' not in field:
            headers.append(field.upper().replace('_', ' '))
        else:
            related_model_name, related_field = field.split('__')
            headers.append(related_field.upper().replace('_', ' '))
    return headers


def upload_file_to_s3(file, folder_name):
    file_name = str(uuid4()) + "_" + file.filename
    access_key = os.environ.get('S3_ACCESS_KEY', 'AKIAY6IABUUUFQDQ2N52')
    secret_key = os.environ.get(
        'S3_SECRET_KEY', 'dk+XWd1leZBlFgp2mu848cPPuba9JGY3lNmr70JlPYi')
    region_name = os.environ.get('S3_REGION_NAME', 'ap-southeast-1')
    bucket_name = os.environ.get('BUCKET_NAME', 'exrunner-sgp-public-storage')

    s3 = boto3.client('s3', aws_access_key_id=access_key,
                      aws_secret_access_key=secret_key)
    key = f"{folder_name}/{file_name}"
    url = f"https://{bucket_name}.s3.{region_name}.amazonaws.com/{key}"
    try:
        s3.upload_fileobj(
            Fileobj=file.file,
            Bucket=bucket_name,
            Key=key,
            ExtraArgs={"ContentType": "image/jpeg"}
        )
        print(
            f'Uploaded file {file_name} to S3 folder {folder_name} successfully')
        return url
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Could not upload file to S3: {str(e)}")

def format_urls_json(data):
    return {'urls':data} if data else None

def format_urls_list(data):
    return data.get('urls') if data else None

def format_urls_to_list(data):
    return data.get('urls') if data else []

def format_validate_error(type_field:str, field, value, message:str):
    return {
        "type": type(value.v),
        "loc": [
            type_field,
            field
        ],
        "msg": message,
        "input": value
    }

def read_json_file(file_path):
    with open(file_path, "r") as json_file:
        data = json.load(json_file)
    return data

def table_exists(conn, tablename):
    inspector = inspect(conn)
    return tablename in inspector.get_table_names()

def table_has_records(conn, tablename):
    result = conn.execute(sa.select(func.count('*')).select_from(table(tablename)))
    count = result.scalar()
    return count > 0

def init_list_object(model, list_data, **kwargs):
    return [model(**item) for item in list_data]

def seeding_data():
    conn = op.get_bind()
    json_data = read_json_file("seeding_data.json")
    for table_name, records in json_data.items():
        if table_exists(conn, table_name) and not table_has_records(conn, table_name):
            for record in records:
                values = [f"'{str(item)}'" if isinstance(item,str) or isinstance(item, dict) else NULL if item is None else str(item) for item in list(record.values())]
                script = f"""
                    INSERT INTO {table_name} (`{"`, `".join(record.keys())}`)
                    VALUES ({", ".join(values)})
                """
                op.execute(script)
