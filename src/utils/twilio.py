import os 
from dotenv import load_dotenv
from twilio.rest import Client
from src.notification_history.models import SMSHistory

def send_sms_sendgrid(db,phoneNumber, body ):
    account_sid = os.environ.get('TWILIO_ACCOUNT_SID')
    auth_token = os.environ.get('TWILIO_AUTH_TOKEN')
    from_phoneNumber = os.environ.get('TWILIO_FROM_PHONENUMBER')
    client = Client(account_sid, auth_token)
    phone_code = "+84"
    phone_number = f"{phone_code}{phoneNumber}"
    message = client.messages.create(
        from_=f'{from_phoneNumber}',
        to=f'+***********',
        body = f'{body}'
    )
    sms_history = SMSHistory(
        payload_data=message.sid,
        phone_number=phone_number,
        message=body,
    )
    db.add(sms_history)
    db.commit()

