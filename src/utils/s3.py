import boto3
from botocore.exceptions import NoCredentialsError
from botocore.config import Config
from dotenv import load_dotenv
import os
from fastapi import FastAPI, HTTPException

# Load env files
load_dotenv(verbose=True)

class S3:

    def __init__(self):
        self.client = boto3.client('s3',
                                   region_name = os.environ.get('S3_REGION_NAME'),
                                   aws_access_key_id=os.environ.get('S3_ACCESS_KEY', 'AKIAY6IABUUUAU3D6GXV'),
                                   aws_secret_access_key=os.environ.get('S3_SECRET_KEY', 'bN2yUBBzS5lowDulvfnvQVc5ZerdMo2z4b5AVez3/HLsFpVIm'),
                                   config=Config(signature_version='s3v4')
                                   )
        
    def generate_presigned_url(self,file_extension, object_name, expiration=3600):
        """
        Generate a presigned URL to upload a file to S3.

        :param bucket_name: Name of the S3 bucket
        :param object_name: S3 object name (file name in S3)
        :param expiration: Time in seconds for the presigned URL to remain valid
        :return: Presigned URL as string, or None if error
        """
        object_name = f'media/AIOSLink/{object_name}'
        try:
            response = self.client.generate_presigned_url(
                'put_object',
                Params={'Bucket': os.environ.get('S3_BUCKET_NAME', 'exn-sgp-public-storage'), 'Key': object_name},
                ExpiresIn=expiration
            )
            return response
        except NoCredentialsError:
            raise HTTPException(status_code=401, detail="AWS credentials not available.")
        except Exception as e:
            raise HTTPException(status_code=500, detail=str(e))