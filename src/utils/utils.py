import threading, random, os, math
from src.utils.exceptions import CustomException
import datetime
from src.utils.redis_cache import RedisUtils
from email.mime.text import MIMEText
from email.mime.multipart import MI<PERSON><PERSON>ultipart
from email.mime.application import MIMEApplication
import smtplib
from email.message import EmailMessage

redis_utils = RedisUtils()

# Email configuration
EMAIL_HOST = os.environ.get("EMAIL_HOST", "smtp.gmail.com")
EMAIL_PORT = int(os.environ.get("EMAIL_PORT", "465"))
EMAIL_HOST_USER = os.environ.get("EMAIL_HOST_USER", "")
EMAIL_HOST_PASSWORD = os.environ.get("EMAIL_PASSWORD", "")
FROM_EMAIL = os.environ.get("FROM_MAIL", EMAIL_HOST_USER)

def send_email_background(to_email: str, subject: str, body: str):
    def _send():
        try:
            msg = EmailMessage()
            msg["Subject"] = subject
            msg["From"] = FROM_EMAIL
            msg["To"] = to_email
            msg.content_subtype = "html"
            msg.add_alternative(body, subtype="html")
            # msg.set_content(body)
            with smtplib.SMTP_SSL(EMAIL_HOST, EMAIL_PORT) as smtp:
                smtp.login(EMAIL_HOST_USER, EMAIL_HOST_PASSWORD)
                smtp.send_message(msg)
        except Exception as e:
            print(f"Failed to send email to {to_email}: {str(e)}")

    # Tạo và chạy thread mới
    thread = threading.Thread(target=_send)
    thread.daemon = True  # Thread sẽ tự động kết thúc khi chương trình chính kết thúc
    thread.start()


def generate_code(n):
    digits = '0123456789'
    code = ''
    while n > 0:
        code += digits[math.floor(random.random() * 10)]
        n -= 1
        
    return code

def validate_otp(phone_number, otp):
    data = redis_utils.hgetall(phone_number, False)
    if not data:
        raise CustomException(content={"error": "Please get otp code before"}).bad_request_exception()
    now_timestamp = datetime.datetime.timestamp(datetime.datetime.now())
    expired_time = data.get(b'expired_time').decode()
    redis_otp = data.get(b'otp').decode()

    if now_timestamp > float(expired_time) or redis_otp != otp:
        raise CustomException(content={"error": "Your otp is incorrect/expired"}).bad_request_exception()
    redis_utils.delete_key(phone_number)

import random
import string

def generate_random_code(prefix: str, length: int) -> str:
    random_str = ''.join(random.choices(string.ascii_uppercase + string.digits, k=length))
    return f"{prefix}{random_str}"

def paginated_example(item_schema):
    # Lấy example đầu tiên từ schema item
    item_example = item_schema.model_config["json_schema_extra"]["examples"][0]
    return {
        "items": [item_example],
        "total": 1,
        "page": 1,
        "size": 10,
        "total_pages": 1
    }

def get_example(item_schema):
    # Lấy example đầu tiên từ schema item
    item_example = item_schema.model_config["json_schema_extra"]["examples"][0]
    return {
        "success": True,
        "status": 200,
        "message": "Get data successfully",
        "data": item_example
    }