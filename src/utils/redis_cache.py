
import redis
import os

class RedisUtils:
    def __init__(self):
        self.r = self.connection_redis()

    def connection_redis(self):
        return redis.Redis(
            host=os.environ.get('REDIS_HOST'),
            port=os.environ.get('REDIS_PORT'),
            password=os.environ.get('REDIS_PWD')
        )
    def set_value(self, key, args, expire_time=None):
        self.r.hmset(key, args)
        if expire_time:
            self.r.expire(key, expire_time)

    def delete_key(self, key):
        self.r.delete(key)

    def hgetall(self, key, is_deleted=False):
        data = self.r.hgetall(key)
        if is_deleted:
            self.r.delete(key)
        return data

    def check_key(self, key) -> int:
        return self.r.exists(key)


def connection_redis():
    pwd = 'my_redis_password'
    r = redis.Redis(
        host = 'redis-17710.c292.ap-southeast-1-1.ec2.redns.redis-cloud.com',
        port = 17710,
        password = '5Zj9nUfJnnJX4F7Cg1DcHYqqAqZKxFtZ'
    )
    return r

def set_value_redis(self, key, args):
    r = connection_redis()
    r.hmset(key, args)

def delete_key_redis(key):
    r = connection_redis()
    r.delete(key)

def hgetall(key, is_deleted):
    r = connection_redis()
    data = r.hgetall(key)
    if is_deleted:
        r.delete(key)
    return data
    
def set_expired_key(self, key, expired_time):
    r = connection_redis()
    r.expire(key, expired_time)

def check_key(self, key) -> int:
    r = connection_redis()
    num = r.exists(key)
    return num