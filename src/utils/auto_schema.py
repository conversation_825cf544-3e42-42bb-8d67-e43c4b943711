from pydantic import BaseModel

class AutoSchema(BaseModel):
    def __init_subclass__(cls, **kwargs):
        super().__init_subclass__(**kwargs)
        try:
            examples = {}
            for name, field in cls.model_fields.items():
                extra = getattr(field, 'json_schema_extra', None) or {}
                if 'example' in extra:
                    examples[name] = extra['example']
                elif field.default is not None:
                    examples[name] = field.default
            if examples:
                config = getattr(cls, 'model_config', {})
                json_extra = config.get('json_schema_extra', {})
                json_extra.setdefault('examples', [examples])
                config['json_schema_extra'] = json_extra
                cls.model_config = config
        except Exception:
            pass
