from jose import JW<PERSON><PERSON>r, jwt
from sqlalchemy.orm import Session
from src.user.service import get_user_by_user_name, get_token_in_db
from fastapi import Fast<PERSON><PERSON>, Request as FastAPIRequest
from src.utils.database import SessionLocal
import os
from src.utils.exceptions import CustomException
from datetime import datetime, timedelta, timezone
from starlette.middleware.base import BaseHTTPMiddleware


class BearerTokenMiddleware(BaseHTTPMiddleware):
    async def dispatch(self, request: FastAPIRequest, call_next):
        exc = CustomException(is_exception=False)
        request.state.db = SessionLocal()
        try:
            # Get token from request
            authorization: str = request.headers.get("Authorization")
            if authorization:
                scheme, _, token = authorization.partition(" ")

                # Check valid token
                if scheme.lower() != "bearer":
                    return exc.invalid_token_exception()

                try:
                    payload = jwt.decode(
                        token,
                        os.environ.get(
                            "SECRET_KEY",
                            "478fe93a5eb60c9a98ed8c09fd51b749b54ef5f2718982ed531c6818d707f96a",
                        ),
                        algorithms=[os.environ.get("ALGORITHM", "HS256")],
                    )

                    # Check token expire time
                    if not payload.get("exp") or (
                        payload.get("exp") and payload.get("exp") < datetime.now(timezone.utc).timestamp()
                    ):
                        return exc.expired_token_exception()

                    # Check token data exist user and activated user
                    user = get_user_by_user_name(request.state.db, payload.get("user_name"))
                    if not user:
                        return exc.invalid_token_exception()
                    elif not user.is_active:
                        return exc.deactivated_account_exception()
                    if not get_token_in_db(request.state.db, token):
                        return exc.invalid_token_exception()

                    # Store the user in the state of request
                    request.state.user = user
                    # request.state.scopes = payload.get('scopes', [])
                except JWTError:
                    return exc.invalid_token_exception()
            else:
                request.state.user = None
                # return exc.authorization_exception()

            response = await call_next(request)
            return response
        finally:
            request.state.db.close()
