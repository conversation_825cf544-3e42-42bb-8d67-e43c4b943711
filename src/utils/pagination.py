from fastapi.encoders import jsonable_encoder
from fastapi_pagination import Page, paginate
from sqlalchemy.orm import Session
from pydantic import BaseModel
from fastapi.responses import JSONResponse
from typing import Generic, Optional, TypeVar, List, Union, Type, Any
from fastapi import Query, Request, status

def count_objects(db: Session, model: Type) -> int:
    return db.query(model).count()

def get_objects(db: Session, model: Type, skip: int = 0, limit: int = 100) -> List:
    return db.query(model).offset(skip).limit(limit).all()


def convert_page_to_skip(page: int, size: int):
    return (page - 1) * size, size


def paginate_objects(db: Session, model: Type, page: int, size: int):
    skip, limit = convert_page_to_skip(page, size)
    objects = get_objects(db, model, skip=skip, limit=limit)
    
    # Đếm tổng số mục
    total_items = count_objects(db, model)
    
    # T<PERSON>h to<PERSON> các gi<PERSON> trị phân trang
    total_pages = (total_items + size - 1) // size
    next_page = page + 1 if page < total_pages else None
    previous_page = page - 1 if page > 1 else None
    
    return objects, total_items, total_pages, next_page, previous_page

def paginate_and_create_response(db: Session, model: Any, page: int, size: int, objects_dict) -> JSONResponse:
        skip = (page - 1) * size
        objects = get_objects(db, model, skip=skip, limit=size)
        
        total_items = count_objects(db, model)
        total_pages = (total_items + size - 1) // size
        
        next_page = page + 1 if page < total_pages else None
        previous_page = page - 1 if page > 1 else None
        
        response = {
            "success": True,
            "status": status.HTTP_200_OK,
            "message": f"Get page {page} {model.__name__} successfully",
            "total_items": total_items,
            "next_page": next_page,
            "previous_page": previous_page,
            "current_page": page,
            "total_pages": total_pages,
            "limit": size,
            "data": objects_dict
            
        }
        response = jsonable_encoder(response)
        return response

def get_parameters_of_list(
    page: int = Query(1, ge=1),
    limit: int = Query(10, le=100, ge=1),
    search: Optional[str] = Query(None, description="Search by some fields"),
    order_by: Optional[str] = Query(None, description="Order by field"),
    request: Request = Request,
    # **filter_fields: Optional[str]  # Define filter_fields as a catch-all parameter
):
    # """
    #     Defines filter fields when using to generate in documentation
    #     get_parameters_of_list(
    #         filter_field_1 = Query(None, description="Filter by field filter_field_1"),
    #         filter_field_2 = Query(None, description="Filter by field filter_field_2"),
    #     )
    # """
    common_params = {
        "page": page,
        "limit": limit,
        "skip": (page - 1) * limit if page > 1 else 0,
        "search": search,
        "order_by": order_by,
    }
    filter_fields = {key: None if value == 'null' else value for key, value in request.query_params.items() if key not in common_params}
    common_params['filter_fields'] = filter_fields
    return common_params

# Define a TypeVar to use for generic typing
T = TypeVar("T", bound=BaseModel)

class PaginationData(BaseModel,Generic[T]):
    success: bool = True
    status_code: int = 200
    message: str = "Get data successfully"
    total_items: int = 1
    total_pages: int = 1
    total_items_in_page: int = 1
    previous_page: Union[int, None] = None
    next_page: Union[int, None] = None
    current_page: int = 1
    limit: int = 10
    data: Union[List[T], List[str]]

    @classmethod
    def create_pagination(cls, data: Union[List[T], List[Any]], params:dict, total_items: int):
        page = params['page']
        limit = params['limit']
        total_pages = (total_items + limit - 1) // limit  # Calculate total pages
        previous_page = page - 1 if page > 1 else None
        next_page = page + 1 if page < total_pages else None
        
        if data:
            if not isinstance(data[0], BaseModel):
                start = (page - 1) * limit
                end = start + limit
                data = data[start:end]       

        return cls(
            total_items=total_items,
            total_pages=total_pages,
            total_items_in_page=len(data),
            previous_page=previous_page,
            next_page=next_page,
            current_page=page,
            limit=limit,
            data=data,
        )
