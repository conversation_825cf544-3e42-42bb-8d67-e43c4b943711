from sqlalchemy import <PERSON>olean, Column, DateTime, Integer
from sqlalchemy.ext.declarative import declarative_base
import datetime

Base = declarative_base()

class BaseModel():
    id = Column(Integer, primary_key=True, autoincrement=True, index=True)
    created_at = Column(DateTime,default=datetime.datetime.now(datetime.timezone.utc))
    updated_at = Column(DateTime,default=datetime.datetime.now(datetime.timezone.utc))
    is_deleted = Column(Boolean, default=False)