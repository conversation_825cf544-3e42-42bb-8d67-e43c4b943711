from typing import Any, Mapping, Union
from fastapi import HTTPException, status
from fastapi.encoders import jsonable_encoder
from fastapi.responses import JSONResponse
from starlette.background import BackgroundTask
from src.utils.base_model import BaseModel
from src.utils.enum import EnumResponseMessage

def get_format_response(
    status: int = 200,
    message: str = EnumResponseMessage.GET_DATA_SUCCESS,
    data={},
    is_exception: bool = False,
    headers: Any = {}
):
    content = {
        "success": True if status >= 200 and status < 300 else False,
        "status": status,
        "message": message,
        "data": jsonable_encoder(data)
    }
    if not is_exception:
        return JSONResponse(status_code=status, content=content, headers=headers)
    else:
        # <PERSON>hi trả về HTTPException, detail phải là content (bao gồm data)
        exc = HTTPException(status_code=status, detail=content, headers=headers)
        exc.detail = content  # Đảm bảo detail giữ nguyên content (có data)
        return exc

class CustomResponse():
    def __init__(self, content: Union[BaseModel,dict] = {}, status_code: int = 200, headers: Union[Mapping[str, str], None] = None) -> None:
        self.status_code = status_code
        self.content = content
        self.headers = headers

    def format_data_get(self):
        return get_format_response(status=status.HTTP_200_OK, message=EnumResponseMessage.GET_DATA_SUCCESS, data=self.content, headers=self.headers)

    def format_data_create(self):
        return get_format_response(status=status.HTTP_201_CREATED, message=EnumResponseMessage.CREATE_DATA_SUCCESS, data=self.content, headers=self.headers)

    def format_data_update(self):
        return get_format_response(status=status.HTTP_202_ACCEPTED, message=EnumResponseMessage.UPDATE_DATA_SUCCESS, data=self.content, headers=self.headers)

    def format_data_delete(self):
        return get_format_response(status=status.HTTP_202_ACCEPTED, message=EnumResponseMessage.DELETE_DATA_SUCCESS, data=self.content, headers=self.headers)
    
    def format_data_check_in_out(self):
        return get_format_response(status=status.HTTP_201_CREATED, message="Check Successfully", data=self.content, headers=self.headers)
    
    def format_data_export(self):
        return get_format_response(status=status.HTTP_200_OK, message="Export Successfully", data=self.content, headers=self.headers)
    
    def format_data_required_otp_auth(self):
        return get_format_response(status=status.HTTP_202_ACCEPTED, message="Check your email and get OTP to verify 2 Factor Authentication.", data=self.content, headers=self.headers)
    
    def format_check_data(self):
        return get_format_response(status=status.HTTP_200_OK, message="Verify successfully", data=self.content, headers=self.headers)


    
get_data_response = {
    'status_code':200,
    'response_description':'Get data successfully'
}

create_data_response = {
    'status_code':201,
    'response_description':'Create data successfully'
}

update_data_response = {
    'status_code':202,
    'response_description':'Update data successfully'
}

delete_data_response = {
    'status_code':202,
    'response_description':'Delete data successfully'
}

check_data_response = {
    'status_code':200,
    'response_description': 'Verify successfully'
}