from typing import List
from fastapi import Depends, Request
from fastapi.security import SecurityScopes
# from app.models.route import Scope
# from app.models.user import UserScope
# from src.employee.models import Employee
from src.utils.database import get_db
from src.utils.exceptions import CustomException
from src.user.models import User

async def check_security_scope(
    security_scopes: SecurityScopes, request: Request
):
    exc = CustomException()
    if not request.state.user:
        raise exc.authorization_exception()
    
    if request.state.user.is_admin:
        return request.state.user
    
    # user_scopes = request.state.db.query(Scope.name).join(UserScope).filter(UserScope.user_id == request.state.user.id).all()
    # for scope in security_scopes.scopes:
    #     if scope in user_scopes:
    #         return request.state.user
        
    raise exc.permission_denied_exception()

def has_scopes(scopes: List[str]):
    def scope_dependency(db = Depends(get_db)):
        return scopes
    return scope_dependency

async def is_admin(request: Request)-> User:
    exc = CustomException()
    if not request.state.user:
        raise exc.authorization_exception()
    
    if not request.state.user.is_admin:
        raise exc.permission_denied_exception()
    
    return request.state.user

async def is_authenticated(request: Request) -> User:
    if not request.state.user:
        raise CustomException().authorization_exception()
    
    return request.state.user

async def is_merchant(request: Request) -> User:
    if not request.state.user:
        raise CustomException().authorization_exception()
    
    if request.state.user.user_type != "MERCHANT":
        raise CustomException(content={"error": "Only MERCHANT can access this endpoint"}).permission_denied_exception()
    
    return request.state.user