from enum import _EnumDict, Enum
from typing import List
import enum


class EnumResponseMessage(str, Enum):
    # Status code 200-202
    GET_DATA_SUCCESS = 'GET_DATA_SUCCESS'
    CREATE_DATA_SUCCESS = 'CREATE_DATA_SUCCESS'
    UPDATE_DATA_SUCCESS = 'UPDATE_DATA_SUCCESS'
    DELETE_DATA_SUCCESS = 'DELETE_DATA_SUCCESS'
    # Status code 400
    BAD_REQUEST = 'BAD_REQUEST'
    # Status code 401
    EMAIL_OR_PASSWORD_IS_INCORRECT = 'EMAIL_OR_PASSWORD_IS_INCORRECT'
    ACCOUNT_DEACTIVATED = 'ACCOUNT_DEACTIVATED'
    AUTHORIZATION_NOT_PROVIDED = 'AUTHORIZATION_NOT_PROVIDED'
    INVALID_TOKEN = 'INVALID_TOKEN'
    TOKEN_EXPIRED = 'TOKEN_EXPIRED'
    INVALID_PORTAL = 'INVALID_PORTAL'
    INVALID_LEAVE_TYPE = 'INVALID_LEAVE_TYPE'
    INVALID_FULLDAY_TYPE = 'FULL DAY CAN NOT HAVE START TIME AND END TIME'
    INVALID_MORNING_TYPE = 'MORNING SHIFT CAN NOT HAVE 2 DAYS'
    INVALID_AFTERNOON_TYPE = 'AFTERNOON SHIFT CAN NOT HAVE 2 DAYS'
    INVALID_REQUEST_TYPE = 'INVALID REQUEST TYPE'
    NOT_NULL_LATE_TIME = 'LATE TIME NOT NULL'
    # Status code 404
    NOT_FOUND = 'NOT_FOUND'
    # Status code 403
    PERMISSION_DENIED = 'PERMISSION_DENIED'
class DonorType(str, Enum):
    ORGANIZATIONAL = "ORGANIZATIONAL"
    SPONSOR = "SPONSOR"


class Scopes(Enum):
    # Synchronized route and scopes
    SYNCHRONIZED_ROUTES_AND_SCOPES = ["Synchronized routes and scopes"]
    # User
    GET_USER_PROFILE = ["Get user profile"]
    GET_USER_LIST = ["Get user list"]
    GET_USER_DETAIL = ["Get user detail"]
    CREATE_STAFF = ["Create staff"]
    CREATE_ADMIN = ["Create admin"]
    # Product
    GET_PRODUCT_LIST = ["Get product list"]
    CREATE_PRODUCT = ["Create product"]
    GET_PRODUCT_DETAIL = ["Get product detail"]
    UPDATE_PRODUCT = ["Update product"]
    DELETE_PRODUCT = ["Delete product"]
    RESTORE_PRODUCT = ["Restore product"]
    # Category
    GET_CATEGORY_LIST = ["Get category list"]
    CREATE_CATEGORY = ["Create category"]
    GET_CATEGORY_DETAIL = ["Get category detail"]
    UPDATE_CATEGORY = ["Update category"]
    DELETE_CATEGORY = ["Delete category"]
    RESTORE_CATEGORY = ["Restore category"]
    # Scope
    GET_SCOPE_LIST = ["Get scope list"]
    ASSIGN_SCOPES = ["Assign scopes"]
    GET_SCOPE_DETAIL = ["Get scope detail"]
    # Route
    GET_ROUTE_LIST = ["Get route list"]
    GET_ROUTE_DETAIL = ["Get route detail"]


class Gender(str, Enum):
    MALE = 'MALE'
    FEMALE = 'FEMALE'
    OTHER = 'OTHER'


class Status(Enum):
    PENDING = 'PENDING'
    APPROVED = 'APPROVED'
    REJECTED = 'REJECTED'


class CheckType(Enum):
    CHECKIN = 'CHECKIN'
    CHECKOUT = 'CHECKOUT'
    GO_OUT = 'GO_OUT'


class ExpirationUnit(str, Enum):
    HOURS = 'HOURS'
    MINUTES = 'MINUTES'
    DAYS = 'DAY'


class SalaryType(str, Enum):
    HOURS = 'HOURS'
    DAY = 'DAY'
    MONTH = 'MONTH'


class Currency(str, Enum):
    VND = 'VND'
    USD = 'USD'


class Portal(str, Enum):
    ADMIN = 'ADMIN'
    USER = 'USER'


class FormatDateTime(str, Enum):
    NORMAL = "%d/%m/%Y-%H:%M:%S"
    DATE = "%d/%m/%Y"


class SalaryReason(str, Enum):
    INITIAL = 'INITIAL'
    INCREASE = "INCREASE"
    REDUCE = "REDUCE"

class TypeFee(str, Enum):
    TAX ='TAX'
    INSURANCE = 'INSURANCE'
    OVERTIME = 'OVERTIME'
    OTHER = 'OTHER'

class LeaveType(str, Enum):
    MORNING = 'MORNING'
    AFTERNOON = 'AFTERNOON'
    FULLDAY = 'FULLDAY'


class RequestType(str, Enum):
    LEAVE = 'LEAVE'
    WORK_LATE = 'WORK LATE'
    REMOTE = 'REMOTE'


class Reason(str, Enum):
    FAMILY_EMERGENCY = 'FAMILY EMERGENCY'
    TRAFFIC_ACCIDENT = 'TRAFFIC ACCIDENT'
    ILLNESS = 'ILLNESS'
    HOMETOWN = 'HOMETOWN'
    OTHER = 'OTHER'


class FolderNameS3(str, Enum):
    AVATAR = 'images/avatar'
    EXCEL = 'xlsx'
    LEAVE_REQUEST = 'images/leave_request'
    NEWS = 'images/news'





class EntityType(str, Enum):
    EMPLOYEE = 'EMPLOYEE'
    LEAVE_REQUEST = 'LEAVE_REQUEST'
    NEWS = 'NEWS'


class FileType(str, Enum):
    IMAGE = 'IMAGE'
    DOCUMENT = 'DOCUMENT'


class Status_OT_Request(str, Enum):
    ACTIVE = 'ACTIVE'
    DEACTIVE = 'DEACTIVE'


class WeekDayEnum(Enum):
    MONDAY = 0
    TUESDAY = 1
    WEDNESDAY = 2
    THURSDAY = 3
    FRIDAY = 4
    SATURDAY = 5
    SUNDAY = 6


class LanguageEnum(str, Enum):
    VI = "VI"
    EN = "EN"


# Dictionary ánh xạ mã ngôn ngữ sang tên ngôn ngữ
language_names = {
    'vi': "Tiếng Việt",
    'en': "English",
}

# Hàm để lấy tên ngôn ngữ từ mã ngôn ngữ


def get_language_name(language_code):
    return language_names.get(language_code, "Unknown")

class QuestionType(str, Enum):
    SINGLE_CHOICE = 'SINGLE_CHOICE'
    MULTIPLE_CHOICE = 'MULTIPLE_CHOICE'
    INPUT_TEXT = 'INPUT_TEXT'

class TicketDraftType(str, Enum):
    SINGLE = 'SINGLE'
    GROUP = 'GROUP'

class TicketStatusEnum(str, Enum):
    PENDING = 'PENDING'
    SCHEDULED = 'SCHEDULED'
    COMPLETED = 'COMPLETED'
    CANCELLED = 'CANCELLED'

class ContactRoleEnum(str, Enum):
    FRIEND = "FRIEND"
    FAMILY = "FAMILY"
    COLLEAGUE = "COLLEAGUE"
    BUSINESS = "BUSINESS"
    OTHER = "OTHER"

class ContactStatusEnum(str, Enum):
    ACTIVE = "ACTIVE"
    INACTIVE = "INACTIVE"
    BLOCKED = "BLOCKED"

class EventTypeEnum(str, Enum):
    INPERSON = "INPERSON"
    VIRTUAL = "VIRTUAL"
    OTHER = "OTHER"

class EventStatusEnum(str, Enum):
    UPCOMING = "UPCOMING"
    SCHEDULED = "SCHEDULED"
    IN_PROGRESS = "IN_PROGRESS"
    COMPLETED = "COMPLETED"
    CANCELLED = "CANCELLED"
    POSTPONED = "POSTPONED"

class EventAccessEnum(str, Enum):
    FREE = "FREE"
    TICKETED = "TICKETED"
class EntityTypeEnum(str, Enum):
    USER = "user"
    CONTACT = "contact"
    LISTING = "listing"

class ListingTypeEnum(str, Enum):
    SALE = "SALE"
    RENT = "RENT"
    SERVICE = "SERVICE"
    JOB = "JOB"
    OTHER = "OTHER"

class ListingStatusEnum(str, Enum):
    DRAFT = "DRAFT"
    PUBLISHED = "PUBLISHED"
    SOLD = "SOLD"
    CANCELLED = "CANCELLED"
    EXPIRED = "EXPIRED"

class GiftStatusEnum(str, Enum):
    PENDING = "PENDING"
    SENT = "SENT"
    DELIVERED = "DELIVERED"
    REDEEMED = "REDEEMED"
    CANCELLED = "CANCELLED"
    EXPIRED = "EXPIRED"
    ACCEPTED = "ACCEPTED"
    REJECTED = "REJECTED"
class GiftTypeEnum(str, Enum):
    """Type of gift that can be sent between users."""
    TICKET = "TICKET"
    PRODUCT = "PRODUCT"

class ProductStatusEnum(str, Enum):
    ACTIVE = "ACTIVE"
    INACTIVE = "INACTIVE"

class PaymentMethodEnum(str, Enum):
    CASH = "CASH"
    BANK_TRANSFER = "BANK_TRANSFER"
    CREDIT_CARD = "CREDIT_CARD"
   

class ShippingMethodEnum(str, Enum):
    STANDARD = "STANDARD"
    EXPRESS = "EXPRESS"
    SAME_DAY = "SAME_DAY"

class OrderStatusEnum(str, Enum):
    PENDING = "PENDING"
    PAID = "PAID"
    SHIPPING = "SHIPPING"
    DELIVERED = "DELIVERED"
    CANCELLED = "CANCELLED"

class PaymentStatusEnum(str, Enum):
    UNPAID = "UNPAID"
    PAID = "PAID"
    REFUNDED = "REFUNDED"

class ConnectionTagEnum(str, Enum):
    INVESTOR = "INVESTOR"
    VENDOR = "VENDOR"
    ADVISOR = "ADVISOR"
    FRIEND = "FRIEND"
    OTHER = "OTHER"

class UserInteractionTypeEnum(str, Enum):
    SKIP = "SKIP"  # Bỏ qua user này
    SAVE_FOR_LATER = "SAVE_FOR_LATER"  # Lưu để xem sau
    BLOCK = "BLOCK"  # Chặn user này
    REPORT = "REPORT"  # Báo cáo user này

class RSVPStatusEnum(str, Enum):
    PENDING = "PENDING"  # Chưa phản hồi
    ACCEPTED = "ACCEPTED"  # Đã chấp nhận
    DECLINED = "DECLINED"  # Đã từ chối
    MAYBE = "MAYBE"  # Có thể tham gia
    NOT_RESPONDED = "NOT_RESPONDED"  # Chưa được mời

class EventInvitationEnum(str, Enum):
    INVITE_ONLY = "INVITE_ONLY"  # chỉ mời
    INVITE_WITH_TICKET = "INVITE_WITH_TICKET"  # mời kèm vé

class UserRoleEnum(str, Enum):
    ADMIN = "ADMIN"
    USER = "USER"
    MERCHANT = "MERCHANT"
class OTPTypeEnum(str, Enum):
    USER_VERIFICATION = "USER_VERIFICATION"
    FORGOT_PASSWORD = "FORGOT_PASSWORD"
    CHANGE_EMAIL = "CHANGE_EMAIL"
    CHANGE_PHONE = "CHANGE_PHONE"
    

class AvailabilityStatusEnum(str, Enum):
    AVAILABLE = "AVAILABLE"
    BUSY = "BUSY"
    DO_NOT_DISTURB = "DO_NOT_DISTURB"
    OFFLINE = "OFFLINE"
    CUSTOM_MESSAGE = "CUSTOM_MESSAGE"

class AvailabilityTagsEnum(str, Enum):
    COFFEE = "COFFEE"
    BRUNCH = "BRUNCH"
    DINNER = "DINNER"
    EVENT = "EVENT"
    CHAT = "CHAT"

class IndustryTagsEnum(str, Enum):
    ADVERTISING_MARKETING = "ADVERTISING & MARKETING"
    AGRICULTURE = "AGRICULTURE"
    ARCHITECTURE_DESIGN = "ARCHITECTURE & DESIGN"
    ARTS_ENTERTAINMENT = "ARTS & ENTERTAINMENT"
    AUTOMOTIVE = "AUTOMOTIVE"
    BEAUTY_WELLNESS = "BEAUTY & WELLNESS"
    CONSTRUCTION = "CONSTRUCTION"
    CONSULTING = "CONSULTING"
    CONSUMER_GOODS = "CONSUMER GOODS"
    E_COMMERCE = "E-COMMERCE"
    EDUCATION_TRAINING = "EDUCATION & TRAINING"
    ENERGY_UTILITIES = "ENERGY & UTILITIES"
    ENGINEERING = "ENGINEERING"
    EVENTS_HOSPITALITY = "EVENTS & HOSPITALITY"
    FINANCE_BANKING = "FINANCE & BANKING"
    FOOD_BEVERAGE = "FOOD & BEVERAGE"
    GOVERNMENT_PUBLIC_SECTOR = "GOVERNMENT & PUBLIC SECTOR"
    HEALTHCARE_MEDICAL = "HEALTHCARE & MEDICAL"
    HR_RECRUITMENT = "HR & RECRUITMENT"
    INFORMATION_TECHNOLOGY = "INFORMATION TECHNOLOGY"
    INSURANCE = "INSURANCE"
    LEGAL_SERVICES = "LEGAL SERVICES"
    LOGISTICS_SUPPLY_CHAIN = "LOGISTICS & SUPPLY CHAIN"
    MANUFACTURING = "MANUFACTURING"
    MEDIA_PUBLISHING = "MEDIA & PUBLISHING"
    NONPROFIT_NGOs = "NONPROFIT & NGOs"
    REAL_ESTATE = "REAL ESTATE"
    RETAIL_WHOLESALE = "RETAIL & WHOLESALE"
    SCIENCE_RESEARCH = "SCIENCE & RESEARCH"
    SOFTWARE_SAAS = "SOFTWARE & SaaS"
    SPORTS_FITNESS = "SPORTS & FITNESS"
    TELECOMMUNICATIONS = "TELECOMMUNICATIONS"
    TOURISM_TRAVEL = "TOURISM & TRAVEL"
    TRANSPORTATION = "TRANSPORTATION"
    VENTURE_CAPITAL_STARTUPS = "VENTURE CAPITAL & STARTUPS"
    WEB3_BLOCKCHAIN = "WEB3 / BLOCKCHAIN"
    OTHER = "OTHER"

class ProductTypeEnum(str, Enum):
    PRODUCT = "PRODUCT"
    SERVICE = "SERVICE"
    OTHER = "OTHER"

class AvailabilityDayStatusEnum(str, Enum):
    ACTIVE = "ACTIVE"
    BUSY = "BUSY"

class AvailabilitySmartTagsEnum(str, Enum):
    COFFEE = "COFFEE"
    BRUNCH = "BRUNCH"
    DINNER = "DINNER"
    EVENT = "EVENT"
    CHAT = "CHAT"

class DayInWeekEnum(str, Enum):
    MON = "MON"
    TUE = "TUE"
    WED = "WED"
    THU = "THU"
    FRI = "FRI"
    SAT = "SAT"
    SUN = "SUN"