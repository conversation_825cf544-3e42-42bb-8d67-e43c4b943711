from typing import Any, Dict, Union
from fastapi import HTTPException, status
from src.utils.enum import EnumResponseMessage
from src.utils.response import get_format_response

class CustomException(HTTPException):
    def __init__(self, status_code: int = 400, content: Any = {}, headers: Union[Dict[str, str], None] = None, is_exception:bool = True) -> None:
        self.status_code = status_code
        self.content = content
        self.headers = headers
        self.is_exception = is_exception
        super().__init__(status_code, content, headers)

    def credentials_exception(self):
        return get_format_response(status=status.HTTP_401_UNAUTHORIZED, message=EnumResponseMessage.EMAIL_OR_PASSWORD_IS_INCORRECT,is_exception=self.is_exception,data=self.content,headers=self.headers)
    
    def deactivated_account_exception(self):
        return get_format_response(status=status.HTTP_401_UNAUTHORIZED, message=EnumResponseMessage.ACCOUNT_DEACTIVATED,is_exception=self.is_exception,data=self.content,headers=self.headers)
    
    def authorization_exception(self):
        return get_format_response(status=status.HTTP_401_UNAUTHORIZED, message=EnumResponseMessage.AUTHORIZATION_NOT_PROVIDED,is_exception=self.is_exception,data=self.content,headers=self.headers)
    
    def invalid_token_exception(self):
        return get_format_response(status=status.HTTP_401_UNAUTHORIZED, message=EnumResponseMessage.INVALID_TOKEN,is_exception=self.is_exception,data=self.content,headers=self.headers)
    
    def expired_token_exception(self):
        return get_format_response(status=status.HTTP_401_UNAUTHORIZED, message=EnumResponseMessage.TOKEN_EXPIRED,is_exception=self.is_exception,data=self.content,headers=self.headers)
    
    def not_found_exception(self):
        return get_format_response(status=status.HTTP_404_NOT_FOUND, message=EnumResponseMessage.NOT_FOUND,is_exception=self.is_exception,data=self.content,headers=self.headers)
    
    def permission_denied_exception(self):
        return get_format_response(status=status.HTTP_403_FORBIDDEN, message=EnumResponseMessage.PERMISSION_DENIED,is_exception=self.is_exception,data=self.content,headers=self.headers)
    
    def bad_request_exception(self):
        return get_format_response(status=status.HTTP_400_BAD_REQUEST, message=EnumResponseMessage.BAD_REQUEST,is_exception=self.is_exception,data=self.content,headers=self.headers)
    
    def invalid_portal_exception(self):
        return get_format_response(status=status.HTTP_400_BAD_REQUEST, message=EnumResponseMessage.INVALID_PORTAL,is_exception=self.is_exception,data=self.content,headers=self.headers)
    
    def invalid_leave_type_exception(self):
        return get_format_response(status=status.HTTP_400_BAD_REQUEST, message=EnumResponseMessage.INVALID_LEAVE_TYPE,is_exception=self.is_exception,data=self.content,headers=self.headers)
    
    def invalid_leave_fullday_exception(self):
        return get_format_response(status=status.HTTP_400_BAD_REQUEST, message=EnumResponseMessage.INVALID_FULLDAY_TYPE,is_exception=self.is_exception,data=self.content,headers=self.headers)
    
    def invalid_leave_morning_exception(self):
        return get_format_response(status=status.HTTP_400_BAD_REQUEST, message=EnumResponseMessage.INVALID_MORNING_TYPE,is_exception=self.is_exception,data=self.content,headers=self.headers)
    
    def invalid_leave_afternoon_exception(self):
        return get_format_response(status=status.HTTP_400_BAD_REQUEST, message=EnumResponseMessage.INVALID_AFTERNOON_TYPE,is_exception=self.is_exception,data=self.content,headers=self.headers)
    
    def invalid_leave_work_late_day_exception(self):
        return get_format_response(status=status.HTTP_400_BAD_REQUEST, message=EnumResponseMessage.INVALID_AFTERNOON_TYPE,is_exception=self.is_exception,data=self.content,headers=self.headers)
    
    def not_null_leave_work_late_time_exception(self):
        return get_format_response(status=status.HTTP_400_BAD_REQUEST, message=EnumResponseMessage.NOT_NULL_LATE_TIME,is_exception=self.is_exception,data=self.content,headers=self.headers)
    
    def invalid_request_type_exception(self):
        return get_format_response(status=status.HTTP_400_BAD_REQUEST, message=EnumResponseMessage.INVALID_REQUEST_TYPE,is_exception=self.is_exception,data=self.content,headers=self.headers)

    def api_exception(self):
        return get_format_response(status=status.HTTP_500_INTERNAL_SERVER_ERROR, message=EnumResponseMessage.NOT_FOUND, is_exception=self.is_exception, data=self.content, headers=self.headers)