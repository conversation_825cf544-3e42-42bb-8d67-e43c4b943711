from sqlalchemy import <PERSON>umn, Integer, ForeignKey, Text, UniqueConstraint
from sqlalchemy.orm import relationship

from src.utils.base_model import BaseModel, Base


class Lead(BaseModel, Base):
    __tablename__ = "lead"

    user_id = Column(Integer, Foreign<PERSON>ey("user.id"), nullable=False)
    contact_id = Column(Integer, ForeignKey("contact.id"), nullable=False)
    notes = Column(Text, nullable=True)

    user = relationship("User", foreign_keys=[user_id], back_populates="leads")
    contact = relationship("Contact", back_populates="leads")

    __table_args__ = (
        UniqueConstraint('user_id', 'contact_id', name='uq_user_contact_lead'),
    )
