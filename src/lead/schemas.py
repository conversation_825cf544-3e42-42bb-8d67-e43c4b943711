from datetime import datetime
from typing import Optional, Dict, Any, List

from pydantic import Field, BaseModel
from src.utils.schemas import PaginatedResponse


class LeadCreateSchemas(BaseModel):
    contact_id: int = Field(..., description="Contact ID to convert to lead")
    notes: Optional[str] = Field(None, example="Potential big client")


class LeadBulkCreateSchemas(BaseModel):
    contact_ids: List[int] = Field(..., description="List of contact IDs to convert to leads")
    notes: Optional[str] = Field(None, example="Bulk conversion notes")


class LeadUpdateSchemas(BaseModel):
    notes: Optional[str] = Field(None, example="Updated note")


class LeadNoteCreateSchemas(BaseModel):
    content: str = Field(..., description="Note content", max_length=500)


class LeadReferSchemas(BaseModel):
    target_contact_id: int = Field(..., description="Contact ID to refer this lead to")
    message: Optional[str] = Field(None, description="Referral message")


class LeadResponseSchemas(BaseModel):
    id: int
    user_id: int
    contact_id: int
    notes: Optional[str]
    created_at: datetime
    updated_at: datetime
    is_deleted: bool
    contact: Optional[Dict[str, Any]] = None
    last_interaction: Optional[str] = None
    lead_status: Optional[str] = "NEW"

    class Config:
        from_attributes = True


class AvailableContactResponse(BaseModel):
    id: int
    contact_id: int
    contact_name: str
    contact_avatar: Optional[str] = None
    contact_email: Optional[str] = None
    is_already_lead: bool = False

    class Config:
        from_attributes = True

PaginatedAvailableContactResponse = PaginatedResponse[AvailableContactResponse]
