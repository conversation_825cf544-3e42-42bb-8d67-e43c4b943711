from typing import Optional, List
from sqlalchemy.orm import Session, joinedload
import json
from datetime import datetime, timedelta

from src.contact.models import Contact
from src.user.models import User, UserProfile, BusinessProfile
from src.utils.exceptions import CustomException
from src.utils.schemas import PaginatedResponse

from .models import Lead
from .schemas import (
    LeadCreateSchemas, LeadUpdateSchemas, LeadResponseSchemas,
    LeadBulkCreateSchemas, LeadNoteCreateSchemas, LeadReferSchemas,
    AvailableContactResponse, PaginatedAvailableContactResponse
)


PaginatedLeadResponse = PaginatedResponse[LeadResponseSchemas]


def _parse_tags_safely(tags_str):
    """Parse tags JSON string to list safely"""
    if not tags_str:
        return None
    try:
        return json.loads(tags_str)
    except (json.JSONDecodeError, TypeError):
        return None


def _convert_profile_to_dict(profile):
    """Convert UserProfile object to dict safely"""
    if not profile:
        return None
    
    return {
        "full_name": getattr(profile, 'full_name', None),
        "gender": getattr(profile, 'gender', None),
        "address": getattr(profile, 'address', None),
        "date_of_birth": getattr(profile, 'date_of_birth', None),
        "title": getattr(profile, 'title', None),
        "bio": getattr(profile, 'bio', None),
        "notes": getattr(profile, 'notes', None),
        "visibility": "PUBLIC" if getattr(profile, 'public_profile', False) else "PRIVATE",
        "latitude": getattr(profile, 'lattitude', None),
        "longitude": getattr(profile, 'longitude', None),
        "public_profile": getattr(profile, 'public_profile', False),
        "social_media_links": getattr(profile, 'social_media_links', None),
        "industry": getattr(profile, 'industry_tags', None),
    }


def _convert_business_profile_to_dict(business_profile):
    """Convert BusinessProfile object to dict safely"""
    if not business_profile:
        return None
    
    return {
        "title": getattr(business_profile, 'business_name', None),
        "company": getattr(business_profile, 'business_name', None),
        "company_address": getattr(business_profile, 'business_address', None),
        "company_description": getattr(business_profile, 'business_description', None),
        "industry_tags": getattr(business_profile, 'industry_tags', None),
    }


def _get_last_interaction(lead):
    # TODO: Thực tế nên lấy từ message/gift/event, tạm thời trả về placeholder
    if lead.updated_at:
        return f"Last updated: {lead.updated_at.strftime('%Y-%m-%d')}"
    return "No recent interaction"


def create_lead_service(db: Session, user_id: int, data: LeadCreateSchemas) -> Lead:
    try:
        contact = db.query(Contact).filter(
            Contact.id == data.contact_id,
            Contact.user_id == user_id,
            Contact.is_deleted == False,
        ).first()
        if not contact:
            raise CustomException(content={"error": ["Contact not found"]}).not_found_exception()

        # Check if lead already exists (including soft deleted ones)
        existing_lead = db.query(Lead).filter(
            Lead.user_id == user_id,
            Lead.contact_id == data.contact_id,
        ).first()
        
        if existing_lead:
            if not existing_lead.is_deleted:
                raise CustomException(content={"error": ["Already a lead"]}).bad_request_exception()
            else:
                # Restore soft deleted lead
                existing_lead.is_deleted = False
                existing_lead.notes = data.notes
                existing_lead.updated_at = datetime.now()
                db.add(existing_lead)
                db.commit()
                db.refresh(existing_lead)
                return existing_lead

        # Create new lead
        lead = Lead(user_id=user_id, contact_id=data.contact_id, notes=data.notes)
        db.add(lead)
        db.commit()
        db.refresh(lead)
        return lead
        
    except Exception as e:
        db.rollback()
        raise CustomException(content={"error": ["Failed to create lead"]}).bad_request_exception()


def bulk_create_leads_service(db: Session, user_id: int, data: LeadBulkCreateSchemas) -> List[Lead]:
    """Bulk create leads from multiple contacts"""
    try:
        leads = []
        
        for contact_id in data.contact_ids:
            # Check if contact exists
            contact = db.query(Contact).filter(
                Contact.id == contact_id,
                Contact.user_id == user_id,
                Contact.is_deleted == False,
            ).first()
            
            if not contact:
                continue  # Skip if contact not found
            
            # Check if lead already exists (including soft deleted ones)
            existing_lead = db.query(Lead).filter(
                Lead.user_id == user_id,
                Lead.contact_id == contact_id,
            ).first()
            
            if existing_lead:
                if not existing_lead.is_deleted:
                    continue  # Skip if already a lead
                else:
                    # Restore soft deleted lead
                    existing_lead.is_deleted = False
                    if data.notes:
                        existing_lead.notes = data.notes
                    existing_lead.updated_at = datetime.now()
                    db.add(existing_lead)
                    leads.append(existing_lead)
                    continue
            
            # Create new lead
            lead = Lead(
                user_id=user_id, 
                contact_id=contact_id, 
                notes=data.notes
            )
            leads.append(lead)
        
        if leads:
            # Add new leads to session
            new_leads = [lead for lead in leads if not hasattr(lead, 'id') or lead.id is None]
            if new_leads:
                db.add_all(new_leads)
            
            db.commit()
            for lead in leads:
                db.refresh(lead)
        
        return leads
        
    except Exception as e:
        db.rollback()
        raise CustomException(content={"error": ["Failed to bulk create leads"]}).bad_request_exception()


def get_available_contacts_service(
    db: Session, 
    user_id: int, 
    page: int = 1, 
    size: int = 10,
    search: Optional[str] = None
) -> PaginatedAvailableContactResponse:
    """Get contacts that can be converted to leads (AC3)"""
    if page < 1:
        page = 1
    if size < 1:
        size = 10
    if size > 100:
        size = 100
    skip = (page - 1) * size

    # Get all contacts for user
    query = db.query(Contact).options(
        joinedload(Contact.user_contact).joinedload(User.profile)
    ).filter(
        Contact.user_id == user_id,
        Contact.is_deleted == False
    )
    
    # Apply search filter
    if search:
        from sqlalchemy import or_
        query = query.join(UserProfile, Contact.user_contact_id == UserProfile.user_id).filter(
            or_(
                UserProfile.full_name.ilike(f"%{search}%"),
                Contact.user_contact.has(User.email.ilike(f"%{search}%"))
            )
        )
    
    total = query.count()
    total_pages = (total + size - 1) // size
    contacts = query.offset(skip).limit(size).all()
    
    # Get existing lead contact IDs
    existing_lead_contact_ids = set(
        db.query(Lead.contact_id).filter(
            Lead.user_id == user_id,
            Lead.is_deleted == False
        ).all()
    )
    existing_lead_contact_ids = {item[0] for item in existing_lead_contact_ids}
    
    # Convert to response
    items = []
    for contact in contacts:
        contact_data = {
            "id": contact.id,
            "contact_id": contact.id,
            "contact_name": contact.user_contact.profile.full_name if contact.user_contact and contact.user_contact.profile else "Unknown",
            "contact_avatar": contact.user_contact.avatar if contact.user_contact else None,
            "contact_email": contact.user_contact.email if contact.user_contact else None,
            "is_already_lead": contact.id in existing_lead_contact_ids
        }
        items.append(AvailableContactResponse.model_validate(contact_data))
    
    return PaginatedAvailableContactResponse(
        items=items, 
        total=total, 
        page=page, 
        size=size, 
        total_pages=total_pages
    )


def refer_lead_service(db: Session, lead_id: int, user_id: int, data: LeadReferSchemas) -> bool:
    """Refer a lead to another contact (AC5)"""
    lead = db.query(Lead).filter(
        Lead.id == lead_id,
        Lead.user_id == user_id,
        Lead.is_deleted == False,
    ).first()
    if not lead:
        raise CustomException(content={"error": ["Lead not found"]}).not_found_exception()
    # TODO: Implement referral logic
    return True


def add_lead_note_service(db: Session, lead_id: int, user_id: int, data: LeadNoteCreateSchemas) -> bool:
    """Add note to lead (AC6)"""
    lead = db.query(Lead).filter(
        Lead.id == lead_id,
        Lead.user_id == user_id,
        Lead.is_deleted == False,
    ).first()
    if not lead:
        raise CustomException(content={"error": ["Lead not found"]}).not_found_exception()
    current_notes = lead.notes or ""
    new_notes = f"{current_notes}\n\n{datetime.now().strftime('%Y-%m-%d %H:%M')}: {data.content}"
    lead.notes = new_notes.strip()
    lead.updated_at = datetime.now()
    db.add(lead)
    db.commit()
    return True


def get_lead_service(db: Session, lead_id: int, user_id: int) -> Optional[Lead]:
    return db.query(Lead).filter(
        Lead.id == lead_id,
        Lead.user_id == user_id,
        Lead.is_deleted == False,
    ).first()


def get_lead_detail_service(db: Session, lead_id: int, user_id: int) -> Optional[LeadResponseSchemas]:
    lead = db.query(Lead).options(
        joinedload(Lead.contact).joinedload(Contact.user_contact).joinedload(User.profile),
        joinedload(Lead.contact).joinedload(Contact.user_contact).joinedload(User.business_profile)
    ).filter(
        Lead.id == lead_id,
        Lead.user_id == user_id,
        Lead.is_deleted == False,
    ).first()
    
    if not lead:
        return None
    
    lead_data = {
        "id": lead.id,
        "user_id": lead.user_id,
        "contact_id": lead.contact_id,
        "notes": lead.notes,
        "created_at": lead.created_at,
        "updated_at": lead.updated_at,
        "is_deleted": lead.is_deleted,
        "contact": {
            "id": lead.contact.id,
            "user_id": lead.contact.user_id,
            "user_contact_id": lead.contact.user_contact_id,
            "nickname": lead.contact.nickname,
            "is_favorite": lead.contact.is_favorite,
            "notes": lead.contact.notes,
            "tags": _parse_tags_safely(lead.contact.tags) if lead.contact.tags else None,
            "last_contact_date": lead.contact.last_contact_date,
            "created_at": lead.contact.created_at,
            "updated_at": lead.contact.updated_at,
            "is_deleted": lead.contact.is_deleted,
            "user_contact": {
                "id": lead.contact.user_contact.id,
                "email": lead.contact.user_contact.email,
                "user_type": lead.contact.user_contact.user_type,
                "avatar": lead.contact.user_contact.avatar,
            } if lead.contact.user_contact else None,
            "profile": _convert_profile_to_dict(lead.contact.user_contact.profile if lead.contact.user_contact else None),
            "business_profile": _convert_business_profile_to_dict(lead.contact.user_contact.business_profile if lead.contact.user_contact else None),
        } if lead.contact else None,
        "last_interaction": _get_last_interaction(lead),
        "lead_status": "NEW"
    }
    return LeadResponseSchemas.model_validate(lead_data)


def get_leads_service(db: Session, user_id: int, page: int = 1, size: int = 10) -> PaginatedLeadResponse:
    if page < 1:
        page = 1
    if size < 1:
        size = 10
    if size > 100:
        size = 100
    skip = (page - 1) * size

    # Load leads with contact information
    query = db.query(Lead).options(
        joinedload(Lead.contact).joinedload(Contact.user_contact).joinedload(User.profile),
        joinedload(Lead.contact).joinedload(Contact.user_contact).joinedload(User.business_profile)
    ).filter(
        Lead.user_id == user_id, 
        Lead.is_deleted == False
    )
    
    total = query.count()
    total_pages = (total + size - 1) // size
    leads = query.offset(skip).limit(size).all()
    
    # Convert to response with contact details
    items = []
    for lead in leads:
        lead_data = {
            "id": lead.id,
            "user_id": lead.user_id,
            "contact_id": lead.contact_id,
            "notes": lead.notes,
            "created_at": lead.created_at,
            "updated_at": lead.updated_at,
            "is_deleted": lead.is_deleted,
            "contact": {
                "id": lead.contact.id,
                "user_id": lead.contact.user_id,
                "user_contact_id": lead.contact.user_contact_id,
                "nickname": lead.contact.nickname,
                "is_favorite": lead.contact.is_favorite,
                "notes": lead.contact.notes,
                "tags": _parse_tags_safely(lead.contact.tags) if lead.contact.tags else None,
                "last_contact_date": lead.contact.last_contact_date,
                "created_at": lead.contact.created_at,
                "updated_at": lead.contact.updated_at,
                "is_deleted": lead.contact.is_deleted,
                "user_contact": {
                    "id": lead.contact.user_contact.id,
                    "email": lead.contact.user_contact.email,
                    "user_type": lead.contact.user_contact.user_type,
                    "avatar": lead.contact.user_contact.avatar,
                } if lead.contact.user_contact else None,
                "profile": _convert_profile_to_dict(lead.contact.user_contact.profile if lead.contact.user_contact else None),
                "business_profile": _convert_business_profile_to_dict(lead.contact.user_contact.business_profile if lead.contact.user_contact else None),
            } if lead.contact else None,
            "last_interaction": _get_last_interaction(lead),
            "lead_status": "NEW"
        }
        items.append(LeadResponseSchemas.model_validate(lead_data))
    
    return PaginatedLeadResponse(items=items, total=total, page=page, size=size, total_pages=total_pages)


def update_lead_service(db: Session, lead_id: int, user_id: int, data: LeadUpdateSchemas) -> Optional[Lead]:
    lead = get_lead_service(db, lead_id, user_id)
    if not lead:
        return None
    if data.notes is not None:
        lead.notes = data.notes
    db.add(lead)
    db.commit()
    db.refresh(lead)
    return lead


def remove_lead_service(db: Session, lead_id: int, user_id: int) -> bool:
    lead = get_lead_service(db, lead_id, user_id)
    if not lead:
        return False
    lead.is_deleted = True
    db.add(lead)
    db.commit()
    return True
