from fastapi import APIRouter, Depends, Query
from sqlalchemy.orm import Session

from src.utils.database import get_db
from src.utils.permission import is_authenticated
from src.utils.token_docs import jwt_auth
from src.utils.response import CustomResponse
from src.user.models import User as UserModel
from src.utils.exceptions import CustomException

from .schemas import (
    LeadCreateSchemas, LeadUpdateSchemas, LeadBulkCreateSchemas,
    LeadNoteCreateSchemas, LeadReferSchemas
)
from .service import (
    create_lead_service,
    bulk_create_leads_service,
    get_lead_service,
    get_lead_detail_service,
    get_leads_service,
    get_available_contacts_service,
    update_lead_service,
    remove_lead_service,
    refer_lead_service,
    add_lead_note_service,
)

router = APIRouter()
tags = ["Lead"]


@router.post("/create", dependencies=[Depends(jwt_auth)], tags=tags)
def create_lead(
    lead_data: LeadCreateSchemas,
    current_user: UserModel = Depends(is_authenticated),
    db: Session = Depends(get_db),
):
    """Create a single lead from contact"""
    result = create_lead_service(db, current_user.id, lead_data)
    return CustomResponse(content=result).format_data_create()


@router.post("/bulk-create", dependencies=[Depends(jwt_auth)], tags=tags)
def bulk_create_leads(
    lead_data: LeadBulkCreateSchemas,
    current_user: UserModel = Depends(is_authenticated),
    db: Session = Depends(get_db),
):
    """Bulk create leads from multiple contacts (AC2, AC4)"""
    result = bulk_create_leads_service(db, current_user.id, lead_data)
    return CustomResponse(content={
        "message": f"{len(result)} contacts added to Lead List",
        "leads_created": len(result)
    }).format_data_create()


@router.get("/available-contacts", dependencies=[Depends(jwt_auth)], tags=tags)
def get_available_contacts(
    page: int = Query(1, ge=1, description="Page number"),
    size: int = Query(10, ge=1, le=100, description="Number of items per page"),
    search: str = Query(None, description="Search contacts by name or email"),
    current_user: UserModel = Depends(is_authenticated),
    db: Session = Depends(get_db),
):
    """Get contacts that can be converted to leads (AC3)"""
    result = get_available_contacts_service(db, current_user.id, page, size, search)
    return CustomResponse(content=result).format_data_get()


@router.get("/list", dependencies=[Depends(jwt_auth)], tags=tags)
def list_leads(
    page: int = Query(1, ge=1, description="Page number"),
    size: int = Query(10, ge=1, le=100, description="Number of items per page"),
    current_user: UserModel = Depends(is_authenticated),
    db: Session = Depends(get_db),
):
    """Get list of leads (AC1)"""
    result = get_leads_service(db, current_user.id, page, size)
    return CustomResponse(content=result).format_data_get()


@router.get("/detail/{lead_id}", dependencies=[Depends(jwt_auth)], tags=tags)
def get_lead(
    lead_id: int,
    current_user: UserModel = Depends(is_authenticated),
    db: Session = Depends(get_db),
):
    """Get lead details with contact information"""
    result = get_lead_detail_service(db, lead_id, current_user.id)
    if not result:
        raise CustomException(content={
            "error": ["Lead not found"]
        }).not_found_exception()
    return CustomResponse(content=result).format_data_get()


@router.post("/{lead_id}/refer", dependencies=[Depends(jwt_auth)], tags=tags)
def refer_lead(
    lead_id: int,
    refer_data: LeadReferSchemas,
    current_user: UserModel = Depends(is_authenticated),
    db: Session = Depends(get_db),
):
    """Refer a lead to another contact (AC5)"""
    result = refer_lead_service(db, lead_id, current_user.id, refer_data)
    return CustomResponse(content={
        "message": "Lead referred successfully"
    }).format_data_create()


@router.post("/{lead_id}/notes", dependencies=[Depends(jwt_auth)], tags=tags)
def add_lead_note(
    lead_id: int,
    note_data: LeadNoteCreateSchemas,
    current_user: UserModel = Depends(is_authenticated),
    db: Session = Depends(get_db),
):
    """Add note to lead (AC6)"""
    result = add_lead_note_service(db, lead_id, current_user.id, note_data)
    return CustomResponse(content={
        "message": "Note added successfully"
    }).format_data_create()


@router.put("/update/{lead_id}", dependencies=[Depends(jwt_auth)], tags=tags)
def update_lead(
    lead_id: int,
    data: LeadUpdateSchemas,
    current_user: UserModel = Depends(is_authenticated),
    db: Session = Depends(get_db),
):
    """Update lead information"""
    result = update_lead_service(db, lead_id, current_user.id, data)
    if not result:
        raise CustomException(content={
            "error": ["Lead not found"]
        }).not_found_exception()
    return CustomResponse(content=result).format_data_update()


@router.delete("/delete/{lead_id}", dependencies=[Depends(jwt_auth)], tags=tags)
def remove_lead(
    lead_id: int,
    current_user: UserModel = Depends(is_authenticated),
    db: Session = Depends(get_db),
):
    """Remove lead (returns contact to Contact List) (AC7)"""
    result = remove_lead_service(db, lead_id, current_user.id)
    if not result:
        raise CustomException(content={
            "error": ["Lead not found"]
        }).not_found_exception()
    return CustomResponse(content={
        "message": "Contact removed from Lead List"
    }).format_data_delete()
