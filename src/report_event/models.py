import enum
from sqlalchemy import <PERSON>umn, Integer, ForeignKey, Text, Enum, UniqueConstraint, DateTime
from sqlalchemy.orm import relationship
from datetime import datetime

from src.utils.base_model import BaseModel, Base


class ReportReasonEnum(str, enum.Enum):
    SPAM = "SPAM"
    MISLEADING = "MISLEADING"
    SAFETY = "SAFETY"
    OTHER = "OTHER"


class ReportStatusEnum(str, enum.Enum):
    OUTSTANDING = "OUTSTANDING"
    RESOLVED = "RESOLVED"


class ReportEvent(BaseModel, Base):
    __tablename__ = "report_event"
    __table_args__ = (
        UniqueConstraint("user_id", "event_id", name="uq_report_user_event"),
    )

    user_id = Column(Integer, ForeignKey("user.id"), nullable=False)
    event_id = Column(Integer, ForeignKey("event.id"), nullable=False)
    reason = Column(Enum(ReportReasonEnum), nullable=False)
    comment = Column(Text, nullable=True)
    status = Column(Enum(ReportStatusEnum), default=ReportStatusEnum.OUTSTANDING, nullable=False)
    resolved_by = Column(Integer, ForeignKey("user.id"), nullable=True)
    resolved_at = Column(DateTime, nullable=True)

    user = relationship("User", foreign_keys=[user_id])
    event = relationship("Event")
    admin = relationship("User", foreign_keys=[resolved_by])
