from fastapi import APIRouter, Depends
from sqlalchemy.orm import Session

from src.utils.database import get_db
from src.utils.permission import is_authenticated, is_admin
from src.utils.token_docs import jwt_auth
from src.utils.response import CustomResponse
from src.user.models import User as UserModel
from .schemas import (
    ReportEventCreate,
    ReportEventFilter,
    PaginatedReportEventListResponse,
    ReportEventResponse,
)
from .service import create_report_event_service, get_report_events_service

router = APIRouter()
tags = ["Report Event"]


@router.post("", dependencies=[Depends(jwt_auth)], tags=tags, response_model=ReportEventResponse)
def create_report(
    data: ReportEventCreate,
    db: Session = Depends(get_db),
    current_user: UserModel = Depends(is_authenticated),
):
    """Report a concluded event"""
    result = create_report_event_service(db, current_user, data)
    return CustomResponse(content=result).format_data_create()


@router.get("", dependencies=[Depends(jwt_auth)], tags=tags, response_model=PaginatedReportEventListResponse)
def list_reports(
    filters: ReportEventFilter = Depends(),
    db: Session = Depends(get_db),
    current_user: UserModel = Depends(is_admin),
):
    """Get list of reported events (admin)"""
    result = get_report_events_service(db, filters)
    return CustomResponse(content=result).format_data_get()
