from datetime import datetime
from typing import List, Optional

from pydantic import BaseModel, Field, field_validator

from src.utils.schemas import PaginatedResponse
from .models import ReportReasonEnum


class ReportEventCreate(BaseModel):
    event_id: int = Field(..., example=1)
    reason: ReportReasonEnum = Field(..., example=ReportReasonEnum.SPAM)
    comment: Optional[str] = Field(None, max_length=500, example="Inappropriate content")

    model_config = {
        "json_schema_extra": {
            "examples": [
                {"event_id": 1, "reason": "SPAM", "comment": "Inappropriate content"}
            ]
        }
    }

    @field_validator("comment")
    def validate_comment_length(cls, v):
        if v and len(v) > 500:
            raise ValueError("Comment must be 500 characters or less")
        return v


class ReportEventResponse(BaseModel):
    id: int
    user_id: int
    event_id: int
    reason: ReportReasonEnum
    comment: Optional[str] = None
    status: str
    created_at: datetime
    updated_at: datetime
    is_deleted: bool

    class Config:
        from_attributes = True


class ReportEventFilter(BaseModel):
    event_id: Optional[int] = None
    user_id: Optional[int] = None
    reason: Optional[ReportReasonEnum] = None
    page: int = Field(1, ge=1)
    size: int = Field(10, ge=1, le=100)


class PaginatedReportEventListResponse(PaginatedResponse[ReportEventResponse]):
    items: List[ReportEventResponse]
