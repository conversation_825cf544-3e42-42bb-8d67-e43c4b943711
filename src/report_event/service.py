from datetime import datetime
from typing import Optional

from sqlalchemy.orm import Session
from sqlalchemy import func, case

from src.utils.exceptions import CustomException
from src.user.models import User
from src.event.models import Event as EventModel, EventInvitation
from src.contact.models import Contact
from src.ticket_sale.models import TicketOrder
from src.ticket.models import Ticket
from .models import ReportEvent, ReportReasonEnum, ReportStatusEnum
from .schemas import ReportEventCreate, ReportEventFilter, PaginatedReportEventListResponse, ReportEventResponse
from src.gift.models import UserGift
from src.utils.enum import GiftTypeEnum


def _user_attended_event(db: Session, user_id: int, event_id: int) -> bool:
    ticket_order = (
        db.query(TicketOrder)
        .join(Ticket)
        .filter(TicketOrder.user_id == user_id, Ticket.event_id == event_id)
        .first()
    )
    if ticket_order:
        return True

    invitation = (
        db.query(EventInvitation)
        .join(Contact, Contact.id == EventInvitation.contact_id)
        .filter(
            EventInvitation.event_id == event_id,
            Contact.user_contact_id == user_id,
        )
        .first()
    )
    if invitation:
        return True

    # Kiểm tra UserGift (vé được tặng)
    user_gift = (
        db.query(UserGift)
        .join(TicketOrder, UserGift.ticket_order_id == TicketOrder.id)
        .join(Ticket, TicketOrder.ticket_id == Ticket.id)
        .filter(
            UserGift.receiver_id == user_id,
            UserGift.gift_type == GiftTypeEnum.TICKET,
            Ticket.event_id == event_id
        )
        .first()
    )
    if user_gift:
        return True

    return False


def create_report_event_service(db: Session, user: User, data: ReportEventCreate) -> ReportEventResponse:
    event = db.query(EventModel).filter(EventModel.id == data.event_id).first()
    if not event:
        raise CustomException(content={"error": "Event not found"}).not_found_exception()

    if event.end_time > datetime.utcnow():
        raise CustomException(content={"error": "Event has not ended"}).bad_request_exception()

    if not _user_attended_event(db, user.id, data.event_id):
        raise CustomException(content={"error": "User did not attend this event"}).bad_request_exception()

    existing = db.query(ReportEvent).filter(
        ReportEvent.user_id == user.id,
        ReportEvent.event_id == data.event_id,
    ).first()
    if existing:
        raise CustomException(content={"error": "You already reported this event"}).bad_request_exception()

    report = ReportEvent(
        user_id=user.id,
        event_id=data.event_id,
        reason=data.reason,
        comment=data.comment,
    )
    db.add(report)
    db.commit()
    db.refresh(report)

    return ReportEventResponse.model_validate(report)


def get_report_events_service(db: Session, filters: ReportEventFilter) -> PaginatedReportEventListResponse:
    query = db.query(ReportEvent)

    if filters.event_id:
        query = query.filter(ReportEvent.event_id == filters.event_id)
    if filters.user_id:
        query = query.filter(ReportEvent.user_id == filters.user_id)
    if filters.reason:
        query = query.filter(ReportEvent.reason == filters.reason)

    # Sắp xếp: outstanding trước, resolved sau, mỗi nhóm theo created_at mới nhất
    status_order = case(
        (ReportEvent.status == ReportStatusEnum.OUTSTANDING, 0),
        (ReportEvent.status == ReportStatusEnum.RESOLVED, 1),
        else_=2
    )
    total = query.count()
    total_pages = (total + filters.size - 1) // filters.size
    skip = (filters.page - 1) * filters.size
    reports = (
        query.order_by(status_order, ReportEvent.created_at.desc())
        .offset(skip)
        .limit(filters.size)
        .all()
    )

    items = [ReportEventResponse.model_validate(r) for r in reports]
    return PaginatedReportEventListResponse(
        items=items,
        total=total,
        page=filters.page,
        size=filters.size,
        total_pages=total_pages,
    )
