from typing import Optional, List
from sqlalchemy.orm import Session
from sqlalchemy import or_
from datetime import datetime, timezone

from .models import Meeting, MeetingStatusEnum, MeetingParticipant, MeetingParticipantStatusEnum
from .schemas import MeetingCreate, MeetingUpdate, PaginatedMeetingResponse, MeetingResponse, MeetingParticipantResponse
from src.user.models import User
from src.utils.exceptions import CustomException
from src.meeting.schemas import UserShortInfo

def create_meeting_service(db: Session, inviter_id: int, meeting_data: MeetingCreate) -> MeetingResponse:
    # Check start_time and end_time are not in the past
    now = datetime.now(timezone.utc)
    if meeting_data.start_time < now:
        raise CustomException(content={"error": "start_time cannot be in the past."}).bad_request_exception()
    if meeting_data.end_time < now:
        raise CustomException(content={"error": "end_time cannot be in the past."}).bad_request_exception()
    if meeting_data.end_time <= meeting_data.start_time:
        raise CustomException(content={"error": "end_time must be after start_time."}).bad_request_exception()
    # <PERSON><PERSON><PERSON> tra tất cả invitee_ids có tồn tạ<PERSON> không
    user_ids = [u.id for u in db.query(User.id).filter(User.id.in_(meeting_data.invitee_ids)).all()]
    invalid_ids = set(meeting_data.invitee_ids) - set(user_ids)
    if invalid_ids:
        raise CustomException(content={"invalid_invitee_ids": list(invalid_ids)}).bad_request_exception()
    meeting = Meeting(
        inviter_id=inviter_id,
        title=meeting_data.title,
        description=meeting_data.description,
        meeting_type=meeting_data.meeting_type,
        start_time=meeting_data.start_time,
        end_time=meeting_data.end_time,
        location=meeting_data.location,
        meeting_link=meeting_data.meeting_link,
        status=MeetingStatusEnum.PENDING,
    )
    db.add(meeting)
    db.flush()  # Để lấy meeting.id
    # Thêm participants
    for uid in meeting_data.invitee_ids:
        participant = MeetingParticipant(meeting_id=meeting.id, user_id=uid)
        db.add(participant)
    db.commit()
    db.refresh(meeting)
    # Chuẩn bị dữ liệu trả về đúng schema
    inviter = UserShortInfo(
        id=meeting.inviter.id,
        full_name=meeting.inviter.profile.full_name if meeting.inviter.profile else meeting.inviter.user_name,
        avatar=meeting.inviter.avatar,
        email=meeting.inviter.email
    )
    participants = [
        MeetingParticipantResponse(
            user=UserShortInfo(
                id=p.user.id,
                full_name=p.user.profile.full_name if p.user.profile else p.user.user_name,
                avatar=p.user.avatar,
                email=p.user.email
            ),
            status=p.status.value
        ) for p in meeting.participants
    ]
    return MeetingResponse(
        id=meeting.id,
        inviter=inviter,
        participants=participants,
        title=meeting.title,
        description=meeting.description,
        meeting_type=meeting.meeting_type,
        start_time=meeting.start_time,
        end_time=meeting.end_time,
        location=meeting.location,
        meeting_link=meeting.meeting_link,
        status=meeting.status,
        created_at=meeting.created_at,
        updated_at=meeting.updated_at,
    )


def get_meeting_by_id_service(db: Session, meeting_id: int, user_id: int) -> MeetingResponse:
    meeting = db.query(Meeting).filter(
        Meeting.id == meeting_id,
        Meeting.is_deleted == False,
    ).first()
    if not meeting:
        raise CustomException(content={"error": "Meeting not found"}).not_found_exception()
    inviter = UserShortInfo(
        id=meeting.inviter.id,
        full_name=meeting.inviter.profile.full_name if meeting.inviter.profile else meeting.inviter.user_name,
        avatar=meeting.inviter.avatar,
        email=meeting.inviter.email
    )
    participants = [
        MeetingParticipantResponse(
            user=UserShortInfo(
                id=p.user.id,
                full_name=p.user.profile.full_name if p.user.profile else p.user.user_name,
                avatar=p.user.avatar,
                email=p.user.email
            ),
            status=p.status.value
        ) for p in meeting.participants
    ]
    return MeetingResponse(
        id=meeting.id,
        inviter=inviter,
        participants=participants,
        title=meeting.title,
        description=meeting.description,
        meeting_type=meeting.meeting_type,
        start_time=meeting.start_time,
        end_time=meeting.end_time,
        location=meeting.location,
        meeting_link=meeting.meeting_link,
        status=meeting.status,
        created_at=meeting.created_at,
        updated_at=meeting.updated_at,
    )


def list_meetings_service(db: Session, user_id: int, page: int = 1, size: int = 10) -> PaginatedMeetingResponse:
    if page < 1:
        page = 1
    if size < 1:
        size = 10
    if size > 100:
        size = 100

    skip = (page - 1) * size

    query = db.query(Meeting)
    total = query.count()
    meetings = query.order_by(Meeting.start_time.desc()).offset(skip).limit(size).all()

    items = []
    for m in meetings:
        inviter = UserShortInfo(
            id=m.inviter.id,
            full_name=m.inviter.profile.full_name if m.inviter.profile else m.inviter.user_name,
            avatar=m.inviter.avatar,
            email=m.inviter.email
        )
        participants = [
            MeetingParticipantResponse(
                user=UserShortInfo(
                    id=p.user.id,
                    full_name=p.user.profile.full_name if p.user.profile else p.user.user_name,
                    avatar=p.user.avatar,
                    email=p.user.email
                ),
                status=p.status.value
            ) for p in m.participants
        ]
        item = MeetingResponse(
            id=m.id,
            inviter=inviter,
            participants=participants,
            title=m.title,
            description=m.description,
            meeting_type=m.meeting_type,
            start_time=m.start_time,
            end_time=m.end_time,
            location=m.location,
            meeting_link=m.meeting_link,
            status=m.status,
            created_at=m.created_at,
            updated_at=m.updated_at,
        )
        items.append(item)

    total_pages = (total + size - 1) // size
    return PaginatedMeetingResponse(items=items, total=total, page=page, size=size, total_pages=total_pages)


def update_meeting_service(db: Session, meeting_id: int, meeting_data: MeetingUpdate, user_id: int) -> MeetingResponse:
    meeting = db.query(Meeting).filter(Meeting.id == meeting_id, Meeting.is_deleted == False).first()
    if not meeting:
        raise CustomException(content={"error": "Meeting not found"}).not_found_exception()
    if meeting.inviter_id != user_id:
        raise CustomException().permission_denied_exception()

    from datetime import timezone
    now = datetime.now(timezone.utc)
    # Kiểm tra start_time nếu truyền vào và khác giá trị cũ
    if meeting_data.start_time is not None and meeting_data.start_time != meeting.start_time:
        if meeting_data.start_time < now:
            raise CustomException(content={"error": "start_time cannot be in the past."}).bad_request_exception()
    # Kiểm tra end_time nếu truyền vào
    if meeting_data.end_time is not None:
        if meeting_data.end_time < now:
            raise CustomException(content={"error": "end_time cannot be in the past."}).bad_request_exception()
        # Nếu có start_time mới thì so sánh với start_time mới, còn không thì so với start_time cũ
        compare_start = meeting_data.start_time if meeting_data.start_time is not None else meeting.start_time
        if meeting_data.end_time <= compare_start:
            raise CustomException(content={"error": "end_time must be after start_time."}).bad_request_exception()

    # Cập nhật các trường thông tin meeting
    for key, value in meeting_data.model_dump(exclude_unset=True, exclude={"invitee_ids"}).items():
        setattr(meeting, key, value)

    # Cập nhật danh sách người được mời nếu có invitee_ids
    if meeting_data.invitee_ids is not None:
        # Kiểm tra user_id hợp lệ
        user_ids = [u.id for u in db.query(User.id).filter(User.id.in_(meeting_data.invitee_ids)).all()]
        invalid_ids = set(meeting_data.invitee_ids) - set(user_ids)
        if invalid_ids:
            raise CustomException(content={"invalid_invitee_ids": list(invalid_ids)}).bad_request_exception()
        # Lấy danh sách user_id hiện tại
        current_ids = set(p.user_id for p in meeting.participants)
        new_ids = set(meeting_data.invitee_ids)
        # Xóa participant không còn trong invitee_ids và chưa ACCEPTED
        for p in list(meeting.participants):
            if p.user_id not in new_ids and p.status.value != MeetingParticipantStatusEnum.ACCEPTED:
                db.delete(p)
        # Thêm participant mới
        for uid in new_ids - current_ids:
            db.add(MeetingParticipant(meeting_id=meeting.id, user_id=uid))

    db.commit()
    db.refresh(meeting)
    # Trả về MeetingResponse đầy đủ
    inviter = UserShortInfo(
        id=meeting.inviter.id,
        full_name=meeting.inviter.profile.full_name if meeting.inviter.profile else meeting.inviter.user_name,
        avatar=meeting.inviter.avatar,
        email=meeting.inviter.email
    )
    participants = [
        MeetingParticipantResponse(
            user=UserShortInfo(
                id=p.user.id,
                full_name=p.user.profile.full_name if p.user.profile else p.user.user_name,
                avatar=p.user.avatar,
                email=p.user.email
            ),
            status=p.status.value
        ) for p in meeting.participants
    ]
    return MeetingResponse(
        id=meeting.id,
        inviter=inviter,
        participants=participants,
        title=meeting.title,
        description=meeting.description,
        meeting_type=meeting.meeting_type,
        start_time=meeting.start_time,
        end_time=meeting.end_time,
        location=meeting.location,
        meeting_link=meeting.meeting_link,
        status=meeting.status,
        created_at=meeting.created_at,
        updated_at=meeting.updated_at,
    )


def delete_meeting_service(db: Session, meeting_id: int, user_id: int):
    meeting = db.query(Meeting).filter(Meeting.id == meeting_id).first()
    if not meeting:
        raise CustomException(content={"error": "Meeting not found"}).not_found_exception()
    if meeting.inviter.id != user_id:
        raise CustomException().permission_denied_exception()
    db.delete(meeting)
    db.commit()


def respond_invite_service(db: Session, meeting_id: int, user_id: int, action: str):
    meeting = db.query(Meeting).filter(Meeting.id == meeting_id).first()
    if not meeting:
        raise CustomException(content={"error": "Meeting not found"}).not_found_exception()
    # Find participant
    participant = db.query(MeetingParticipant).filter(
        MeetingParticipant.meeting_id == meeting_id,
        MeetingParticipant.user_id == user_id
    ).first()
    # Check if meeting expired
    if meeting.end_time and meeting.end_time < datetime.now(timezone.utc):
        if participant.status == MeetingParticipantStatusEnum.INVITED:
            participant.status = MeetingParticipantStatusEnum.EXPIRED
        else:
            raise CustomException(content={"error": "The meeting has already ended."}).bad_request_exception()
        db.commit()
        return {
            "status": participant.status.value,
            "message": "The invitation has expired."
        }
    if not participant:
        raise CustomException(content={"error": "You are not in the invitee list."}).bad_request_exception()
    if participant.status == MeetingParticipantStatusEnum.ACCEPTED:
        raise CustomException(content={"error": "You have already accepted this invitation."}).bad_request_exception()
    if participant.status == MeetingParticipantStatusEnum.REJECTED:
        raise CustomException(content={"error": "You have already rejected this invitation."}).bad_request_exception()
    if action == "ACCEPT":
        participant.status = MeetingParticipantStatusEnum.ACCEPTED
        msg = "You have accepted the meeting invitation."
        # TODO: Send notification to inviter
    elif action == "REJECT":
        participant.status = MeetingParticipantStatusEnum.REJECTED
        msg = "You have rejected the meeting invitation."
        # TODO: Send notification to inviter
    else:
        raise CustomException(content={"error": "Invalid action."}).bad_request_exception()
    db.commit()
    return {
        "status": participant.status.value,
        "message": msg
    }
