from datetime import datetime
from typing import Optional, List, Literal
from pydantic import BaseModel, Field

from src.utils.enum import EventTypeEnum
from src.meeting.models import MeetingStatusEnum
from src.utils.schemas import PaginatedResponse


class MeetingBase(BaseModel):
    title: str = Field(..., example="Project Sync")
    description: Optional[str] = Field(None, example="Weekly sync meeting")
    meeting_type: EventTypeEnum = Field(..., example=EventTypeEnum.INPERSON)
    start_time: datetime = Field(..., example="2025-06-20T09:00:00Z")
    end_time: datetime = Field(..., example="2025-06-20T10:00:00Z")
    location: Optional[str] = Field(None, example="Office")
    meeting_link: Optional[str] = Field(None, example="https://meet.google.com/abc-defg-hij")

    model_config = {
        "json_schema_extra": {
            "examples": [
                {
                    "title": "Project Sync",
                    "description": "Weekly sync meeting",
                    "meeting_type": "INPERSON",
                    "start_time": "2025-06-20T09:00:00Z",
                    "end_time": "2025-06-20T10:00:00Z",
                    "location": "Office",
                    "meeting_link": "https://meet.google.com/abc-defg-hij"
                }
            ]
        }
    }


class MeetingCreate(MeetingBase):
    invitee_ids: List[int] = Field(..., example=[2, 3, 4])
    model_config = {
        "json_schema_extra": {
            "examples": [
                {
                    "title": "Project Sync",
                    "description": "Weekly sync meeting",
                    "meeting_type": "INPERSON",
                    "start_time": "2025-06-20T09:00:00Z",
                    "end_time": "2025-06-20T10:00:00Z",
                    "location": "Office",
                    "meeting_link": "https://meet.google.com/abc-defg-hij",
                    "invitee_ids": [2, 3, 4]
                }
            ]
        }
    }


class MeetingUpdate(BaseModel):
    title: Optional[str] = Field(None, example="Project Sync")
    description: Optional[str] = Field(None, example="Weekly sync meeting")
    meeting_type: Optional[EventTypeEnum] = Field(None, example=EventTypeEnum.INPERSON)
    start_time: Optional[datetime] = Field(None, example="2025-06-20T09:00:00Z")
    end_time: Optional[datetime] = Field(None, example="2025-06-20T10:00:00Z")
    location: Optional[str] = Field(None, example="Office")
    meeting_link: Optional[str] = Field(None, example="https://meet.google.com/abc-defg-hij")
    status: Optional[MeetingStatusEnum] = Field(None, example=MeetingStatusEnum.SCHEDULED)
    invitee_ids: Optional[List[int]] = Field(None, example=[2, 3, 4])


class UserShortInfo(BaseModel):
    id: int
    full_name: str
    avatar: str = None
    email: str


class MeetingParticipantResponse(BaseModel):
    user: UserShortInfo
    status: str


class MeetingResponse(MeetingBase):
    id: int
    inviter: UserShortInfo
    participants: List[MeetingParticipantResponse]
    title: str
    description: Optional[str] = None
    meeting_type: EventTypeEnum
    status: MeetingStatusEnum
    start_time: datetime
    end_time: datetime
    location: Optional[str] = None
    meeting_link: Optional[str] = None
    created_at: datetime
    updated_at: datetime


class PaginatedMeetingResponse(PaginatedResponse):
    items: List[MeetingResponse]


class MeetingInviteResponseRequest(BaseModel):
    action: Literal["ACCEPT", "REJECT"] = Field(..., description="ACCEPT hoặc REJECT")


class MeetingInviteResponse(BaseModel):
    meeting_id: int
    user_id: int
    status: str
    message: str
