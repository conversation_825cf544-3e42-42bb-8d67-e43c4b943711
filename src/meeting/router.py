from fastapi import APIRouter, Depends, Query
from sqlalchemy.orm import Session

from src.utils.database import get_db
from src.utils.permission import is_authenticated
from src.utils.token_docs import jwt_auth
from src.utils.response import CustomResponse
from src.user.models import User

from src.meeting.schemas import MeetingCreate, MeetingUpdate, MeetingResponse, MeetingInviteResponseRequest, MeetingInviteResponse
from src.meeting.service import (
    create_meeting_service,
    list_meetings_service,
    get_meeting_by_id_service,
    update_meeting_service,
    delete_meeting_service,
    respond_invite_service,
)

router = APIRouter(tags=["Meeting"])


@router.post("", response_model=MeetingResponse, dependencies=[Depends(jwt_auth)])
def create_meeting(
    meeting: MeetingCreate,
    db: Session = Depends(get_db),
    current_user: User = Depends(is_authenticated),
):
    result = create_meeting_service(db, current_user.id, meeting)
    return CustomResponse(content=result).format_data_create()


@router.get("", dependencies=[Depends(jwt_auth)])
def list_meetings(
    page: int = Query(1, ge=1),
    size: int = Query(10, ge=1, le=100),
    db: Session = Depends(get_db),
    current_user: User = Depends(is_authenticated),
):
    result = list_meetings_service(db, current_user.id, page, size)
    return CustomResponse(content=result).format_data_get()


@router.get("/{meeting_id}", dependencies=[Depends(jwt_auth)])
def get_meeting(
    meeting_id: int,
    db: Session = Depends(get_db),
    current_user: User = Depends(is_authenticated),
):
    result = get_meeting_by_id_service(db, meeting_id, current_user.id)
    return CustomResponse(content=result).format_data_get()


@router.put("/{meeting_id}", dependencies=[Depends(jwt_auth)])
def update_meeting(
    meeting_id: int,
    meeting: MeetingUpdate,
    db: Session = Depends(get_db),
    current_user: User = Depends(is_authenticated),
):
    result = update_meeting_service(db, meeting_id, meeting, current_user.id)
    return CustomResponse(content=result).format_data_update()


@router.delete("/{meeting_id}", dependencies=[Depends(jwt_auth)])
def delete_meeting(
    meeting_id: int,
    db: Session = Depends(get_db),
    current_user: User = Depends(is_authenticated),
):
    delete_meeting_service(db, meeting_id, current_user.id)
    return CustomResponse().format_data_delete()


@router.post("/{meeting_id}/respond", response_model=MeetingInviteResponse, dependencies=[Depends(jwt_auth)])
def respond_invite(
    meeting_id: int,
    data: MeetingInviteResponseRequest,
    db: Session = Depends(get_db),
    current_user: User = Depends(is_authenticated),
):
    result = respond_invite_service(db, meeting_id, current_user.id, data.action)
    return CustomResponse(content=result).format_data_update()
