from sqlalchemy import Column, Integer, ForeignKey, String, DateTime, Enum, Text
from sqlalchemy.orm import relationship
from datetime import datetime
import enum

from src.utils.base_model import BaseModel, Base
from src.utils.enum import EventTypeEnum

class MeetingStatusEnum(enum.Enum):
    PENDING = "PENDING"
    SCHEDULED = "SCHEDULED"
    PENDING_CHANGE = "PENDING_CHANGE"
    CANCELLED = "CANCELLED"

class MeetingParticipantStatusEnum(enum.Enum):
    INVITED = "INVITED"
    ACCEPTED = "ACCEPTED"
    REJECTED = "REJECTED"
    EXPIRED = "EXPIRED"

class Meeting(BaseModel, Base):
    __tablename__ = "meeting"

    inviter_id = Column(Integer, ForeignKey("user.id"), nullable=False)
    title = Column(String(255), nullable=False)
    description = Column(Text, nullable=True)
    meeting_type = Column(Enum(EventTypeEnum), nullable=False)
    status = Column(Enum(MeetingStatusEnum), default=MeetingStatusEnum.PENDING)
    start_time = Column(DateTime, nullable=False)
    end_time = Column(DateTime, nullable=False)
    location = Column(String(255), nullable=True)
    meeting_link = Column(String(255), nullable=True)

    inviter = relationship("User", foreign_keys=[inviter_id], back_populates="created_meetings")
    participants = relationship("MeetingParticipant", back_populates="meeting", cascade="all, delete-orphan")

class MeetingParticipant(BaseModel, Base):
    __tablename__ = "meeting_participant"
    meeting_id = Column(Integer, ForeignKey("meeting.id"), primary_key=True)
    user_id = Column(Integer, ForeignKey("user.id"), primary_key=True)
    status = Column(Enum(MeetingParticipantStatusEnum), nullable=False, default=MeetingParticipantStatusEnum.INVITED)
    
    meeting = relationship("Meeting", back_populates="participants")
    user = relationship("User")
