import re
from typing import  Union
from pydantic import Field, EmailStr, field_validator, BaseModel
from datetime import datetime, timezone
from sqlalchemy.orm import Session
from src.utils.exceptions import CustomException
from src.user.schemas import UserInfoBase
class Token(BaseModel):
    access_token: str
    refresh_token: str

class TokenData(Token):
    info: UserInfoBase

class LoginData(BaseModel):
    user_name: str = Field(
        title="The user name of the user",
        min_length=5,
        max_length=50,
        default="<EMAIL>"
    )
    password: str = Field(
        title="The password of the user",
        min_length=8,
        default="Abcd1234!"
    ) 
    # def validate(self, db: Session, employee: EmployeeModel):
    #     if employee.setting and employee.setting.enable_two_factor_auth:
    #         if self.otp:
    #             otp = db.query(OTP).join(EmployeeModel).filter((OTP.code == self.otp) & (EmployeeModel.email == employee.email) & (OTP.type == OTPType.AUTHENTICATION)).first()
    #             if not otp:
    #                 raise CustomException(content={"otp":["Invalid OTP code"]}).bad_request_exception()
                
    #             if otp.expired_at.replace(tzinfo=timezone.utc) <= datetime.now(timezone.utc):
    #                 raise CustomException(content={"otp":["OTP is expired"]}).bad_request_exception()
    #             return True
    #         else:
    #             return False # Response 202
    #     else:
    #         return True


class TokenCreate(Token):
    expire_at: datetime = Field(
        title="The expired time of token",
        default=None
    ) 
    ip_address: Union[str, None] = Field(
        title="The ip address of the user",
        default=None
    ) 
    user_agent: Union[str, None] = Field(
        title="The user agent of the user",
        default=None
    ) 
    device_id: Union[str, None] = Field(
        title="The device id of the user",
        default=None
    )
    
class ChangePassword(BaseModel):
    current_password: str = Field(
        title="The password of the employee",
        min_length=8,
    ) 
    new_password: str = Field(
        title="The confirm password of the employee",
        min_length=8,
    )

    @field_validator('current_password')
    def validate_current_password(cls, value):
        if not re.search(r'[A-Z]', value) or not re.search(r'[a-z]', value) or not re.search(r'\d', value) or not re.search(r'[@$!%*?&]', value):
            raise CustomException(content={"current_password":['Password must contain at least one uppercase letter, least one lowercase letter, least one digit and least one special character']}).bad_request_exception()
        return value
    
    @field_validator('new_password')
    def validate_new_password(cls, value):
        if not re.search(r'[A-Z]', value) or not re.search(r'[a-z]', value) or not re.search(r'\d', value) or not re.search(r'[@$!%*?&]', value):
            raise CustomException(content={"new_password":['Password must contain at least one uppercase letter, least one lowercase letter, least one digit and least one special character']}).bad_request_exception()
        return value

class VerifyOTP(BaseModel):
    email: EmailStr = Field(
        title="The email of the user to send otp",
    )
    code: str = Field(
        title="The code of the otp",
    )

class SetPassword(ChangePassword, VerifyOTP):
    pass
    
class RefeshTokenBase(BaseModel):
    refresh_token: str = Field(
        title="The refresh token of the user",
    )

class RefeshToken(RefeshTokenBase):
    access_token: str = Field(
        title="The access token of the user",
    )