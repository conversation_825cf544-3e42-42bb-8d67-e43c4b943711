# from typing import List, Union
# from sqlalchemy import or_
from typing import List
from sqlalchemy.orm import Session
from src.user.models import Token
# from src.employee.models import <PERSON><PERSON>, Employee, Token
# from src.authentication.schemas import OTPCreate
# from datetime import datetime, timedelta, timezone
# import os
# from jose import jwt
# from passlib.context import CryptContext
# from src.employee.service import get_employee_by_user_name_or_email, get_password_hash, pwd_context
# from src.utils.common import calculate_expired_time, generate_otp
# from src.utils.enum import ExpirationUnit, OTPType
# from src.utils.exceptions import CustomException
# from src.employee.schemas import Employee as EmployeeSchema

# def check_exist_or_create(db: Session, employee: Employee, type:OTPType):
#     existing_otp = db.query(OTP).filter((OTP.user_id == employee.id)&(OTP.expired_at > datetime.now(timezone.utc))&(OTP.type == type)).first()
#     if existing_otp:
#         otp = existing_otp
#     else:
#         otp_code = generate_otp()
#         otp_data = OTPCreate(code=otp_code,expired_at=calculate_expired_time(expire_unit=ExpirationUnit.MINUTES,value=5), type=type)
#         otp = create_otp(db, otp_data, employee.id)
#     return otp

# def create_otp(db: Session, create_data: OTPCreate, user_id:int):
#     otp = OTP(
#         **create_data.model_dump(),
#         user_id=user_id
#     )
#     db.add(otp)
#     db.commit()
#     db.refresh(otp)
#     return otp

# def update_password(db: Session, new_password: str, employee: Employee):
#     setattr(employee, 'password', get_password_hash(new_password))
#     db.add(employee)
#     db.commit()
#     db.refresh(employee)
#     return employee

def get_token_by_access_token(db: Session, access_token:str, user_id: int, return_list:bool = False):
    query = db.query(Token).filter((Token.access_token == access_token) & (Token.user_id == user_id))
    return query.all() if return_list else query.first()

def get_all_token_by_user(db: Session, user_id: int):
    return db.query(Token).filter(Token.user_id == user_id).all()

def update_token_is_expired(db: Session, tokens:List[Token]):
    for token in tokens:
        setattr(token, 'is_expired', True)
        db.add(token)
    db.commit()
    return tokens
