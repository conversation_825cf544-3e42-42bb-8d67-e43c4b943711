from datetime import datetime, timedelta, timezone
from multiprocessing import get_context
import os
from fastapi import APIRouter, BackgroundTasks, Depends, Request, Security
from sqlalchemy.orm import Session
from src.authentication.schemas import ChangePassword, SetPassword, TokenCreate, LoginData, TokenData
from src.authentication.service import  get_token_by_access_token, update_token_is_expired
from src.user.models import User as UserModel
# from src.employee.schemas import Employee, ProfileUpdate
from src.user.service import authenticate_user, create_access_token, create_token_of_user, update_user_profile_service, reresh_token_service
# from src.utils.common import calculate_expired_time, generate_otp, send_email
from src.utils.database import get_db, get_token
from src.utils.permission import check_security_scope, is_authenticated
# from src.utils.template import get_otp_template
from ..utils.response import *
from typing_extensions import Annotated
from src.user.schemas import UserInfoBase, UpdateUserInfo
from src.authentication.schemas import RefeshTokenBase, RefeshToken
from src.utils.token_docs import jwt_auth
router = APIRouter()
tags = ['Authentication']

@router.post("/login", tags=tags, **create_data_response)
def sign_in(data: LoginData, db: Session = Depends(get_db)):
    user = authenticate_user(db, data.user_name, data.password)
    user_dict = {
        "id": user.id,
        "user_name": user.email,
        "email": user.email,
        "full_name": user.profile.full_name,
    }
    info = {
        "id": user.id,
        "email": user.email,
        "phone_number": user.phone_number,
        "full_name": user.profile.full_name,
        "gender": user.profile.gender,
        "date_of_birth": user.profile.date_of_birth if user.profile.date_of_birth else None,
        "address": user.profile.address,
        "avatar": user.avatar,
        "last_login": user.last_login,
        "is_admin": user.is_admin,
        "user_type": user.user_type,
        "organization": user.organization
    }
    access_token, expired_access_token = create_access_token(data=user_dict, expires_delta=timedelta(days=int(os.environ.get('ACCESS_TOKEN_EXPIRE_DAYS',1))))
    refresh_token, expired_refresh_token = create_access_token(data=user_dict, expires_delta=timedelta(days=int(os.environ.get('REFRESH_TOKEN_EXPIRE_DAYS',3))))
    create_token_of_user(db, TokenCreate(access_token=access_token, refresh_token=refresh_token, expire_at=expired_access_token),user_id=user.id)
    return CustomResponse(content=TokenData(access_token=access_token, refresh_token=refresh_token, info=info)).format_data_create()



@router.put("/auth/refresh-token", tags=tags, **update_data_response)
async def refresh_token(
    data: RefeshTokenBase,
    db: Session = Depends(get_db)
):
    token = reresh_token_service(db, data.refresh_token)
    return CustomResponse(content=RefeshToken(refresh_token=token.refresh_token, access_token=token.access_token)).format_data_update()

@router.post("/logout", tags=tags, **get_data_response, dependencies=[Depends(jwt_auth)])
async def logout(
    current_user: UserModel = Depends(is_authenticated),
    access_token: Session = Depends(get_token),
    db: Session = Depends(get_db)
):  
    token = get_token_by_access_token(db, access_token, current_user.id, True)
    update_token_is_expired(db, token)
    return CustomResponse().format_data_update()
# @router.put("/forgot-password/send-otp", tags=tags, **get_data_response)
# async def send_otp(
#     data: SendOTP,
#     background_tasks: BackgroundTasks,
#     db: Session = Depends(get_db)
# ):
#     employee = get_employee_by_user_name_or_email(db, data.email)
#     if not employee:
#         raise CustomException(content={'email':['Email does not exist']}).bad_request_exception()
    
#     otp = check_exist_or_create(db, employee, OTPType.FORGOT_PASSWORD)
#     background_tasks.add_task(
#         send_email,
#         subject="Your OTP to Retrieve password",
#         to=[employee.email],
#         html_body=get_otp_template(
#             employee.first_name if employee.first_name else '', 
#             employee.last_name if employee.last_name else '', 
#             otp.code
#         ),
#     )
#     return CustomResponse().format_data_update()

# @router.put("/forgot-password/set-password", tags=tags, **get_data_response)
# async def set_password(
#     data: SetPassword,
#     db: Session = Depends(get_db)
# ):  
#     employee = get_employee_by_user_name_or_email(db, data.email)
#     if not employee:
#         raise CustomException(content={'email':['Email does not exist']}).bad_request_exception()
    
#     otp = db.query(OTP).join(Employee).filter(
#         (OTP.code == data.code) & \
#         (Employee.email == data.email) & \
#         (OTP.type == OTPType.FORGOT_PASSWORD)
#     ).first()
#     if not otp:
#         raise CustomException(content={"code":["Invalid OTP code"]}).bad_request_exception()
    
#     if otp.expired_at.replace(tzinfo=timezone.utc) <= datetime.now(timezone.utc):
#         raise CustomException(content={"code":["OTP is expired"]}).bad_request_exception()
    
#     if isinstance(data, SetPassword) and not pwd_context.verify(data.current_password, employee.password):
#         raise CustomException(content={"current_password":["Invalid current password"]}).bad_request_exception()
#     update_password(db, data.new_password, employee)
#     return CustomResponse().format_data_update()

# @router.put("/me/change-password", tags=tags, **get_data_response)
# async def change_password(
#     data: ChangePassword,
#     db: Session = Depends(get_db),
#     current_user: Employee = Depends(is_authenticated),
# ):  
#     is_valid = pwd_context.verify(data.current_password, current_user.password)
#     if not is_valid:
#         raise CustomException(content={"current_password":["Invalid current password"]}).bad_request_exception()
#     update_password(db, data.new_password, current_user)
#     return CustomResponse().format_data_update()

# @router.post("/logout", tags=tags, **get_data_response)
# async def logout(
#     current_user: Employee = Depends(is_authenticated),
#     access_token: Session = Depends(get_token),
#     db: Session = Depends(get_db)
# ):  
#     token = get_token_by_access_token(db, access_token, current_user.id, True)
#     update_token_is_expired(db, token)
#     return CustomResponse().format_data_update()

# @router.post("/logout-all", tags=tags, **get_data_response)
# async def logout_all_devices(
#     current_user: Employee = Depends(is_authenticated),
#     db: Session = Depends(get_db)
# ):  
#     tokens = get_all_token_by_user(db, current_user.id)
#     update_token_is_expired(db, tokens)
#     return CustomResponse().format_data_create()

# @router.put("/auth/send-otp", tags=tags, **get_data_response)
# async def send_auth_otp(
#     data: SendOTP,
#     background_tasks: BackgroundTasks,
#     db: Session = Depends(get_db)
# ):
#     employee = get_employee_by_user_name_or_email(db, data.email)
#     if not employee:
#         raise CustomException(content={'email':['Email does not exist']}).bad_request_exception()
    
#     otp = check_exist_or_create(db, employee, OTPType.AUTHENTICATION)
#     background_tasks.add_task(
#         send_email,
#         subject="Your OTP to Login",
#         to=[employee.email],
#         html_body=get_otp_template(
#             employee.first_name if employee.first_name else '', 
#             employee.last_name if employee.last_name else '', 
#             otp.code
#         ),
#     )
#     return CustomResponse().format_data_update()