from sqlalchemy import Column, Integer, ForeignKey, String, DateTime, Text, Boolean, Enum, UniqueConstraint
from src.utils.base_model import BaseModel, Base
from sqlalchemy.orm import relationship
from datetime import datetime
import enum
from src.event.models import EventInvitation

class ContactRequestStatusEnum(str, enum.Enum):
    PENDING = "PENDING"
    APPROVED = "APPROVED"
    REJECTED = "REJECTED"
    CANCELED = "CANCELED"

class Contact(BaseModel, Base):
    __tablename__ = "contact"

    id = Column(Integer, primary_key=True)
    user_id = Column(Integer, ForeignKey("user.id"), nullable=False)  # User sở hữu contact này
    
    # Liên kết với user hoặc external_contact
    user_contact_id = Column(Integer, ForeignKey("user.id"), nullable=True)  # Nếu là user
    # Thông tin bổ sung cho contact
    nickname = Column(String(100), nullable=True)  # Tên gọi riêng cho contact này
    notes = Column(Text, nullable=True)  # Ghi chú riêng cho contact này
    tags = Column(Text, nullable=True)  # Tags riêng cho contact này
    is_favorite = Column(Boolean, default=False)
    last_contact_date = Column(DateTime, nullable=True)
    created_at = Column(DateTime, default=datetime.now)
    updated_at = Column(DateTime, default=datetime.now, onupdate=datetime.now)
    is_deleted = Column(Boolean, default=False)

    # Relationships
    user = relationship("User", foreign_keys=[user_id], back_populates="contacts")
    user_contact = relationship("User", foreign_keys=[user_contact_id], back_populates="contact_of")
    invitations = relationship(
        "EventInvitation",
        back_populates="contact",
        lazy="select"
    )
    leads = relationship(
        "Lead",
        back_populates="contact",
        foreign_keys="Lead.contact_id",
        lazy="select"
    )
    __table_args__ = (
        # Đảm bảo mỗi user không có contact trùng lặp
        UniqueConstraint('user_id', 'user_contact_id', name='uq_user_contact'),
    ) 

class ContactRequest(BaseModel, Base):
    __tablename__ = "contact_requests"

    id = Column(Integer, primary_key=True, index=True)
    from_user_id = Column(Integer, ForeignKey("user.id"))
    to_user_id = Column(Integer, ForeignKey("user.id"))
    status = Column(Enum(ContactRequestStatusEnum), default=ContactRequestStatusEnum.PENDING)
    message = Column(Text, nullable=True)

    # Relationships
    from_user = relationship("User", foreign_keys=[from_user_id], back_populates="sent_contact_requests")
    to_user = relationship("User", foreign_keys=[to_user_id], back_populates="received_contact_requests")

class ContactReferralLog(BaseModel, Base):
    __tablename__ = "contact_referral_log"
    id = Column(Integer, primary_key=True)
    sender_id = Column(Integer, ForeignKey("user.id"), nullable=False)
    contact_id = Column(Integer, ForeignKey("contact.id"), nullable=False)
    recipient_id = Column(Integer, ForeignKey("user.id"), nullable=False)
    created_at = Column(DateTime, default=datetime.now, nullable=False) 

class ContactDismissSuggestion(BaseModel, Base):
    __tablename__ = "contact_dismiss_suggestion"

    id = Column(Integer, primary_key=True)
    user_id = Column(Integer, nullable=False)
    user_dismiss_id = Column(Integer, nullable=False)
    dismissed_to = Column(DateTime, nullable=False)