from fastapi import APIRouter, Depends, Query
from sqlalchemy.orm import Session
from typing import List, Optional

from src.utils.database import get_db
from src.utils.permission import is_authenticated
from src.utils.token_docs import jwt_auth
from src.utils.response import CustomResponse
from src.user.models import User as UserModel
from src.utils.exceptions import CustomException
from .schemas import ContactCreateSchemas, ContactFilter, ContactUpdateSchemas, ContactRequestCreate, ReferContactRequest, ReferContactResponse, SuggestionContactFilter, ContactMapFilter, ContactMapPaginatedResponse
from .service import (
    cancel_contact_request_service,
    create_contact_service,
    get_contact_service,
    get_contact_detail_service,
    get_contacts_service,
    update_contact_service,
    delete_contact_service,
    update_last_contact_date_service,
    get_available_contacts_for_event_service,
    create_contact_request_service,
    get_received_contact_requests_service,
    get_sent_contact_requests_service,
    approve_contact_request_service,
    reject_contact_request_service,
    refer_contact_service,
    get_contact_suggestions_service,
    dismiss_contact_suggestion_service,
    list_contact_map_service
)

router = APIRouter()
tags = ['Contact']
@router.post("", dependencies=[Depends(jwt_auth)], tags=tags)
def create_new_contact(
    contact: ContactCreateSchemas,
    current_user: UserModel = Depends(is_authenticated),
    db: Session = Depends(get_db)
):
    """Create a new contact (user or external)"""
    result = create_contact_service(db, current_user.id, contact)
    return CustomResponse(content=result).format_data_create()

@router.get("/{contact_id}", dependencies=[Depends(jwt_auth)], tags=tags)
def get_contact_by_id(
    contact_id: int,
    current_user: UserModel = Depends(is_authenticated),
    db: Session = Depends(get_db)
):
    """Get a specific contact by ID with full details"""
    result = get_contact_detail_service(db, contact_id, current_user.id)
    if not result:
        raise CustomException(content={
            "error": ["Contact not found"]
        }).not_found_exception()
    return CustomResponse(content=result).format_data_get()

@router.get("", dependencies=[Depends(jwt_auth)], tags=tags)
def list_contacts(
    filters: ContactFilter = Depends(),
    current_user: UserModel = Depends(is_authenticated),
    db: Session = Depends(get_db)
):
    """List all contacts with optional filtering"""
    result = get_contacts_service(db, current_user.id, filters)
    return CustomResponse(content=result).format_data_get()

@router.put("/{contact_id}", dependencies=[Depends(jwt_auth)], tags=tags)
def update_contact_by_id(
    contact_id: int,
    contact: ContactUpdateSchemas,
    current_user: UserModel = Depends(is_authenticated),
    db: Session = Depends(get_db)
):
    """Update a specific contact"""
    result = update_contact_service(db, contact_id, current_user.id, contact)
    if not result:
        raise CustomException(content={
            "error": ["Contact not found"]
        }).not_found_exception()
    return CustomResponse(content=result).format_data_update()

@router.delete("/{contact_id}", dependencies=[Depends(jwt_auth)], tags=tags)
def delete_contact_by_id(
    contact_id: int,
    current_user: UserModel = Depends(is_authenticated),
    db: Session = Depends(get_db)
):
    """Delete a specific contact (soft delete)"""
    result = delete_contact_service(db, contact_id, current_user.id)
    if not result:
        raise CustomException(content={
            "error": ["Contact not found"]
        }).not_found_exception()
    return CustomResponse().format_data_delete()

@router.put("/{contact_id}/last-contact", dependencies=[Depends(jwt_auth)], tags=tags)
def update_contact_last_contact_date(
    contact_id: int,
    current_user: UserModel = Depends(is_authenticated),
    db: Session = Depends(get_db)
):
    """Update the last contact date for a specific contact"""
    result = update_last_contact_date_service(db, contact_id, current_user.id)
    if not result:
        raise CustomException(content={
            "error": ["Contact not found"]
        }).not_found_exception()
    return CustomResponse(content=result).format_data_update() 

@router.get("/available-contacts/{event_id}", dependencies=[Depends(jwt_auth)], tags=tags)
def get_available_contacts(
    event_id: int,
    page: int = Query(1, ge=1, description="Page number"),
    size: int = Query(10, ge=1, le=100, description="Number of items per page"),
    search: Optional[str] = None,
    current_user: UserModel = Depends(is_authenticated),
    db: Session = Depends(get_db)
):
    """Get available contacts for an event"""
    result = get_available_contacts_for_event_service(db, current_user.id, event_id, search, page, size)
    return CustomResponse(content=result).format_data_get()

@router.post("/requests", dependencies=[Depends(jwt_auth)], tags=tags)
def create_contact_request(
    request: ContactRequestCreate,
    current_user: UserModel = Depends(is_authenticated),
    db: Session = Depends(get_db)
):
    """Create a new contact request"""
    result = create_contact_request_service(db, current_user.id, request)
    return CustomResponse(content=result).format_data_create()

@router.get("/requests/received", dependencies=[Depends(jwt_auth)], tags=tags)
def get_received_contact_requests(
    page: int = Query(1, ge=1, description="Page number"),
    size: int = Query(10, ge=1, le=100, description="Number of items per page"),
    current_user: UserModel = Depends(is_authenticated),
    db: Session = Depends(get_db)
):
    """Get received contact requests"""
    result = get_received_contact_requests_service(db, current_user.id, page, size)
    return CustomResponse(content=result).format_data_get()

@router.get("/requests/sent", dependencies=[Depends(jwt_auth)], tags=tags)
def get_sent_contact_requests(
    page: int = Query(1, ge=1, description="Page number"),
    size: int = Query(10, ge=1, le=100, description="Number of items per page"),
    current_user: UserModel = Depends(is_authenticated),
    db: Session = Depends(get_db)
):
    """Get sent contact requests"""
    result = get_sent_contact_requests_service(db, current_user.id, page, size)
    return CustomResponse(content=result).format_data_get()

@router.post("/requests/{request_id}/approve", dependencies=[Depends(jwt_auth)], tags=tags)
def approve_contact_request(
    request_id: int,
    current_user: UserModel = Depends(is_authenticated),
    db: Session = Depends(get_db)
):
    """Approve a contact request"""
    result = approve_contact_request_service(db, request_id, current_user.id)
    if not result:
        raise CustomException(content={
            "error": ["Contact request not found"]
        }).not_found_exception()
    return CustomResponse(content=result).format_data_update()

@router.post("/requests/{request_id}/reject", dependencies=[Depends(jwt_auth)], tags=tags)
def reject_contact_request(
    request_id: int,
    current_user: UserModel = Depends(is_authenticated),
    db: Session = Depends(get_db)
):
    """Reject a contact request"""
    result = reject_contact_request_service(db, request_id, current_user.id)
    if not result:
        raise CustomException(content={
            "error": ["Contact request not found"]
        }).not_found_exception()
    return CustomResponse(content=result).format_data_update()

@router.post("/requests/{request_id}/canceled", dependencies=[Depends(jwt_auth)], tags=tags)
def cancel_contact_request(
    request_id: int,
    current_user: UserModel = Depends(is_authenticated),
    db: Session = Depends(get_db)
):
    """Cancel a contact request"""
    result = cancel_contact_request_service(db, request_id, current_user.id)
    if not result:
        raise CustomException(content={
            "error": ["Contact request not found"]
        }).not_found_exception()
    return CustomResponse(content=result).format_data_update()

@router.post("/refer", response_model=ReferContactResponse, dependencies=[Depends(jwt_auth)], tags=tags)
def refer_contact(
    data: ReferContactRequest,
    current_user: UserModel = Depends(is_authenticated),
    db: Session = Depends(get_db)
):
    result = refer_contact_service(db, current_user.id, data)
    return CustomResponse(content=result).format_data_create()

@router.get("/suggestions/", dependencies=[Depends(jwt_auth)], tags=tags)
def get_contact_suggestions(
    filters: SuggestionContactFilter = Depends(),
    current_user: UserModel = Depends(is_authenticated),
    db: Session = Depends(get_db)
):
    """Get personalized contact suggestions for the current user"""
    result = get_contact_suggestions_service(db, current_user.id, filters)
    return CustomResponse(content=result).format_data_get()

@router.post("/suggestions/{user_dismiss_id}/dismiss", dependencies=[Depends(jwt_auth)], tags=tags)
def dismiss_contact_suggestion(
    user_dismiss_id: int,
    current_user: UserModel = Depends(is_authenticated),
    db: Session = Depends(get_db)
):
    """
    Dismiss a contact suggestion for 48 hours.
    """
    result = dismiss_contact_suggestion_service(db, current_user.id, user_dismiss_id)
    return CustomResponse(content=result).format_data_create()

@router.get("/map-view/", response_model=ContactMapPaginatedResponse, dependencies=[Depends(jwt_auth)], tags=tags)
def contact_map_view(
    filters: ContactMapFilter = Depends(),
    current_user: UserModel = Depends(is_authenticated),
    db: Session = Depends(get_db)
):
    """
    List nearby contacts for map view (availability + visibility required)
    """
    result = list_contact_map_service(db, current_user.id, filters)
    return CustomResponse(content=result).format_data_get()
