import json
from typing import Union
from sqlalchemy.orm import Session, joinedload, selectinload
from sqlalchemy import or_, and_, desc
from typing import List, Optional, Union
from datetime import datetime, timedelta, timezone
import json
from src.utils.exceptions import CustomException
from src.user.models import User, UserProfile, BusinessProfile
from src.contact.models import Contact, ContactRequest, ContactRequestStatusEnum, ContactReferralLog
from src.event.models import EventInvitation as EventInvitationModel, Event
from src.lead.models import Lead
from .schemas import ContactCreateSchemas, ContactFilter, ContactUpdateSchemas, ContactResponseSchemas, ContactListResponseSchemas, PaginatedContactListResponse, ContactRequestCreate, PaginatedContactRequestResponse, ContactRequestResponse
from src.contact.schemas import SuggestionContactResponse, PaginatedSuggestionContactResponse, SuggestionContextEnum, SuggestionContextPriorityEnum, SuggestionContactFilter, ContactMapFilter, ContactMapPaginatedResponse, ContactMapBase
from sqlalchemy.sql import func
from src.contact.models import ContactDismissSuggestion
from geopy.distance import geodesic

def create_contact_service(db: Session, user_id: int, contact: ContactCreateSchemas) -> ContactResponseSchemas:
    # Validate if user_contact_id exists and is a valid user
    user_contact = db.query(User).filter(
        User.id == contact.user_contact_id,
        User.is_deleted == False
    ).first()
    
    if not user_contact:
        raise CustomException(content={
            "error": ["User not found"]
        }).bad_request_exception()

    # Check if contact already exists
    existing_contact = db.query(Contact).filter(
        Contact.user_id == user_id,
        Contact.user_contact_id == contact.user_contact_id,
        Contact.is_deleted == False
    ).first()

    if existing_contact:
        raise CustomException(content={
            "error": ["Contact already exists"]
        }).bad_request_exception()

    # Create new contact
    db_contact = Contact(
        user_id=user_id,
        user_contact_id=contact.user_contact_id,
        nickname=contact.nickname,
        notes=contact.notes,
        tags=json.dumps(contact.tags) if contact.tags else None,
        is_favorite=contact.is_favorite
    )
    db.add(db_contact)
    db.commit()
    db.refresh(db_contact)
    
    # Convert to response schema
    contact_data = {
        "id": db_contact.id,
        "user_id": db_contact.user_id,
        "user_contact_id": db_contact.user_contact_id,
        "nickname": db_contact.nickname,
        "is_favorite": db_contact.is_favorite,
        "notes": db_contact.notes,
        "tags": _parse_tags_safely(db_contact.tags) if db_contact.tags else None,
        "last_contact_date": db_contact.last_contact_date,
        "created_at": db_contact.created_at,
        "updated_at": db_contact.updated_at,
        "is_deleted": db_contact.is_deleted,
        "user_contact": {
            "id": user_contact.id,
            "email": user_contact.email,
            "user_type": user_contact.user_type,
            "avatar": user_contact.avatar,
        },
        "profile": None,
        "business_profile": None,
    }
    
    return ContactResponseSchemas.model_validate(contact_data)

def get_contact_service(db: Session, contact_id: int, user_id: int) -> Optional[Contact]:
    return db.query(Contact).options(
        joinedload(Contact.user_contact).joinedload(User.profile),
        joinedload(Contact.user_contact).joinedload(User.business_profile),
    ).filter(
        Contact.id == contact_id,
        Contact.user_id == user_id,
        Contact.is_deleted == False
    ).first()

def _parse_tags_safely(tags_str):
    """Parse tags JSON string to list safely"""
    if not tags_str:
        return None
    try:
        return json.loads(tags_str)
    except (json.JSONDecodeError, TypeError):
        return None

def _convert_profile_to_dict(profile):
    """Convert UserProfile object to dict safely"""
    if not profile:
        return None
    
    return {
        "full_name": getattr(profile, 'full_name', None),
        "gender": getattr(profile, 'gender', None),
        "address": getattr(profile, 'address', None),
        "date_of_birth": getattr(profile, 'date_of_birth', None),
        "title": getattr(profile, 'title', None),
        "bio": getattr(profile, 'bio', None),
        "notes": getattr(profile, 'notes', None),
        "visibility": "PUBLIC" if getattr(profile, 'public_profile', False) else "PRIVATE",
        "latitude": getattr(profile, 'lattitude', None),
        "longitude": getattr(profile, 'longitude', None),
        "public_profile": getattr(profile, 'public_profile', False),
        "social_media_links": getattr(profile, 'social_media_links', None),
        "industry": getattr(profile, 'industry_tags', None),
    }

def _convert_business_profile_to_dict(business_profile):
    """Convert BusinessProfile object to dict safely"""
    if not business_profile:
        return None
    
    return {
        "title": getattr(business_profile, 'business_name', None),
        "company": getattr(business_profile, 'business_name', None),
        "company_address": getattr(business_profile, 'business_address', None),
        "company_description": getattr(business_profile, 'business_description', None),
        "industry_tags": getattr(business_profile, 'industry_tags', None),
    }

def get_contact_detail_service(db: Session, contact_id: int, user_id: int) -> Optional[ContactResponseSchemas]:
    contact = get_contact_service(db, contact_id, user_id)
    if not contact:
        return None
    
    # Convert contact object to dict with proper user_contact data
    contact_data = {
        "id": contact.id,
        "user_id": contact.user_id,
        "user_contact_id": contact.user_contact_id,
        "nickname": contact.nickname,
        "is_favorite": contact.is_favorite,
        "notes": contact.notes,
        "tags": _parse_tags_safely(contact.tags) if contact.tags else None,
        "last_contact_date": contact.last_contact_date,
        "created_at": contact.created_at,
        "updated_at": contact.updated_at,
        "is_deleted": contact.is_deleted,
        "user_contact": {
            "id": contact.user_contact.id,
            "email": contact.user_contact.email,
            "user_type": contact.user_contact.user_type,
            "avatar": contact.user_contact.avatar,
        } if contact.user_contact else None,
        "profile": _convert_profile_to_dict(contact.user_contact.profile if contact.user_contact else None),
        "business_profile": _convert_business_profile_to_dict(contact.user_contact.business_profile if contact.user_contact else None),
    }
    
    return ContactResponseSchemas.model_validate(contact_data)

def get_contacts_service(
    db: Session, 
    user_id: int,
    filters: ContactFilter
) -> PaginatedContactListResponse:
    """
    Get paginated contacts for a user, applying filters from ContactFilter.
    """
    from sqlalchemy import or_, and_
    # Extract filter params safely from ContactFilter
    page = getattr(filters, "page", 1) or 1
    size = getattr(filters, "size", 10) or 10
    search = getattr(filters, "search", None)
    is_favorite = getattr(filters, "is_favorite", None)

    # Validate pagination parameters
    if page < 1:
        page = 1
    if size < 1:
        size = 10
    if size > 100:  # Limit maximum page size
        size = 100

    # Calculate skip and limit
    skip = (page - 1) * size
    limit = size

    # Build base query with joins
    query = db.query(
        Contact,
        User.email,
        User.user_type,
        UserProfile.full_name,
        User.avatar,
        BusinessProfile.business_name,
        BusinessProfile.business_address,
        BusinessProfile.business_description,
        UserProfile.lattitude,
        UserProfile.longitude
    ).join(
        Contact.user_contact
    ).outerjoin(
        UserProfile, User.id == UserProfile.user_id
    ).outerjoin(
        BusinessProfile, User.id == BusinessProfile.user_id
    ).outerjoin(
        Lead, (Lead.user_id == user_id) & (Lead.contact_id == Contact.id)
    )

    # Apply base filters
    query = query.filter(
        Contact.user_id == user_id, 
        Contact.is_deleted == False,
        Lead.id.is_(None)  # Exclude contacts that are already leads
    )

    # Apply is_favorite filter if provided
    if is_favorite is not None:
        query = query.filter(Contact.is_favorite == is_favorite)

    # Apply search filter if provided
    if search:
        search_pattern = f"%{search}%"
        query = query.filter(or_(
            Contact.nickname.ilike(search_pattern),
            UserProfile.full_name.ilike(search_pattern),
            BusinessProfile.business_name.ilike(search_pattern)
        ))

    # Get total count
    total = query.count()
    
    # Calculate total pages
    total_pages = (total + size - 1) // size if size else 1

    # Execute query with pagination
    results = query.offset(skip).limit(limit).all()

    # Transform results
    contacts = []
    for result in results:
        # unpacking with new fields
        (contact, email, user_type, full_name, avatar, business_name, business_address, business_description, profile_lat, profile_long) = result
        
        # Build user_contact dict
        user_contact = {
            "id": contact.user_contact_id,
            "email": email,
            "user_type": user_type,
            "full_name": full_name,
            "avatar": avatar,
            "lattitude": profile_lat,
            "longitude": profile_long
        }

        # Add company info if exists
        if business_name or business_address or business_description:
            user_contact["business_info"] = {
                "business_name": business_name,
                "business_address": business_address,
                "business_description": business_description
            }

        # Build contact dict
        contact_dict = {
            "id": contact.id,
            "nickname": contact.nickname,
            "notes": contact.notes,
            "tags": _parse_tags_safely(contact.tags) if contact.tags else None,
            "is_favorite": contact.is_favorite,
            "last_contact_date": contact.last_contact_date,
            "created_at": contact.created_at,
            "updated_at": contact.updated_at,
            "user_contact": user_contact
        }
        
        contacts.append(ContactListResponseSchemas(**contact_dict))

    return PaginatedContactListResponse(
        items=contacts,
        total=total,
        page=page,
        size=size,
        total_pages=total_pages
    )

def update_contact_service(
    db: Session,
    contact_id: int,
    user_id: int,
    contact: ContactUpdateSchemas
) -> Optional[ContactResponseSchemas]:
    db_contact = get_contact_service(db, contact_id, user_id)
    if not db_contact:
        return None

    update_data = contact.model_dump(exclude_unset=True)
    if "tags" in update_data and update_data["tags"] is not None:
        update_data["tags"] = json.dumps(update_data["tags"])

    for key, value in update_data.items():
        setattr(db_contact, key, value)

    db_contact.updated_at = datetime.now()
    db.commit()
    db.refresh(db_contact)
    
    # Convert to response schema
    contact_data = {
        "id": db_contact.id,
        "user_id": db_contact.user_id,
        "user_contact_id": db_contact.user_contact_id,
        "nickname": db_contact.nickname,
        "is_favorite": db_contact.is_favorite,
        "notes": db_contact.notes,
        "tags": _parse_tags_safely(db_contact.tags) if db_contact.tags else None,
        "last_contact_date": db_contact.last_contact_date,
        "created_at": db_contact.created_at,
        "updated_at": db_contact.updated_at,
        "is_deleted": db_contact.is_deleted,
        "user_contact": {
            "id": db_contact.user_contact.id,
            "email": db_contact.user_contact.email,
            "user_type": db_contact.user_contact.user_type,
            "avatar": db_contact.user_contact.avatar,
        } if db_contact.user_contact else None,
        "profile": _convert_profile_to_dict(db_contact.user_contact.profile if db_contact.user_contact else None),
        "business_profile": _convert_business_profile_to_dict(db_contact.user_contact.business_profile if db_contact.user_contact else None),
    }
    
    return ContactResponseSchemas.model_validate(contact_data)

def delete_contact_service(db: Session, contact_id: int, user_id: int) -> bool:
    db_contact = get_contact_service(db, contact_id, user_id)
    if not db_contact:
        return False

    db_contact.is_deleted = True
    db_contact.updated_at = datetime.now()

    reverse_contact = db.query(Contact).filter(
        Contact.user_id == db_contact.user_contact_id,
        Contact.user_contact_id == db_contact.user_id,
        Contact.is_deleted == False
    ).first()
    if reverse_contact:
        reverse_contact.is_deleted = True
        reverse_contact.updated_at = datetime.now()

    db.commit()
    return True

def update_last_contact_date_service(db: Session, contact_id: int, user_id: int) -> Optional[Contact]:
    db_contact = get_contact_service(db, contact_id, user_id)
    if not db_contact:
        return None

    db_contact.last_contact_date = datetime.now()
    db_contact.updated_at = datetime.now()
    db.commit()
    db.refresh(db_contact)
    return db_contact 


def find_contact_with_code_service(db: Session, code: str, user_id: int) -> Optional[Contact]:
    return db.query(Contact).options(
        joinedload(Contact.user_contact).joinedload(User.profile),
        joinedload(Contact.user_contact).joinedload(User.business_profile)
    ).filter(
        Contact.code == code,
        Contact.user_id == user_id,
        Contact.is_deleted == False
    ).first()

def get_available_contacts_for_event_service(
    db: Session, 
    user_id: int,
    event_id: int,
    search: Optional[str] = None,
    page: int = 1,
    size: int = 10
) -> PaginatedContactListResponse:
    # Get event to check if it exists and belongs to user
    event = db.query(Event).filter(
        Event.id == event_id,
        Event.created_by == user_id,
        Event.is_deleted == False
    ).first()
    
    if not event:
        raise CustomException().not_found_exception()
    
    # Get all contacts that haven't been invited yet
    query = db.query(
        Contact,
        User.email,
        User.user_type,
        UserProfile.full_name,
        User.avatar,
        BusinessProfile.business_name,
        BusinessProfile.business_address,
        BusinessProfile.business_description
    ).join(
        Contact.user_contact
    ).outerjoin(
        UserProfile, User.id == UserProfile.user_id
    ).outerjoin(
        BusinessProfile, User.id == BusinessProfile.user_id
    ).outerjoin(
        EventInvitationModel,
        and_(
            EventInvitationModel.contact_id == Contact.id,
            EventInvitationModel.event_id == event_id
        )
    ).filter(
        Contact.user_id == user_id,
        Contact.is_deleted == False,
        EventInvitationModel.id == None  # Only get contacts that haven't been invited
    )
    
    # Apply search filter if provided
    if search:
        search_term = f"%{search}%"
        query = query.filter(
            or_(
                UserProfile.full_name.ilike(search_term),
                User.email.ilike(search_term),
                Contact.nickname.ilike(search_term),
                BusinessProfile.business_name.ilike(search_term)
            )
        )
    
    # Get total count
    total = query.count()
    
    # Calculate pagination
    total_pages = (total + size - 1) // size
    skip = (page - 1) * size
    
    # Execute query with pagination
    results = query.offset(skip).limit(size).all()
    
    # Format response
    contacts = []
    for result in results:
        contact, email, user_type, full_name, avatar, business_name, business_address, business_description = result
        
        # Build user_contact dict
        user_contact = {
            "id": contact.user_contact_id,
            "email": email,
            "user_type": user_type,
            "full_name": full_name,
            "avatar": avatar
        }

        # Add company info if exists
        if business_name or business_address or business_description:
            user_contact["business_info"] = {
                "business_name": business_name,
                "business_address": business_address,
                "business_description": business_description
            }

        # Build contact dict
        contact_dict = {
            "id": contact.id,
            "nickname": contact.nickname,
            "notes": contact.notes,
            "tags": _parse_tags_safely(contact.tags) if contact.tags else None,
            "is_favorite": contact.is_favorite,
            "last_contact_date": contact.last_contact_date,
            "created_at": contact.created_at,
            "updated_at": contact.updated_at,
            "user_contact": user_contact
        }
        
        contacts.append(ContactListResponseSchemas(**contact_dict))
    
    return PaginatedContactListResponse(
        items=contacts,
        total=total,
        page=page,
        size=size,
        total_pages=total_pages
    )

def create_contact_request_service(
    db: Session,
    from_user_id: int,
    request_data: ContactRequestCreate
) -> ContactRequest:
        # Check if to_user exists
        to_user = db.query(User).filter(User.id == request_data.to_user_id).first()
        if not to_user:
            raise CustomException().not_found_exception(
                data={
                    "error": ["User not found"]
                }
            )
        
        # Check if request already exists
        existing_request = db.query(ContactRequest).filter(
            ContactRequest.from_user_id == from_user_id,
            ContactRequest.to_user_id == request_data.to_user_id,
            ContactRequest.status == ContactRequestStatusEnum.PENDING
        ).first()
        
        if existing_request:
            raise CustomException(content={"error": "Contact request already sent"}).bad_request_exception()
        
        # Check if already contacts (including soft deleted ones)
        existing_contact = db.query(Contact).filter(
            Contact.user_id == from_user_id,
            Contact.user_contact_id == request_data.to_user_id,
            Contact.is_deleted == False
        ).first()
        
        if existing_contact and not existing_contact.is_deleted:
            raise CustomException(content={"error": "Already contacts"}).bad_request_exception()
        
        # Create request
        contact_request = ContactRequest(
            from_user_id=from_user_id,
            to_user_id=request_data.to_user_id,
            message=request_data.message
        )
        db.add(contact_request)
        db.commit()
        db.refresh(contact_request)
        
        return contact_request

def batch_mutual_connections_2way(db, user_id, other_user_ids):
    all_user_ids = set(other_user_ids) | {user_id}
    contacts = db.query(Contact.user_id, Contact.user_contact_id).filter(
        Contact.user_id.in_(all_user_ids),
        Contact.is_deleted == False
    ).all()
    user_contacts = {}
    for uid, cid in contacts:
        if uid not in user_contacts:
            user_contacts[uid] = set()
        if cid is not None:
            user_contacts[uid].add(cid)
    mutual_candidates_dict = {}
    for other_id in other_user_ids:
        a_contacts = user_contacts.get(user_id, set())
        b_contacts = user_contacts.get(other_id, set())
        # Loại bỏ user_id và other_id khỏi mutual_candidates
        mutual_candidates = (a_contacts & b_contacts) - {user_id, other_id}
        mutual_candidates_dict[other_id] = mutual_candidates
    all_mutual_candidates = set()
    for s in mutual_candidates_dict.values():
        all_mutual_candidates.update(s)
    reverse_contacts = db.query(Contact.user_id, Contact.user_contact_id).filter(
        Contact.user_id.in_(all_mutual_candidates),
        Contact.user_contact_id.in_(all_user_ids),
        Contact.is_deleted == False
    ).all()
    reverse_contacts_dict = {}
    for x_id, contact_id in reverse_contacts:
        if x_id not in reverse_contacts_dict:
            reverse_contacts_dict[x_id] = set()
        reverse_contacts_dict[x_id].add(contact_id)
    result = {}
    for other_id in other_user_ids:
        count = 0
        for x in mutual_candidates_dict[other_id]:
            # Loại bỏ trường hợp x là user_id hoặc other_id (phòng trường hợp dữ liệu dư thừa)
            if x == user_id or x == other_id:
                continue
            if reverse_contacts_dict.get(x, set()) >= {user_id, other_id}:
                count += 1
        result[other_id] = count
    return result

def get_received_contact_requests_service(
    db: Session,
    user_id: int,
    page: int = 1,
    size: int = 10
) -> dict:
    # Build query
    query = db.query(ContactRequest).filter(
        ContactRequest.to_user_id == user_id,
        ContactRequest.status == ContactRequestStatusEnum.PENDING
    ).options(
        joinedload(ContactRequest.from_user).joinedload(User.profile)
    ).order_by(
        desc(ContactRequest.created_at)
    )

    from_user_ids = [r.from_user_id for r in query.all()]
    mutual_dict = batch_mutual_connections_2way(db, user_id, from_user_ids)

    total = query.count()
    total_pages = (total + size - 1) // size
    skip = (page - 1) * size
    requests = query.offset(skip).limit(size).all()

    items = []
    for request in requests:
        request_dict = {
            "id": request.id,
            "from_user_id": request.from_user_id,
            "to_user_id": request.to_user_id,
            "status": request.status,
            "message": request.message,
            "created_at": request.created_at,
            "updated_at": request.updated_at,
            "contact_request": {
                "id": request.from_user.id,
                "email": request.from_user.email,
                "full_name": request.from_user.profile.full_name if request.from_user.profile else None,
                "avatar": request.from_user.avatar,
                "user_type": request.from_user.user_type,
                "profile": None if not request.from_user.profile else {
                    **{k: (v.isoformat() if k == "date_of_birth" and v is not None else v) for k, v in request.from_user.profile.__dict__.items() if not k.startswith("_")},
                },
                "job_title": request.from_user.profile.title if request.from_user.profile else None,
            },
            "mutual_connections": mutual_dict.get(request.from_user_id, 0)
        }
        items.append(ContactRequestResponse(**request_dict))

    return PaginatedContactRequestResponse(
        items=items,
        total=total,
        page=page,
        size=size,
        total_pages=total_pages
    )

def get_sent_contact_requests_service(
    db: Session,
    user_id: int,
    page: int = 1,
    size: int = 10
) -> dict:
    # Build query
    query = db.query(ContactRequest).filter(
        ContactRequest.from_user_id == user_id,
        ContactRequest.status == ContactRequestStatusEnum.PENDING
    ).options(
        joinedload(ContactRequest.to_user).joinedload(User.profile)
    ).order_by(
        desc(ContactRequest.created_at)
    )

    to_user_ids = [r.to_user_id for r in query.all()]
    mutual_dict = batch_mutual_connections_2way(db, user_id, to_user_ids)

    total = query.count()
    total_pages = (total + size - 1) // size
    skip = (page - 1) * size
    requests = query.offset(skip).limit(size).all()

    items = []
    for request in requests:
        request_dict = {
            "id": request.id,
            "from_user_id": request.from_user_id,
            "to_user_id": request.to_user_id,
            "status": request.status,
            "message": request.message,
            "created_at": request.created_at,
            "updated_at": request.updated_at,
            "contact_request": {
                "id": request.to_user.id,
                "email": request.to_user.email,
                "full_name": request.to_user.profile.full_name if request.to_user.profile else None,
                "avatar": request.to_user.avatar,
                "user_type": request.to_user.user_type,
                "profile": None if not request.to_user.profile else {
                    **{k: (v.isoformat() if k == "date_of_birth" and v is not None else v) for k, v in request.to_user.profile.__dict__.items() if not k.startswith("_")},
                },
                "job_title": request.to_user.profile.title if request.to_user.profile else None,
            },
            "mutual_connections": mutual_dict.get(request.to_user_id, 0)
        }
        items.append(ContactRequestResponse(**request_dict))

    return PaginatedContactRequestResponse(
        items=items,
        total=total,
        page=page,
        size=size,
        total_pages=total_pages
    )

def approve_contact_request_service(
    db: Session,
    request_id: int,
    user_id: int
) -> ContactRequest:
        # Get request
        request = db.query(ContactRequest).filter(
            ContactRequest.id == request_id,
            ContactRequest.to_user_id == user_id,
            ContactRequest.status == ContactRequestStatusEnum.PENDING
        ).first()
        
        if not request:
            raise CustomException(content={"error": "Contact request not found"}).not_found_exception()
        
        # Update request status
        request.status = ContactRequestStatusEnum.APPROVED
        db.add(request)
        
        # Check if contacts already exist (including soft deleted ones)
        existing_contact1 = db.query(Contact).filter(
            Contact.user_id == request.from_user_id,
            Contact.user_contact_id == request.to_user_id,
            Contact.is_deleted == False
        ).first()
        
        existing_contact2 = db.query(Contact).filter(
            Contact.user_id == request.to_user_id,
            Contact.user_contact_id == request.from_user_id,
            Contact.is_deleted == False
        ).first()
        
        # Handle contact1
        if not existing_contact1:
            # Create new contact
            contact1 = Contact(
                user_id=request.from_user_id,
                user_contact_id=request.to_user_id
            )
            db.add(contact1)
        elif existing_contact1.is_deleted:
            # Restore soft deleted contact
            existing_contact1.is_deleted = False
            existing_contact1.updated_at = datetime.now()
            db.add(existing_contact1)
        
        # Handle contact2
        if not existing_contact2:
            # Create new contact
            contact2 = Contact(
                user_id=request.to_user_id,
                user_contact_id=request.from_user_id
            )
            db.add(contact2)
        elif existing_contact2.is_deleted:
            # Restore soft deleted contact
            existing_contact2.is_deleted = False
            existing_contact2.updated_at = datetime.now()
            db.add(existing_contact2)
        
        db.commit()
        db.refresh(request)
        
        return request

def reject_contact_request_service(
    db: Session,
    request_id: int,
    user_id: int
) -> ContactRequest:
        # Get request
        request = db.query(ContactRequest).filter(
            ContactRequest.id == request_id,
            ContactRequest.to_user_id == user_id,
            ContactRequest.status == ContactRequestStatusEnum.PENDING
        ).first()
        
        if not request:
            raise CustomException(content={"error": "Contact request not found"}).not_found_exception()
        
        # Update request status
        request.status = ContactRequestStatusEnum.REJECTED
        db.add(request)
        db.commit()
        db.refresh(request)
        
        return request

def cancel_contact_request_service(
    db: Session,
    request_id: int,
    user_id: int
) -> ContactRequest:
        # Get request
        request = db.query(ContactRequest).filter(
            ContactRequest.id == request_id,
            ContactRequest.from_user_id == user_id,
            ContactRequest.status == ContactRequestStatusEnum.PENDING
        ).first()
        
        if not request:
            raise CustomException(content={"error": "Contact request not found"}).not_found_exception()
        
        # Update request status
        request.status = ContactRequestStatusEnum.CANCELED
        db.add(request)
        db.commit()
        db.refresh(request)
        
        return request

def refer_contact_service(db: Session, sender_id: int, data) -> dict:
    from datetime import datetime, timedelta, timezone
    from src.user.models import User
    # 1. Kiểm tra contact_id phải là contact của sender
    contact = db.query(Contact).filter(
        Contact.id == data.contact_id,
        Contact.user_id == sender_id,
        Contact.is_deleted == False
    ).first()
    if not contact:
        raise CustomException(content={"error": "Contact to refer not found or not your contact."}).bad_request_exception()
    # 2. Kiểm tra recipient_ids phải là contact của sender
    sender_contacts = db.query(Contact).filter(
        Contact.user_id == sender_id,
        Contact.is_deleted == False
    ).all()
    sender_contact_user_ids = set(c.user_contact_id for c in sender_contacts if c.user_contact_id)
    failed = []
    success = 0
    # 3. Kiểm tra referral permission của contact được refer
    referred_user = db.query(User).filter(User.id == contact.user_contact_id).first()
    if not referred_user:
        raise CustomException(content={"error": "Referred user not found."}).bad_request_exception()
    if hasattr(referred_user, "referral_visibility") and not referred_user.referral_visibility:
        raise CustomException(content={"error": "This contact does not allow referrals."}).bad_request_exception()
    # 4. Lặp qua từng recipient để kiểm tra và gửi referral
    for rid in data.recipient_ids:
        # a. Recipient phải là contact của sender
        if rid not in sender_contact_user_ids:
            failed.append({"recipient_id": rid, "reason": "Recipient is not your contact."})
            continue
        # b. Không gửi nếu recipient đã có contact này
        existing = db.query(Contact).filter(
            Contact.user_id == rid,
            Contact.user_contact_id == contact.user_contact_id,
            Contact.is_deleted == False
        ).first()
        if existing:
            failed.append({"recipient_id": rid, "reason": "Recipient already has this contact."})
            continue
        # c. Không gửi duplicate trong 24h
        twenty_four_hours_ago = datetime.now(timezone.utc) - timedelta(hours=24)
        duplicate = db.query(ContactReferralLog).filter(
            ContactReferralLog.sender_id == sender_id,
            ContactReferralLog.contact_id == contact.id,
            ContactReferralLog.recipient_id == rid,
            ContactReferralLog.created_at >= twenty_four_hours_ago
        ).first()
        if duplicate:
            failed.append({"recipient_id": rid, "reason": "Duplicate referral in 24h is not allowed."})
            continue
        # d. Gửi notification (TODO: implement notification)
        # e. Ghi log referral
        log = ContactReferralLog(
            sender_id=sender_id,
            contact_id=contact.id,
            recipient_id=rid,
            created_at=datetime.now(timezone.utc)
        )
        db.add(log)
        # f. Không tự động add contact
        # => Nếu qua hết, tính là thành công
        success += 1
    db.commit()
    msg = f"{referred_user.email if referred_user else 'Contact'} was referred to {success} people."
    return {
        "success_count": success,
        "failed": failed,
        "message": msg
    }

def get_contact_suggestions_service(
    db: Session,
    user_id: int,
    filter: SuggestionContactFilter = None,
    exclude_dismissed: bool = True
) -> PaginatedSuggestionContactResponse:
    """
    Trả về danh sách gợi ý kết nối cho user_id, loại trừ contact đã có, request pending, user đã dismiss trong 48h.
    Sắp xếp theo similarity_score giảm dần, ưu tiên context có priority nhỏ hơn.
    Hỗ trợ filter theo SuggestionContactFilter nếu truyền vào.
    """
    # 1. Lấy danh sách user đã là contact hoặc đã gửi/nhận request
    # Lấy danh sách user_contact_id đã là contact (không bị xóa)
    contact_ids = set()
    contact_row_ids = set()
    for row in db.query(Contact.user_contact_id, Contact.id).filter(Contact.user_id == user_id, Contact.is_deleted == False).all():
        if row.user_contact_id is not None:
            contact_ids.add(row.user_contact_id)
            contact_row_ids.add(row.id)

    # Lấy danh sách user đã gửi request (pending)
    sent_ids = set(
        row.to_user_id
        for row in db.query(ContactRequest.to_user_id)
        .filter(
            ContactRequest.from_user_id == user_id,
            ContactRequest.status == ContactRequestStatusEnum.PENDING
        )
        .all()
        if row.to_user_id is not None
    )

    # Lấy danh sách user đã nhận request (pending)
    received_ids = set(
        row.from_user_id
        for row in db.query(ContactRequest.from_user_id)
        .filter(
            ContactRequest.to_user_id == user_id,
            ContactRequest.status == ContactRequestStatusEnum.PENDING
        )
        .all()
        if row.from_user_id is not None
    )
    exclude_ids = contact_ids | sent_ids | received_ids | {user_id}

    # 2. Loại trừ user đã dismiss trong 48h (nếu có bảng lưu dismiss, ví dụ ContactSuggestionDismiss)
    if exclude_dismissed:
        now = datetime.now(timezone.utc)
        rows = db.query(ContactDismissSuggestion.user_dismiss_id).filter(
            ContactDismissSuggestion.user_id == user_id,
            ContactDismissSuggestion.dismissed_to >= now
        ).all()
        dismissed_ids = set(r[0] for r in rows)
        exclude_ids |= dismissed_ids

    # 3. Lấy tất cả user còn lại, áp dụng filter.search nếu có
    query = db.query(User).filter(
        User.id.notin_(exclude_ids),
        User.is_deleted == False
    )

    # Áp dụng filter.search (tìm theo tên hoặc email)
    if filter and filter.search:
        search = f"%{filter.search.lower()}%"
        query = query.join(UserProfile, User.profile).filter(
            (UserProfile.full_name.ilike(search)) | (User.email.ilike(search))
        )

    candidates = query.all()

    # 4. Chuẩn bị dữ liệu so sánh
    # 4.1 Mutual connections
    all_candidate_ids = [u.id for u in candidates]
    mutual_dict = batch_mutual_connections_2way(db, user_id, all_candidate_ids) if all_candidate_ids else {}

    # 4.2 Shared event (dùng EventInvitation + Contact)
    user_events = set(
        row[0]
        for row in db.query(EventInvitationModel.event_id)
        .filter(EventInvitationModel.contact_id.in_(contact_row_ids))
        .all()
    )

    # Lấy ra mapping: event_id -> set(user_id) cho tất cả candidate (và user hiện tại)
    # 1. Lấy contact_id của tất cả candidate (và user hiện tại)
    all_user_ids = list(all_candidate_ids) + [user_id]
    contact_ids_by_user = {}
    contact_rows = db.query(Contact.user_id, Contact.id).filter(
        Contact.user_id.in_(all_user_ids),
        Contact.is_deleted == False
    ).all()
    for uid, cid in contact_rows:
        contact_ids_by_user.setdefault(uid, set()).add(cid)

    # 2. Lấy tất cả EventInvitation cho các contact_id này
    all_contact_ids = [cid for cids in contact_ids_by_user.values() for cid in cids]
    event_inv_rows = db.query(EventInvitationModel.event_id, EventInvitationModel.contact_id).filter(
        EventInvitationModel.contact_id.in_(all_contact_ids)
    ).all()

    # 3. Xây dựng mapping: event_id -> set(user_id)
    event_to_user_ids = {}
    contact_id_to_user_id = {}
    for uid, cids in contact_ids_by_user.items():
        for cid in cids:
            contact_id_to_user_id[cid] = uid
    for event_id, contact_id in event_inv_rows:
        uid = contact_id_to_user_id.get(contact_id)
        if uid is not None:
            event_to_user_ids.setdefault(event_id, set()).add(uid)

    # 4. Với mỗi candidate, lấy ra các event_id mà candidate join cùng user hiện tại
    candidate_events = {}
    for cid in all_candidate_ids:
        shared_events = set()
        for event_id in user_events:
            if cid in event_to_user_ids.get(event_id, set()):
                shared_events.add(event_id)
        candidate_events[cid] = shared_events
    # 4.3 Job title, industry, location
    user_profile = db.query(UserProfile).filter(UserProfile.user_id == user_id).first()

    # 5. Tính điểm và context
    suggestions = []
    for u in candidates:
        score = 0.0
        context_candidates = []
        # Mutual connections
        mutual = mutual_dict.get(u.id, 0)
        if mutual > 0:
            score += 100 + mutual * 10
            context_candidates.append(SuggestionContextEnum.MUTUAL_CONNECTION)
        # Shared event
        shared_events = user_events & candidate_events.get(u.id, set()) if user_events else set()
        if shared_events:
            score += 50 + len(shared_events) * 5
            context_candidates.append(SuggestionContextEnum.SHARED_EVENT)
        # Job title
        if user_profile and u.profile and user_profile.title and u.profile.title:
            if user_profile.title.lower() in u.profile.title.lower() or u.profile.title.lower() in user_profile.title.lower():
                score += 20
                context_candidates.append(SuggestionContextEnum.JOB_TITLE)
        # Industry
        # Lưu ý: industry có thể là list hoặc string, nên kiểm tra kỹ
        user_industry = getattr(user_profile, "industry_tags", None)
        candidate_industry = getattr(u.profile, "industry_tags", None)
        if user_profile and u.profile and user_industry and candidate_industry:
            # Nếu là list, kiểm tra giao nhau
            if isinstance(user_industry, list) and isinstance(candidate_industry, list):
                if set(user_industry) & set(candidate_industry):
                    score += 10
                    context_candidates.append(SuggestionContextEnum.INDUSTRY)
            else:
                if user_industry == candidate_industry:
                    score += 10
                    context_candidates.append(SuggestionContextEnum.INDUSTRY)
        # Location (giả sử có lat/long và visibility)
        if user_profile and u.profile and u.profile.is_visibled_map and getattr(user_profile, "lattitude", None) and getattr(user_profile, "longitude", None) and getattr(u.profile, "lattitude", None) and getattr(u.profile, "longitude", None):
            dist = geodesic((user_profile.lattitude, user_profile.longitude), (u.profile.lattitude, u.profile.longitude)).km
            if dist < 50:
                score += 5
                context_candidates.append(SuggestionContextEnum.LOCATION)
        # Nếu không có context nào thì NONE
        if not context_candidates:
            context_candidates.append(SuggestionContextEnum.NONE)
        # Lấy context có priority nhỏ nhất
        context = min(context_candidates, key=lambda c: SuggestionContextPriorityEnum[c.name])
        # Nếu filter.match_context được truyền vào, chỉ lấy đúng context
        if filter and filter.match_context and context != filter.match_context:
            continue
        suggestions.append(SuggestionContactResponse(
            id=u.id,
            avatar=u.avatar,
            full_name=u.profile.full_name if u.profile else u.email,
            job_title=u.profile.title if u.profile else None,
            match_context=context,
            similarity_score=score,
            industry_tags=getattr(u.profile, 'industry_tags', None) if u.profile else None,
            lattitude=getattr(u.profile, 'lattitude', None) if u.profile else None,
            longitude=getattr(u.profile, 'longitude', None) if u.profile else None,
            mutual_connections=mutual_dict.get(u.id, 0)
        ))

    # Sắp xếp: điểm giảm dần, nếu bằng nhau thì context priority tăng dần
    suggestions.sort(key=lambda x: (-x.similarity_score, SuggestionContextPriorityEnum[x.match_context.name]))

    # Lấy page, size từ filter nếu có, mặc định 1/10
    page = filter.page if filter and hasattr(filter, "page") else 1
    size = filter.size if filter and hasattr(filter, "size") else 10

    total = len(suggestions)
    total_pages = (total + size - 1) // size if size else 1
    start = (page - 1) * size
    end = start + size
    items = suggestions[start:end]
    return PaginatedSuggestionContactResponse(
        items=items,
        total=total,
        page=page,
        size=size,
        total_pages=total_pages
    )

def dismiss_contact_suggestion_service(db: Session, user_id: int, user_dismiss_id: int) -> dict:
    """
    Dismiss a contact suggestion for 48 hours.
    """
    from datetime import datetime, timedelta, timezone
    from src.contact.models import ContactDismissSuggestion

    now = datetime.now(timezone.utc)
    dismiss_entry = db.query(ContactDismissSuggestion).filter(
        ContactDismissSuggestion.user_id == user_id,
        ContactDismissSuggestion.user_dismiss_id == user_dismiss_id,
        ContactDismissSuggestion.dismissed_to >= now
    ).first()
    if dismiss_entry:
        return {"message": "Suggestion already dismissed."}

    dismiss_to = now + timedelta(hours=48)
    new_dismiss = ContactDismissSuggestion(
        user_id=user_id,
        user_dismiss_id=user_dismiss_id,
        dismissed_to=dismiss_to
    )
    db.add(new_dismiss)
    db.commit()
    return {"message": "Suggestion dismissed for 48 hours."}

def list_contact_map_service(
    db,
    user_id: int,
    filters: ContactMapFilter
) -> ContactMapPaginatedResponse:
    # Lấy vị trí user hiện tại
    lat = filters.lattitude
    lng = filters.longitude
    radius = filters.radius_km or 25

    # Query các user profile có availability + visibility
    query = db.query(UserProfile, User).join(User, UserProfile.user_id == User.id)
    query = query.filter(
        UserProfile.enabled_availability == True,
        UserProfile.is_visibled_map == True,
        UserProfile.lattitude.isnot(None),
        UserProfile.longitude.isnot(None),
        UserProfile.user_id != user_id
    )
    if filters.search:
        query = query.filter(UserProfile.full_name.ilike(f"%{filters.search}%"))
    # Tính khoảng cách (giả sử dùng Haversine SQL hoặc lọc sau)
    profiles = query.all()
    results = []
    for profile, user in profiles:
        if profile.lattitude and profile.longitude:
            dist = geodesic((lat, lng), (profile.lattitude, profile.longitude)).km
            if dist <= radius:
                results.append(ContactMapBase(
                    id=user.id,
                    full_name=profile.full_name,
                    avatar=user.avatar,
                    gender=profile.gender,
                    job_title=profile.title,
                    distance=round(dist, 2),
                    lattitude=profile.lattitude,
                    longitude=profile.longitude,
                    is_available=profile.enabled_availability,
                    is_visible=profile.is_visibled_map
                ))
    # Pagination
    total = len(results)
    page = filters.page
    size = filters.size
    start = (page - 1) * size
    end = start + size
    items = results[start:end]
    total_pages = (total + size - 1) // size if size else 1
    return ContactMapPaginatedResponse(
        items=items,
        total=total,
        page=page,
        size=size,
        total_pages=total_pages
    )
