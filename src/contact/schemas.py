from pydantic import BaseModel
from typing import Optional, List, Union
from datetime import datetime
from pydantic import EmailStr, Field
from src.user.schemas import UserProfileBase, BusinessProfileBase
from src.utils.schemas import PaginatedResponse
from .models import ContactRequestStatusEnum
from src.chat.schemas import UserBasicInfo
from enum import Enum

class ContactBaseSchemas(BaseModel):
    nickname: Optional[str] = None
    notes: Optional[str] = None
    tags: Optional[List[str]] = None
    is_favorite: Optional[bool] = False

class ContactCreateSchemas(BaseModel):
    user_contact_id: int = Field(..., description="ID of the user to be added as contact")
    nickname: Optional[str] = Field(None, example="John Doe")
    notes: Optional[str] = Field(None, example="Met at tech conference")
    tags: Optional[List[str]] = Field(None, example=["Tech", "Developer"])
    is_favorite: Optional[bool] = Field(False, example=False)

    model_config = {
        "json_schema_extra": {
            "examples": [
                {
                    "user_contact_id": 1,
                    "nickname": "<PERSON>",
                    "notes": "Met at tech conference",
                    "tags": ["Tech", "Developer"],
                    "is_favorite": False
                }
            ]
        }
    }

class ContactUpdateSchemas(ContactBaseSchemas):
    pass

class ContactResponseSchemas(BaseModel):
    id: int
    user_id: int
    user_contact_id: Optional[int] = None
    nickname: Optional[str] = None
    is_favorite: bool = False
    notes: Optional[str] = None
    tags: Optional[List[str]] = None
    last_contact_date: Optional[datetime] = None
    created_at: datetime
    updated_at: datetime
    is_deleted: bool

    # User contact information
    user_contact: Optional[dict] = None
    profile: Optional[UserProfileBase] = None
    business_profile: Optional[BusinessProfileBase] = None

    class Config:
        from_attributes = True

# Schema cho response chi tiết
class ContactDetailResponseSchemas(ContactResponseSchemas):
    # Thông tin user nếu là user contact
    user_contact: Optional[dict] = None
    # Thông tin external contact nếu là external contact 

class ContactListResponseSchemas(BaseModel):
    id: int
    nickname: Optional[str] = None
    notes: Optional[str] = None
    tags: Optional[List[str]] = None
    is_favorite: bool = False
    last_contact_date: Optional[datetime] = None
    created_at: datetime
    updated_at: datetime
    user_contact: Optional[dict] = Field(None, example={
        "id": 1,
        "email": "<EMAIL>",
        "full_name": "John Doe",
        "avatar": "https://example.com/avatar.jpg",
        "user_type": "BUSINESS",
        "availability_status": "AVAILABLE",
        "availability_message": "Available for meetings",
        "company_info": {
            "company": "Tech Solutions Inc.",
            "company_address": "123 Business Street",
            "company_description": "Leading technology solutions provider"
        }
    })

    model_config = {
        "json_schema_extra": {
            "examples": [
                {
                    "id": 1,
                    "nickname": "John Doe",
                    "notes": "Met at tech conference",
                    "tags": ["Tech", "Developer"],
                    "is_favorite": True,
                    "last_contact_date": "2024-03-20T10:00:00",
                    "created_at": "2024-03-20T09:00:00",
                    "updated_at": "2024-03-20T09:00:00",
                    "user_contact": {
                        "id": 1,
                        "email": "<EMAIL>",
                        "full_name": "John Doe",
                        "avatar": "https://example.com/avatar.jpg",
                        "user_type": "BUSINESS",
                        "availability_status": "AVAILABLE",
                        "availability_message": "Available for meetings",
                        "company_info": {
                            "company": "Tech Solutions Inc.",
                            "company_address": "123 Business Street",
                            "company_description": "Leading technology solutions provider"
                        }
                    }
                }
            ]
        }
    }

class ContactRequestBase(BaseModel):
    to_user_id: int = Field(..., description="ID of the user to send request to")
    message: Optional[str] = Field(None, description="Optional message with the request")

    model_config = {
        "json_schema_extra": {
            "examples": [
                {
                    "to_user_id": 123,
                    "message": "Xin chào, tôi muốn kết bạn với bạn"
                }
            ]
        }
    }

class ContactRequestCreate(ContactRequestBase):
    pass

class ContactRequestResponse(ContactRequestBase):
    id: int
    from_user_id: int
    status: ContactRequestStatusEnum
    created_at: datetime
    updated_at: datetime
    contact_request: UserBasicInfo
    mutual_connections: int = 0

    model_config = {
        "json_schema_extra": {
            "examples": [
                {
                    "id": 1,
                    "from_user_id": 456,
                    "to_user_id": 123,
                    "status": "PENDING",
                    "message": "Xin chào, tôi muốn kết bạn với bạn",
                    "created_at": "2024-03-20T10:00:00",
                    "updated_at": "2024-03-20T10:00:00",
                    "contact_request": {
                        "id": 456,
                        "email": "<EMAIL>",
                        "full_name": "John Doe",
                        "avatar": "https://example.com/avatar.jpg",
                        "user_type": "BUSINESS"
                    }
                }
            ]
        }
    }

# Use generic pagination response
PaginatedContactListResponse = PaginatedResponse[ContactListResponseSchemas]
PaginatedContactRequestResponse = PaginatedResponse[ContactRequestResponse]

# Add example for paginated response
PaginatedContactRequestResponse.model_config = {
    "json_schema_extra": {
        "examples": [
            {
                "items": [
                    {
                        "id": 1,
                        "from_user_id": 456,
                        "to_user_id": 123,
                        "status": "PENDING",
                        "message": "Xin chào, tôi muốn kết bạn với bạn",
                        "created_at": "2024-03-20T10:00:00",
                        "updated_at": "2024-03-20T10:00:00",
                        "from_user": {
                            "id": 456,
                            "email": "<EMAIL>",
                            "full_name": "John Doe",
                            "avatar": "https://example.com/avatar.jpg",
                            "user_type": "BUSINESS",
                            "availability_status": "AVAILABLE",
                            "availability_message": "Available for meetings"
                        }
                    },
                    {
                        "id": 2,
                        "from_user_id": 789,
                        "to_user_id": 123,
                        "status": "APPROVED",
                        "message": "Chào bạn, tôi là Alice",
                        "created_at": "2024-03-19T15:30:00",
                        "updated_at": "2024-03-19T15:35:00",
                        "from_user": {
                            "id": 789,
                            "email": "<EMAIL>",
                            "full_name": "Alice Smith",
                            "avatar": "https://example.com/alice.jpg",
                            "user_type": "USER",
                            "availability_status": "BUSY",
                            "availability_message": "In a meeting"
                        }
                    }
                ],
                "total": 2,
                "page": 1,
                "size": 10,
                "total_pages": 1
            }
        ]
    }
}

class ReferContactRequest(BaseModel):
    contact_id: int = Field(..., description="ID of the contact to refer (must be your contact)")
    recipient_ids: List[int] = Field(..., description="List of user IDs to refer this contact to (must be your contacts)")

class ReferContactResponse(BaseModel):
    success_count: int
    failed: List[dict] = []  # Mỗi phần tử: {recipient_id, reason}
    message: str

class ContactFilter(BaseModel):
    page: int = Field(1, ge=1, description="Page number")
    size: int = Field(10, ge=1, le=100, description="Number of items per page")
    search: Optional[str] = Field(None, description="Search keyword")
    is_favorite: Optional[bool] = Field(None, description="Filter by favorite status")

class SuggestionContextEnum(str, Enum):
    MUTUAL_CONNECTION = "MUTUAL_CONNECTION"
    SHARED_EVENT = "SHARED_EVENT"
    JOB_TITLE = "JOB_TITLE"
    INDUSTRY = "INDUSTRY"
    LOCATION = "LOCATION"
    NONE = ""

class SuggestionContextPriorityEnum(int, Enum):
    MUTUAL_CONNECTION = 1
    SHARED_EVENT = 2
    JOB_TITLE = 3
    INDUSTRY = 4
    LOCATION = 5
    NONE = 6

class SuggestionContactFilter(BaseModel):
    page: int = Field(1, ge=1, description="Page number")
    size: int = Field(10, ge=1, le=100, description="Number of items per page")
    search: Optional[str] = Field(None, description="Search keyword")
    match_context: Optional[SuggestionContextEnum] = Field(None, description="Filter by match context")

class SuggestionContactResponse(BaseModel):
    id: int
    avatar: Optional[str] = None
    full_name: str
    job_title: Optional[str] = None
    match_context: Optional[SuggestionContextEnum] = SuggestionContextEnum.NONE
    similarity_score: float = 0.0
    industry_tags: Optional[dict] = None
    lattitude: Optional[float] = None
    longitude: Optional[float] = None

    class Config:
        orm_mode = True

PaginatedSuggestionContactResponse = PaginatedResponse[SuggestionContactResponse]

class ContactMapBase(BaseModel):
    id: int = Field(..., example=123)
    full_name: str = Field(..., example="Nguyen Van A")
    avatar: Optional[str] = Field(None, example="https://...")
    gender: Optional[str] = Field(None, example="MALE")
    job_title: Optional[str] = Field(None, example="Engineer")
    distance: Optional[float] = Field(None, example=0.5, description="Distance in km")
    lattitude: Optional[float] = Field(None, example=10.762622)
    longitude: Optional[float] = Field(None, example=106.660172)
    is_available: bool = Field(..., example=True)
    is_visible: bool = Field(..., example=True)

class ContactMapFilter(BaseModel):
    page: int = Field(1, ge=1, example=1)
    size: int = Field(20, ge=1, le=100, example=20)
    search: Optional[str] = Field(None, example="John")
    radius_km: Optional[float] = Field(25, example=25)
    # filter by availability/visibility
    is_available: Optional[bool] = Field(True, example=True)
    is_visible: Optional[bool] = Field(True, example=True)
    lattitude: float = Field(..., example=10.762622)
    longitude: float = Field(..., example=106.660172)

ContactMapPaginatedResponse = PaginatedResponse[ContactMapBase]