from fastapi import APIRouter, Depends
from sqlalchemy.orm import Session

from src.utils.database import get_db
from src.utils.token_docs import jwt_auth
from src.utils.permission import is_authenticated, is_admin
from src.utils.response import CustomResponse
from src.user.models import User as UserModel
from src.utils.schemas import GetDataResponse
from src.organization.schemas import OrganizationCreate, OrganizationFilter, OrganizationUpdate, OrganizationResponse, OrganizationListResponse, OrganizationUserUpdate
from src.organization.service import (
    create_organization_service,
    get_organization_service,
    list_organizations_service,
    update_organization_service,
    delete_organization_service,
    get_organization_current_user_service,
    update_organization_current_user_service,
)

router = APIRouter()
tags = ["Organization"]


@router.post("", dependencies=[Depends(jwt_auth)], tags=tags)
def create_organization(
    data: OrganizationCreate,
    db: Session = Depends(get_db),
    current_user: UserModel = Depends(is_admin),
):
    organization = create_organization_service(db, data)
    return CustomResponse(content=organization).format_data_create()


@router.get("/{organization_id}", dependencies=[Depends(jwt_auth)], tags=tags)
def get_organization(
    organization_id: int,
    db: Session = Depends(get_db),
    current_user: UserModel = Depends(is_admin),
):
    organization = get_organization_service(db, organization_id)
    return CustomResponse(content=organization).format_data_get()


@router.get("", dependencies=[Depends(jwt_auth)], tags=tags)
def list_organizations(
    filters: OrganizationFilter = Depends(),
    db: Session = Depends(get_db),
    current_user: UserModel = Depends(is_admin),
):
    organizations = list_organizations_service(db, filters)
    return CustomResponse(content=organizations).format_data_get()


@router.put("/{organization_id}", dependencies=[Depends(jwt_auth)], tags=tags)
def update_organization(
    organization_id: int,
    data: OrganizationUpdate,
    db: Session = Depends(get_db),
    current_user: UserModel = Depends(is_admin),
):
    organization = update_organization_service(db, organization_id, data)
    return CustomResponse(content=organization).format_data_update()


@router.delete("/{organization_id}", dependencies=[Depends(jwt_auth)], tags=tags)
def delete_organization(
    organization_id: int,
    db: Session = Depends(get_db),
    current_user: UserModel = Depends(is_admin),
):
    delete_organization_service(db, organization_id)
    return CustomResponse().format_data_delete()


@router.get("/me/", response_model=OrganizationResponse, dependencies=[Depends(jwt_auth)], tags=tags)
def get_my_organization(
    db: Session = Depends(get_db),
    current_user: UserModel = Depends(is_authenticated)
):
    org = get_organization_current_user_service(db, current_user)
    return CustomResponse(content=OrganizationResponse.model_validate(org)).format_data_get()


@router.put("/me/", response_model=OrganizationResponse, dependencies=[Depends(jwt_auth)], tags=tags)
def update_my_organization(
    data: OrganizationUserUpdate,
    db: Session = Depends(get_db),
    current_user: UserModel = Depends(is_authenticated)
):
    org = update_organization_current_user_service(db, current_user, data)
    return CustomResponse(content=OrganizationResponse.model_validate(org)).format_data_update()
