from datetime import datetime, date
from typing import List, Optional
from src.utils.schemas import PaginatedResponse, GetDataResponse
from src.utils.utils import paginated_example, get_example
from pydantic import BaseModel, Field, field_validator
import re

class OrganizationBase(BaseModel):
    name: str = Field(..., min_length=1, max_length=100, example="AIOS LINK")
    address: Optional[str] = Field(None, max_length=200, example="123 Main St, Anytown, USA")
    established_at: Optional[datetime] = Field(None, example="2024-04-01 09:00:00")
    ein: str = Field(..., example="12-3456789")
    owner_id: Optional[int] = Field(None, example=1)
    description: Optional[str] = Field(None, example="Company description")
    opening_hours: Optional[dict] = Field(None, example={"mon": "9-5"})
    industry_tags: Optional[dict] = Field(None, example={"primary": "Tech"})
    about: Optional[str] = Field(None, example="About us")
    services: Optional[dict] = Field(None, example={"service": "value"})
    logo: Optional[str] = Field(None, example="https://example.com/photo.jpg")
    is_active: bool = Field(True, example=True)

    model_config = {
        "from_attributes": True,
        "json_schema_extra": {
            "examples": [
                {
                    "name": "AIOS LINK",
                    "address": "123 Main St, Anytown, USA",
                    "established_at": "2024-04-01 09:00:00",
                    "ein": "12-3456789",
                    "owner_id": 1,
                    "description": "Company description",
                    "opening_hours": {"mon": "9-5"},
                    "industry_tags": {"primary": "Tech"},
                    "about": "About us",
                    "services": {"service": "value"},
                    "logo": "https://example.com/photo.jpg",
                    "is_active": True
                }
            ]
        }
    }

class OrganizationCreate(OrganizationBase):
    @field_validator('established_at')
    def validate_established_at(cls, v: datetime) -> datetime:
        if v > datetime.now():
            raise ValueError('Date of Establishment must be in the past or today')
        return v

    @field_validator('ein')
    def validate_ein_format(cls, v: str) -> str:
        if not re.match(r'^\d{2}-\d{7}$', v):
            raise ValueError('EIN must be in the format XX-XXXXXXX')
        return v

class OrganizationUserCreate(BaseModel):
    name: str = Field(..., min_length=1, max_length=100, example="AIOS LINK")
    ein: str = Field(..., example="12-3456789")
    owner_id: Optional[int] = Field(None, example=1)
    
    @field_validator('ein')
    def validate_ein_format(cls, v: str) -> str:
        if not re.match(r'^\d{2}-\d{7}$', v):
            raise ValueError('EIN must be in the format XX-XXXXXXX')
        return v
    
    model_config = {
        "from_attributes": True,
        "json_schema_extra": {
            "examples": [
                {
                    "name": "AIOS LINK",
                    "ein": "12-3456789",
                }
            ]
        }
    }
    
class OrganizationUpdate(OrganizationBase):
    @field_validator('established_at')
    def validate_established_at(cls, v: datetime) -> datetime:
        if v > datetime.now():
            raise ValueError('Date of Establishment must be in the past or today')
        return v

    @field_validator('ein')
    def validate_ein_format(cls, v: str) -> str:
        if not re.match(r'^\d{2}-\d{7}$', v):
            raise ValueError('EIN must be in the format XX-XXXXXXX')
        return v

class OrganizationUserUpdate(BaseModel):
    name: str = Field(..., min_length=1, max_length=100, example="AIOS LINK")
    address: Optional[str] = Field(..., max_length=200, example="123 Main St, Anytown, USA")
    established_at: Optional[datetime] = Field(..., example="2024-01-01")
    ein: str = Field(..., example="12-3456789")
    description: Optional[str] = Field(None, example="Company description")
    opening_hours: Optional[dict] = Field(None, example={"mon": "9-5"})
    industry_tags: Optional[dict] = Field(None, example={"primary": "Tech"})
    about: Optional[str] = Field(None, example="About us")
    services: Optional[dict] = Field(None, example={"service": "value"})
    logo: Optional[str] = Field(None, example="https://example.com/photo.jpg")
    is_active: bool = Field(True, example=True)
    
    @field_validator('established_at')
    def validate_established_at(cls, v: datetime) -> datetime:
        if v > datetime.now():
            raise ValueError('Date of Establishment must be in the past or today')
        return v
    
    @field_validator('ein')
    def validate_ein_format(cls, v: str) -> str:
        if not re.match(r'^\d{2}-\d{7}$', v):
            raise ValueError('EIN must be in the format XX-XXXXXXX')
        return v
    
    model_config = {
        "from_attributes": True,
        "json_schema_extra": {
            "examples": [
                {
                    "name": "AIOS LINK",
                    "address": "123 Main St, Anytown, USA",
                    "established_at": "2024-01-01",
                    "ein": "12-3456789",
                    "description": "Company description",
                    "opening_hours": {"mon": "9-5"},
                    "industry_tags": {"primary": "Tech"},
                    "about": "About us",
                    "services": {"service": "value"},
                    "logo": "https://example.com/photo.jpg",
                    "is_active": True
                }
            ]
        }
    }

class OrganizationInDB(OrganizationBase):
    id: int
    created_at: datetime
    updated_at: datetime
    is_deleted: bool

    class Config:
        from_attributes = True

class OrganizationResponse(OrganizationInDB):
    pass

class OrganizationListResponse(BaseModel):
    items: List[OrganizationResponse]
    total: int
    page: int
    size: int
    pages: int

    model_config = {
        "json_schema_extra": {
            "examples": [
                {
                    "items": [],
                    "total": 0,
                    "page": 1,
                    "size": 10,
                    "pages": 1,
                }
            ]
        }
    }

class OrganizationFilter(BaseModel):
    # Search by name (partial match)
    search: Optional[str] = Field(None, description="Search by organization name, address, ein,...")
    # Filter by owner_id
    owner_id: Optional[int] = Field(None, description="Filter by owner ID")
    # Filter by is_deleted
    is_deleted: Optional[bool] = Field(None, description="Filter by deletion status")
    # Filter by is_active
    is_active: Optional[bool] = Field(None, description="Filter by status active/inactive")

    # Pagination
    page: Optional[int] = Field(1, description="Page number for pagination (default: 1)")
    size: Optional[int] = Field(10, description="Page size for pagination (default: 10)")

    model_config = {
        "json_schema_extra": {
            "examples": [
                {
                    "search": "AIOS",
                    "owner_id": 1,
                    "is_active": True,
                    "page": 1,
                    "size": 10
                }
            ]
        }
    }
# class OrganizationResponse(GetDataResponse[OrganizationInDB]):
#     model_config = {
#         "from_attributes": True,
#         "json_schema_extra": {
#             "examples": [get_example(OrganizationInDB)]
#         }
#     }

# class OrganizationListResponse(PaginatedResponse[OrganizationInDB]):
#     model_config = {
#         "from_attributes": True,
#         "json_schema_extra": {
#             "examples": [paginated_example(OrganizationInDB)]
#         }
#     }