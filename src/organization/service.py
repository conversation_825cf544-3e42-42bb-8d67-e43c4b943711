from sqlalchemy.orm import Session
from typing import Optional

from src.organization.models import Organization
from src.organization.schemas import (
    OrganizationCreate,
    OrganizationFilter,
    OrganizationUpdate,
    OrganizationListResponse,
    OrganizationResponse,
)
from src.utils.exceptions import CustomException
from src.user.models import User as UserModel


def create_organization_service(db: Session, data) -> Organization:
    organization = Organization(**data.model_dump())
    db.add(organization)
    
    db.commit()
    db.refresh(organization)

    # Cập nhật organization_id cho user
    update_user_organization_service(db, data.owner_id, organization)
    return organization


def get_organization_service(db: Session, organization_id: int) -> Optional[Organization]:
    organization = db.query(Organization).filter(
        Organization.id == organization_id,
        Organization.is_deleted == False
    ).first()

    if organization:
        # Tính tổng số sản phẩm (total_products)
        from src.product.models import Product
        total_products = db.query(Product).filter(
            Product.organization_id == organization_id,
            Product.is_deleted == False
        ).count()

        # T<PERSON>h tổng số sự kiện (total_events)
        from src.event.models import Event
        if organization.owner_id:
            total_events = db.query(Event).filter(
                Event.created_by == organization.owner_id,
                Event.is_deleted == False
            ).count()
        else:
            total_events = 0

        # Gắn vào object (nếu cần trả về dạng dict hoặc custom schema)
        organization.total_products = total_products
        organization.total_events = total_events
    if not organization:
        raise CustomException(content={"error": ["Organization not found"]}).not_found_exception()
    return organization

def update_organization_service(
    db: Session,
    organization_id: int,
    data: OrganizationUpdate,
) -> Organization:
    organization = get_organization_service(db, organization_id)
    for field, value in data.model_dump(exclude_unset=True).items():
        setattr(organization, field, value)
    db.commit()
    db.refresh(organization)
    # Cập nhật organization_id cho user
    update_user_organization_service(db, data.owner_id, organization)
    return organization


def delete_organization_service(db: Session, organization_id: int) -> None:
    organization = get_organization_service(db, organization_id)
    organization.is_deleted = True
    db.commit()


from typing import Optional
from src.organization.schemas import OrganizationListResponse, OrganizationResponse

def list_organizations_service(
    db: Session,
    filters: OrganizationFilter = None,  # Expect OrganizationFilter schema
) -> OrganizationListResponse:
    # Get pagination parameters from filters
    page = filters.page if filters else 1
    size = filters.size if filters else 10
    
    # Sanitize pagination parameters
    page = max(page, 1)
    size = min(max(size, 1), 100)
    skip = (page - 1) * size

    query = db.query(Organization).filter(Organization.is_deleted == False)

    # Apply filters if provided
    if filters:
        filter_data = filters.model_dump(exclude_unset=True)
        # Search by name, address, or ein (partial match)
        search = filter_data.get("search")
        if search:
            search_pattern = f"%{search}%"
            query = query.filter(
                (Organization.name.ilike(search_pattern)) |
                (Organization.address.ilike(search_pattern)) |
                (Organization.ein.ilike(search_pattern))
            )
        # Filter by owner_id
        owner_id = filter_data.get("owner_id")
        if owner_id is not None:
            query = query.filter(Organization.owner_id == owner_id)
        # Filter by industry_tags (primary tag)
        industry_tags = filter_data.get("industry_tags")
        if industry_tags and isinstance(industry_tags, dict):
            primary_tag = industry_tags.get("primary")
            if primary_tag:
                query = query.filter(Organization.industry_tags["primary"].astext == primary_tag)
        # Filter by is_deleted
        is_deleted = filter_data.get("is_deleted")
        if is_deleted is not None:
            query = query.filter(Organization.is_deleted == is_deleted)
        # Filter by created_at range
        created_at_from = filter_data.get("created_at_from")
        if created_at_from:
            query = query.filter(Organization.created_at >= created_at_from)
        created_at_to = filter_data.get("created_at_to")
        if created_at_to:
            query = query.filter(Organization.created_at <= created_at_to)
        # Filter by updated_at range
        updated_at_from = filter_data.get("updated_at_from")
        if updated_at_from:
            query = query.filter(Organization.updated_at >= updated_at_from)
        updated_at_to = filter_data.get("updated_at_to")
        if updated_at_to:
            query = query.filter(Organization.updated_at <= updated_at_to)
        # Filter by is_active
        is_active = filter_data.get("is_active")
        if is_active is not None:
            query = query.filter(Organization.is_active == is_active)

    total = query.count()
    items = query.offset(skip).limit(size).all()
    pages = (total + size - 1) // size if size else 1

    return OrganizationListResponse(
        items=[OrganizationResponse.model_validate(i) for i in items],
        total=total,
        page=page,
        size=size,
        pages=pages,
    )


def update_user_organization_service(db: Session, user_id: int, organization):
    if user_id:
        user = db.query(UserModel).filter(UserModel.id == user_id).first()
        if not user:
            raise CustomException().not_found_exception(message="User not found")
        user.organization_id = organization.id
        db.commit()
        db.refresh(user)
        return user
    else:
        # Find users with organization_id equal to organization.id and set their organization_id to None
        users = db.query(UserModel).filter(UserModel.organization_id == organization.id).all()
        for user in users:
            user.organization_id = None
            db.add(user)
        db.commit()
        return None
    
def get_organization_current_user_service(db: Session, user) -> Organization:
    try:
        org = db.query(Organization).filter(
            Organization.id == user.organization_id,
            Organization.is_deleted == False
        ).first()
    except Exception as e:
        print(e)
    if not org:
        raise CustomException(content={"error": ["Organization not found"]}).not_found_exception()
    return org

def update_organization_current_user_service(db: Session, user, data: OrganizationUpdate) -> Organization:
    org = get_organization_current_user_service(db, user)
    update_data = data.model_dump(exclude_unset=True)
    update_data.pop("owner_id", None)  # Không cho update owner_id
    for field, value in update_data.items():
        setattr(org, field, value)
    db.commit()
    db.refresh(org)
    return org
