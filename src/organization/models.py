from sqlalchemy import Column, String, Text, J<PERSON><PERSON>, Integer, DateTime, Boolean
from sqlalchemy.orm import relationship
import datetime
from src.utils.base_model import BaseModel, Base

class Organization(BaseModel, Base):
    __tablename__ = "organization"

    name = Column(String(255), nullable=False)
    ein = Column(String(255), nullable=True)
    address = Column(String(255), nullable=True)
    description = Column(Text, nullable=True)
    opening_hours = Column(JSON, nullable=True)
    industry_tags = Column(JSON, nullable=True)
    about = Column(Text, nullable=True)
    services = Column(JSON, nullable=True)
    logo = Column(String(255), nullable=True)
    owner_id = Column(Integer, nullable=True)
    established_at = Column(DateTime,default=datetime.datetime.now(datetime.timezone.utc))
    is_active = Column(Boolean, nullable=False, default=True)
    
    users = relationship("User", back_populates="organization", lazy="select")
    products = relationship("Product", back_populates="organization", lazy="select")
    orders = relationship("Order", back_populates="organization", lazy="select")

