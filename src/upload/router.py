from typing import List
from uuid import uuid4
from fastapi import APIRouter, File, Form, UploadFile
from src.upload.schemas import FormData
from src.utils.common import upload_file_to_s3
from src.utils.enum import FolderNameS3
from ..utils.response import *
from src.upload.schemas import UploadFileToS3Schemas
from src.utils.s3 import S3
from datetime import datetime
from src.utils.utils import generate_code, send_email_background
from src.utils.email_template import template_customer_welcome
router = APIRouter()
tags = ['Upload']
s3 = S3()
@router.post("/upload", tags=tags, **create_data_response)
async def upload_file(
    files: List[UploadFile] = File(...)
):  
    form_data = FormData(folder_name="media", files=files) # Validate files
    urls = []
    for file in form_data.files:
        url = upload_file_to_s3(file=file, folder_name=form_data.folder_name)
        urls.append(url)
    return CustomResponse(content={'urls':urls}).format_data_get()


@router.post("/get-presinged-url", tags = tags, **create_data_response)
def generate_presigned_url(
    data: UploadFileToS3Schemas
):
    current = datetime.now()
    
    path = '{}/{}/{}/{}.{}'.format(current.year, current.month, current.day, f'{data.file_name}{generate_code(6)}', data.file_type)
    presigned = s3.generate_presigned_url(data.file_type, path)
    return CustomResponse(content={'url': presigned}).format_data_get()

@router.get("/send-email", tags = tags, **create_data_response)
def send_email(
    
):
    send_email_background('<EMAIL>', 'Test', template_customer_welcome('<EMAIL>'))
    return CustomResponse(content={'message': 'Email sent successfully'}).format_data_get()