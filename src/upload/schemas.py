from typing import List
from pydantic import field_validator, Field, BaseModel
from fastapi import UploadFile, File
from src.utils.enum import FolderNameS3
from src.utils.exceptions import CustomException

class FormData(BaseModel):
    folder_name: str
    files: List[UploadFile]

    @field_validator('files')
    def validate_file_extensions(cls, files, values):
        errors = []
        allowed_extensions = {
            FolderNameS3.AVATAR: ['.jpg', '.png', '.jpeg'],
            FolderNameS3.EXCEL: ['.xlsx'],
            FolderNameS3.NEWS: ['.jpg', '.png', '.jpeg']
            # Add more folder_name: allowed_extensions mappings as needed
        }
        folder_name = values.data.get('folder_name')
        if folder_name in allowed_extensions.keys():
            for file in files:
                if not any(file.filename.lower().endswith(ext) for ext in allowed_extensions[folder_name]):
                    errors.append(f"File '{file.filename}' extension is not allowed for folder '{folder_name}'")
        if errors:
            raise CustomException(content={'errors':errors}).bad_request_exception()
        return files

class UploadFileToS3Schemas(BaseModel):
    file_name: str = Field(
        title = "name of file"
    )
    file_type: str = Field(
        title = "type of file"
    )
    file_size: int = Field(
        title = "size of file"
    )

    @field_validator('file_size')
    def validate_file_size(cls, value):
        if value > 5:
            raise CustomException(content={"file_size":['File size must be less than 5 mb']}).bad_request_exception()
        return value
    @field_validator('file_type')
    def validate_file_type(cls, value):
        alllow_extensions = ['png', 'jpg', 'svg', 'jpeg', 'doc', 'pdf', 'docx', 'csv', 'xlsx']
        if value not in alllow_extensions:
            raise CustomException(content={"file_type":['File type must be png, jpg, svg, jpeg, doc, pdf, docx, csv, xlsx']}).bad_request_exception()
        return value