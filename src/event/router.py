from fastapi import APIRouter, Depends, Request, Security, Query 
from sqlalchemy.orm import Session
from src.utils.permission import is_admin, is_authenticated
from ..utils.response import *
from src.event.schemas import (
    CancelEventSchemas,
    EventCreate, 
    EventUpdate, 
    EventResponse, 
    EventFilter, 
    EventInteractionCreate, 
    EventInteractionResponse,
    PaginatedEventListResponse,
    AddContactsToEventSchemas
)
from src.utils.database import get_db
from src.utils.token_docs import jwt_auth
from src.user.models import User as UserModel
from src.event.service import (
    get_events_service,
    get_event_by_id_service,
    create_event_service,
    update_event_service,
    delete_event_service,
    add_interaction_service,
    get_events_by_date_range_service,
    add_contacts_to_event_service,
    cancel_event_service
)
from src.utils.pagination import PaginationData, get_parameters_of_list
# from src.image_detection.models import ImageDetailDetection as ImageDetailDetectionModel
# from src.image_detection.schemas import DetailImageEventSchemas
from typing import Optional
import json
from datetime import datetime
from src.utils.enum import EventTypeEnum, EventStatusEnum, EntityTypeEnum

router = APIRouter()
tags = ['Event']




@router.get("/", tags=tags, response_model=PaginatedEventListResponse, dependencies=[Depends(jwt_auth)])
def get_events(
    filters: EventFilter = Depends(),
    db: Session = Depends(get_db),
    current_user: UserModel = Depends(is_authenticated),
):
    """Get list of events with filtering and pagination"""
    result = get_events_service(db, current_user, filters)
    return CustomResponse(content=result).format_data_get()

@router.get("/{event_id}", tags=tags, **get_data_response, dependencies=[Depends(jwt_auth)])
def get_event(
    event_id: int,
    db: Session = Depends(get_db),
    current_user: UserModel = Depends(is_authenticated)
):
    """Get event details by ID"""
    result = get_event_by_id_service(db, event_id, current_user)
    return CustomResponse(content=result).format_data_get()

@router.post("/", tags=tags, **create_data_response, dependencies=[Depends(jwt_auth)])
def create_event(
    event_data: EventCreate,
    db: Session = Depends(get_db),
    current_user: UserModel = Depends(is_authenticated),
):

    result = create_event_service(db, event_data, current_user)
    return CustomResponse(content=result).format_data_create()

@router.put("/{event_id}", tags=tags, **update_data_response, dependencies=[Depends(jwt_auth)])
def update_event(
    event_id: int,
    event_data: EventUpdate,
    db: Session = Depends(get_db),
    current_user: UserModel = Depends(is_authenticated),
):
    result = update_event_service(db, event_id, event_data, current_user)
    return CustomResponse(content=result).format_data_update()

@router.delete("/events/{event_id}", tags=tags, **delete_data_response, dependencies=[Depends(jwt_auth)])
def delete_event(
    event_id: int,
    db: Session = Depends(get_db),
    current_user: UserModel = Depends(is_authenticated),
):
    """Delete event"""
    delete_event_service(db, event_id, current_user.id)
    return CustomResponse().format_data_delete()

@router.post("/{event_id}/interactions", tags=tags, **create_data_response, dependencies=[Depends(jwt_auth)])
def add_interaction(
    event_id: int,
    interaction_data: dict,
    db: Session = Depends(get_db),
    current_user: UserModel = Depends(is_authenticated),
):
    """Add interaction history for event"""
    result = add_interaction_service(db, event_id, interaction_data, current_user.id)
    return CustomResponse(content=result).format_data_create()

@router.post("/{event_id}/contacts", response_model=EventResponse, tags=tags, dependencies=[Depends(jwt_auth)])
def add_contacts_to_event(
    event_id: int,
    data: AddContactsToEventSchemas,
    db: Session = Depends(get_db),
    current_user: UserModel = Depends(is_authenticated),
):
    """
    Add contacts to an event
    """
    event = add_contacts_to_event_service(
        db=db,
        event_id=event_id,
        user_id=current_user.id,
        data=data
    )
    return CustomResponse(content=event).format_data_get()


@router.put("/{event_id}/cancel", tags=tags, **update_data_response, dependencies=[Depends(jwt_auth)])
def cancel_event(
    event_id: int,
    data: CancelEventSchemas,
    db: Session = Depends(get_db),
    current_user: UserModel = Depends(is_authenticated),
):
    """Cancel a specific event."""
    result = cancel_event_service(db, event_id, current_user, data)
    return CustomResponse(content=result).format_data_update()


