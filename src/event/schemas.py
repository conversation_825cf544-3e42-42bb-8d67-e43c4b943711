import re
from typing import  List, Union, Optional
from pydantic import Field, EmailStr, field_validator, BaseModel, ValidationInfo
from datetime import datetime, timezone, date
from src.utils.common import format_urls_json, format_urls_list
from src.utils.exceptions import CustomException
from src.utils.enum import DonorType, EventAccessEnum, EventTypeEnum, EventStatusEnum, EntityTypeEnum
from datetime import date as DateTime
from src.utils.schemas import PaginatedResponse
from src.utils.enum import EventInvitationEnum



class EventBase(BaseModel):
    title: str = Field(..., example="Team Building Event")
    description: str = Field(..., example="Annual team building event for all employees")
    event_type: EventTypeEnum = Field(default=EventTypeEnum.INPERSON, example=EventTypeEnum.INPERSON)
    status: str = Field(
        default="DRAFT", 
        example="DRAFT",
        description="Available statuses: DRAFT, PUBLISHED, CANCELLED, COMPLETED, EXPIRED, PENDING"
    )
    location: str = Field(..., example="Ho Chi Minh City")
    start_time: datetime = Field(..., example="2024-04-01T09:00:00Z")
    end_time: datetime = Field(..., example="2024-04-01T17:00:00Z")
    meeting_link: Optional[str] = Field(None, example="https://meet.google.com/xxx-yyyy-zzz")
    tags: Optional[List[str]] = Field(None, example=["team-building", "fun"])
    attendee_limit: Optional[int] = Field(None, example=50)
    attendee_count: Optional[int] = Field(None, example=0)
    notes: Optional[str] = Field(None, example="Please bring swimming suits")
    lattitude: Optional[float] = Field(None, example=10.776901)
    longitude: Optional[float] = Field(None, example=106.700902)
    linked_entity_type: Optional[str] = Field(None, example="PROJECT")
    telegram_group_link: Optional[str] = Field(None, example="https://t.me/groupname")
    whatsapp_group_link: Optional[str] = Field(None, example="https://chat.whatsapp.com/groupname")
    is_public: bool = Field(default=False, example=False)
    contact_ids: Optional[List[int]] = Field(None, example=[1, 2, 3])

    @field_validator('event_type')
    def validate_event_type(cls, v):
        valid_types = [e.value for e in EventTypeEnum]
        if v not in valid_types:
            raise ValueError(f'Event type must be one of: {", ".join(valid_types)}')
        return v.replace("/", "").replace("_", "")

    @field_validator('status')
    def validate_status(cls, v):
        valid_statuses = [e.value for e in EventStatusEnum]
        if v not in valid_statuses:
            raise ValueError(f'Status must be one of: {", ".join(valid_statuses)}')
        return v.replace("/", "").replace("_", "")

    @field_validator('meeting_link')
    def validate_meeting_link(cls, v, info):
        if info.data.get('event_type') == EventTypeEnum.INPERSON and not v:
            raise ValueError('meeting_link is required when event_type is INPERSON')
        return v

    model_config = {
        "json_schema_extra": {
            "examples": [
                {
                    "title": "Team Building Event",
                    "description": "Annual team building event for all employees",
                    "event_type": "INPERSON",
                    "status": "DRAFT",
                    "location": "Ho Chi Minh City",
                    "start_time": "2024-04-01T09:00:00Z",
                    "end_time": "2024-04-01T17:00:00Z",
                    "meeting_link": "https://meet.google.com/xxx-yyyy-zzz",
                    "tags": ["team-building", "fun"],
                    "attendee_limit": 50,
                    "attendee_count": 0,
                    "notes": "Please bring swimming suits",
                    "lattitude": 10.776901,
                    "longitude": 106.700902,
                    "linked_entity_type": "PROJECT",
                    "telegram_group_link": "https://t.me/groupname",
                    "whatsapp_group_link": "https://chat.whatsapp.com/groupname",
                    "is_public": False,
                    "contact_ids": [1, 2, 3]
                },
                {
                    "title": "Online Workshop",
                    "description": "Web development workshop",
                    "event_type": "ONLINE",
                    "status": "PUBLISHED",
                    "location": "Online",
                    "start_time": "2024-04-15T14:00:00Z",
                    "end_time": "2024-04-15T16:00:00Z",
                    "meeting_link": "https://zoom.us/j/123456789",
                    "tags": ["workshop", "web-development"],
                    "attendee_limit": 100,
                    "attendee_count": 0,
                    "notes": "Please install Zoom before the workshop",
                    "is_public": True
                }
            ]
        }
    }

class EventCreate(BaseModel):
    title: str = Field(..., example="Team Building Event")
    description: str = Field(..., example="Annual team building event for all employees")
    event_type: EventTypeEnum = Field(
        default=EventTypeEnum.INPERSON, 
        example=EventTypeEnum.INPERSON,
        description=f"Available event_type: {', '.join(EventTypeEnum.__members__.keys())}"
    )
    status: EventStatusEnum = Field(
        default=EventStatusEnum.UPCOMING, 
        example=EventStatusEnum.UPCOMING,
        description=f"Available statuses: {', '.join(EventStatusEnum.__members__.keys())}"
    )
    event_access: EventAccessEnum = Field(
        default=EventAccessEnum.FREE, 
        example=EventAccessEnum.FREE,
        description=f"Available statuses: {', '.join(EventAccessEnum.__members__.keys())}"
    )
    location: str = Field(..., example="Ho Chi Minh City")
    start_time: datetime = Field(..., example="2024-04-01T09:00:00Z")
    end_time: datetime = Field(..., example="2024-04-01T17:00:00Z")
    meeting_link: Optional[str] = Field(None, example="https://meet.google.com/xxx-yyyy-zzz")
    tags: Optional[List[str]] = Field(None, example=["team-building", "fun"])
    attendee_limit: Optional[int] = Field(None, example=50)
    notes: Optional[str] = Field(None, example="Please bring swimming suits")
    lattitude: Optional[float] = Field(None, example=10.776901)
    longitude: Optional[float] = Field(None, example=106.700902)
    linked_entity_type: Optional[str] = Field(None, example="PROJECT")
    telegram_group_link: Optional[str] = Field(None, example="https://t.me/groupname")
    whatsapp_group_link: Optional[str] = Field(None, example="https://chat.whatsapp.com/groupname")
    is_public: bool = Field(default=False, example=False)
    contact_ids: Optional[List[int]] = Field(None, example=[1, 2, 3])
    user_id: Optional[int] = Field(None, example=1)
    rsvp_limit: Optional[int] = Field(None, example=50)

    @field_validator('start_time')
    def validate_start_time_future(cls, v):
        from datetime import datetime, timezone
        now = datetime.now(timezone.utc)
        if v <= now:
            raise ValueError('start_time must be in the future')
        return v

    @field_validator('end_time')
    def validate_end_time_future(cls, v, info: ValidationInfo):
        if v is None:
            return v
        now = datetime.now(timezone.utc)
        # Nếu v là naive, convert sang UTC-aware
        if v.tzinfo is None:
            v = v.replace(tzinfo=timezone.utc)
        if v <= now:
            raise ValueError('end_time must be in the future')
        start_time = info.data.get('start_time')
        if start_time:
            # Nếu start_time là naive, convert sang UTC-aware
            if start_time.tzinfo is None:
                start_time = start_time.replace(tzinfo=timezone.utc)
            if v <= start_time:
                raise ValueError('end_time must be after start_time')
        return v

    @field_validator('meeting_link')
    def validate_meeting_link(cls, v, info):
        if info.data.get('event_type') == EventTypeEnum.VIRTUAL and not v:
            raise ValueError('meeting_link is required when event_type is VIRTUAL')
        return v
    
    @field_validator('location')
    def validate_location(cls, v, info):
        if info.data.get('event_type') == EventTypeEnum.INPERSON and not v:
            raise ValueError('location is required when event_type is INPERSON')
        return v

    @field_validator('attendee_limit')
    def validate_attendee_limit(cls, v):
        if v is not None:
            if v < 1 or v > 200:
                raise ValueError('attendee_limit must be between 1 and 200')
        return v

    @field_validator('rsvp_limit')
    def validate_rsvp_limit(cls, v, info):
        if v is not None:
            attendee_limit = info.data.get('attendee_limit')
            if attendee_limit is not None and v > attendee_limit:
                raise ValueError('rsvp_limit cannot be greater than attendee_limit')
        return v

    @field_validator('attendee_limit')
    def validate_public_attendee_limit(cls, v, info):
        if info.data.get('is_public') is False:
            if v != info.data.get('rsvp_limit'):
                raise ValueError('When is_public is False, attendee_limit must equal rsvp_limit')
        return v

    @field_validator('title')
    def validate_unique_title_per_host_time(cls, v, info):
        if v is not None:
            # Note: This is a schema-level validation
            # The actual uniqueness check should be implemented at the database/service level
            # since we need to query existing events
            
            # The validation will need:
            # - Same host (user_id)
            # - Same date/time slot (start_time/end_time overlap)
            # - Same title
            
            # This validator serves as a documentation of the business rule
            # The actual enforcement should be done when saving/updating the event
            return v
        
    # Validate max 10 items in tags
    @field_validator('tags')
    def validate_max_tags(cls, v):
        if v is not None and len(v) > 10:
            raise ValueError('Maximum 10 tags allowed')
        return v

    model_config = {
        "json_schema_extra": {
            "examples": [
                {
                    "title": "Team Building Event",
                    "description": "Annual team building event for all employees",
                    "event_type": "INPERSON",
                    "status": "DRAFT",
                    "event_access": "FREE",
                    "location": "Ho Chi Minh City",
                    "start_time": "2024-04-01T09:00:00Z",
                    "end_time": "2024-04-01T17:00:00Z",
                    "meeting_link": "https://meet.google.com/xxx-yyyy-zzz",
                    "tags": ["team-building", "fun"],
                    "attendee_limit": 200,
                    "notes": "Please bring swimming suits",
                    "lattitude": 10.776901,
                    "longitude": 106.700902,
                    "linked_entity_type": "PROJECT",
                    "telegram_group_link": "https://t.me/groupname",
                    "whatsapp_group_link": "https://chat.whatsapp.com/groupname",
                    "is_public": False,
                    "contact_ids": [1, 2, 3],
                    "user_id": 1,
                    "rsvp_limit": 50
                }
            ]
        }
    }

class EventUpdate(BaseModel):
    title: Optional[str] = Field(None, example="Team Building Event")
    description: Optional[str] = Field(None, example="Annual team building event for all employees")
    location: Optional[str] = Field(None, example="Ho Chi Minh City")
    start_time: Optional[datetime] = Field(None, example="2024-04-01T09:00:00Z")
    end_time: Optional[datetime] = Field(None, example="2024-04-01T17:00:00Z")
    meeting_link: Optional[str] = Field(None, example="https://meet.google.com/xxx-yyyy-zzz")
    tags: Optional[List[str]] = Field(None, example=["team-building", "fun"])
    attendee_limit: Optional[int] = Field(None, example=50)
    attendee_count: Optional[int] = Field(None, example=0)
    notes: Optional[str] = Field(None, example="Please bring swimming suits")
    lattitude: Optional[float] = Field(None, example=10.776901)
    longitude: Optional[float] = Field(None, example=106.700902)
    linked_entity_type: Optional[str] = Field(None, example="PROJECT")
    telegram_group_link: Optional[str] = Field(None, example="https://t.me/groupname")
    whatsapp_group_link: Optional[str] = Field(None, example="https://chat.whatsapp.com/groupname")
    is_public: Optional[bool] = Field(None, example=False)

    @field_validator('start_time')
    def validate_start_time_future(cls, v):
        if v is None:
            return v
        now = datetime.now(timezone.utc)
        # Nếu v là naive, convert sang UTC-aware
        if v.tzinfo is None:
            v = v.replace(tzinfo=timezone.utc)
        if v <= now:
            raise ValueError('start_time must be in the future')
        return v

    @field_validator('end_time')
    def validate_end_time_future(cls, v, info: ValidationInfo):
        if v is None:
            return v
        now = datetime.now(timezone.utc)
        # Nếu v là naive, convert sang UTC-aware
        if v.tzinfo is None:
            v = v.replace(tzinfo=timezone.utc)
        if v <= now:
            raise ValueError('end_time must be in the future')
        start_time = info.data.get('start_time')
        if start_time:
            # Nếu start_time là naive, convert sang UTC-aware
            if start_time.tzinfo is None:
                start_time = start_time.replace(tzinfo=timezone.utc)
            if v <= start_time:
                raise ValueError('end_time must be after start_time')
        return v

    # Validate max 10 items in tags
    @field_validator('tags')
    def validate_max_tags(cls, v):
        if v is not None and len(v) > 10:
            raise ValueError('Maximum 10 tags allowed')
        return v

    # Validate attendee_limit can not less than count rsvp records that have fk to event
    @field_validator('attendee_limit')
    def validate_attendee_limit_vs_rsvp(cls, v, info):
        # _original_data is injected in service layer for update
        rsvp_count = None
        if hasattr(info.data, '_original_data'):
            rsvp_count = info.data._original_data.get('rsvp_count')
        elif '_original_data' in info.data:
            rsvp_count = info.data['_original_data'].get('rsvp_count')
        if rsvp_count is not None and v is not None and v < rsvp_count:
            raise ValueError(f'attendee_limit ({v}) cannot be less than current RSVP count ({rsvp_count})')
        return v

    # Only allow to update ticket price when dont have any sold ticket
    # NOTE: This is a schema-level documentation, actual enforcement should be in service layer
    # You must check in the service: if any ticket has been sold, do not allow price update.
    
    # Improved validation: use the updated object data (merged old + payload) for validation.
    # This requires a root_validator (model_validator) to access the full data context.
    from pydantic import model_validator

    @model_validator(mode="after")
    def validate_event_update(cls, data):
        # Assume 'original_data' is injected into the model before validation (e.g., in the service layer)
        # and contains the current state of the event in DB.
        # Merge logic: updated = {**original_data, **payload}
        # Here, 'data' is the merged result.

        # Validate meeting_link for VIRTUAL events
        event_type = getattr(data, 'event_type', None)
        meeting_link = getattr(data, 'meeting_link', None)
        if event_type == EventTypeEnum.VIRTUAL and not meeting_link:
            raise ValueError('meeting_link is required when event_type is VIRTUAL')

        # Validate location for INPERSON events
        location = getattr(data, 'location', None)
        if event_type == EventTypeEnum.INPERSON and not location:
            raise ValueError('location is required when event_type is INPERSON')

        # Validate attendee_limit range
        attendee_limit = getattr(data, 'attendee_limit', None)
        if attendee_limit is not None:
            if attendee_limit < 1 or attendee_limit > 200:
                raise ValueError('attendee_limit must be between 1 and 200')

        # Validate rsvp_limit <= attendee_limit
        rsvp_limit = getattr(data, 'rsvp_limit', None)
        if rsvp_limit is not None and attendee_limit is not None:
            if rsvp_limit > attendee_limit:
                raise ValueError('rsvp_limit cannot be greater than attendee_limit')

        # Validate attendee_limit == rsvp_limit when is_public is False
        is_public = getattr(data, 'is_public', None)
        if is_public is False and attendee_limit is not None and rsvp_limit is not None:
            if attendee_limit != rsvp_limit:
                raise ValueError('When is_public is False, attendee_limit must equal rsvp_limit')

        # Validate time fields cannot be updated if event_access is TICKETED
        event_access = getattr(data, 'event_access', None)
        # To check if start_time or end_time is being changed, you must compare with original_data
        # This requires the service layer to inject 'original_data' into the model before validation.
        # For demonstration, assume 'data._original_data' is the original event dict.
        original = getattr(data, '_original_data', None)
        if event_access == EventAccessEnum.TICKETED and original:
            # Only raise if start_time or end_time is being changed
            if (
                getattr(data, 'start_time', None) != original.get('start_time')
                or getattr(data, 'end_time', None) != original.get('end_time')
            ):
                raise ValueError('Cannot update start_time or end_time when event_access is TICKETED')
        # The unique title per host/time rule is still a business rule to be enforced at the service/db layer.

        return data

    model_config = {
        "json_schema_extra": {
            "examples": [
                {
                    "title": "Updated Team Building Event",
                    "description": "Updated team building event description",
                    "event_type": "INPERSON",
                    "status": "UPCOMING",
                    "meeting_link": "https://meet.google.com/xxx-yyyy-zzz",
                    "location": "Updated Location",
                    "start_time": "2024-04-02T09:00:00Z",
                    "end_time": "2024-04-02T17:00:00Z",
                    "tags": ["team-building", "fun", "updated"],
                    "attendee_limit": 60,
                    "notes": "Updated notes",
                    "is_public": True
                }
            ]
        }
    }

class EventInteractionBase(BaseModel):
    interaction_type: str
    description: str
    date: datetime

class EventInteractionCreate(EventInteractionBase):
    event_id: int
    user_id: int

class EventInteractionResponse(EventInteractionBase):
    id: int
    event_id: int
    user_id: int
    created_at: datetime
    updated_at: datetime
            
    class Config:
        from_attributes = True

class ContactResponse(BaseModel):
    id: int
    name: str
    email: str
    phone: Optional[str] = None
    avatar: Optional[str] = None

    class Config:
        from_attributes = True
        
class EventResponse(EventBase):
    id: int
    created_at: datetime
    updated_at: datetime
    created_by: int
    contact_ids: List[int] = []
    participants: List[ContactResponse] = []
    interactions: List[EventInteractionResponse] = []
    
    class Config:
        from_attributes = True

class EventListResponse(BaseModel):
    items: List[EventResponse]
    total: int
    page: int
    size: int
    pages: int

    model_config = {
        "json_schema_extra": {
            "examples": [
                {
                    "items": [
                        {
                            "id": 1,
                            "title": "Team Building 2024",
                            "description": "Annual team building event at Vung Tau",
                            "event_type": "TEAM_BUILDING",
                            "status": "DRAFT",
                            "start_date": "2024-04-01T09:00:00Z",
                            "end_date": "2024-04-02T17:00:00Z",
                            "location": "Vung Tau Beach Resort",
                            "max_participants": 50,
                            "registration_deadline": "2024-03-25T23:59:59Z",
                            "notes": "Please bring swimming suits",
                            "created_at": "2024-03-20T10:00:00Z",
                            "updated_at": "2024-03-20T10:00:00Z",
                            "created_by": 1,
                            "participants_count": 2,
                            "contact_ids": [1, 2],
                            "interactions": []
                        }
                    ],
                    "total": 1,
                    "page": 1,
                    "size": 10,
                    "pages": 1
                }
            ]
        }
    }

class EventFilter(BaseModel):
    event_type: Optional[EventTypeEnum] = Field(None, example="TEAM_BUILDING", description="Available types: TEAM_BUILDING, CONFERENCE, TRAINING, SOCIAL, OTHER")
    status: Optional[EventStatusEnum] = Field(None, example="DRAFT", description="Available statuses: DRAFT, PUBLISHED, CANCELLED, COMPLETED")
    search: Optional[str] = Field(None, example="Team Building")
    start_date_from: Optional[datetime] = Field(None, example="2024-04-01T00:00:00Z")
    start_date_to: Optional[datetime] = Field(None, example="2024-04-30T23:59:59Z")
    is_public: Optional[bool] = Field(None, example=False)
    page: int = Field(1, ge=1, example=1)
    size: int = Field(10, ge=1, le=100, example=10)

    model_config = {
        "json_schema_extra": {
            "examples": [
                {
                    "event_type": "TEAM_BUILDING",
                    "status": "DRAFT",
                    "search": "Team Building",
                    "start_date_from": "2024-04-01T00:00:00Z",
                    "start_date_to": "2024-04-30T23:59:59Z",
                    "page": 1,
                    "size": 10
                }
            ]
        }
    }

class EventListResponseSchemas(BaseModel):
    id: int
    title: str
    description: Optional[str] = None
    start_time: datetime
    end_time: datetime
    location: Optional[str] = None
    meeting_link: Optional[str] = None
    tags: Optional[List[str]] = None
    attendee_limit: Optional[int] = None
    attendee_count: Optional[int] = None
    notes: Optional[str] = None
    created_at: datetime
    updated_at: datetime
    event_type: EventTypeEnum
    status: EventStatusEnum
    lattitude: Optional[float] = None
    longitude: Optional[float] = None
    linked_entity_type: Optional[str] = None
    telegram_group_link: Optional[str] = None
    whatsapp_group_link: Optional[str] = None
    is_public: bool
    accepted_count: int
    pending_count: int

    model_config = {
        "json_schema_extra": {
            "examples": [
                {
                    "id": 1,
                    "title": "Team Building Event",
                    "description": "Annual team building event for all employees",
                    "start_time": "2024-04-01T09:00:00Z",
                    "end_time": "2024-04-01T17:00:00Z",
                    "location": "Ho Chi Minh City",
                    "meeting_link": "https://meet.google.com/xxx-yyyy-zzz",
                    "tags": ["team-building", "fun"],
                    "attendee_limit": 50,
                    "notes": "Please bring swimming suits",
                    "created_at": "2024-03-20T10:00:00Z",
                    "updated_at": "2024-03-20T10:00:00Z",
                    "event_type": "INPERSON",
                    "status": "DRAFT",
                    "lattitude": 10.776901,
                    "longitude": 106.700902,
                    "linked_entity_type": "PROJECT",
                    "telegram_group_link": "https://t.me/groupname",
                    "whatsapp_group_link": "https://chat.whatsapp.com/groupname",
                    "is_public": False,
                    "accepted_count": 0,
                    "pending_count": 0
                },
                {
                    "id": 2,
                    "title": "Online Workshop",
                    "description": "Web development workshop",
                    "start_time": "2024-04-15T14:00:00Z",
                    "end_time": "2024-04-15T16:00:00Z",
                    "location": "Online",
                    "meeting_link": "https://zoom.us/j/123456789",
                    "tags": ["workshop", "web-development"],
                    "attendee_limit": 100,
                    "notes": "Please install Zoom before the workshop",
                    "created_at": "2024-03-20T11:00:00Z",
                    "updated_at": "2024-03-20T11:00:00Z",
                    "event_type": "ONLINE",
                    "status": "PUBLISHED",
                    "is_public": True,
                    "accepted_count": 0,
                    "pending_count": 0
                }
            ]
        }
    }

# Use generic pagination response
PaginatedEventListResponse = PaginatedResponse[EventListResponseSchemas]

# Add examples for paginated response
PaginatedEventListResponse.model_config = {
    "json_schema_extra": {
        "examples": [
            {
                "items": [
                    {
                        "id": 1,
                        "title": "Team Building Event",
                        "description": "Annual team building event for all employees",
                        "start_time": "2024-04-01T09:00:00Z",
                        "end_time": "2024-04-01T17:00:00Z",
                        "location": "Ho Chi Minh City",
                        "meeting_link": "https://meet.google.com/xxx-yyyy-zzz",
                        "tags": ["team-building", "fun"],
                        "attendee_limit": 50,
                        "notes": "Please bring swimming suits",
                        "created_at": "2024-03-20T10:00:00Z",
                        "updated_at": "2024-03-20T10:00:00Z",
                        "event_type": "INPERSON",
                        "status": "DRAFT",
                        "lattitude": 10.776901,
                        "longitude": 106.700902,
                        "linked_entity_type": "PROJECT",
                        "telegram_group_link": "https://t.me/groupname",
                        "whatsapp_group_link": "https://chat.whatsapp.com/groupname",
                        "is_public": False,
                        "attendee_count": 0,
                        "pending_count": 0
                    },
                    {
                        "id": 2,
                        "title": "Online Workshop",
                        "description": "Web development workshop",
                        "start_time": "2024-04-15T14:00:00Z",
                        "end_time": "2024-04-15T16:00:00Z",
                        "location": "Online",
                        "meeting_link": "https://zoom.us/j/123456789",
                        "tags": ["workshop", "web-development"],
                        "attendee_limit": 100,
                        "notes": "Please install Zoom before the workshop",
                        "created_at": "2024-03-20T11:00:00Z",
                        "updated_at": "2024-03-20T11:00:00Z",
                        "event_type": "ONLINE",
                        "status": "PUBLISHED",
                        "is_public": True,
                        "attendee_count": 0,
                        "pending_count": 0
                    }
                ],
                "page": 1,
                "size": 10,
                "total": 100,
                "total_pages": 10
            }
        ]
    }
}

class AddContactsToEventSchemas(BaseModel):
    contact_ids: List[int] = Field(..., description="List of contact IDs to add to event")

    invite_type: EventInvitationEnum = Field(EventInvitationEnum.INVITE_ONLY, example=EventInvitationEnum.INVITE_ONLY)

    model_config = {
        "json_schema_extra": {
            "examples": [
                {
                    "contact_ids": [1, 2, 3],
                    "invite_type": "INVITE_ONLY"
                }
            ]
        }
    }

class CancelEventSchemas(BaseModel):
    notes: Optional[str] = Field(default=None, description="Sorry everyone!")

    model_config = {
        "json_schema_extra": {
            "examples": [
                {
                    "notes": "Sorry everyone!"
                }
            ]
        }
    }