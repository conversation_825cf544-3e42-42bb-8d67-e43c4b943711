import json
from typing import List, Optional
from sqlalchemy.orm import Session, joinedload
from sqlalchemy import desc, text, or_, and_, func, case, String, cast, JSON
import datetime
from datetime import datetime, timedelta, timezone
import os
from jose import jwt
from src.event.schemas import AddContactsToEventSchemas, CancelEventSchemas, EventCreate, EventUpdate, EventFilter, PaginatedEventListResponse, EventListResponseSchemas
from src.event.models import Event as EventModel, EventInteraction, EventInvitation
from src.utils.common import format_urls_to_list, query_list
from src.utils.exceptions import CustomException
from src.user.models import User
import uuid
from src.utils.enum import EventInvitationEnum, RSVPStatusEnum, EventStatusEnum, UserRoleEnum
from src.contact.models import Contact
from src.ticket.models import Ticket
from src.ticket_sale.models import PaymentStatusEnum, TicketOrder

def get_events_service(db: Session, user, filters: EventFilter) -> PaginatedEventListResponse:
    # Build base query
    query = db.query(
        EventModel,
        func.count(case((EventInvitation.status == RSVPStatusEnum.ACCEPTED, 1))).label('accepted_count'),
        func.count(case((EventInvitation.status == RSVPStatusEnum.PENDING, 1))).label('pending_count')
    ).outerjoin(
        EventModel.invitations
    ).group_by(
        EventModel.id
    )

    # If not admin, filter for public events or events created by user
    if user.user_type != UserRoleEnum.ADMIN:
        query = query.filter(
            or_(
                EventModel.is_public == True,
                EventModel.created_by == user.id
            )
        )
    
    # Apply filters
    if filters.event_type:
        query = query.filter(EventModel.event_type == filters.event_type)
    if filters.status:
        query = query.filter(EventModel.status == filters.status)
    if filters.start_date_from:
        query = query.filter(EventModel.start_time >= filters.start_date_from)
    if filters.start_date_to:
        query = query.filter(EventModel.start_time <= filters.start_date_to)
    if filters.is_public:
        query = query.filter(EventModel.is_public == filters.is_public)
    if filters.search:
        search = f"%{filters.search}%"
        query = query.filter(
            or_(
                EventModel.title.ilike(search),
                EventModel.description.ilike(search),
                EventModel.location.ilike(search),
                cast(EventModel.tags, String).ilike(search)
            )
        )
        
    # Get total count
    total = query.count()
    
    # Calculate pagination
    total_pages = (total + filters.size - 1) // filters.size
    skip = (filters.page - 1) * filters.size

    # Execute query with pagination
    events = query.offset(skip).limit(filters.size).all()
    
    # Format response
    formatted_events = []
    for event, accepted_count, pending_count in events:
        event_dict = {
            "id": event.id,
            "title": event.title,
            "description": event.description,
            "start_time": event.start_time,
            "end_time": event.end_time,
            "location": event.location,
            "meeting_link": event.meeting_link,
            "tags": event.tags,
            "attendee_limit": event.attendee_limit,
            "attendee_count": event.attendee_count,
            "notes": event.notes,
            "event_type": event.event_type,
            "status": event.status,
            "lattitude": event.lattitude,
            "longitude": event.longitude,
            "linked_entity_type": event.linked_entity_type,
            "telegram_group_link": event.telegram_group_link,
            "whatsapp_group_link": event.whatsapp_group_link,
            "is_public": event.is_public,
            "accepted_count": accepted_count,
            "pending_count": pending_count,
            "created_at": event.created_at,
            "updated_at": event.updated_at
        }
        formatted_events.append(EventListResponseSchemas(**event_dict))
    
    return PaginatedEventListResponse(
        items=formatted_events,
        total=total,
        page=filters.page,
        size=filters.size,
        total_pages=total_pages
    )

def get_event_by_id_service(db: Session, event_id: int, user) -> dict:
    query = db.query(
        EventModel,
    ).outerjoin(
        EventModel.invitations
    ).outerjoin(
        EventModel.tickets
    ).outerjoin(
        EventInvitation.contact
    ).outerjoin(
        Contact.user_contact
    ).outerjoin(
        User.profile
    ).filter(
        EventModel.id == event_id
    ).group_by(
        EventModel.id
    )

    # If not admin, filter for public events or events created by user
    if user.user_type != UserRoleEnum.ADMIN:
        query = query.filter(
            or_(
                EventModel.is_public == True,
                EventModel.created_by == user.id
            )
        )

    event_obj = query.first()
    
    if not event_obj:
        raise CustomException().not_found_exception()
        
    # Format invitations with contact info
    invitations = []
    for invitation in event_obj.invitations:
        contact = invitation.contact
        user = contact.user_contact
        profile = user.profile
        invitation_dict = {
            "id": invitation.id,
            "status": invitation.status,
            "response_message": invitation.response_message,
            "number_of_guests": invitation.number_of_guests,
            "dietary_restrictions": invitation.dietary_restrictions,
            "additional_notes": invitation.additional_notes,
            "response_date": invitation.response_date,
            "response_note": invitation.response_note,
            "uuid": invitation.uuid,
            "contact": {
                "id": contact.id,
                "nickname": contact.nickname,
                "notes": contact.notes,
                "tags": contact.tags,
                "is_favorite": contact.is_favorite,
                "last_contact_date": contact.last_contact_date,
                "full_name": profile.full_name if profile else None,
                "email": user.email,
                "phone_number": user.phone_number if user else None,
                "avatar": user.avatar if user else None,
                "title": profile.title if profile else None
            }
        }
        invitations.append(invitation_dict)

    # Format tickets
    tickets = [
        {
            "id": ticket.id,
            "name": ticket.name,
            "description": ticket.description,
            "price": ticket.price,
            "quantity": ticket.quantity,
            "start_sale": ticket.start_sale,
            "end_sale": ticket.end_sale,
        }
        for ticket in event_obj.tickets
    ]
    
    return {
        "id": event_obj.id,
        "title": event_obj.title,
        "description": event_obj.description,
        "start_time": event_obj.start_time,
        "end_time": event_obj.end_time,
        "location": event_obj.location,
        "meeting_link": event_obj.meeting_link,
        "tags": event_obj.tags,
        "attendee_limit": event_obj.attendee_limit,
        "attendee_count": event_obj.attendee_count,
        "notes": event_obj.notes,
        "event_type": event_obj.event_type,
        "status": event_obj.status,
        "lattitude": event_obj.lattitude,
        "longitude": event_obj.longitude,
        "linked_entity_type": event_obj.linked_entity_type,
        "telegram_group_link": event_obj.telegram_group_link,
        "whatsapp_group_link": event_obj.whatsapp_group_link,
        "is_public": event_obj.is_public,
        "invitations": invitations,
        "tickets": tickets,
        "created_at": event_obj.created_at,
        "updated_at": event_obj.updated_at
    }

def create_event_service(db: Session, event_data: EventCreate, user) -> EventModel:
    try:
        # Validate user
        if user.user_type == UserRoleEnum.ADMIN:
            user = db.query(User).filter(User.id == event_data.user_id).first()
            if not user:
                raise CustomException(content={"error":f"User with id {event_data.user_id} not found"}).not_found_exception()
        

        # Validate contacts if provided
        if event_data.contact_ids:
            # Query to check if all contacts exist and belong to the user
            contacts = db.query(Contact).filter(
                Contact.id.in_(event_data.contact_ids),
                Contact.user_id == user.id,
                Contact.is_deleted == False
            ).all()
            
            # Check if all requested contacts were found
            found_contact_ids = {contact.id for contact in contacts}
            missing_contacts = set(event_data.contact_ids) - found_contact_ids
            
            if missing_contacts:
                raise CustomException(content={"error":f"Contacts with IDs {missing_contacts} not found or do not belong to you"}).bad_request_exception()
        
        # Create event - exclude contact_ids from event data
        event_dict = event_data.dict(exclude={'contact_ids','user_id'})
        event = EventModel(**event_dict, created_by=user.id)
        db.add(event)
        db.commit()
        db.refresh(event)
        
        # Add contacts to event and create invitations if provided
        if event_data.contact_ids:
            # Create EventInvitation for each contact
            for contact in contacts:
                invitation = EventInvitation(
                    event_id=event.id,
                    contact_id=contact.id,
                    status=RSVPStatusEnum.PENDING,
                    uuid=str(uuid.uuid4())  # Generate unique UUID for each invitation
                )
                db.add(invitation)
            
            db.commit()
            db.refresh(event)
        
        return event
        
    except Exception as e:
        print(e)    
        db.rollback()
        raise CustomException().bad_request_exception()

def update_event_service(db: Session, event_id: int, event_data: EventUpdate, user) -> EventModel:
    query = db.query(
        EventModel,
    ).outerjoin(
        EventModel.invitations
    ).outerjoin(
        EventModel.tickets
    ).outerjoin(
        EventInvitation.contact
    ).outerjoin(
        Contact.user_contact
    ).outerjoin(
        User.profile
    ).filter(
        EventModel.id == event_id
    ).group_by(
        EventModel.id
    )

    # If not admin, filter for public events or events created by user
    if user.user_type != UserRoleEnum.ADMIN:
        query = query.filter(
            or_(
                EventModel.is_public == True,
                EventModel.created_by == user.id
            )
        )

    event = query.first()
    
    if not event:
        raise CustomException().not_found_exception()
    
    # Lấy dữ liệu gốc từ event (SQLAlchemy object -> dict)
    original_data = {c.name: getattr(event, c.name) for c in event.__table__.columns}
    # Inject original_data vào event_data để validation nâng cao
    event_data._original_data = original_data
    # Trigger validation lại (nếu cần)
    event_data = EventUpdate(**event_data.dict(exclude_unset=True))
    event_data._original_data = original_data
    # Cập nhật các trường
    for key, value in event_data.dict(exclude_unset=True).items():
        setattr(event, key, value)
    db.commit()
    db.refresh(event)
    return event

def delete_event_service(db: Session, event_id: int, user):
    event = get_event_by_id_service(db, event_id, user)
    db.delete(event)
    db.commit()

def add_interaction_service(db: Session, event_id: int, interaction_data: dict, user):
    event = get_event_by_id_service(db, event_id, user)
    interaction = EventInteraction(event_id=event_id, user_id=user.id, **interaction_data)
    db.add(interaction)
    db.commit()
    db.refresh(interaction)
    return interaction

def get_events_by_date_range_service(db: Session, start_date: datetime, end_date: datetime, user_id: int) -> List[EventModel]:
    return db.query(EventModel).filter(
        and_(
            EventModel.start_time >= start_date,
            EventModel.end_time <= end_date,
            EventModel.created_by == user_id
        )
    ).all()

def add_contacts_to_event_service(
    db: Session,
    event_id: int,
    user_id: int,
    data: AddContactsToEventSchemas
) -> EventModel:
    contact_ids = data.contact_ids
    total_ticket = 0 if data.invite_type == EventInvitationEnum.INVITE_ONLY else len(contact_ids)
    ticket = None

    # Check if event exists and belongs to user
    event = db.query(EventModel).filter(
        EventModel.id == event_id,
        EventModel.created_by == user_id,
        EventModel.is_deleted == False
    ).first()
    
    if not event:
        raise CustomException(content={"error": "Event not found"}).not_found_exception()

    if total_ticket:
        # Lấy ticket đầu tiên của event (hoặc có thể chọn ticket phù hợp hơn nếu cần)
        ticket = db.query(Ticket).filter(
            Ticket.event_id == event_id,
            Ticket.quantity > 0
        ).order_by(Ticket.id.asc()).first()
        if not ticket:
            raise CustomException(content={"error":"No available ticket for this event"}).bad_request_exception()
        # Tạo ticket_order cho contact.user_contact_id
        if not contact.user_contact_id:
            raise CustomException(content={"error":f"Contact {contact.id} does not have a linked user to assign ticket"}).bad_request_exception()
        # Trừ số lượng ticket
        if ticket.quantity < total_ticket:
            raise CustomException(content={"error":"Not enough ticket quantity"}).bad_request_exception()

    try:
        # Get contacts to validate they exist and belong to user
        contacts = db.query(Contact).filter(
            Contact.id.in_(contact_ids),
            Contact.user_id == user_id,
            Contact.is_deleted == False
        ).all()
        
        # Check if all requested contacts were found
        found_contact_ids = {contact.id for contact in contacts}
        missing_contacts = set(contact_ids) - found_contact_ids
        
        if missing_contacts:
            raise CustomException(content={"error": f"Contacts with IDs {missing_contacts} not found or do not belong to you"}).not_found_exception()
        
        # Get contacts that haven't been invited yet
        existing_contact_invitations = db.query(EventInvitation).filter(
            EventInvitation.event_id == event_id,
            EventInvitation.contact_id.in_(contact_ids)
        ).all()
        existing_contact_ids = {inv.contact_id for inv in existing_contact_invitations}
        new_contact_ids = set(contact_ids) - existing_contact_ids
        
        # Create invitations for new contacts
        for contact in contacts:
            if contact.id in new_contact_ids:  # Only create invitation for new contacts
                invitation = EventInvitation(
                    event_id=event_id,
                    contact_id=contact.id,
                    status=RSVPStatusEnum.PENDING,
                    uuid=str(uuid.uuid4()),
                    type=data.invite_type
                )
                db.add(invitation)

                # Nếu data.invite_type == EventInvitationEnum.INVITE_WITH_TICKET
                if data.invite_type == EventInvitationEnum.INVITE_WITH_TICKET:
                    ticket_order = TicketOrder(
                        ticket_id=ticket.id,
                        user_id=contact.user_contact_id,
                        quantity=1,
                        total_price=ticket.price,
                        payment_status=PaymentStatusEnum.PAID
                    )
                    db.add(ticket_order)
                    
        if total_ticket:
            ticket.quantity -= total_ticket
            db.add(ticket)
        db.commit()
        db.refresh(event)

        return event

    except Exception as e:
        db.rollback()
        raise e


def cancel_event_service(db: Session, event_id: int, user, data: CancelEventSchemas) -> EventModel:
    """Cancel an event by setting its status to CANCELLED."""
    query = db.query(
        EventModel,
    ).outerjoin(
        EventModel.invitations
    ).outerjoin(
        EventModel.tickets
    ).outerjoin(
        EventInvitation.contact
    ).outerjoin(
        Contact.user_contact
    ).outerjoin(
        User.profile
    ).filter(
        EventModel.id == event_id
    ).group_by(
        EventModel.id
    )

    # If not admin, filter for public events or events created by user
    if user.user_type != UserRoleEnum.ADMIN:
        query = query.filter(
            or_(
                EventModel.is_public == True,
                EventModel.created_by == user.id
            )
        )

    event = query.first()
    
    if not event:
        raise CustomException().not_found_exception()

    event.status = EventStatusEnum.CANCELLED
    if data.notes:
        event.notes = data.notes
    db.commit()
    db.refresh(event)
    return event

