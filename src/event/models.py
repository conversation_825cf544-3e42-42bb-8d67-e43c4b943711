from sqlalchemy import Column, Date, DateTime, Enum, Float, ForeignKey, Integer, String, Text, Boolean, LargeBinary, Table, UniqueConstraint, JSON
from src.utils.base_model import BaseModel, Base
from sqlalchemy.orm import relationship
from src.utils.enum import EventInvitationEnum, EventTypeEnum, EventStatusEnum, EntityTypeEnum, RSVPStatusEnum, EventAccessEnum
from src.news.models import News
from datetime import datetime
import enum

class RSVPStatus(enum.Enum):
    PENDING = "pending"
    ACCEPTED = "accepted"
    DECLINED = "declined"
    MAYBE = "maybe"

class Event(BaseModel, Base):
    __tablename__ = "event"

    title = Column(String(256), nullable=False)
    description = Column(Text, nullable=True)
    start_time = Column(DateTime, nullable=False)
    end_time = Column(DateTime, nullable=False)
    event_type = Column(Enum(EventTypeEnum), nullable=False, default=EventTypeEnum.INPERSON)
    status = Column(Enum(EventStatusEnum), nullable=False, default=EventStatusEnum.UPCOMING)
    event_access = Column(Enum(EventAccessEnum), nullable=False, default=EventAccessEnum.FREE)
    location = Column(String(256), nullable=True)
    meeting_link = Column(String(256), nullable=True)
    tags = Column(JSON, nullable=True)
    attendee_limit = Column(Integer, nullable=True)
    attendee_count = Column(Integer, nullable=True)
    notes = Column(Text, nullable=True)
    lattitude = Column(Float, nullable=True)
    longitude = Column(Float, nullable=True)
    linked_entity_type = Column(Enum(EntityTypeEnum), nullable=True)
    telegram_group_link = Column(String(256), nullable=True)
    whatsapp_group_link = Column(String(256), nullable=True)
    is_public = Column(Boolean, default=False)
    rsvp_limit = Column(Integer, nullable=True)

    created_by = Column(Integer, ForeignKey('user.id'), nullable=False)
    
    # Relationships
    interactions = relationship("EventInteraction", back_populates="event")
    creator = relationship("User", foreign_keys=[created_by])
    invitations = relationship("EventInvitation", back_populates="event")
    tickets = relationship("Ticket", back_populates="event")

class EventInteraction(BaseModel, Base):
    __tablename__ = "event_interaction"

    event_id = Column(Integer, ForeignKey('event.id'))
    user_id = Column(Integer, ForeignKey('user.id'))
    interaction_type = Column(String(50))  # e.g., "Status Update", "Note Added"
    description = Column(Text)
    date = Column(DateTime)
    
    # Relationships
    event = relationship("Event", back_populates="interactions")
    user = relationship("User")

class EventInvitation(BaseModel, Base):
    __tablename__ = "event_invitation"

    event_id = Column(Integer, ForeignKey("event.id"), nullable=False)
    contact_id = Column(Integer, ForeignKey("contact.id"), nullable=False)
    
    status = Column(Enum(RSVPStatusEnum), default=RSVPStatusEnum.PENDING)
    type = Column(Enum(EventInvitationEnum), default=EventInvitationEnum.INVITE_ONLY)
    response_message = Column(Text, nullable=True)
    number_of_guests = Column(Integer, default=1)
    dietary_restrictions = Column(Text, nullable=True)
    additional_notes = Column(Text, nullable=True)
    response_date = Column(DateTime, nullable=True)
    response_note = Column(Text, nullable=True)
    uuid = Column(String(256), nullable=False)
    is_public = Column(Boolean, default=False)
    
    # Relationships
    event = relationship("Event", back_populates="invitations")
    contact = relationship("Contact", back_populates="invitations")