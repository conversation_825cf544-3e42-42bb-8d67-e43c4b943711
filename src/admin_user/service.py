from sqlalchemy.orm import Session, joinedload
from uuid import uuid4
from typing import Optional

from src.user.models import (
    User as UserModel,
    UserProfile as UserProfileModel,
    UserNotificationSettings,
    UserSubscriptionSettings,
    UserWebLinkSettings,
)
from src.organization.service import create_organization_service, update_organization_service
from src.organization.service import get_organization_service
from src.organization.schemas import OrganizationCreate, OrganizationUpdate
from src.utils.exceptions import CustomException
from src.utils.enum import UserRoleEnum
from src.user.service import get_password_hash
from .schemas import MerchantCreate, MerchantUpdate, MerchantListResponse, MerchantResponse


def create_merchant_service(db: Session, data: MerchantCreate) -> UserModel:
    existing = db.query(UserModel).filter(UserModel.email == data.email, UserModel.is_deleted == False).first()
    if existing:
        raise CustomException(content={"error": ["Email already exists"]}).bad_request_exception()

    org = create_organization_service(db, data.organization)
    hashed_pwd = get_password_hash(data.password)
    user = UserModel(
        email=data.email,
        password=hashed_pwd,
        user_type=UserRoleEnum.MERCHANT,
        is_active=True,
        organization_id=org.id,
        qr_code=uuid4().hex[:8],
    )
    db.add(user)
    db.flush()

    profile = UserProfileModel(full_name=data.full_name, user_id=user.id)
    db.add(profile)

    notification = UserNotificationSettings(user_id=user.id)
    subscription = UserSubscriptionSettings(user_id=user.id)
    web_link = UserWebLinkSettings(user_id=user.id, public_profile_link=f"profile/{user.id}")
    db.add_all([notification, subscription, web_link])

    db.commit()
    db.refresh(user)
    return user


def get_merchant_service(db: Session, user_id: int) -> Optional[UserModel]:
    return db.query(UserModel).options(joinedload(UserModel.profile), joinedload(UserModel.organization)).filter(
        UserModel.id == user_id,
        UserModel.user_type == UserRoleEnum.MERCHANT,
        UserModel.is_deleted == False,
    ).first()


def list_merchants_service(db: Session, page: int = 1, size: int = 10) -> MerchantListResponse:
    if page < 1:
        page = 1
    if size < 1:
        size = 10
    if size > 100:
        size = 100
    skip = (page - 1) * size
    query = db.query(UserModel).filter(UserModel.user_type == UserRoleEnum.MERCHANT, UserModel.is_deleted == False)
    total = query.count()
    items = query.options(joinedload(UserModel.profile), joinedload(UserModel.organization)).offset(skip).limit(size).all()
    pages = (total + size - 1) // size
    responses = []
    for u in items:
        responses.append(
            MerchantResponse.model_validate(
                {
                    "id": u.id,
                    "email": u.email,
                    "full_name": u.profile.full_name if u.profile else None,
                    "is_active": u.is_active,
                    "organization": u.organization,
                }
            )
        )
    return MerchantListResponse(items=responses, total=total, page=page, size=size, pages=pages)


def update_merchant_service(db: Session, user_id: int, data: MerchantUpdate) -> UserModel:
    user = get_merchant_service(db, user_id)
    if not user:
        raise CustomException(content={"error": ["User not found"]}).bad_request_exception()

    if data.full_name is not None:
        if user.profile:
            user.profile.full_name = data.full_name
        else:
            profile = UserProfileModel(full_name=data.full_name, user_id=user.id)
            db.add(profile)
    if data.password is not None:
        user.password = get_password_hash(data.password)
    if data.organization is not None:
        if user.organization_id:
            update_organization_service(db, user.organization_id, data.organization)
        else:
            org = create_organization_service(db, data.organization)
            user.organization_id = org.id
    db.add(user)
    db.commit()
    db.refresh(user)
    return user


def delete_merchant_service(db: Session, user_id: int) -> None:
    user = get_merchant_service(db, user_id)
    if not user:
        raise CustomException(content={"error": ["User not found"]}).bad_request_exception()
    user.is_deleted = True
    db.add(user)
    db.commit()
