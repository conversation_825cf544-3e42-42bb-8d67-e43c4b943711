from typing import Optional, List
from pydantic import BaseModel, Field, EmailStr
from src.organization.schemas import OrganizationCreate, OrganizationUpdate, OrganizationResponse

class MerchantCreate(BaseModel):
    email: EmailStr = Field(..., example="<EMAIL>")
    password: str = Field(..., example="Password123!")
    full_name: str = Field(..., example="Merchant Name")
    organization: OrganizationCreate

class MerchantUpdate(BaseModel):
    full_name: Optional[str] = Field(None, example="Merchant Name")
    password: Optional[str] = Field(None, example="Password123!")
    organization: Optional[OrganizationUpdate] = None

class MerchantResponse(BaseModel):
    id: int
    email: EmailStr
    full_name: Optional[str]
    is_active: bool
    organization: Optional[OrganizationResponse]

    class Config:
        from_attributes = True

class MerchantListResponse(BaseModel):
    items: List[MerchantResponse]
    total: int
    page: int
    size: int
    pages: int

    model_config = {
        "json_schema_extra": {
            "examples": [
                {
                    "items": [],
                    "total": 0,
                    "page": 1,
                    "size": 10,
                    "pages": 1,
                }
            ]
        }
    }
