from fastapi import APIRouter, Depends
from sqlalchemy.orm import Session

from src.utils.database import get_db
from src.utils.token_docs import jwt_auth
from src.utils.permission import is_admin
from src.utils.response import CustomResponse
from src.user.models import User as UserModel

from .schemas import MerchantCreate, MerchantUpdate
from .service import (
    create_merchant_service,
    list_merchants_service,
    get_merchant_service,
    update_merchant_service,
    delete_merchant_service,
)

router = APIRouter()
tags = ["Admin Merchant"]


@router.post("", dependencies=[Depends(jwt_auth)], tags=tags)
def create_merchant(
    data: MerchantCreate,
    db: Session = Depends(get_db),
    current_user: UserModel = Depends(is_admin),
):
    merchant = create_merchant_service(db, data)
    return CustomResponse(content=merchant).format_data_create()


@router.get("", dependencies=[Depends(jwt_auth)], tags=tags)
def list_merchants(
    page: int = 1,
    size: int = 10,
    db: Session = Depends(get_db),
    current_user: UserModel = Depends(is_admin),
):
    merchants = list_merchants_service(db, page, size)
    return CustomResponse(content=merchants).format_data_get()


@router.get("/{user_id}", dependencies=[Depends(jwt_auth)], tags=tags)
def get_merchant(
    user_id: int,
    db: Session = Depends(get_db),
    current_user: UserModel = Depends(is_admin),
):
    merchant = get_merchant_service(db, user_id)
    if not merchant:
        return CustomResponse().format_data_not_found()
    return CustomResponse(content=merchant).format_data_get()


@router.put("/{user_id}", dependencies=[Depends(jwt_auth)], tags=tags)
def update_merchant(
    user_id: int,
    data: MerchantUpdate,
    db: Session = Depends(get_db),
    current_user: UserModel = Depends(is_admin),
):
    merchant = update_merchant_service(db, user_id, data)
    return CustomResponse(content=merchant).format_data_update()


@router.delete("/{user_id}", dependencies=[Depends(jwt_auth)], tags=tags)
def delete_merchant(
    user_id: int,
    db: Session = Depends(get_db),
    current_user: UserModel = Depends(is_admin),
):
    delete_merchant_service(db, user_id)
    return CustomResponse().format_data_delete()
